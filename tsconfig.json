{"compilerOptions": {"outDir": "./built/", "sourceMap": true, "strict": true, "noImplicitReturns": true, "resolveJsonModule": true, "module": "esnext", "noImplicitAny": false, "moduleResolution": "node", "target": "esnext", "isolatedModules": true, "allowJs": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}