<template>
  <div>
    <h1 style="text-align: center; color: var(--scp-text-color-lighter);height:350px;line-height: 350px">{{ message }}</h1>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const $route = useRoute()
const $axios: any = inject('$axios')
const message = ref('Remote Login...')

const handleRlogin = () => {
  const username = $route.query.ru + ''
  const url = $route.query.rn + ''
  $axios({
    method: 'post',
    url: '/remote_login',
    data: {
      username,
      url
    }
  }).then((body) => {
    if (body.status === 200) {
      localStorage.setItem('username', username.toUpperCase())
      localStorage.setItem('name', body.name)
      localStorage.setItem('token', body.token)
      window.location.href = body.url
    } else {
      message.value = 'INVALID REQUEST!'
    }
  }).catch((error) => {
    message.value = 'Error = ' + error
  })
}

onMounted(() => {
  handleRlogin()
})
</script>
