<template>
  <div id="pingssoRedirect">
    <p>
      <font-awesome-icon icon="fa-spinner" spin/>
      Ping SSO Redirecting...
    </p>
    <p>
      <el-link type="primary" @click="gotoLoginPage" style="color: var(--scp-text-color-hightlight-lighter)"><i>Link to Login Page</i></el-link>
    </p>
  </div>
</template>

<script lang="ts" setup>
import { reactive, onBeforeMount, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

const $route = useRoute()
const $store = useStore()
const $router = useRouter()
const $axios: any = inject('$axios')
const $message: any = inject('$message')

const data = reactive({
  code: '',
  code_verifier: ''
})

const gotoLoginPage = () => {
  $router.push('/login')
}

onBeforeMount(() => {
  data.code_verifier = localStorage.getItem('code_verifier') + ''
  data.code = $route.query.code + ''

  $axios({
    method: 'post',
    url: '/pingsso/login',
    data: {
      code: data.code,
      code_verifier: data.code_verifier
    }
  }).then((body) => {
    $store.commit('setEnableManualLogin', false)
    if (body.status === 200) {
      localStorage.setItem('remember', 'Y')
      localStorage.setItem('username', body.userid.toUpperCase())
      localStorage.setItem('name', body.name)
      localStorage.setItem('token', body.token)

      const lastPage = localStorage.getItem('page')
      if ($route.query.redirect) {
        $router.push($route.query.redirect + '')
      } else if (lastPage) {
        $router.push(lastPage)
      } else {
        $router.push('/')
      }
    } else if (body.status === 500) {
      $store.commit('setEnableManualLogin', true)
      $message.error('PingSSO request timed out, please try again')
      $router.push('/login')
    } else {
      if (body.userid) {
        $router.push('/pingsso_failed?u=' + body.userid)
      } else {
        $router.push('/pingsso_failed')
      }
    }
  }).catch((error) => {
    console.log(error)
  })
})
</script>

<style lang="scss">
#pingssoRedirect {
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
  padding-top: 15%;
  color: var(--scp-text-color-lighter)
}
</style>
