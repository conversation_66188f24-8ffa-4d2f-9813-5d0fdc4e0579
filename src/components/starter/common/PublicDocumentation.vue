<template>
  <div style="padding-top: 5px">
    <div style="padding: 12px" :style="{minHeight: initHeight + 'px'}" v-loading="loading.content">
      <div style="margin-bottom: 10px" v-show="viewDoc.CREATE_BY !== ''">
        <div class="markdown-body">
          <h2>{{ viewDoc.SUBJECT }}
            <div style="width: 600px;float: right;text-align:right; font-size: 0.45rem;color: var(--scp-text-color-secondary);font-weight: normal;height: 1.5rem;line-height: 1.5rem">
              作者: {{ viewDoc.CREATE_BY }} | 创建时间: {{ viewDoc.CREATE_DATE }} | 最后编辑: {{ viewDoc.UPDATE_BY || viewDoc.CREATE_BY }} | 编辑时间:
              {{ viewDoc.UPDATE_DATE || viewDoc.CREATE_DATE }}
            </div>
          </h2>
        </div>
      </div>
      <scp-md-preview v-model="viewDoc.CONTENT"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, watch } from 'vue'
import { onBeforeRouteUpdate, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'

const $store = useStore()
const $route = useRoute()
const $axios: any = inject('$axios')
const loading = reactive({
  content: false
})
const viewDoc = reactive({
  DOC_ID: '',
  CONTENT: '',
  SUBJECT: '',
  GROUPS: '',
  CREATE_BY: '',
  CREATE_DATE: '',
  UPDATE_BY: '',
  UPDATE_DATE: ''
})

const initHeight = document.documentElement.clientHeight - 150

const getContentByDocID = (docid) => {
  loading.content = true
  $axios({
    method: 'post',
    url: '/system/query_public_documentation',
    data: {
      docid
    }
  }).then((body) => {
    if (body && body.CREATE_BY) {
      const keys = Object.keys(viewDoc)
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        viewDoc[key] = body[key] || ''
      }
    } else {
      viewDoc.CONTENT = '### NO AVAILABLE DOCUMENTATION'
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loading.content = false
  })
}

watch(() => $route.query.id, () => {
  getContentByDocID($route.query.id)
})

const loadPage = () => {
  const id = $route.query.id
  if (id) {
    getContentByDocID(id)
  } else {
    viewDoc.CONTENT = '### INVALID DOC ID'
  }
}

onMounted(() => {
  loadPage()
})

onBeforeRouteUpdate(() => {
  loadPage()
})
</script>
<style lang="scss" scoped>
.markdown-body {
  font-size: 0.5rem !important;
  border-bottom: 1px solid var(--scp-border-color-lighter);
  h2 {
    margin: 5px 0;
  }
}
</style>
