<template>
  <div>
    <iframe :src="url" width="100%" :height="height" style="border: 0"/>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const $route = useRoute()

const height = ref(document.documentElement.clientHeight - 60)
const url = ref('')

const replaceURL = () => {
  const rawUrl = $route.query.u
  if (rawUrl) {
    if (rawUrl.indexOf('standalone=true') === -1) {
      if (rawUrl.indexOf('?') !== -1) {
        url.value = rawUrl + '&standalone=true'
      } else {
        url.value = rawUrl + '?standalone=true'
      }
    } else {
      url.value = rawUrl + ''
    }
  }
}

onBeforeMount(() => {
  replaceURL()
})

watch(() => $route.query.u, () => {
  replaceURL()
})

window.addEventListener('resize', () => {
  height.value = document.documentElement.clientHeight - 60
})
</script>
