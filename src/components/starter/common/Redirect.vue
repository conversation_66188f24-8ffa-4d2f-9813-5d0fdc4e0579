<template>
  <div id="redirecting">
    <p>
      <font-awesome-icon icon="fa-spinner" spin/>
      Redirecting...
    </p>
    <p>
      <el-link type="primary" @click="gotoHomepage" style="color: var(--scp-text-color-hightlight-lighter)"><i>Link to Homepage</i></el-link>
    </p>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount } from 'vue'
import { RouteRecordRaw, useRouter } from 'vue-router'

const $router = useRouter()
const gotoHomepage = () => {
  $router.push('/demand/tracking')
}

const getRouterMeta = (routes: ReadonlyArray<RouteRecordRaw>, result: Array<string>) => {
  for (let i = 0; i < routes.length; i++) {
    const route = routes[i]
    // @ts-ignore
    if (route.meta && route.meta.filter === false) {
      result.push(route.path.split('/:')[0])
    }
    if (route.children && route.children.length > 0) {
      getRouterMeta(route.children, result)
    }
  }
}

onBeforeMount(() => {
  const lastPage = localStorage.getItem('page')
  if (lastPage && lastPage.lastIndexOf('/') !== 0) {
    // 递归方式获取所有meta: filter为false菜单
    const ignoreList = ['/components/table_viewer']
    getRouterMeta($router.options.routes, ignoreList)

    let canRedirect = true
    for (let i = 0; i < ignoreList.length; i++) {
      const ignoreURL = ignoreList[i]
      canRedirect = ignoreURL !== lastPage && lastPage.indexOf(ignoreURL) !== 0
      if (!canRedirect) {
        break
      }
    }

    if (canRedirect) {
      $router.push(lastPage)
    } else {
      gotoHomepage()
    }
  } else {
    gotoHomepage()
  }
})
</script>

<style lang="scss">
#redirecting {
  font-weight: bold;
  font-size: 1.2rem;
  text-align: center;
  padding-top: 15%;
  color: var(--scp-text-color-lighter);
}
</style>
