<template>
  <div id="pingssoFailed">
    <p style="margin-bottom: 20px" class="register-title">You're not registered</p>
    <div style="width: 600px;margin: auto;padding: 30px 100px 10px 20px;box-shadow: 5px 5px 5px 5px rgb(0 0 0 / 5%)">
      <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="150px" style="margin-bottom: 10px">
        <el-form-item label="SESA" prop="sesaNo">
          <el-input v-model="data.form.sesaNo" size="medium" class="input-uppercase"></el-input>
        </el-form-item>
        <el-form-item label="Name" prop="name">
          <el-input v-model="data.form.name" size="medium"></el-input>
        </el-form-item>
        <el-form-item label="Email" prop="email">
          <el-input v-model="data.form.email" size="medium"></el-input>
        </el-form-item>
        <el-form-item label="Job Code" prop="jobCode">
          <el-input v-model="data.form.jobCode" size="medium" class="input-uppercase" :placeholder="data.rules.jobCode[0].message"></el-input>
        </el-form-item>
        <el-form-item label="Job Title" prop="jobTitle">
          <el-input v-model="data.form.jobTitle" size="medium"></el-input>
        </el-form-item>
        <el-form-item label="Line Manager's SESA" prop="lineManagerSesaNo">
          <el-input v-model="data.form.lineManagerSesaNo" size="medium" class="input-uppercase"></el-input>
        </el-form-item>
        <el-form-item label="Entity" prop="entity">
          <el-select v-model="data.form.entity"
                     size="medium"
                     style="width: 100%"
                     placeholder=""
                     filterable>
            <el-option-group
                v-for="group in data.entityOpts"
                :key="group.label"
                :label="group.label">
              <el-option
                  v-for="item in group.options"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="Backup's SESA" prop="backupSesaNo">
          <el-input v-model="data.form.backupSesaNo" size="medium" class="input-uppercase"></el-input>
        </el-form-item>
        <el-form-item label="Registration Reason" prop="reason">
          <el-input v-model="data.form.reason" size="medium" placeholder="Why you need to access DSS"></el-input>
        </el-form-item>
        <el-form-item style="text-align: center">
          <el-button type="primary" @click="onSubmit" size="medium">JOIN US</el-button>
        </el-form-item>
      </el-form>
    </div>

    <hr style="margin: 30px 0 20px 0;">

    <div style="width: 800px;margin: auto;font-size: 0.5rem">
      <p class="guide-title">Registration Guide</p>
      <div>
        <p>1. Open
          <el-link type="primary" style="font-size: 14px" :href=generateUrl() target="_blank">https://spice.se.com</el-link>
        </p>
        <p>2. Find your profile in IDS and follow the picture below to fill the Registration form.</p>
      </div>
      <img src="/img/registration-guide.png" alt="Registration Guide" style="width:100%"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'
const $router = useRouter()

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $startWith: any = inject('$startWith')
const $endWith: any = inject('$endWith')

const data = reactive({
  entityOpts: [],
  form: {
    sesaNo: '',
    name: '',
    email: '',
    jobCode: '',
    jobTitle: '',
    lineManagerSesaNo: '',
    entity: '',
    backupSesaNo: '',
    reason: ''
  },
  rules: {
    sesaNo: [
      { required: true, validator: (rule, value, callback) => checkSESA(rule, value, callback, 'SESA No'), trigger: ['blur', 'change'] }
    ],
    name: [
      { required: true, message: 'Name required', trigger: ['blur', 'change'] }
    ],
    email: [
      { required: true, validator: (rule, value, callback) => checkEmail(rule, value, callback), trigger: ['blur', 'change'] }
    ],
    jobCode: [
      { required: true, min: 4, max: 4, message: '4-digit job code, if you have no job code, use 0000 instead', trigger: ['blur', 'change'] }
    ],
    jobTitle: [
      { required: true, message: 'Job title required', trigger: ['blur', 'change'] }
    ],
    lineManagerSesaNo: [
      { required: true, validator: (rule, value, callback) => checkSESA(rule, value, callback, 'Line Manager\'s SESA'), trigger: ['blur', 'change'] }
    ],
    entity: [
      { required: true, message: 'Entity required', trigger: ['blur', 'change'] }
    ],
    backupSesaNo: [
      { validator: (rule, value, callback) => checkSESA(rule, value, callback, 'Backup\'s SESA'), trigger: ['blur', 'change'] }
    ],
    reason: [
      { required: true, message: 'Why you need to access DSS', trigger: ['blur', 'change'] }
    ]
  }
})

const generateUrl = () => {
  const NodeBuffer = require('buffer').Buffer

  const unicodeString = localStorage.getItem('username') + '@SE.COM'
  const base64EncodedUnicode = NodeBuffer.from(unicodeString, 'utf-8').toString('base64')

  return 'https://spice.se.com/#/sitepages/employeedetail.aspx?uid=' + base64EncodedUnicode
}

const checkSESA = (rule, value, callback, name) => {
  const v = (value || '').toUpperCase()
  if (rule.required === true) {
    if ($startWith(v, 'SESA') === false || v.length < 6) {
      callback(new Error(name + ' must start with SESA and contain at least 6 letters'))
    } else {
      callback()
    }
  } else {
    if (v.length > 0) {
      if ($startWith(v, 'SESA') === false || v.length < 6) {
        callback(new Error(name + ' must start with SESA and contain at least 6 letters'))
      } else {
        callback()
      }
    } else {
      callback()
    }
  }
}

const checkEmail = (rule, value, callback) => {
  const v = (value || '').toLowerCase()
  if (v === '@se.com' || v === '@non.se.com') {
    callback(new Error('Please provide a valid email address'))
    return false
  }
  if ($endWith(v, '@se.com') === false && $endWith(v, '@non.se.com') === false) {
    callback(new Error('Email address must end with @se.com or @non.se.com'))
    return false
  } else {
    callback()
    return true
  }
}

const formRef = ref<FormInstance>()

const initPage = () => {
  $axios({
    method: 'post',
    url: '/query_available_entity'
  }).then((body) => {
    data.entityOpts = body
  }).catch((error) => {
    console.log(error)
  })
}

const generateUserInfo = () => {
  $axios({
    method: 'post',
    url: '/query_user_info',
    data: {
      sesaCode: $router.currentRoute.value.query.u
    }
  }).then((body) => {
    if (body) {
      data.form.sesaNo = body.SESA_CODE
      data.form.name = body.USER_NAME
      data.form.email = body.EMAIL
      data.form.jobCode = body.JOB_CODE
      data.form.lineManagerSesaNo = body.LINE_MANAGER
      data.form.jobTitle = body.JOB_TITLE
    }
  }).catch((error) => {
    console.log(error)
  })
}

const onSubmit = () => {
  if (!formRef.value) {
    return false
  }
  formRef.value.validate((valid) => {
    if (valid) {
      $axios({
        method: 'post',
        url: '/create_new_account',
        data: data.form
      }).then((body) => {
        if (body) {
          $message.error(body + '')
        } else {
          $message.success('Thanks for your registration. We will contact you after account created.')
        }
      }).catch((error) => {
        console.log(error)
      })
      return true
    } else {
      return false
    }
  })
  return true
}

onMounted(() => {
  if (formRef.value) {
    formRef.value?.resetFields()
    initPage()
  }
  generateUserInfo()
})
</script>

<style lang="scss">
#pingssoFailed {
  padding-bottom: 100px;

  .register-title {
    font-weight: bold;
    font-size: 0.8rem;
    text-align: center;
    padding-top: 20px;
  }

  .input-uppercase {
    input {
      text-transform: uppercase;
    }
  }

  .guide-title {
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
  }

  .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 14px !important
  }

  .el-form-item--small .el-form-item__content, .el-form-item--small .el-form-item__label {
    line-height: 26px !important;
  }

  .el-input__inner {
    height: 26px !important;
    line-height: 26px !important;
  }

  .el-form-item__error {
    font-size: 11px !important;
  }
}
</style>
