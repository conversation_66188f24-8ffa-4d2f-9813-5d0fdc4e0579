<template>
  <div class="left-sidebar" id="mailSignature">
    <div class="widget">
      <div class="widget-body">
        <el-upload
            ref="uploadRef"
            style="padding:15px"
            accept=".htm,.jpg,.png,.jpeg,.gif"
            multiple
            :on-success="onUploadEnd"
            :headers="data.uploadHeader"
            v-model="data.uploadFileList"
            :on-change="onUploadChange"
            :on-remove="onUploadRemove"
            :on-progress="onProgress"
            :show-file-list="true"
            :limit="3"
            :auto-upload="false"
            :action="data.uploadUrl">
          <template #trigger>
            <el-button size="small" type="primary">Select a file</el-button>
          </template>
          <el-button size="small" @click="submitUpload" :loading="data.uploading" style="margin-left: 10px;margin-top:-2px"
                     :disabled="data.uploadFileList.length === 0">Upload
          </el-button>
          <br/>
          <template #tip>
            <div class="el-upload__tip">
              <p>&nbsp;</p>
              <p>从Outlook中找到签名文件并上传: </p>
              <p>1. 点击Select按钮, 在弹出来的窗口地址栏中输入<b>%userprofile%\AppData\Roaming\Microsoft\Signatures</b>, 并回车</p>
              <p>2. 在跳转后的窗口可以看到后缀名为txt, rft和htm的三个签名文件, 和一个文件夹(如果本机有多个签名, 就能看到多组文件)</p>
              <p>3. 选择希望展示的签名文件, 和与这个文件同名文件夹中的一张图片(如果签名中不包含图片, 可以不用上传图片文件)</p>
              <p>4. 选择好后, 点击Upload按钮, 等待系统上传完成 </p>
              <p>查找Outlook签名文件的图文教程可点击 => <a style="color: var(--scp-text-color-highlight)"
                                                           href="https://zh-cn.extendoffice.com/documents/outlook/1453-outlook-import-export-signatures.html"
                                                           target="_blank">Outlook-Import-Export-Signatures</a></p>
            </div>
          </template>
        </el-upload>
      </div>
    </div>
    <br>
    <div class="widget">
      <div class="widget-body">
        <div v-html="data.signature" v-loading="data.uploading"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref, nextTick } from 'vue'

const $axios: any = inject('$axios')
const $notify: any = inject('$notify')
const $message: any = inject('$message')
const $baseUrl: any = inject('$baseUrl')

const uploadRef = ref()

const data = reactive({
  uploadUrl: $baseUrl + '/system/upload_mail_signature',
  uploadHeader: { token: localStorage.getItem('token') },
  uploadFileList: [] as Array<string>,
  uploading: false,
  signature: '',
  uploadedCnt: 0
})

const loadSignature = () => {
  $axios({
    method: 'post',
    url: '/system/query_mail_signature'
  }).then((body) => {
    data.signature = body
  }).catch((error) => {
    console.log(error)
  })
}

const submitUpload = () => {
  data.uploadedCnt = 0
  if (uploadRef.value) {
    uploadRef.value.submit()
  }
}

const onProgress = () => {
  data.uploading = true
}

const onUploadChange = (file) => {
  if (data.uploadFileList.indexOf(file) === -1) {
    data.uploadFileList.push(file)
  }
}

const onUploadRemove = (file) => {
  const index = data.uploadFileList.indexOf(file)
  if (index > -1) {
    data.uploadFileList.splice(index, 1)
  }
}

const onUploadEnd = (res) => {
  if (res.header.status !== 200) {
    $notify({
      title: 'Upload Failed',
      message: res.header.message,
      dangerouslyUseHTMLString: true,
      type: 'error'
    })
  } else {
    data.uploadedCnt = data.uploadedCnt + 1
    if (data.uploadedCnt === data.uploadFileList.length) {
      nextTick(() => {
        $message.success('Upload Success')
        data.uploading = false
        data.uploadFileList = []
        loadSignature()
      })
    }
  }
}

onMounted(() => {
  loadSignature()
})
</script>
<style lang="scss">
#mailSignature {
  p {
    margin: 0 0 10px;
  }
}
</style>
