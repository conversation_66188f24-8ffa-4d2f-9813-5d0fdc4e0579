<template>
  <div class="left-sidebar" id="myFavourite">
    <div class="widget">
      <div class="widget-body" v-loading="data.loading">
        <div class="drawer-layout-box">
          <div class="body" v-for="(e,i) in _selectedObj" :key="i" @click="gotoMenu(e['URL'], e['LABEL'], e['ID'])">
            <font-awesome-icon class="icon" :icon="e['ICON']"/>
            <p>{{ e['LABEL'] }}</p>
          </div>
          <div class="body" @click="data.drawer = true" style="padding-top:2rem;border:0">
            <font-awesome-icon class="icon" icon="ellipsis-h"/>
          </div>
          <div class="clearfix"/>
        </div>
        <el-drawer v-model="data.drawer"
                   size="20.8rem"
                   @close="saveFavourites"
                   direction="rtl">
          <template #header>
          <span>
            Available Components<span style="font-size: 0.4rem"> ({{_availableObjSize}}/{{_availableObjTotalSize}})</span>&nbsp;
            <el-input class="component-filter" v-model="data.filter" clearable size="default"/>
          </span>
          </template>
          <div class="drawer-layout-box" v-for="(sub,i) in _availableObj" :key="i">
            <div class="header" v-if="sub.length > 0">
              <font-awesome-icon class="icon" icon="bookmark"/>
              &nbsp;&nbsp;{{ sub[0]['GROUP_NAME'] }}
            </div>
            <div class="body" v-for="(e,j) in sub" :key="i+'#'+j">
              <div @click="gotoMenu(e['URL'], e['LABEL'], e['ID'])">
                <font-awesome-icon class="icon" :icon="e['ICON']"/>
                <p>{{ e['LABEL'] }}</p>
              </div>
              <el-tooltip class="item" effect="light" content="Add to favourite" placement="bottom">
                <el-checkbox class="checkbox" v-model="e['CHECK']"></el-checkbox>
              </el-tooltip>
            </div>
            <div class="clearfix"/>
          </div>
        </el-drawer>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'

const $router = useRouter()
const $axios: any = inject('$axios')

interface Component {
  'GROUP_NAME': string,
  'LABEL': string,
  'USER_ID': string,
  'CHECK': boolean,
  'ID': string,
  'ICON': string,
  'URL': string
}

const data = reactive({
  drawer: false,
  loading: false,
  filter: '',
  availableObj: [] as Array<Array<Component>>
})

const _selectedObj = computed(() => {
  const r0 = [] as Array<Component>
  for (let i = 0; i < data.availableObj.length; i++) {
    r0.push(...data.availableObj[i].filter(e => e.CHECK))
  }
  return r0
})

const _availableObj = computed(() => {
  if (!data.filter) {
    return data.availableObj
  }
  const r = [] as Array<Array<Component>>
  for (let i = 0; i < data.availableObj.length; i++) {
    const sub = data.availableObj[i]
    const subList = [] as Array<Component>
    for (let j = 0; j < sub.length; j++) {
      const subsub = sub[j]
      if (subsub.GROUP_NAME.toLowerCase().indexOf(data.filter.toLowerCase()) !== -1 ||
          subsub.LABEL.toLowerCase().indexOf(data.filter.toLowerCase()) !== -1) {
        subList.push(subsub)
      }
    }
    if (subList.length > 0) {
      r.push(subList)
    }
  }
  return r
})

const _availableObjSize = computed(() => {
  let count = 0
  for (let i = 0; i < _availableObj.value.length; i++) {
    count += _availableObj.value[i].length
  }
  return count
})

const _availableObjTotalSize = computed(() => {
  let count = 0
  for (let i = 0; i < data.availableObj.length; i++) {
    count += data.availableObj[i].length
  }
  return count
})

const initAvailableObj = () => {
  data.loading = true
  $axios({
    method: 'post',
    url: '/system/query_all_available_components'
  }).then((body) => {
    data.availableObj = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    data.loading = false
  })
}

const saveFavourites = () => {
  $axios({
    method: 'post',
    url: '/system/save_my_favourites',
    data: {
      selectedObj: _selectedObj.value
    }
  }).catch((error) => {
    console.log(error)
  })
  return true
}

const gotoMenu = (url, label, id) => {
  $router.replace({ path: url, query: { id, label } })
}

onMounted(() => {
  initAvailableObj()
})

</script>
<style lang="scss">
#myFavourite {
  header {
    background-color: var(--scp-text-color-highlight);
    height: 2.1rem;
    font-size: 0.54166rem;
    line-height: 4;
    vertical-align: middle;
    max-height: 50px;
  }

  header.el-drawer__header {
    margin-bottom: 10px;
    padding: 0 20px;
    color: #fff;

    .el-input__wrapper {
      box-shadow: none;
      background-color: #fff;
    }
  }

  .el-drawer__body {
    padding: 0.41666rem;
  }

  .drawer-layout-box {
    .header {
      font-size: 0.5rem;
      font-weight: 700;
      padding: 5px 0 0 8px;
      margin: 0;
    }

    .body {
      height: 3.5rem;
      width: 8.8rem;
      padding: 10px 5px 0 5px;
      position: relative;
      box-sizing: border-box;
      text-align: center;
      border: solid 1px var(--scp-border-color);
      margin: 0.333rem;
      float: left;

      .icon {
        font-size: 1.333rem;
      }

      .checkbox {
        position: absolute;
        top: 0;
        right: 5px;
      }

      p {
        margin: 0.333rem 0 0 0;
      }
    }

    .body:hover {
      box-shadow: #e5e5e5 0 0 10px;
      cursor: pointer;
    }
  }

  .el-drawer__close:hover {
    color: var(--scp-bg-color-fill) !important;
  }

  .component-filter {
    width: calc(100% - 8.5rem);
    line-height: 2;

    .el-input__inner {
      background-color: #fff !important;
    }
  }
}

</style>
