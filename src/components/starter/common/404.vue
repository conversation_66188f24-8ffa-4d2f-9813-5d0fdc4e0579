<template>
  <div class="error-page">
    <div class="error-code">4<span>0</span>4</div>
    <div class="error-desc">Sorry, the page you are looking for is currently unavailable.</div>
    <div class="error-handle">
      <el-button type="primary" size="large" @click="goHome">Home</el-button>
      <el-button class="error-btn" type="primary" size="large" @click="goBack">Back</el-button>
    </div>
  </div>
</template>

<script>
import router from '@/router'

export default {
  name: 'PageNotFound',
  methods: {
    goBack () {
      router.go(-1)
    },
    goHome () {
      router.push('/')
    }
  }
}
</script>

<style scoped>
  .error-page {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    padding-top: 5%;
    box-sizing: border-box;
  }

  .error-code {
    line-height: 1;
    font-size: 128px;
    font-weight: bolder;
    color: var(--scp-text-color-highlight);
  }

  .error-code span {
    color: var(--scp-text-color-success);
  }

  .error-desc {
    font-size: 20px;
    margin-top: 20px;
    letter-spacing: 0.04em;
  }

  .error-handle {
    margin-top: 30px;
    padding-bottom: 200px;
  }

  .error-btn {
    margin-left: 60px;
    letter-spacing: 0.06em;
  }
</style>
