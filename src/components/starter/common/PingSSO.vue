<template>
  <div id="pingssoLogin">
    <p><font-awesome-icon icon="fa-spinner" spin/> Ping SSO Login...</p>
    <p><el-link type="primary" @click="gotoLoginPage" style="color: var(--scp-text-color-hightlight-lighter)"><i>Link to Login Page</i></el-link></p>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, reactive } from 'vue'
import { useRouter } from 'vue-router'

const $router = useRouter()
const crypto = require('crypto-browserify')
const data = reactive({
  code_challenge: null,
  code_verifier: null
})

const base64URLEncode = (str) => {
  return str.toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
}
const sha256 = (buffer) => {
  return crypto.createHash('sha256').update(buffer).digest()
}
const gotoLoginPage = () => {
  $router.push('/login')
}

onBeforeMount(() => {
  data.code_verifier = base64URLEncode(crypto.randomBytes(32))
  data.code_challenge = base64URLEncode(sha256(data.code_verifier))
  localStorage.setItem('code_verifier', data.code_verifier + '')

  const pingSSOURL = 'https://ping-sso.schneider-electric.com/as/authorization.oauth2?response_type=code&scope=openid%20profile'
  const clientID = 'GSC%20China%20SCP%20Decision%20Support%20System_D2754'
  const redirectURL = 'https://scp-dss.cn.schneider-electric.com:8443/pingsso/callback'

  // 重定向到Ping SSO服务器
  window.location.href = pingSSOURL + '&client_id=' + clientID + '&redirect_uri=' + redirectURL + '&code_challenge_method=S256&code_challenge=' + data.code_challenge
})
</script>

<style lang="scss">
#pingssoLogin {
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
  padding-top: 15%;
  color: var(--scp-text-color-lighter)
}
</style>
