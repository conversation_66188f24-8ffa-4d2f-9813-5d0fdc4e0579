<template>
  <scp-draggable-resizable w="75vw" h="710px" v-model="_visible"
                           :save="() => {
                             saveBroadcastConfig(ruleFormRef)
                           }"
                           :draft="clearRuleForm"
                           draft-text="Reset"
                           title="Maintain Broadcast">
    <template v-slot="{height}">
      <div style="margin: auto;padding: 10px 70px 0 40px;">
        <el-form
            ref="ruleFormRef"
            class="bc-form"
            :model="ruleForm"
            style="margin: 10px 10px 0 10px;"
            :style="{height: (height - 150) + 'px'}"
            :rules="rules"
            label-width="auto"
            :size="formSize"
            status-icon
        >
          <el-form-item label="Subject" required>
            <el-form-item prop="remindSubjectType" style="margin: 0; width: 30%; font-size: 11px" required>
              <el-select v-model="ruleForm.remindSubjectType" filterable @change="updateRemindSubject">
                <el-option v-for="item in ['【新字段上线】', '【新功能上线】', '【系统通知】', '【通知】']"
                           :key="item"
                           :label="item"
                           :value="item"/>
              </el-select>
            </el-form-item>
            <el-form-item prop="remindSubjectContent" style="margin: 0; width: 70%" required>
              <el-input style="border-radius: 4px" v-model="ruleForm.remindSubjectContent" @input="updateRemindSubject"/>
            </el-form-item>
          </el-form-item>
          <el-form-item label="URL" prop="url">
            <el-select v-model="ruleForm.url" filterable multiple collapse-tags style="width: 100%">
              <el-option v-for="item in pageCtl.maintainOptions.URL"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="Valid Date Range" required>
            <el-col :span="11">
              <el-form-item prop="validFrom" :min-width="150" style="margin: 0">
                <el-date-picker
                    v-model="ruleForm.validFrom"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    format="YYYY-MM-DD HH:mm:ss"
                    aria-label="Valid From"
                    placeholder="Valid From"
                    style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col style="text-align: center" :span="2">
              <span>-</span>
            </el-col>
            <el-col :span="11">
              <el-form-item prop="validTo" :min-width="150" style="margin: 0">
                <el-date-picker
                    v-model="ruleForm.validTo"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    format="YYYY-MM-DD HH:mm:ss"
                    aria-label="Valid To"
                    placeholder="Valid To"
                    style="width: 100%; margin: 0; padding: 0"
                />
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label="Frequency" prop="frequency">
            <el-radio-group style="width: 100%" v-model="ruleForm.frequency">
              <el-radio value="Daily" name="type">Daily</el-radio>
              <el-radio value="Weekly" name="type">Weekly</el-radio>
              <el-radio value="Monthly" name="type">Monthly</el-radio>
              <el-radio value="Yearly" name="type">Yearly</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="Remind Count" prop="remindCount">
            <el-input-number style="width: 300px" v-model="ruleForm.remindCount" :min=0 />
          </el-form-item>
          <el-form-item label="Specific Target" prop="specificTarget">
            <el-cascader placeholder="Default distribution to all users" style="width: 420px" filterable :options="_authOptions" v-model="ruleForm.specificTarget" :props="specificTarget" aria-multiselectable="true" collapse-tags clearable />
          </el-form-item>
          <el-form-item label="Remind Content" prop="description">
            <scp-ck-editor v-model="ruleForm.description"
                           style="width: 100%; height: 300px; overflow-y: hidden"/>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpDraggableResizable from '@/components/starter/components/DraggableResizable.vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'

const $message: any = inject('$message')
const $axios: any = inject('$axios')
const ruleFormRef = ref()
const formSize = ref('default')

interface RuleForm {
  url: []
  remindSubjectType: string
  remindSubjectContent: string
  remindSubject: string
  validFrom: string
  validTo: string
  frequency: string
  remindCount: number
  description: string
  specificTarget: []
}

const props = withDefaults(defineProps<{
  modelValue: boolean,
  ruleForm?: RuleForm | undefined
}>(), {
  modelValue: false,
  ruleForm: () => {
    return {
      url: '',
      remindType: '',
      remindSubject: '',
      validFrom: '',
      validTo: '',
      frequency: '',
      remindCount: 0,
      description: '<p>【简要概括】-</p>' +
          '<p>【举例说明】-</p>' +
          '<p>【截图示例】-</p>' +
          '<p>【提示】-</p>' +
          '<p>【在线介绍】-</p>',
      specificTarget: []
    }
  }
} as any)

const ruleForm = reactive(props.ruleForm) as any

const specificTarget = ref({ multiple: true })

const clearRuleForm = () => {
  ruleForm.url = ''
  ruleForm.remindSubjectType = ''
  ruleForm.remindSubjectContent = ''
  ruleForm.remindSubject = ''
  ruleForm.validFrom = ''
  ruleForm.validTo = ''
  ruleForm.frequency = ''
  ruleForm.remindCount = 0
  ruleForm.description = ''
  ruleForm.specificTarget = []
}

const pageCtl = reactive({
  maintainOptions: {} as any
})

const updateRemindSubject = () => {
  ruleForm.remindSubject = `${ruleForm.remindSubjectType}${ruleForm.remindSubjectContent}`
}

const rules = reactive({
  url: [
    { required: true, message: 'Please Select url', trigger: 'blur' }
  ],
  remindSubjectType: [
    { required: true, message: 'Please select type', trigger: 'blur' }
  ],
  remindSubjectContent: [
    { required: true, message: 'Please input subject', trigger: 'blur' }
  ],
  validFrom: [
    {
      type: 'date',
      required: true,
      message: 'Please pick a start date',
      trigger: 'change'
    }
  ],
  validTo: [
    {
      type: 'date',
      required: true,
      message: 'Please pick a end date',
      trigger: 'change'
    }
  ],
  frequency: [
    {
      required: true,
      message: 'Please select frequency',
      trigger: 'change'
    }
  ],
  remindCount: [
    {
      required: true,
      message: 'Please input remind times',
      trigger: 'change'
    }
  ],
  description: [
    {
      required: true,
      message: 'Please input remind content',
      trigger: 'change'
    }
  ]
})

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}

onMounted(() => {
  queryBroadcastOpts()
})

watch(() => props.modelValue, () => {
  if (ruleForm.remindSubject) {
    const firstSpaceIndex = ruleForm.remindSubject.indexOf('】')

    if (firstSpaceIndex !== -1) { // 检查是否存在至少一个】
      ruleForm.remindSubjectType = ruleForm.remindSubject.substring(0, firstSpaceIndex + 1)
      ruleForm.remindSubjectContent = ruleForm.remindSubject.substring(firstSpaceIndex + 1)
    } else {
      ruleForm.remindSubjectType = null
      ruleForm.remindSubjectContent = ruleForm.remindSubject
    }
  }
})

const queryBroadcastOpts = () => {
  $axios({
    method: 'post',
    url: 'system/broadcast/query_broadcast_options'
  }).then((response) => {
    pageCtl.maintainOptions = response
  }).catch((error) => {
    console.log(error)
  })
}

const _authOptions = computed(() => {
  const result = [] as any
  let idCounter = 1

  for (const [key, values] of Object.entries(pageCtl.maintainOptions)) {
    if (key !== 'URL') {
      const children = values.map(value => ({
        id: idCounter++,
        value,
        label: value
      }))
      result.push({
        id: idCounter++,
        label: key,
        value: key,
        children
      })
    }
  }
  return result
})

const saveBroadcastConfig = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      $axios({
        method: 'post',
        url: 'system/broadcast/save_broadcast_config',
        data: ruleForm
      }).then(() => {
        emmits('update:modelValue', false)
        $message({
          message: 'Broadcast successfully Created',
          center: true,
          type: 'success',
          duration: 1000,
          iconClass: 'el-message__icon el-icon-loading color-white',
          customClass: 'el-message el-message--success'
        })
        clearRuleForm()
      }).catch((error) => {
        console.log(error)
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}

const emmits = defineEmits(['update:modelValue'])

const _visible = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})

</script>

<script lang="ts">
export default {
  name: 'ScpBroadcastCreate'
}
</script>

<style scoped>
  .bc-form {
    .el-form-item--default {
      --font-size: 12px !important;
      margin-bottom: 12px
    }

    >>> .el-select__selection {
      font-size: 12px;
    }
  }
</style>
