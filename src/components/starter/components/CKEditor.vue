<template>
  <div :style="{height: _height}" v-loading="$store.state.ckEditorUploading">
    <ckeditor ref="ckRef" :editor="editor" v-model="_content" :config="editorConfig" @ready="onReady"/>
  </div>
</template>

<script lang="ts" setup>
import DocumentEditor from '@ckeditor/ckeditor5-build-decoupled-document/build/ckeditor'
import CKEditorAdapter from '@/assets/js/CKEditorAdapter'
import { computed, readonly } from 'vue'
import { useStore } from 'vuex'

const $store = useStore()
const editor = DocumentEditor
// @ts-ignore
const props = withDefaults(defineProps<{
  modelValue?: string,
  placeholder?: string,
  height?: string | number
}>(), {
  modelValue: '',
  placeholder: '',
  height: '100%'
})

const emmits = defineEmits(['update:modelValue'])
const _content = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})

const _height = computed(() => {
  if (typeof props.height === 'number') {
    return props.height + 'px'
  } else {
    return props.height
  }
})

const onReady = (editor) => {
  editor.plugins.get('FileRepository').createUploadAdapter = loader => {
    return new CKEditorAdapter(loader)
  }
  editor.ui.getEditableElement().parentElement.insertBefore(
    editor.ui.view.toolbar.element,
    editor.ui.getEditableElement()
  )
}

const editorConfig = readonly({
  toolbar: {
    items: [
      'heading',
      '|',
      'fontSize',
      'fontFamily',
      'fontColor',
      'fontBackgroundColor',
      '|',
      'bold',
      'italic',
      'underline',
      'strikethrough',
      '|',
      'alignment',
      '|',
      'numberedList',
      'bulletedList',
      '|',
      'indent',
      'outdent',
      '|',
      'blockQuote',
      'insertTable',
      '|',
      'undo',
      'redo',
      '|',
      'toggleImageCaption'
    ]
  },
  placeholder: props.placeholder
})
</script>

<script lang="ts">
export default {
  name: 'ScpCkEditor'
}
</script>

<style lang="scss">
body {
  --ck-font-size-base: 0.45rem !important;
  .ck-placeholder::before {
    color: var(--scp-text-color-secondary) !important;
  }

  .ck-editor__editable_inline {
    height: calc(100% - 50px) !important;
  }
}
</style>
