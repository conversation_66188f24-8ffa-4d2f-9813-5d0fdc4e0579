<template>
  <v-ace-editor :id="editorCtl.id" v-model:value="content" @init="props.editorInit" :lang="props.lang" theme="github" class="scp-ace-editor" :options="_options" :style="$attrs.style"/>
</template>

<script lang="ts" setup>
import { VAceEditor } from 'vue3-ace-editor'
import { computed, inject, onBeforeMount, onMounted, reactive, watch } from 'vue'
import 'ace-builds/src-min-noconflict/ext-language_tools'
import 'ace-builds/src-min-noconflict/theme-github'
import 'ace-builds/src-min-noconflict/mode-sql'
import 'ace-builds/src-min-noconflict/snippets/sql'
import 'ace-builds/src-min-noconflict/mode-java'
import 'ace-builds/src-min-noconflict/snippets/java'
import 'ace-builds/src-min-noconflict/mode-javascript'
import 'ace-builds/src-min-noconflict/snippets/javascript'
import 'ace-builds/src-min-noconflict/mode-json'
import 'ace-builds/src-min-noconflict/snippets/json'
import 'ace-builds/src-min-noconflict/mode-html'
import 'ace-builds/src-min-noconflict/snippets/html'
import 'ace-builds/src-min-noconflict/mode-plain_text'
import 'ace-builds/src-min-noconflict/snippets/plain_text'

const $randomString: any = inject('$randomString')
const $dateFormatter: any = inject('$dateFormatter')

// 当使用autoHeight时, 需要提前给aceEditor一个足够高度
// 因为在动态获取高度时, 只会在初始高度内获取可视的元素个数, 然后乘以一个高度, 并调整为新的高度
// @ts-ignore
const props = withDefaults(defineProps<{
  modelValue: string
  lang?: string,
  enableLiveAutocompletion?: boolean,
  readonly?: boolean,
  editorInit?: Function,
  wrap?: boolean,
  autoHeight?: boolean,
  showLineNumbers?: boolean,
  showGutter?: boolean,
}>(), {
  modelValue: '',
  lang: 'sql',
  enableLiveAutocompletion: false,
  readonly: false,
  wrap: false,
  autoHeight: false,
  editorInit: () => {

  },
  showLineNumbers: true,
  showGutter: true
})

const editorCtl = reactive({
  id: ''
})

onBeforeMount(() => {
  editorCtl.id = 'aceEditor_' + $randomString(8)
})

onMounted(() => {
  if (props.autoHeight) {
    const aceObj = document.getElementById(editorCtl.id)
    const orgHeight = aceObj.offsetHeight || -1
    if (orgHeight < 0) {
      aceObj.style.height = '500rem'
    }
  }
})

watch(() => props.modelValue, () => {
  if (props.autoHeight) {
    setTimeout(() => {
      const aceObj = document.getElementById(editorCtl.id)
      const height = aceObj.getElementsByClassName('ace_line').length * 16 + 30
      aceObj.style.height = height + 'px'
    }, 1000)
  }
})

const _options = computed(() => {
  return {
    enableLiveAutocompletion: props.enableLiveAutocompletion,
    readOnly: props.readonly,
    wrap: props.wrap,
    printMargin: false,
    showLineNumbers: props.showLineNumbers,
    showGutter: props.showGutter
  }
})

const emmits = defineEmits(['update:modelValue'])

const content = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})
</script>

<script lang="ts">
export default {
  name: 'ScpAceEditor'
}
</script>

<style lang="scss">
.scp-ace-editor {
  height: 100%;
  width: 100%;
}

.ace-github {
  background-color: var(--scp-bg-color) !important;
  color: var(--scp-text-color-primary);

  .ace_gutter {
    background-color: var(--scp-bg-color-fill) !important;
    color: var(--scp-text-color-primary);
  }

  .ace_cursor {
    color: var(--scp-text-color-primary) !important;
  }

  .ace_tag {
    font-weight: bold;
  }
}

.ace-github .ace_gutter-active-line {
  background-color: var(--scp-border-color);
}

.ace-github.ace_focus .ace_marker-layer .ace_active-line {
  background: var(--scp-bg-color-fill);
}

.ace-github .ace_marker-layer .ace_active-line {
  background: var(--scp-bg-color-fill);
}

.ace-github .ace_marker-layer .ace_selection {
  background: #d8d8d8 !important;
}
</style>
