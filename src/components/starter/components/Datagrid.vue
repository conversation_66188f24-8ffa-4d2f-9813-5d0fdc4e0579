<template>
  <div class="table-box" ref="tableBoxRef">
    <div v-loading="tableCtl.loading" class="table-area" :style="fullscreenCtl.fullscreenStyle">
      <hot-table ref="hotTableRef"
                 :licenseKey="tableCtl.licenseKey"
                 :data="tableCtl.data"
                 :width="tableCtl.width"
                 :renderAllRows="props.renderAllRows"
                 :stretchH="props.stretchH"
                 :autoWrapRow="props.autoWrapRow"
                 :autoColumnSize="props.autoColumnSize"
                 :manualRowResize="props.manualRowResize"
                 :manualColumnResize="props.manualColumnResize"
                 :rowHeaders="props.rowHeaders"
                 :manualRowMove="props.manualRowMove"
                 :manualColumnMove="props.manualColumnMove"
                 :allowInsertRow="props.allowInsertRow"
                 :filters="props.filters"
                 :formulas="props.formulas"
                 :height="tableCtl.height + 'px'"
                 :columnSorting="props.columnSorting"
                 :clearColumn="props.clearColumn"
                 :hiddenRows="props.hiddenRows"
                 :dropdownMenu="props.dropdownMenu"
                 :contextMenu="_contextMenu"
                 :columns="tableCtl.columns"
                 :colHeaders="tableCtl.colHeaders"
                 :beforeColumnSort="beforeColumnSort"
                 :beforeFilter="beforeFilter"
                 :afterChange="afterChange"
                 :beforeRemoveRow="beforeRemoveRow"
                 :afterCreateRow="afterCreateRow"
                 class="table-striped"
      ></hot-table>
      <el-pagination
          ref="paginationRef"
          size="small"
          v-show="props.pagging"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableCtl.currentPage"
          :page-sizes="props.pageSizes"
          :page-size="tableCtl.pageSize"
          layout="total, sizes, ->, prev, pager, next"
          style="margin-top:10px;padding-bottom: 2px"
          :total="tableCtl.total">
      </el-pagination>
    </div>
    <el-dialog
        title="Save Changes?"
        v-model="tableCtl.confirmVisible"
        :append-to-body="true"
        width="30%">
      <span v-html="tableCtl.confirmMessage"></span>
      <template #footer>
        <span class="dialog-footer">
        <el-button @click="tableCtl.confirmVisible = false">Cancel</el-button>
        <el-button type="primary" @click="saveChanges" :loading="tableCtl.loading">Confirm</el-button>
      </span>
      </template>
    </el-dialog>

    <scp-draggable-resizable w="600px" h="360px" v-model="uploadCtl.uploadVisible" title="Data Upload">
      <el-upload
          style="padding:15px"
          accept=".xlsx"
          :multiple="false"
          :before-upload="onUploadStart"
          :on-success="onUploadEnd"
          :headers="uploadCtl.uploadHeader"
          :file-list="uploadCtl.uploadFileList"
          :on-change="onUploadChange"
          :action="uploadCtl.uploadUrl"
          :data="{uploadModule: uploadCtl.uploadModule, bindTo: bindTo}">
        <template #trigger>
          <el-button size="small" type="primary" :loading="uploadCtl.uploading" style="padding:6px 12px">Select a file</el-button>
        </template>
        <el-button size="small" type="primary" link :loading="uploadCtl.downloading" style="padding:6px 12px;margin-left:6px" @click="downloadTemplate">
          Download Template
        </el-button>
        <hr/>
        <div class="el-upload__tip">
          <p style="margin-bottom:5px">
            <el-radio :disabled="(!props.uploadModule) || props.uploadModule.indexOf('INSERT-IGNORE') === -1" v-model="uploadCtl.uploadModule"
                      value="INSERT-IGNORE">
              Import and <b>ignore</b> existing data &nbsp;&nbsp;&nbsp;&nbsp; [INSERT-IGNORE]
            </el-radio>
          </p>
          <p style="margin-bottom:5px">
            <el-radio :disabled="(!props.uploadModule) || props.uploadModule.indexOf('MERGE') === -1" v-model="uploadCtl.uploadModule" value="MERGE">Import and
              <b>merge</b> existing data &nbsp;&nbsp;&nbsp;&nbsp; [MERGE]
            </el-radio>
          </p>
          <p style="margin-bottom:5px">
            <el-radio :disabled="(!props.uploadModule) || props.uploadModule.indexOf('REPLACE-ALL') === -1" v-model="uploadCtl.uploadModule"
                      value="REPLACE-ALL">Delete
              existing data and re-import &nbsp;[REPLACE-ALL]
            </el-radio>
          </p>
          <p>
            <el-radio :disabled="(!props.uploadModule) || props.uploadModule.indexOf('REPLACE-MY-DATA') === -1" v-model="uploadCtl.uploadModule"
                      value="REPLACE-MY-DATA">
              Delete the data created by currupt user and re-import [REPLACE-MY-DATA]
            </el-radio>
          </p>
          <div style="padding-left:1rem">(Data created by other users will not be affected)</div>
        </div>
      </el-upload>
      <template #footer>
        <el-button :type="uploadCtl.uploadCloseBtnType" @click="uploadCtl.uploadVisible = false" :loading="uploadCtl.uploading">
          {{ uploadCtl.uploadCloseBtn }}
        </el-button>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { HotTable } from '@handsontable/vue3'
import 'handsontable/dist/handsontable.full.css'
import { registerAllModules } from 'handsontable/registry'
import html2canvas from 'html2canvas'
import { computed, inject, nextTick, onMounted, onUpdated, reactive, ref, watch } from 'vue'

registerAllModules()

// region 变量和computed
const $axios: any = inject('$axios')
const $notify: any = inject('$notify')
const $message: any = inject('$message')
const $baseUrl: any = inject('$baseUrl')
const $downloadFile: any = inject('$downloadFile')
const $randomString: any = inject('$randomString')
const $selectValidator: any = inject('$selectValidator')
const $renderColumnName: any = inject('$renderColumnName')
const $renderColumnAsNumber: any = inject('$renderColumnAsNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const hotTableRef: any = ref()
const tableBoxRef: any = ref()
const paginationRef: any = ref()
const rowHeight = 23.4

interface Column {
  title?: string,
  data: string,
  type?: string,
  render?: Function,
  renderer?: Function,
  readOnly?: boolean,
  precision?: number,
  editor?: string,
  selectOptions?: Array<string>,
  source?: Function | Array<string>,
  strict?: boolean,
  width?: number,
  numericFormat?: string,
  dateFormat?: string,
  validator?: Function,
  sorting?: boolean,
  columnSorting?: any,
  datePickerConfig?: any,
  filter?: boolean
}

const fullscreenCtl = reactive({
  isFullscreen: false,
  orgHeight: 0,
  orgWidth: '',
  fullscreenStyle: '',
  disableFullscreen: false
})

const _colOrgHeadersSystemField = computed(() => {
  if (props.displaySystemFields === true) {
    return tableCtl.colOrgHeaders
  } else {
    const header = [] as Array<string>
    for (const i in tableCtl.colOrgHeaders) {
      if (tableCtl.colOrgHeaders.hasOwnProperty(i)) {
        const h = tableCtl.colOrgHeaders[i]
        if (h.indexOf('$') !== h.length - 1) {
          header.push(h)
        }
      }
    }
    return header
  }
})

const _contextMenu = computed(() => {
  if (props.customerContextMenu) {
    const menu = { items: {} }
    for (const key in props.customerContextMenu) {
      if (props.customerContextMenu.hasOwnProperty(key)) {
        menu.items[key] = props.customerContextMenu[key]
      }
    }
    for (const key in defaultContentMenu.items) {
      if (defaultContentMenu.items.hasOwnProperty(key)) {
        menu.items[key] = defaultContentMenu.items[key]
      }
    }
    return menu
  } else {
    return defaultContentMenu
  }
})

const props = withDefaults(
  defineProps<{
      bindTo: string, // 绑定的数据表名称
      lazy?: boolean,
      cacheKeys?: string,
      enableUpload?: boolean,
      uploadModule?: Array<string>, // 上传数据处理行为, 忽略重复, 合并, 删除重新插入, 删除自己忽略其他人的
      displaySystemFields?: boolean, // 是否显示表字段中的CREATE_BY和UPDATE_BY等信息
      dateformat?: string,
      numericPrecision?: number,
      maxRows?: number, // 在不分页的情况下, 允许显示的最大行数
      maxHeight?: number, // 表格最大高度, 默认是500
      renderAllRows?: boolean, // 不渲染所有的表格, 增加性能
      stretchH?: string,
      autoWrapRow?: boolean, // 自动换行, 设置了好像没用, 现已经在App.vue中进行强制不换行
      autoColumnSize?: boolean, // 自适应列大小
      manualRowResize?: boolean, // 可以手动拖拽行高
      manualColumnResize?: boolean, // 可以手动拖拽列宽
      rowHeaders?: boolean, // 有列标题
      manualRowMove?: boolean, // 可移动行
      manualColumnMove?: boolean, // 可移动列
      allowInsertRow?: boolean, // 允许插入行
      filters?: boolean, // 启用筛选器
      formulas?: boolean, // 公式, 支持大部分excel公式
      columnSorting?: boolean, // 允许排序
      sortColumn?: string, // 排序使用, 如果用户传过来值, 会按照指定顺序排序, 但是表头不会显示排序箭头
      sortOrder?: string, // 排序使用, 顺序, 其他的参考sortColumn
      defaultSortColumn?: string | Array<string>,
      defaultSortOrder?: string | Array<string>,
      columnOrder?: Array<string>,
      where?: string, // 默认筛选条件
      clearColumn?: boolean, // 是否可以清除行, 这么危险的操作还是不要了, 但是设置之后好像没用
      hiddenRows?: any, // 隐藏行
      pagging?: boolean, // 是否分页, 你也可以选择不分页, 为了性能考虑, 不分页系统只会默认显示部分行
      pageSizes?: Array<number>, // 分页大小
      dropdownMenu?: Array<string> | boolean, // 下拉菜单样式
      afterChanged?: Function,
      customerContextMenu?: any
    }>(),
  {
    bindTo: '',
    lazy: false,
    cacheKeys: '',
    enableUpload: false,
    uploadModule: () => {
      return ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA']
    },
    displaySystemFields: false,
    dateformat: 'YYYY/MM/DD',
    numericPrecision: undefined,
    maxRows: 1024,
    maxHeight: 500,
    renderAllRows: false,
    stretchH: 'all',
    autoWrapRow: false,
    autoColumnSize: true,
    manualRowResize: true,
    manualColumnResize: true,
    rowHeaders: true,
    manualRowMove: true,
    manualColumnMove: true,
    allowInsertRow: true,
    filters: true,
    formulas: false,
    columnSorting: true,
    sortColumn: '',
    sortOrder: '',
    defaultSortColumn: '',
    defaultSortOrder: '',
    columnOrder: () => [],
    where: '',
    clearColumn: false,
    hiddenRows: () => {
    },
    pagging: true,
    pageSizes: () => [10, 20, 50, 100, 200, 500],
    dropdownMenu: () => ['filter_by_condition', 'filter_operators', 'filter_by_condition2', 'filter_by_value', 'filter_action_bar'],
    afterChanged: undefined,
    customerContextMenu: () => {
    }
  }
)

const tableCtl = reactive({
  licenseKey: 'non-commercial-and-evaluation',
  currentPage: 1,
  pageSize: props.pageSizes[0],
  loading: false,
  height: 150,
  width: '100%', // 表格宽度
  data: [{}] as Array<any>,
  conditions: [] as Array<object>,
  saveStatus: false,
  confirmVisible: false,
  confirmMessage: '',
  colOrgTypes: [] as Array<string>,
  colOrgHeaders: [] as Array<string>,
  colOrgFormats: [] as Array<string>,
  createdRows: [] as Array<string>,
  removedRows: [] as Array<string>,
  updatedRows: {} as any,
  changesCnt: 0,
  sortColumn: '',
  sortOrder: '',
  columns: [] as Array<Column>,
  colHeaders: [] as Array<String>,
  total: 0,
  searching: false
})
// endregion

// region 右键菜单
const defaultContentMenu = {
  items: {
    refresh: {
      name: 'Refresh',
      callback: function () {
        search()
      }
    },
    clear_filter: {
      name: 'Clear filter',
      disabled: function () {
        return tableCtl.conditions.length === 0
      },
      callback: function () {
        clearAndSearch()
      }
    },
    split: { name: '---------' },
    fullscreen: {
      name: 'Fullscreen',
      disabled: function () {
        return fullscreenCtl.isFullscreen || fullscreenCtl.disableFullscreen
      },
      callback: function () {
        fullscreenCtl.isFullscreen = true // 设置为全屏
        fullscreenCtl.orgHeight = tableCtl.height // 记录全屏以前高度
        tableCtl.height = document.documentElement.clientHeight - (props.pagging ? 40 : 0) // 设置表格高度为全屏高度, 40为分页高度
        fullscreenCtl.fullscreenStyle = 'position:fixed;top:0;left:0;width:100%;background-color:#fff;z-index:1080;'

        // 全屏前记录宽度
        fullscreenCtl.orgWidth = tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width
        // 设置宽度为100%
        tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width = '100%'
      }
    },
    exit_fullscreen: {
      name: 'Exit fullscreen',
      disabled: function () {
        return fullscreenCtl.isFullscreen === false || fullscreenCtl.disableFullscreen
      },
      callback: function () {
        fullscreenCtl.isFullscreen = false // 设置为非全屏
        tableCtl.height = fullscreenCtl.orgHeight // 从之前保存的高度中设定表格高度
        fullscreenCtl.fullscreenStyle = '' // 移除全屏样式

        // 将宽度设置为全屏前的宽度
        tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width = fullscreenCtl.orgWidth
      }
    },
    split0: { name: '---------' },
    save_changes: {
      name: function () {
        if (tableCtl.saveStatus === false) {
          return 'Save changes'
        }
        return '<b>Save changes</b>'
      },
      disabled: function () {
        return tableCtl.saveStatus === false
      },
      callback: function () {
        preSaveChanges()
      }
    },
    split1: { name: '---------' },
    upload: {
      name: 'Upload',
      disabled: function () {
        return props.enableUpload === false
      },
      callback: function () {
        uploadCtl.uploadVisible = true
      }
    },
    download_all: {
      name: 'Download all result',
      callback: function () {
        downloadAll()
      }
    },
    download_this_page: {
      name: 'Download this page',
      callback: function () {
        downloadThisPage()
      }
    },
    download_this_page_as_image: {
      name: 'Download this page as image',
      callback: function () {
        downloadPageAsImage()
      }
    },
    copy_this_page_as_image: {
      name: 'Copy this page as image',
      callback: function () {
        copyPageAsImage()
      }
    },
    split6: { name: '---------' },
    row_above: {},
    row_below: {},
    split2: { name: '---------' },
    remove_row: {}
  }
}
// endregion

// region search

const search = () => {
  // 不管如何, 只要调用了查询方法, 都将更新的缓存置为空, 不再接受保存操作
  resetEditorCache()
  // 如果当前没有查询, 那么searching = true, 阻止后续的查询, 同时显示loading画面
  // drawtable的时候会重置filter和sort, 这两个操作都会触发查询, 加上这个开关可以屏蔽搜索
  if (tableCtl.searching === true) {
    return
  }

  tableCtl.loading = true
  tableCtl.searching = true
  // 读取数据
  $axios({
    method: 'post',
    url: '/datagrid/query_data',
    data: getSearchParams()
  }).then((body) => {
    // 查询成功之后, 判断当前是否有分页, 如果有分页, 则按照分页的展示, 否则按照无分页展示
    if (body.data && body.data.length > 0) {
      tableCtl.data = body.data
    } else {
      tableCtl.data = [{}] // 空的data必须是这种结构, 否则会报错
    }
    hotTableRef.value.hotInstance.updateData(tableCtl.data)
    tableCtl.total = body.total
    // 非全屏下, 根据数据行数调整表格父级div的高度, 保证不会出现纵向滚动条
    if (fullscreenCtl.isFullscreen === false) {
      tableCtl.height = Math.min(((tableCtl.data.length * 23.6) + 24 + 20), props.maxHeight)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    tableCtl.loading = false
    tableCtl.searching = false
  })
}

// 开启绘制表格
const drawTable = () => {
  tableCtl.loading = true // 绘制表头的时候也需要显示loading画面
  // 生成handsontable可以识别的表头和列定义
  const cs = [] as Array<Column>// column
  const ch = [] as Array<string> // column header
  const coh = [] as Array<string> // column org header
  const cot = [] as Array<string> // column org type
  const cof = [] as Array<string> // column org format

  // 根据表名从数据库中查询列定义
  $axios({
    method: 'post',
    url: '/datagrid/query_column_def',
    data: {
      bindTo: props.bindTo,
      systemFields: props.displaySystemFields
    }
  }).then((body) => {
    const columnDef = [] as Array<string>
    const columnDefTemp = body.COLUMN_DEF

    // sort column by this.columnOrder
    if (props.columnOrder) {
      for (let i = 0; i < props.columnOrder.length; i++) {
        const co = props.columnOrder[i]
        for (let j = 0; j < columnDefTemp.length; j++) {
          const cdt = columnDefTemp[j]
          if (cdt && cdt.COLUMN_NAME === co) {
            columnDef.push(cdt)
            columnDefTemp[j] = null
          }
        }
      }
    }

    for (let i = 0; i < columnDefTemp.length; i++) {
      const cdt = columnDefTemp[i]
      if (cdt !== null) {
        columnDef.push(cdt)
      }
    }

    const columnDropdown = body.COLUMN_DROPDOWN
    for (const i in columnDef) {
      if (columnDef.hasOwnProperty(i) === false) {
        continue
      }
      const col = renderColumn(columnDef[i])
      coh.push(col.COLUMN_NAME)

      // 根据数据库中的列定义, 生成handsontable的列约束
      const c = {} as Column
      c.data = col.COLUMN_NAME
      switch (col.DATA_TYPE) {
        case 'NUMBER':
          c.type = 'numeric'
          c.renderer = (hotInstance, td, row, column, prop, value) => $renderColumnAsNumber(hotInstance, td, row, column, prop, value, col.DATA_SCALE || props.numericPrecision, true)
          cot.push('NUMBER')
          cof.push('')
          break
        case 'DATE':
          c.type = 'date'
          c.dateFormat = props.dateformat || 'YYYY/MM/DD' // oralce默认的日期格式
          c.datePickerConfig = {
            showWeekNumber: true
          }
          cot.push('date')
          cof.push(c.dateFormat)
          break
        default:
          const dropdown = columnDropdown[col.COLUMN_NAME]
          if (dropdown) {
            const type = dropdown.type.toLowerCase()
            if (type === 'select') {
              c.editor = type
              c.selectOptions = dropdown.source
              c.validator = (value, callback) => $selectValidator(value, c.selectOptions, callback)
            } else if (type === 'autocomplete' || type === 'dropdown') {
              c.type = 'autocomplete'
              c.strict = type !== 'autocomplete'
              c.filter = true
              c.source = (query, process) => {
                if (query) {
                  process(dropdown.source.filter(e => e.toLowerCase().indexOf(query.toLowerCase()) !== -1))
                } else {
                  process(dropdown.source)
                }
              }
            }
          } else {
            c.type = 'text'
            if ((columnDef[i].DATA_LENGTH || 0) > 128) {
              c.width = 220
            }
          }
          cot.push('')
          cof.push('')
      }
      if (col.render) {
        c.renderer = col.render
      }

      // system field may not include in ch(column header) and cs(column source)
      if (props.displaySystemFields === false) {
        if (col.COLUMN_NAME.indexOf('$') === col.COLUMN_NAME.length - 1) {
          continue
        }
      }

      ch.push(col.columnDisplay || col.COLUMN_NAME)
      cs.push(c)
    }
    tableCtl.colOrgTypes = cot
    tableCtl.colOrgHeaders = coh
    tableCtl.colOrgFormats = cof
    tableCtl.colHeaders = ch
    tableCtl.columns = cs

    // 清除排序和筛选
    clearAndSearch()
  }).catch((error) => {
    console.log(error)
  })
}

// renderColumn
// 行渲染, 从数据库中获取的字段名都是大写用下划线分割, 这个方法可以将这种格式的代码转换为比较容易理解的文字
// 如果你想重写这个方法, 那么请在col中添加columnDisplay, dataFormat, render来达到你想要的效果
// columnDisplay, 最终表头显示的文字
// dataFormat, 如果某一字段是日期, 那么请设置它的格式, 默认将使用Oracle的格式, 不过为了保证稳定性, 建议不要修改, 因为你写的格式万一后台不识别, 保存的时候会报错, 如果一定要改, 请同时修改后台代码
// render, 如果你需要对某一行进行特别的渲染, 请设置RENDER属性, RENDER将遵循handsontable的要求, 完成的方法是这样的 render: function (instance, td, row, col, prop, value, cellProperties),具体怎么用可以参考handson手册
const renderColumn = (col) => {
  if (col && col.COLUMN_NAME && col.COLUMN_NAME.length > 1) {
    let display = $renderColumnName(col.COLUMN_NAME)
    if (col.CONSTRAINT_TYPE === 'P') {
      display += ' <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="key" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="svg-inline--fa fa-key fa-w-16" style="font-size: 8px;"><path fill="currentColor" d="M512 176.001C512 273.203 433.202 352 336 352c-11.22 0-22.19-1.062-32.827-3.069l-24.012 27.014A23.999 23.999 0 0 1 261.223 384H224v40c0 13.255-10.745 24-24 24h-40v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24v-78.059c0-6.365 2.529-12.47 7.029-16.971l161.802-161.802C163.108 213.814 160 195.271 160 176 160 78.798 238.797.001 335.999 0 433.488-.001 512 78.511 512 176.001zM336 128c0 26.51 21.49 48 48 48s48-21.49 48-48-21.49-48-48-48-48 21.49-48 48z" class=""></path></svg>'
    }
    col.columnDisplay = display
  }
  return col
}

const clearAndSearch = () => {
  tableCtl.sortColumn = ''
  tableCtl.sortOrder = ''
  tableCtl.conditions = []
  if (hotTableRef.value) {
    hotTableRef.value.hotInstance.getPlugin('filters').clearConditions()
    hotTableRef.value.hotInstance.getPlugin('columnSorting').clearSort()
  }
}

const beforeFilter = (sortSettings) => {
  const conditions = JSON.parse(JSON.stringify(sortSettings))
  for (let i = 0; i < conditions.length; i++) {
    conditions[i].column = _colOrgHeadersSystemField.value[conditions[i].column]
  }
  tableCtl.conditions = conditions
  search()
  return false
}

const handlePageSizeChange = (val) => {
  tableCtl.pageSize = val
  search()
}

const handleCurrentChange = (val) => {
  tableCtl.currentPage = val
  search()
}

// 监控传过来的bindTo, 如果bindTo变化, 则刷新表格
watch(() => props.bindTo, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    updateTable()
  }
})

const updateTable = () => {
  if (props.bindTo) {
    drawTable()
    if (uploadCtl.uploadModule.length > 0) {
      uploadCtl.uploadModule = props.uploadModule[0]
    }
  }
}

onMounted(() => {
  if (props.lazy === false) {
    updateTable()
  }
})
// endregion

// region save change
const preSaveChanges = () => {
  hotTableRef.value.hotInstance.validateCells((valid) => {
    if (valid) {
      // 获取新增的行, 没有rowid的行都是新增行
      tableCtl.createdRows = []
      for (let i = 0; i < tableCtl.data.length; i++) {
        const c = tableCtl.data[i]
        if (c && !c.ROW_ID) {
          tableCtl.createdRows.push(c)
        }

        // 确认待删除的列, 是否仍存在于表格, 如果存在, 则从待删除列中移除
        if (c && c.ROW_ID) {
          const di = tableCtl.removedRows.indexOf(c.ROW_ID)
          if (di !== -1) {
            tableCtl.removedRows.splice(di, 1)
          }
        }
      }

      // 检查待更新的行, 如果此行已经被删除, 那么就从待更新的列表中移除, 无须再执行更新
      for (let i = 0; i < tableCtl.removedRows.length; i++) {
        const rowid = tableCtl.removedRows[i]
        if (tableCtl.updatedRows[rowid]) {
          delete tableCtl.updatedRows[rowid]
        }
      }
      // 显示更新信息
      tableCtl.changesCnt = 0
      const html = ['<ul>']
      let l = tableCtl.createdRows.length
      if (l > 0) {
        tableCtl.changesCnt += l
        html.push('<li style="color:var(--scp-text-color-primary);font-size: 16px;line-height:24px">' + l + (l > 1 ? ' rows' : ' row') + ' will be created</li>')
      }
      l = tableCtl.removedRows.length
      if (l > 0) {
        tableCtl.changesCnt += l
        html.push('<li style="color:var(--scp-text-color-primary);font-size: 16px;line-height:24px">' + l + (l > 1 ? ' rows' : ' row') + ' will be deleted</li>')
      }
      l = Object.keys(tableCtl.updatedRows).length
      if (l > 0) {
        tableCtl.changesCnt += l
        html.push('<li style="color:var(--scp-text-color-primary);font-size: 16px;line-height:24px">' + l + (l > 1 ? ' rows' : ' row') + ' will be updated</li>')
      }
      html.push('</ul>')
      tableCtl.confirmMessage = html.join('')
      tableCtl.confirmVisible = true
    } else {
      $message.error('Invalid input, please check the highlight cell in the table.')
    }
  })
}

const saveChanges = () => {
  tableCtl.loading = true // 显示loading页面
  // 隐藏修改页面
  $axios({
    method: 'post',
    url: '/datagrid/save_changes',
    data: {
      bindTo: props.bindTo,
      cacheKeys: props.cacheKeys,
      systemFields: props.displaySystemFields,
      _colOrgTypes: tableCtl.colOrgTypes,
      _colOrgHeaders: tableCtl.colOrgHeaders,
      _colOrgFormats: tableCtl.colOrgFormats,
      createdRows: tableCtl.createdRows,
      removedRows: tableCtl.removedRows,
      updatedRows: tableCtl.updatedRows,
      _where: props.where
    }
  }).then((body) => {
    if (body > 0) {
      $message.success('Changes Saved')
      search()

      // 回调方法
      if (props.afterChanged) {
        props.afterChanged.call(null)
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    tableCtl.loading = false
    tableCtl.confirmVisible = false
  })
}

const afterChange = (changes) => {
  if (changes) {
    let changed = false
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const rowid = tableCtl.data[row].ROW_ID
        changed = true
        if (rowid) {
          let e = tableCtl.updatedRows[rowid]
          if (typeof (e) === 'undefined') {
            e = {} as any
          }
          e[prop] = newValue
          tableCtl.updatedRows[rowid] = e
        }
      }
    })
    // @ts-ignore
    if (changed === true) {
      tableCtl.saveStatus = true
    }
  }
}

const beforeRemoveRow = (index, amount, physicalRows) => {
  for (let i = 0; i < physicalRows.length; i++) {
    const rowid = tableCtl.data[physicalRows[i]].ROW_ID
    if (rowid && rowid.length > 0) {
      tableCtl.removedRows.push(rowid)
    }
  }
  tableCtl.saveStatus = true
}

const beforeColumnSort = (currentSortConfig, destinationSortConfigs) => {
  if (destinationSortConfigs && destinationSortConfigs.length > 0) {
    tableCtl.sortColumn = tableCtl.columns[destinationSortConfigs[0].column].data
    tableCtl.sortOrder = destinationSortConfigs[0].sortOrder
  } else {
    tableCtl.sortColumn = ''
    tableCtl.sortOrder = ''
  }

  const columnSortPlugin = hotTableRef.value.hotInstance.getPlugin('columnSorting')
  columnSortPlugin.setSortConfig(destinationSortConfigs)
  search()
  return false
}

const afterCreateRow = () => {
  tableCtl.saveStatus = true
  if (fullscreenCtl.isFullscreen === false) {
    let height = tableCtl.height
    height = Math.min(props.maxHeight, height + 25)
    tableCtl.height = height
  }
}

const resetEditorCache = () => {
  tableCtl.saveStatus = false
  tableCtl.updatedRows = {}
  tableCtl.removedRows = []
  tableCtl.createdRows = []
}
// endregion

// region upload
const uploadCtl = reactive({
  uploadVisible: false,
  uploading: false,
  uploadCloseBtn: 'Close',
  uploadCloseBtnType: null as any,
  downloading: false,
  uploadModule: props.uploadModule[0],
  uploadUrl: $baseUrl + '/datagrid/upload_data',
  uploadFileList: [] as Array<string>,
  uploadHeader: { token: localStorage.getItem('token') }
})

const onUploadChange = (file) => {
  uploadCtl.uploadFileList = [file]
}

const onUploadStart = () => {
  uploadCtl.uploading = true
  uploadCtl.uploadCloseBtn = 'Uploading'
  uploadCtl.uploadCloseBtnType = null
}

const onUploadEnd = (res) => {
  if (res.header.status !== 200) {
    $notify({
      title: 'Upload Failed',
      message: res.header.message,
      dangerouslyUseHTMLString: true,
      type: 'error'
    })
    uploadCtl.uploadCloseBtn = 'Failed'
    uploadCtl.uploadCloseBtnType = 'danger'
  } else {
    $message.success('Upload Success')
    uploadCtl.uploadVisible = false
    uploadCtl.uploadFileList = []
    uploadCtl.uploadCloseBtn = 'Close'
    uploadCtl.uploadCloseBtnType = 'default'
    search()

    // 回调方法
    if (props.afterChanged) {
      props.afterChanged.call(null)
    }
  }
  uploadCtl.uploading = false
}

const downloadTemplate = () => {
  uploadCtl.downloading = true
  $downloadFile('/datagrid/download_template', { bindTo: props.bindTo }, () => {
    uploadCtl.downloading = false
  })
}
// endregion

// region download
const downloadAll = () => {
  $downloadFile('/datagrid/download_data', getSearchParams())
}

const downloadThisPage = () => {
  hotTableRef.value.hotInstance.getPlugin('exportFile').downloadFile('csv', {
    filename: 'page_data_' + $randomString(4),
    columnHeaders: true,
    rowHeaders: false
  })
}

const downloadPageAsImage = () => {
  // 使用html2canvas 转换html为canvas
  // 先将滚动条滚动至最顶端, 然后调整截图区域的宽高, 使其全部可以展示出来, 截图完毕后, 将之前调整的内容全部恢复
  const h2 = document.documentElement.scrollTop
  const h3 = document.body.scrollTop
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0

  hotTableRef.value.hotInstance.deselectCell()

  tableCtl.loading = true
  const ht = tableCtl.height
  const wt = hotTableRef.value.$el.getElementsByClassName('wtHider')[0].style.width
  tableCtl.height = (tableCtl.data.length * rowHeight) + 24 + 20
  tableCtl.width = wt
  nextTick(() => {
    html2canvas(hotTableRef.value.$el).then((canvas) => {
      tableCtl.height = ht
      tableCtl.width = '100%'
      document.documentElement.scrollTop = h2
      document.body.scrollTop = h3

      tableCtl.loading = false
      const imgUri = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream') // 获取生成的图片的url

      const saveLink = document.createElement('a')
      saveLink.href = imgUri
      saveLink.download = 'screenshot_' + new Date().getTime() + '.png'
      saveLink.click()
      saveLink.remove()
    })
  })
}

const copyPageAsImage = () => {
  // 使用html2canvas 转换html为canvas
  // 先将滚动条滚动至最顶端, 然后调整截图区域的宽高, 使其全部可以展示出来, 截图完毕后, 将之前调整的内容全部恢复
  const h2 = document.documentElement.scrollTop
  const h3 = document.body.scrollTop
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0

  hotTableRef.value.hotInstance.deselectCell()

  tableCtl.loading = true
  const ht = tableCtl.height
  const wt = hotTableRef.value.$el.getElementsByClassName('wtHider')[0].style.width
  tableCtl.height = (tableCtl.data.length * rowHeight) + 24 + 20
  tableCtl.width = wt
  nextTick(() => {
    html2canvas(hotTableRef.value.$el).then(function (canvas) {
      tableCtl.height = ht
      tableCtl.width = '100%'
      document.documentElement.scrollTop = h2
      document.body.scrollTop = h3

      tableCtl.loading = false
      const img = document.createElement('img')
      img.src = canvas.toDataURL('image/png')
      document.getElementsByClassName('table-box')[0].appendChild(img)

      const range = document.createRange()
      range.selectNode(img)
      const selection = window.getSelection()
      if (selection) {
        selection.addRange(range)
      }
      document.execCommand('Copy')
      img.remove()
    })
  })
}
// endregion

// region getter and setter
onUpdated(() => {
  // 覆盖原生页码显示的总数, element ui默认不显示千分位, 老板肯定会不开心的, 所以添加为有千分位的总数
  const paggingDisplay = paginationRef.value.$el.getElementsByClassName('el-pagination__total')
  if (paggingDisplay && paggingDisplay[0]) {
    paggingDisplay[0].innerHTML = 'Total ' + $thousandBitSeparator(tableCtl.total)
  }
})

const getSearchParams = () => {
  return {
    bindTo: props.bindTo,
    systemFields: props.displaySystemFields,
    _colOrgHeaders: tableCtl.colOrgHeaders,
    _colOrgTypes: tableCtl.colOrgTypes,
    _colOrgFormats: tableCtl.colOrgFormats,
    _maxRows: props.maxRows,
    _pagging: props.pagging,
    _sortColumn: tableCtl.sortColumn || (Array.isArray(props.defaultSortColumn) ? (props.defaultSortColumn as Array<string>).join(',') : props.defaultSortColumn),
    _sortOrder: tableCtl.sortOrder || (Array.isArray(props.defaultSortOrder) ? (props.defaultSortOrder as Array<string>).join(',') : props.defaultSortOrder),
    _pageSize: tableCtl.pageSize,
    _currentPage: tableCtl.currentPage,
    _conditions: tableCtl.conditions,
    _where: props.where
  }
}

defineExpose({
  getSearchParams, updateTable, search
})

// endregion
</script>

<script lang="ts">
export default {
  name: 'ScpDatagrid'
}
</script>
<style lang="scss" scoped>
.table-box {
  width: 100%;
  box-sizing: border-box;

  .table-area {
    overflow: hidden;
    width: calc(100% - 2px);
  }
}
</style>
