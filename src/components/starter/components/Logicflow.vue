<template>
  <div class="logicflow-component">
    <!-- 控制器 -->
    <div class="logicflow-control">
      <el-button-group>
        <el-button size="default" @click="zoomIn">Zoom In</el-button>
        <el-button size="default" @click="zoomOut">Zoom Out</el-button>
        <el-button size="default" @click="reset">Reset</el-button>
        <el-button size="default" @click="undo">Undo</el-button>
        <el-button size="default" @click="redo">Redo</el-button>
        <el-button size="default" @click="showMiniMap">Show Mini Map</el-button>
      </el-button-group>
    </div>

    <!-- 组件面板 -->
    <div class="logicflow-panel" v-if="pageCtl.baseNodeNames.length > 0">
      <el-collapse v-model="pageCtl.activeNames">
        <el-collapse-item :title="item" :name="item" v-for="(item, index) in pageCtl.baseNodeNames" :key="'l1_' + index">
          <div class="node-item" @mousedown='startDrag(item2)' v-for="(item2, index2) in pageCtl.baseNodes[item]" :key="'l2_' + index2">
            <div class="node-item-sharp" :style="{
                        height: $px2Rem(_height) + 'px',
                        lineHeight: $px2Rem(_height) + 'px',
                        backgroundColor: item2.fill}">
              <div class="node-label" :style="{paddingLeft: $px2Rem(_width) + 'px'}">
                <font-awesome-icon class="node-icon" :icon="[item2.iconPrefix, item2.iconName]"
                                   :style="{color: item2.color,
                                             width: $px2Rem(_width - 10) + 'px',
                                             marginLeft: '-' + $px2Rem(_width - 5) + 'px'}"/>
                <div class="node-label-text" :style="{color: item2.color,
                                                      height: $px2Rem(_height) + 'px',
                                                      lineHeight: $px2Rem(_height) + 'px',
                                                      fontSize: $px2Rem(_fontSize) + 'px'}">
                  {{ item2.text }}
                </div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 画布 -->
    <div id="_container" class="container"/>
  </div>
</template>

<script lang="ts" setup>
import global from '@/assets/js/logicflow/global'
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'
import LogicFlow from '@logicflow/core'
import { MiniMap, SelectionSelect } from '@logicflow/extension'
import RectBase from '@/assets/js/logicflow/RectBase.js'
import FlowLink from '@/assets/js/logicflow/FlowLink.js'
import { reactive, computed, inject } from 'vue'

LogicFlow.use(MiniMap)
LogicFlow.use(SelectionSelect)

const $px2Rem: any = inject('$px2Rem')

const pageCtl = reactive({
  lf: {} as any,
  activeNames: [] as Array<string>,
  baseNodes: {},
  baseNodeNames: [] as Array<string>,
  height: 300
})

const _width = computed(() => {
  return global.width
})
const _height = computed(() => {
  return global.height
})
const _fontSize = computed(() => {
  return global.fontSize
})

const startDrag = (item) => {
  pageCtl.lf.dnd.startDrag({
    type: item.type,
    text: item.text,
    properties: item.properties || {}
  })
}
const getComponent = (_config) => {
  class View extends RectBase.view {
    getIcon () {
      return super.getIcon(_config.viewBox, _config.pathd)
    }
  }

  class Model extends RectBase.model {
    initNodeData (data) {
      data.text = { value: _config.text }
      super.initNodeData(data)
    }

    getNodeStyle () {
      const style = super.getNodeStyle()
      style.fill = _config.fill
      return style
    }

    getTextStyle () {
      const style = super.getTextStyle()
      style.color = _config.color
      return style
    }
  }

  return {
    type: _config.type,
    view: View,
    model: Model
  }
}
const registeComponent = (baseNodes, lf) => {
  const baseNodeNames = Object.keys(baseNodes)
  for (let i = 0; i < baseNodeNames.length; i++) {
    const subTitles = baseNodes[baseNodeNames[i]]
    for (let j = 0; j < subTitles.length; j++) {
      pageCtl.lf.register(getComponent(subTitles[j]))
    }
  }

  pageCtl.lf.register(FlowLink)
  pageCtl.lf.setDefaultEdgeType('flow-link')
}
const createLogicFlow = (selector) => {
  const lf = new LogicFlow({
    container: document.querySelector(selector), // 定义容器
    background: { // 背景色
      color: '#F0F0F0'
    },
    grid: { // 背景网格
      type: 'dot',
      size: 20
    },
    keyboard: { // 开启键盘, 支持CTRL+C, CTRL+V, Backspace
      enabled: true
    },
    stopZoomGraph: true, // 禁止缩放
    stopScrollGraph: true, // 禁止滚动, 改为页面缩放
    textEdit: false, // 禁止编译元素文本
    style: {
      anchorLine: {
        stroke: '#999',
        strokeWidth: 3,
        strokeDasharray: '3,2'
      }
    }
  })
  lf.openSelectionSelect()
  return lf
}
const create = (config) => {
  pageCtl.baseNodes = config
  pageCtl.baseNodeNames = Object.keys(config)
  pageCtl.activeNames = Object.keys(config)
  pageCtl.lf = createLogicFlow('#_container')
  // 注册所有组件
  registeComponent(config, pageCtl.lf)
  return pageCtl.lf
}
const zoomIn = () => {
  pageCtl.lf.zoom(true)
}
const zoomOut = () => {
  pageCtl.lf.zoom(false)
}
const reset = () => {
  pageCtl.lf.resetZoom()
  pageCtl.lf.resetTranslate()
}
const undo = () => {
  pageCtl.lf.undo()
}
const redo = () => {
  pageCtl.lf.redo()
}
const showMiniMap = () => {
  pageCtl.lf.extension.miniMap.show(pageCtl.lf.graphModel.width - 150, 40)
}

defineExpose({
  create
})
</script>

<script lang="ts">
export default {
  name: 'LogicFlow'
}
</script>

<style lang="scss">
.logicflow-component {
  width: 100%;
  height: 100%;
  min-height: 560px;

  .container {
    width: 100%;
    height: 100%;
  }

  .lf-node-content {
    cursor: move !important;

    text {
      cursor: move !important;
    }
  }

  .logicflow-control {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--scp-bg-color);
    box-shadow: 0 0 10px 1px var(--scp-border-color);
    border-radius: 5px;
    z-index: 101;

    .el-button {
      color: var(--scp-text-color-primary) !important;
      border: 0 !important;
    }

    .el-button:hover {
      border: 0 !important;
      background-color: var(--scp-bg-color-fill) !important;
    }

    .el-input__inner {
      border: 0 !important;
      text-align: center;
      padding-top: 1px;
    }

    .el-input__inner::-ms-input-placeholder {
      color: var(--scp-text-color-primary) !important;
    }

    .el-input__inner::-webkit-input-placeholder {
      color: var(--scp-text-color-primary) !important;
    }

    .el-input__inner::-moz-placeholder {
      color: #000 !important;
    }

    .el-input__suffix {
      color: var(--scp-text-color-primary);
    }
  }

  .logicflow-panel {
    position: absolute;
    padding: 10px 10px;
    background-color: var(--scp-bg-color);
    box-shadow: 0 0 10px 1px var(--scp-border-color);
    border-radius: 5px;
    z-index: 101;
    max-height: calc(100% - 50px);
    overflow: auto;
    top: 5px;
    left: 5px;
    width: 110px;

    .node-item {
      cursor: pointer;
      margin-bottom: 0.4rem;
    }

    .node-item-sharp {
      border-radius: 5px;
      cursor: move;
    }

    .node-label {
      user-select: none;

      .node-label-text {
        border-left: 1px solid rgba(0, 0, 0, 0.1);
        padding-left: 10px;
      }
    }

    .node-icon {
      color: #fff;
      font-size: 18px;
      margin-top: 2px;
      float: left;
    }

    .el-collapse {
      border: 0 !important;
    }

    .el-collapse-item__content {
      padding-bottom: 0 !important;
    }

    .el-collapse-item__wrap {
      border: 0 !important;
    }

    .el-collapse-item__header {
      height: 28px !important;
      line-height: 28px !important;
      border: 0 !important;
      font-size: 12px;
    }
  }

  .lf-mini-map {
    background: var(--scp-bg-color) !important;
  }
}
</style>
