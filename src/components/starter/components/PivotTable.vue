<template>
  <div style="width:100%">
    <el-row class="search-box">
      <el-col :span="6">
        <scp-cascader v-model="pageCtl.conditions.filterList"
                      :loading="props.cascaderOpts.length === 0"
                      :options="props.cascaderOpts"/>
      </el-col>
      <el-col :span="3" v-if="props.manualOpts.length > 0">
        <el-select v-model="pageCtl.conditions.specialType" style="width: 100% !important;"
                   class="search-group-left">
          <el-option :label="item" :value="item" :key="item" v-for="item in props.manualOpts"/>
        </el-select>
      </el-col>
      <el-col :span="3" v-if="props.manualOpts.length > 0">
        <el-input :placeholder="pageCtl.conditions.specialType" v-model="pageCtl.conditions.specialContent"
                  style="width: var(--scp-input-width) !important;" type="textarea"
                  class="search-group-right"></el-input>
      </el-col>
      <el-col :span="3" v-if="props.dateOpts.length > 0">
        <el-select v-model="pageCtl.conditions.dateRangeType" style="width: 100% !important;"
                   class="search-group-left">
          <el-option :label="item" :value="item" :key="item" v-for="item in props.dateOpts"/>
        </el-select>
      </el-col>
      <el-col :span="5" v-if="props.dateOpts.length > 0">
        <div class="search-group-right" style="width: var(--scp-input-width) !important;">
          <el-date-picker
              v-model="pageCtl.conditions.dateRange"
              type="daterange"
              unlink-panels
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              range-separator="to"
              :clearable="false"
              :picker-options="pageCtl.pickerOptions"
              class="search-group-right"
              style="width: calc(100% - 20px) !important;">
          </el-date-picker>
        </div>
      </el-col>
      <el-col :span="2">
        <el-select v-model="pageCtl.conditions.calcType">
          <el-option
              v-for="item in pageCtl.calcTypeOpts"
              :key="item"
              :label="item"
              :value="item">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="1">
        <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                    :data-exclude="['dateRange']"/>
      </el-col>
    </el-row>
    <el-row class="search-box">
      <el-col :span="6">
        <el-select v-model="pageCtl.conditions.categroy" placeholder="Categroy" filterable clearable multiple
                   collapse-tags>
          <el-option
              v-for="item in _pivotColumns"
              :key="item"
              :label="item"
              :value="item">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="6">
        <el-select v-model="pageCtl.conditions.valueColumns" placeholder="Value Columns" filterable clearable multiple
                   collapse-tags>
          <el-option
              v-for="item in _valueColumnOpts"
              :key="item"
              :label="item"
              :value="item">
          </el-option>
        </el-select>
      </el-col>
    </el-row>

    <div class="subscript-container">
      <scp-subscript :id="props.subscriptId"/>
      <scp-table
          ref="tableRef"
          :columns="pageCtl.columns"
          :show-total="true"
          :params="_params"
          :after-select="afterSelect"
          :fixedColumnsLeft="_selectedCategroy.length"
          :context-menu-items="pageCtl.contextItems"
          :page-sizes="[20, 50, 100, 200, 500]"
          :lazy="true"
          url="/pivot_table/query_data"
          download-url="/pivot_table/download_data"
          :editable="false"
      />
    </div>

  </div>

  <!-- master data summary details-->
  <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.detailsVisible" id="summaryDetailsDiv"
                           :title="pageCtl.detailsTitle">
    <template v-slot="{ height }">
      <scp-table style="margin-bottom:10px;"
                 :max-height="height - 150"
                 ref="detailsTableRef"
                 url="/pivot_table/query_details"
                 download-url="/pivot_table/download_details"
                 :lazy="true"
                 :params="_params"
                 :fixedColumnsLeft="_selectedCategroy.length"
                 :columns="props.detailsColumns"
                 :maxHeight="pageCtl.maxHeight"
                 :editable="false"
      />
    </template>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const searchRef = ref()
const tableRef = ref()
const detailsTableRef = ref()

const props = withDefaults(defineProps<{
  bindTo: string,
  cascaderOpts?: Array<any>,
  manualOpts?: Array<string>,
  dateOpts?: Array<string>,
  detailsColumns?: Array<any>,
  defaultPivotColumns: Array<string>,
  valueColumns?: Array<string>,
  subscriptId?: string
}>(), {
  bindTo: '',
  cascaderOpts: () => {
    return []
  },
  manualOpts: () => {
    return []
  },
  dateOpts: () => {
    return []
  },
  detailsColumns: () => {
    return []
  },
  defaultPivotColumns: () => {
    return []
  },
  valueColumns: () => {
    return []
  },
  subscriptId: 'EMPTY'
})

const _selectedCategroy = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return props.defaultPivotColumns
  }
})

const _valueColumnOpts = computed(() => {
  const v = props.valueColumns
  return v.sort()
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(props.cascaderOpts, ...props.manualOpts)
})

const _params = computed(() => {
  return {
    filterList: pageCtl.conditions.filterList,
    calcType: pageCtl.conditions.calcType,
    categroy: pageCtl.conditions.categroy,
    specialType: pageCtl.conditions.specialType,
    specialContent: pageCtl.conditions.specialContent,
    dateRangeType: pageCtl.conditions.dateRangeType,
    dateRange: pageCtl.conditions.dateRange,
    selectedValue: pageCtl.conditions.selectedValue,
    bindTo: props.bindTo,
    defaultPivotColumns: props.defaultPivotColumns,
    valueColumns: props.valueColumns
  }
})

const initPage = () => {
  pageCtl.loading.filter = true
  pageCtl.conditions.dateRange = ['1999/01/01', $dateFormatter(new Date(), 'yyyy/MM/dd')]

  $axios({
    method: 'post',
    url: '/master/master_data_summary/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  parseTableColumn()
  tableRef.value.search()
}
const afterSelect = (row) => {
  if (row[_selectedCategroy.value[0]] === 'Total') {
    pageCtl.conditions.selectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _selectedCategroy.value.length; i++) {
      selectedValue.push(row[_selectedCategroy.value[i]])
    }
    pageCtl.conditions.selectedValue = selectedValue
  }
}
const viewDataDetails = () => {
  pageCtl.detailsTitle = 'View Summary Details ' + (pageCtl.conditions.selectedValue.length ? ('[' + pageCtl.conditions.selectedValue.join(', ') + ']') : '')
  pageCtl.detailsVisible = true
  detailsTableRef.value.clearAndSearch()
}
const parseTableColumn = () => {
  const columns = [] as any
  for (let i = 0; i < _selectedCategroy.value.length; i++) {
    columns.push({
      data: _selectedCategroy.value[i]
    })
  }
  columns.push({
    data: 'CNT',
    title: 'Count',
    type: 'numeric'
  })
  for (let i = 0; i < pageCtl.conditions.valueColumns.length; i++) {
    columns.push({
      data: pageCtl.conditions.valueColumns[i],
      type: 'numeric'
    })
  }

  pageCtl.columns = columns
}

const pageCtl = reactive({
  filterOpts: [],
  calcTypeOpts: ['Sum', 'Avg', 'Max', 'Min', 'Std'],
  columns: [],
  loading: {
    filter: false
  },
  detailsVisible: false,
  maxHeight: 280,
  conditions: {
    filterList: [],
    calcType: 'Sum',
    categroy: props.defaultPivotColumns,
    specialType: props.manualOpts.length > 0 ? props.manualOpts[0] : '',
    specialContent: '',
    dateRangeType: props.dateOpts.length > 0 ? props.dateOpts[0] : '',
    dateRange: [] as any,
    selectedValue: [],
    valueColumns: props.valueColumns
  },
  contextItems: {
    view_details: {
      name: 'View details',
      callback: viewDataDetails
    },
    view_split0: { name: '---------' }
  },
  pickerOptions: {
    shortcuts: [{
      text: 'Next 6 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 6)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Next 1 year',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 12)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  detailsTitle: ''
})

onMounted(() => {
  initPage()
})

defineExpose({
  search
})
</script>

<script lang="ts">
export default {
  name: 'ScpPivotTable'
}
</script>
