<template>
  <div class="subscript-area" :id="props.id">
    <!-- <div :class="introductionData.subclass" @click="showSubscript" @contextmenu.prevent="toggleView"></div> -->
    <div class="subscript-info" @click="showSubscript">
      <el-tooltip effect="light" content="Report Memos" :show-after="1000">
        <font-awesome-icon icon="paperclip"/>
      </el-tooltip>
    </div>
    <div class="subscript-flip" v-show="hasFlip" @click="toggleView">
      <el-tooltip effect="light" content="More Options" :show-after="1000">
        <font-awesome-icon icon="retweet"/>
      </el-tooltip>
    </div>
  </div>

  <div class="subscript-win">
    <scp-draggable-resizable w="80vw" h="600px" v-model="subscriptVisible" :title="introductionData.title" :resizestop="resizeChart">
      <div class="subscript-content">
        <div class="subscript-title">
          <div class="description"><b>{{ props.id }}</b></div>
          <div style="float:right">
            <el-tooltip class="item" effect="light" placement="bottom">
              <template #content>
                <b>Report Update&nbsp;:</b> &nbsp;{{ introductionData.updateTime }}&nbsp;
              </template>
              <span><b>Data Sync&nbsp;:</b> &nbsp;{{ introductionData.dataSync }}&nbsp;</span>
            </el-tooltip>
            <span style="color: var(--scp-border-color-lighter);">|</span>&nbsp;
            <font-awesome-icon :icon="introductionData.showIntroduction ? 'project-diagram' : 'tasks'" @click="toggleChart"
                               style="cursor: pointer"/>&nbsp;
            <span style="color: var(--scp-border-color-lighter);">|</span>&nbsp;
            <font-awesome-icon icon="edit" style="cursor: pointer" @click="showEditSubscriptWin"/>&nbsp;
            <span style="color: var(--scp-border-color-lighter);" v-show="$store.state.maintainer==='Y'">|</span>&nbsp;
            <font-awesome-icon icon="marker" style="cursor: pointer" @click="showReportRelWin"/>
          </div>
          <div class="clearfix"/>
        </div>
        <div class="tree" v-show="!introductionData.showIntroduction" v-loading="chartData.loading">
          <el-radio v-model="chartData.chartType" value="tree" style="margin-right:10px">Tree</el-radio>
          <el-radio v-model="chartData.chartType" value="sankey">Sankey</el-radio>
          <chart ref="chartRef" :style="{width:'100%',height:'100%'}" :option="_diagramOpt"/>
        </div>
        <div class="details" v-show="introductionData.showIntroduction" style="height: calc(100% - 40px);overflow: auto">
          <scp-md-preview v-model="introductionData.descriptions"/>
        </div>
      </div>
    </scp-draggable-resizable>

    <!-- modify doc -->
    <scp-draggable-resizable w="85vw" h="600px" v-model="modifyData.visible" :title="'Modify '+ props.id + ' Subscript'" offset="7rem"
                             :save="modifySubscriptDoc" :save-loading="modifyData.loadingModify">
      <template v-slot="{height}">
        <div style="padding: 5px" v-loading="modifyData.loading">
          <div style="margin-bottom: 10px">
            <el-row>
              <el-col :span="24" style="height: 20px; line-height: 25px">
                <b style="margin: 0 10px">Subject: </b>{{ modifyData.subject }}
                <b style="margin: 0 10px 0 35px">Groups: </b>{{ modifyData.groups }}

                <div style="width: 100px; float:right;text-align: right;padding-right: 10px">
                  <el-popconfirm
                      @confirm="applyDocTemplate"
                      title="确定要导入模板? 这会覆盖已存在的内容">
                    <template #reference>
                      <font-awesome-icon icon="file-import" style="cursor: pointer" title="导入模板"/>
                    </template>
                  </el-popconfirm>
                  &nbsp;
                  <a href="https://www.runoob.com/markdown/md-tutorial.html" target="_blank" style="color:var(--scp-text-color-primary)">
                    <font-awesome-icon icon="question-circle" style="cursor: pointer" title="Markdown 教程"/>
                  </a>
                </div>
              </el-col>
            </el-row>
          </div>
          <scp-md-editor v-model="modifyData.content" :style="{height: (height - 130) + 'px'}"/>
        </div>
      </template>
    </scp-draggable-resizable>

    <!-- modify relationship -->
    <scp-draggable-resizable :w="600" :h="150" v-model="relData.visible" :title="'Modify '+ props.id + ' Method Mapping'"
                             :save="modifyReportRel" :save-loading="relData.loading" :show-save="$store.state.maintainer==='Y'">
      <div style="padding: 5px" v-loading="relData.loading" class="subscript-modify-mapping">
        <el-select v-model="relData.selectedMethods"
                   placeholder="Select methods" collapse-tags multiple clearable filterable
                   style="width: calc(100% - 130px);">
          <el-option v-for="item in relData.avalibleMethods"
                     :key="item"
                     :label="item"
                     :value="item"/>
        </el-select>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import { useStore } from 'vuex'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'
import { useRoute } from 'vue-router'

const $store = useStore()
const $route = useRoute()
const $startWith: any = inject('$startWith')
const $axios: any = inject('$axios')
const $notify: any = inject('$notify')
const $message: any = inject('$message')

// @ts-ignore
const props = withDefaults(defineProps<{
  id?: string
}>(), {
  id: ''
})
const hasFlip = ref(false)

// region 1. introduction
const subscriptVisible = ref(false)
const introductionData = reactive({
  loading: false,
  title: '',
  owner: '',
  updateTime: '',
  descriptions: '',
  dataSync: '',
  subclass: 'subscript-langtime',
  showIntroduction: true
})

const showSubscript = () => {
  querySubscript()
}

const querySubscript = () => {
  if (introductionData.loading === true) {
    return
  } else {
    introductionData.loading = true
  }
  // 加载角标信息
  $axios({
    method: 'post',
    url: '/subscript/query_report_subscript',
    data: {
      id: props.id
    }
  }).then((body) => {
    if (body) {
      introductionData.title = body.TITLE
      introductionData.owner = body.OWNER
      introductionData.updateTime = body.CREATE_TIME
      introductionData.descriptions = body.DESCRIPTION
      introductionData.dataSync = body.DATA_SYNC
      nextTick(() => {
        subscriptVisible.value = true
      })
    } else {
      console.log('ID:' + props.id + ' does not exist')
    }
  }).catch((error) => {
    $notify({
      title: 'Error',
      message: error,
      type: 'error'
    })
  }).finally(() => {
    introductionData.loading = false
  })
}

onMounted(() => {
  if (props.id === 'EMPTY') {
    introductionData.subclass = 'subscript'
  }
  const dom = document.getElementById(props.id.toUpperCase())
  if (dom && dom.parentElement) {
    const parent = dom.parentElement
    const back = parent.getElementsByClassName('back')[0] as HTMLDivElement
    hasFlip.value = !!back
  }
})

// endregion

// region 2. relationship chart
interface Node {
  name: string,
  children: Array<Node>
}

interface Link {
  source: string,
  target: string,
  value: number
}

const chartData = reactive({
  chartType: 'tree',
  rawData: {} as Node,
  loading: false
})

const readTreeNodes = (nodes = {} as Node, arr = [] as Array<string>) => {
  if (nodes && nodes.name && arr.indexOf(nodes.name) === -1) {
    arr.push(nodes.name)
  }
  if (nodes && nodes.children && nodes.children.length) {
    for (let i = 0; i < nodes.children.length; i++) {
      readTreeNodes(nodes.children[i], arr)
    }
  }
  return arr
}

const readTreeLinks = (nodes = {} as Node, arr = [] as Array<Link>) => {
  if (nodes && nodes.children && nodes.children.length) {
    for (let i = 0; i < nodes.children.length; i++) {
      arr.push({ source: nodes.name, target: nodes.children[i].name, value: 1 })
      readTreeLinks(nodes.children[i], arr)
    }
  }
  return arr
}

const _diagramOpt = computed(() => {
  if (chartData.chartType === 'tree') {
    return {
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        formatter: function (params) {
          const tip = [] as Array<string>
          tip.push(params.name)
          tip.push('<br>')
          tip.push('<span style="font-size: 90%">')
          tip.push(params.value ? '- ' + params.value.replace('[REPORT]', '') : '')
          tip.push('</span>')
          return tip.join('')
        }
      },
      series: [
        {
          type: 'tree',
          data: [chartData.rawData || []],
          symbolSize: 5,
          left: '100px',
          right: '100px',
          top: '0',
          bottom: '0',
          lineStyle: {
            width: 1
          },
          label: {
            position: 'left',
            verticalAlign: 'middle',
            align: 'right',
            fontSize: 7
          },

          leaves: {
            label: {
              position: 'right',
              verticalAlign: 'middle',
              align: 'left'
            }
          },
          expandAndCollapse: false,
          animationDuration: 550,
          animationDurationUpdate: 750
        }
      ]
    }
  } else if (chartData.chartType === 'sankey') {
    const color = ['#1f77b4']
    let i = 0
    const data = readTreeNodes(chartData.rawData).map(e => {
      const index = i % color.length
      i++
      return {
        name: e,
        itemStyle: {
          color: color[index],
          borderColor: color[index]
        }
      }
    })
    let links = readTreeLinks(chartData.rawData)
    for (let j = 0; j < data.length; j++) {
      const node = data[j] as Node
      clearEndlessLoop(node.name, links, [node.name])
    }
    links = links.filter(e => !!e)
    return {
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        formatter: function (params) {
          const tip = [] as Array<String>
          tip.push(params.name)
          tip.push('<br>')
          tip.push('<div style="width:7rem;">')
          tip.push('Dependent tables: ')
          tip.push('<span style="font-size: 90%;float: right">')
          tip.push(params.value)
          tip.push('</span></div>')
          return tip.join('')
        }
      },
      series: [
        {
          top: 20,
          left: 100,
          right: 150,
          bottom: 20,
          type: 'sankey',
          data,
          links,
          emphasis: { focus: 'adjacency' },
          lineStyle: {
            color: 'source',
            curveness: 0.5
          },
          itemStyle: {
            borderColor: 'rgba(0,0,0,0)'
          },
          label: {
            fontSize: 7
          }
        }]
    }
  }
  return {}
})

const clearEndlessLoop = (header, links, result) => {
  for (let j = 0; j < links.length; j++) {
    const link = links[j] as Link
    if (link && link.source === header) {
      if (result.indexOf(link.target) === -1) {
        result.push(link.target)
        clearEndlessLoop(link.target, links, result)
      } else {
        links[j] = null
      }
    }
  }
}

const queryProjectDiagram = () => {
  // load project chart data
  chartData.loading = true
  chartData.rawData = {} as Node
  $axios({
    method: 'post',
    url: '/subscript/query_project_diagram',
    data: {
      id: props.id
    }
  }).then((body) => {
    chartData.rawData = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    chartData.loading = false
  })
}

const queryTableDiagram = (tablename: string) => {
  // load table diagram
  chartData.loading = true
  chartData.rawData = {} as Node
  $axios({
    method: 'post',
    url: '/subscript/query_table_diagram',
    data: {
      tablename
    }
  }).then((body) => {
    chartData.rawData = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    chartData.loading = false
  })
}

const chartRef = ref()
// switch view between introduction and chart

const resizeChart = () => {
  chartRef.value.resize()
}
const toggleChart = () => {
  introductionData.showIntroduction = !introductionData.showIntroduction
  if (introductionData.showIntroduction === false) {
    queryProjectDiagram()
  }
  nextTick(() => {
    resizeChart()
  })

  chartRef.value.chart().off('dblclick').on('dblclick', function (params) {
    if (chartData.chartType === 'tree') {
      if (params.value && $startWith(params.value, '[REPORT]')) {
        queryProjectDiagram()
      } else {
        queryTableDiagram(params.name)
      }
    }
  })
}

// endregion

// region 3. document modify
const modifyData = reactive({
  loading: false,
  loadingModify: false,
  subject: '',
  content: '',
  groups: '',
  template: '',
  visible: false
})

const showEditSubscriptWin = () => {
  modifyData.loading = true
  $axios({
    method: 'post',
    url: '/subscript/query_subscript_doc',
    data: {
      id: props.id,
      url: $route.path
    }
  }).then((body) => {
    if (body.message) {
      $message.error(body.message)
    } else {
      modifyData.subject = body.subject
      modifyData.content = body.content || ''
      modifyData.groups = body.groups
      modifyData.template = body.template || ''
      modifyData.visible = true
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    modifyData.loading = false
  })
}

const modifySubscriptDoc = () => {
  modifyData.loadingModify = true
  $axios({
    method: 'post',
    url: '/subscript/modify_subscript_doc',
    data: {
      id: props.id,
      url: $route.path,
      content: modifyData.content
    }
  }).then((body) => {
    if (body.message) {
      $message.error(body.message)
    } else {
      querySubscript()
      $message.success('Documentation Saved.')
      modifyData.visible = false
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    modifyData.loadingModify = false
  })
}
const applyDocTemplate = () => {
  modifyData.content = modifyData.template
}

// endregion

// region 4. toggle
// 处理翻转
const emmits = defineEmits(['afterToggle'])
const toggleData = reactive({
  viewType: 'front'
})

const getViewType = () => {
  return toggleData.viewType
}

const frontHeight = ref(-1)

const toggleView = () => {
  const dom = document.getElementById(props.id.toUpperCase())
  if (dom && dom.parentElement) {
    const parent = dom.parentElement
    const front = parent.getElementsByClassName('front')[0] as HTMLDivElement
    const back = parent.getElementsByClassName('back')[0] as HTMLDivElement

    if (front && back) {
      if (frontHeight.value < 0) {
        frontHeight.value = front.offsetHeight
      }

      if (toggleData.viewType === 'front') {
        back.style.transform = 'rotateY(0deg)'
        front.style.transform = 'rotateY(180deg)'
        setTimeout(() => {
          back.style.setProperty('display', 'block', 'important')
          front.style.setProperty('display', 'none', 'important')
        }, 300)
      } else {
        back.style.transform = 'rotateY(-180deg)'
        front.style.transform = 'rotateY(0deg)'
        setTimeout(() => {
          back.style.setProperty('display', 'none', 'important')
          front.style.setProperty('display', 'block', 'important')
        }, 300)
      }
    }

    toggleData.viewType = toggleData.viewType === 'front' ? 'back' : 'front'

    setTimeout(() => {
      window.dispatchEvent(new Event('resize')) // 手动触发resize事件, 让前台的echarts自动调整宽度
      emmits('afterToggle', toggleData.viewType)
    }, 500) // 延迟500ms, 等页面完全翻转过去之后再call方法
  }
}
// endregion

// region 5. 配置报表与方法的关系
const relData = reactive({
  visible: false,
  loading: false,
  avalibleMethods: [],
  selectedMethods: []
})

const showReportRelWin = () => {
  $axios({
    method: 'post',
    url: '/subscript/query_related_methods',
    data: {
      id: props.id,
      url: $route.path
    }
  }).then((body) => {
    relData.avalibleMethods = body.avalibleMethods
    relData.selectedMethods = body.selectedMethods
    relData.visible = true
  }).catch((error) => {
    console.log(error)
  })
}

const modifyReportRel = () => {
  relData.loading = true
  $axios({
    method: 'post',
    url: '/subscript/modify_report_rel',
    data: {
      id: props.id,
      urls: relData.selectedMethods
    }
  }).then(() => {
    relData.visible = false
    subscriptVisible.value = false
    $message.success('Mapping saved.')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    relData.loading = false
  })
}

// endregion

defineExpose({
  getViewType, toggleView
})
</script>

<script lang="ts">
export default {
  name: 'Subscript'
}
</script>

<style lang="scss" scoped>
.subscript-content {
  padding: 10px;
  height: calc(100% - 30px);

  .tree {
    height: calc(100% - 70px);
  }

  .subscript-title {
    line-height: 30px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--scp-border-color-lighter);
  }

  .subscript-title :nth-child(1).description {
    float: left;
  }

  .subscript-title :nth-child(2).description {
    float: right;
    padding-top: 9px;
    width: 30px;
  }

  .subscript-title :nth-child(2).description:hover {
    cursor: pointer;
  }

  .subscript-title :nth-child(3).description {
    float: right;
  }

  .subscript-description {
    padding-top: 10px;
  }

  .subscript-description .description li {
    margin-bottom: 6px;
  }

  .subscript-description .description li:before {
    content: '';
    border-width: 3px;
    border-style: solid;
    border-color: transparent transparent transparent var(--scp-border-color-lighter);
    margin-right: 4px;
  }

  .subscript-front, .subscript-back {
    width: calc(100% - 10px);
    height: calc(100% - 10px);
    position: absolute;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: ease-in-out 500ms;
  }

  .subscript-back {
    transform: rotateY(-180deg);
  }
}
</style>

<style lang="scss">
.subscript-modify-mapping {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset !important;
  }
}

.subscript-area {
  width: 128px;
  height: 16px;
  position: absolute;
  right: 0;
  overflow: hidden;
  top: 0;
  z-index: 180;
}

.subscript-area:hover {
  background-color: transparent;

  .subscript-flip {
    color: var(--scp-text-color);
  }

  .subscript-info {
    color: var(--scp-text-color);
  }

  .subscript-flip:hover, .subscript-info:hover {
    color: var(--scp-text-color-highlight);
  }
}

.subscript-flip {
  color: transparent;
  font-size: 0.5rem;
  cursor: pointer;
  position: absolute;
  right: 20px;
}

.subscript-info {
  color: transparent;
  font-size: 0.5rem;
  cursor: pointer;
  position: absolute;
  z-index: 800;
  right: 5px;
}
</style>
