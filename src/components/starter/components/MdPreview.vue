<template>
  <div>
    <md-preview v-model="_content" :theme="props.theme || $store.state.theme" preview-theme="github" code-theme="github" :auto-fold-threshold="props.autoFoldThreshold"
                :show-code-row-number="true" :editor-id="props.editorId || ('md-editor-v3_' + (Math.random() * 10000 + '').split('.')[0])"
                :md-heading-id="props.markedHeading" :noKatex="props.noKatex"/>
  </div>
</template>

<script lang="ts" setup>
import { MdPreview } from 'md-editor-v3'
import { computed } from 'vue'
import { useStore } from 'vuex'
import '@/assets/css/markdown.css' /* you must replace this file by @md-editor-v3/lib/style.css when md-editor-v3 upgrade */

const $store = useStore()
// @ts-ignore
const props = withDefaults(defineProps<{
  modelValue?: string,
  editorId?: string,
  markedHeading?: any,
  theme?: any,
  autoFoldThreshold?: number,
  noKatex?: boolean
}>(), {
  modelValue: '',
  editorId: '',
  markedHeading: undefined,
  theme: undefined,
  autoFoldThreshold: 1024,
  noKatex: false
})

const emmits = defineEmits(['update:modelValue'])
const _content = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})
</script>

<script lang="ts">
export default {
  name: 'ScpMdPreview'
}
</script>
