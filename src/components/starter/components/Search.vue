<template>
  <span>
    <el-popover
            style="padding:10px 10px !important;"
            :placement="props.placement"
            @show="queryConditions"
            width="350"
            trigger="contextmenu"
            :offset="offset"
            v-model:visible="visibleCtl.pop">
      <div>
        <el-row>
          <el-col :span="20">
            <el-input placeholder="Filter Name" v-model="pageCtl.name" size="small" clearable></el-input>
          </el-col>
          <el-col :span="4" style="text-align: center; line-height: 2">
            <font-awesome-icon class="add-name" icon="check" @click="saveCondition"/>
          </el-col>
        </el-row>
        <el-row class="search-include-date">
          <el-col :span="24">
              <el-tooltip class="item" effect="light" placement="left" :show-after="500">
                  <template #content>
                      默认情况下，DSS在保存页面参数时，会排除日期字段，以保证此参数可以用于长期追踪 <br>
                      但在个别情况下，我们需要带着日期来保证不同用户可以拥有相同视角，此时请勾选此项<br>
                      <b><u>当你想分享一个异常数据时，务必勾选此选项</u></b>
                  </template>
                  <el-checkbox v-model="pageCtl.withDate" label="Include Date Field" size="small"/>
              </el-tooltip>
          </el-col>
        </el-row>
        <div class="saved-name" v-loading="loadingCtl.params" style="min-height: 60px">
          <div v-for="(e,i) in pageCtl.conditions" :key="i">
            <div class="pull-left" style="width: calc(100% - 105px);max-width: 220px;height: 24px;overflow: hidden"
                 @click="()=>{pageCtl.name = e['NAME']; pageCtl.withDate = e['WITH_DATE'] === 'true'}"
                 @dblclick="fillConditions(i)">
              <span v-show="e['WITH_DATE'] === 'true'">
                  <el-tooltip effect="light" content="Include Date Field" :show-after="1000">
                    <font-awesome-icon icon="fa-regular fa-clock"/>
                  </el-tooltip>&nbsp;
              </span>
              <span v-show="e['SHARED_BY']" style="color:var(--scp-text-color-highlight)"
                    :title="e['NAME'] + '\r' + '- Shared by: ' + e['SHARED_BY'] + '\r' + (e['SHARED_REMARKS'] || '')">{{ e["NAME"] }}</span>
              <span v-show="!e['SHARED_BY']">{{ e["NAME"] }}</span>
            </div>
            <div class="pull-right" style="width: 96px;text-align: right">
              <el-tooltip class="item" effect="light" :show-after="1000" content="Set as Default">
                <font-awesome-icon style="line-height:24px;margin-right: 8px" class="add-name"
                                   :icon="e['IS_DEFAULT'] === '1'? 'fa-solid fa-star' : 'fa-regular fa-star'"
                                   @click="toggleDefaultCondition(e['COND_ID'], e['IS_DEFAULT'])" v-show="pageCtl.enableDefaultPage"/>
              </el-tooltip>
              <el-tooltip class="item" effect="light" :show-after="1000" content="Copy Share Link">
                <font-awesome-icon class="add-name" style="line-height:24px;margin-right: 8px" icon="fa-solid fa-link" @click="copyShareLink(e['COND_ID'])"
                                   v-show="pageCtl.enableUrlShare"/>
              </el-tooltip>
              <el-tooltip class="item" effect="light" :show-after="1000" content="Share Variant">
                <font-awesome-icon class="add-name" style="line-height:24px;margin-right: 8px" icon="fa-solid fa-share-nodes"
                                   @click="shareCondition(e['COND_ID'], e['NAME'], e['WITH_DATE'])"/>
              </el-tooltip>
              <el-tooltip class="item" effect="light" :show-after="1000" content="Delete">
                <font-awesome-icon class="add-name" style="line-height:24px;margin-right: 8px" icon="times" @click="delCondition(i, e['COND_ID'])"/>
              </el-tooltip>
            </div>
          </div>
          <div class="clearfix"/>
        </div>
        <el-button class="pull-right" size="small" @click="closePop" style="margin-top: 10px">Close</el-button>
        <div class="clearfix"/>
      </div>

      <template #reference>
        <div style="display: inline-block">
          <el-button size="small" @click="search" :disabled="props.disabled" :loading="loadingCtl.init" v-show="!_expandEnable">
            <font-awesome-icon :icon="icon" v-show="!loadingCtl.init"/>
            {{ props.text }}
          </el-button>
          <el-button-group v-show="_expandEnable">
            <el-button size="small" @click="search" :disabled="props.disabled" :loading="loadingCtl.init">
              <font-awesome-icon :icon="icon" v-show="!loadingCtl.init"/>
              {{ props.text }}
            </el-button>
            <el-button size="small" @click="expandClick" class="expand-button">
              <font-awesome-icon :icon="pageCtl.expand ? 'angle-up': 'angle-down'"/>
            </el-button>
          </el-button-group>
        </div>
      </template>
    </el-popover>

    <scp-draggable-resizable v-model="visibleCtl.share" :save="shareConditionAction" :save-loading="loadingCtl.share" save-text="Share"
                             w="60vw" h="600px" :title="pageCtl.shareTitle">
      <template v-slot="{height}">
        <div style="padding: 5px 5px 0 5px;">
          <el-row>
            <el-col :span="24">
              <el-select v-model="pageCtl.sharedUsers" :placeholder="loadingCtl.query ? 'Loading...' : 'Share to'" style="width: 100% !important;"
                         collapse-tags clearable multiple filterable>
                <el-option
                        v-for="user in pageCtl.allUsers" :key="user['VAL']" :label="user['LABEL']" :value="user['VAL']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="24">
              <scp-ck-editor v-model="pageCtl.remarks" :style="{height: (height - 180) + 'px'}"/>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>
  </span>
</template>

<!--
自动加载逻辑:

1. 页面开启了enableUrlShare, 系统会判断url是否带cid, 走cid校验逻辑, 否则忽略cid
2. 页面开启了enableDefaultPage, 系统会自动判断默认页面, 如果没有默认条件, 自动触发查询, 否则不触发
3. 仅有当enableDefaultPage为true时, 才会主动加载load方法, 其他情况不会自动加载
4. 使用load方法时, 需要避免方法的重复调用, 建议使用F12进行调试查看
5. enableDefaultPage为true时, 务必配置load方法, 否则会导致页面无法正常加载或者重复加载

-->
<script lang="ts" setup>
import { computed, inject, nextTick, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'

// region 变量声明和定义
const $route = useRoute()
const $router = useRouter()
const $axios: any = inject('$axios')
const $copyText: any = inject('$copyText')
const $message: any = inject('$message')
const $startWith: any = inject('$startWith')

const props = withDefaults(defineProps<{
    disabled?: boolean,
    icon?: string,
    text?: string,
    clickNative: Function,
    beforeSaveCondition?: Function,
    data?: Object,
    dataExclude?: Array<string>,
    offset?: number,
    placement?: string,
    afterExpand?: Function,
    expand?: boolean
}>(), {
  disabled: false,
  icon: 'search',
  text: '',
  clickNative: undefined,
  beforeSaveCondition: undefined,
  data: () => {
    return {}
  },
  dataExclude: () => {
    return []
  },
  offset: 0,
  placement: 'bottom',
  afterExpand: undefined,
  expand: false
})

const _expandEnable = computed(() => {
  return typeof props.afterExpand !== 'undefined'
})

interface Conditions {
    COND_ID: string,
    CONDITIONS: string,
    SHARED_BY: string,
    NAME: string,
    WITH_DATE: string,
    SHARED_REMARKS: string,
    IS_DEFAULT: string
}

const pageCtl = reactive({
  cid: '',
  name: '',
  withDate: false,
  allUsers: [],
  sharedUsers: [],
  remarks: '',
  shareTitle: '',
  conditions: [] as Array<Conditions>,
  expand: false,
  enableUrlShare: false,
  enableDefaultPage: false
})

const visibleCtl = reactive({
  pop: false,
  share: false
})

const loadingCtl = reactive({
  share: false,
  query: false,
  params: false,
  init: false
})

// endregion

// 监控路由变化, 如果变化则重新执行查询
watch(() => $route, () => {
  init()
})

const init = () => {
  pageCtl.expand = props.expand
  const id = $route.query.cid
  if (id) {
    if (pageCtl.enableUrlShare === true) {
      redirectByConditionID(id)
    }
  } else {
    if (pageCtl.enableDefaultPage === true) {
      redirectByDefaultCondition()
    }
  }
}

const search = () => {
  visibleCtl.pop = false
  props.clickNative()
}

const loadAndClick = (enableUrlShare?, enableDefaultPage?) => {
  loadWithoutClick(enableUrlShare, enableDefaultPage)
  init()
}

const loadWithoutClick = (enableUrlShare, enableDefaultPage) => {
  if (typeof enableUrlShare !== 'undefined') {
    pageCtl.enableUrlShare = enableUrlShare
  } else {
    pageCtl.enableUrlShare = true
  }
  if (typeof enableDefaultPage !== 'undefined') {
    pageCtl.enableDefaultPage = enableDefaultPage
  } else {
    pageCtl.enableDefaultPage = true
  }
}

const fillConditionsByObject = (obj, fillingWithDate: boolean) => {
  if (obj) {
    let conditions = obj
    if ((typeof obj) === 'string') {
      conditions = JSON.parse(obj)
    }

    for (const key in conditions) {
      if (conditions.hasOwnProperty(key) && $startWith(key, '_') === false) {
        if (fillingWithDate) {
          props.data[key] = conditions[key]
        } else if (props.dataExclude.indexOf(key) === -1) {
          props.data[key] = conditions[key]
        }
      }
    }
    nextTick(() => {
      props.clickNative('VARIANT')
    })
    visibleCtl.pop = false
  }
}

// 根据页面传参来决定条件跳转, 如果id无效, 弹窗提示
// 如果当前页面没有开放cid分享, 则只执行查询
const redirectByConditionID = (cid) => {
  loadingCtl.init = true
  $axios({
    method: 'post',
    url: '/search/query_page_condition_by_id',
    data: {
      cid,
      url: $route.path
    }
  }).then((body) => {
    if (body) {
      fillConditionsByObject(body.CONDITIONS, body.WITH_DATE + '' === 'true')
    } else {
      $message.error('The variant you select does not exist, request will be sent using default variant!')
      $router.push($route.path)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.init = false
  })
}

// 查询默认的条件
const redirectByDefaultCondition = () => {
  $axios({
    method: 'post',
    url: '/search/query_page_condition_by_default',
    data: {
      url: $route.path
    }
  }).then((body) => {
    if (body) {
      fillConditionsByObject(body.CONDITIONS, body.WITH_DATE + '' === 'true')
    } else {
      nextTick(() => {
        props.clickNative()
      })
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.params = false
  })
}

const closePop = () => {
  visibleCtl.pop = false
}

const queryConditions = () => {
  pageCtl.name = ''
  pageCtl.withDate = false
  loadingCtl.params = true
  $axios({
    method: 'post',
    url: '/search/query_page_conditions',
    data: {
      url: $route.path
    }
  }).then((body) => {
    pageCtl.conditions = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.params = false
  })
}

const _jsonData = computed(() => {
  const p = {}
  for (const key in props.data) {
    if (props.data.hasOwnProperty(key) && $startWith(key, '_') === false) {
      if (pageCtl.withDate === false) {
        if (props.dataExclude.indexOf(key) === -1) {
          p[key] = props.data[key]
        }
      } else {
        p[key] = props.data[key]
      }
    }
  }
  return JSON.stringify(p)
})

const saveCondition = () => {
  if (pageCtl.name) {
    if (props.beforeSaveCondition) {
      props.beforeSaveCondition()
    }
    $axios({
      method: 'post',
      url: '/search/save_page_conditions',
      data: {
        url: $route.path,
        name: pageCtl.name,
        withDate: pageCtl.withDate.toString(),
        conditions: _jsonData.value
      }
    }).then((body) => {
      pageCtl.conditions = body
      pageCtl.name = ''
      pageCtl.withDate = false
      $message.success('Variant saved!')
    }).catch((error) => {
      console.log(error)
    })
  } else {
    $message.error('Please provide a variant name!')
  }
}

const toggleDefaultCondition = (cid, isDefault) => {
  $axios({
    method: 'post',
    url: '/search/toggle_default_conditions',
    data: {
      url: $route.path,
      cid,
      isDefault
    }
  }).then((body) => {
    pageCtl.conditions = body
    if (isDefault === '1') {
      $message.success('Default variant removed.')
    } else {
      $message.success('Default variant set.')
    }
  }).catch((error) => {
    console.log(error)
  })
}

const copyShareLink = (cid) => {
  let url = window.location.href
  url = url.split('?')[0]
  url = url + '?cid=' + cid
  $copyText(url)
  $message.success('Share link has been copied to the clipboard.')
}

const delCondition = (i, cid) => {
  $axios({
    method: 'post',
    url: '/search/delete_page_conditions',
    data: {
      url: $route.path,
      cid
    }
  }).then(() => {
    pageCtl.conditions.splice(i, 1)
  }).catch((error) => {
    console.log(error)
  })
}

const fillConditions = (i) => {
  fillConditionsByObject(pageCtl.conditions[i].CONDITIONS, pageCtl.conditions[i].WITH_DATE === 'true')
}

const expandClick = () => {
  pageCtl.expand = !pageCtl.expand
  if (props.afterExpand) {
    props.afterExpand(pageCtl.expand)
  }
}

const shareCondition = (cid, cname, withDate) => {
  pageCtl.cid = cid
  pageCtl.name = cname
  pageCtl.withDate = withDate === 'true'
  loadingCtl.query = true
  visibleCtl.share = true
  pageCtl.shareTitle = 'Share [ ' + pageCtl.name + ' ]'
  $axios({
    method: 'post',
    url: '/search/query_all_users'
  }).then((body) => {
    pageCtl.allUsers = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.query = false
  })
}

const shareConditionAction = () => {
  if (pageCtl.sharedUsers.length === 0) {
    $message.error('Please select at least one user to share')
    return
  }
  loadingCtl.share = true
  $axios({
    method: 'post',
    url: '/search/share_condition',
    data: {
      cid: pageCtl.cid,
      name: pageCtl.name,
      withDate: pageCtl.withDate,
      url: $route.path,
      users: pageCtl.sharedUsers,
      remarks: pageCtl.remarks
    }
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success(pageCtl.name + ' shared!')
      visibleCtl.share = false
      pageCtl.remarks = ''
      pageCtl.sharedUsers = []
      pageCtl.cid = ''
      pageCtl.name = ''
      pageCtl.withDate = false
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.share = false
  })
}

defineExpose({
  loadAndClick, loadWithoutClick
})
</script>

<script lang="ts">
export default {
  name: 'ScpSearch'
}
</script>
<style lang="scss" scoped>
.add-name {
  font-size: 0.4rem;

  &:hover {
    cursor: pointer;
    color: var(--scp-text-color-highlight);
  }
}

.saved-name {
  margin-top: 4px;
  padding: 8px 0 8px 0;
  border-top: solid 1px var(--scp-border-color-lighter);
  min-width: 345px;

  .pull-left:hover {
    cursor: pointer;
    background-color: var(--scp-bg-color-fill);
  }

  div {
    line-height: 24px;
    padding: 0;
    user-select: none;

    i {
      cursor: pointer;

      &:hover {
        color: var(--scp-text-color-error);
      }
    }
  }
}

.expand-button {
  padding: 0.25rem 0.35rem !important;
}
</style>

<style lang="scss">
.search-include-date {
  margin-top: 4px;

  .el-checkbox__label {
    font-size: 0.45rem !important;
  }
}
</style>
