<template>
  <el-cascader
      :style="$attrs.style"
      v-model="_content"
      :placeholder="props.loading ? 'Loading...' : props.placeholderText"
      :options="props.options"
      :props="_cascaderProps"
      :collapse-tags="true"
      :filter-method="cascaderFilter"
      :size="props.size"
      clearable
      filterable
      collapse-tags-tooltip
      class="scp-cascader"
  ></el-cascader>
  <font-awesome-icon icon="copy" class="cascader-copy-config" title="copy admin auth details" @click="copyAdminAuth"
                     v-if="$store.state.maintainer==='Y' && props.showCopy === true"/>
</template>
<script lang="ts" setup>
import { computed, inject } from 'vue'
import { useStore } from 'vuex'

const $store = useStore()
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')

const props = withDefaults(
  defineProps<{
      modelValue: Array<any>, // 双向绑定
      placeholderText?: any,
      loading?: boolean,
      size?: any,
      options?: Array<any> // 参数筛选,
      multiple?: boolean,
      showCopy?: boolean
    }>(),
  {
    modelValue: () => [],
    loading: false,
    placeholderText: 'Filters',
    size: 'small',
    multiple: true,
    showCopy: true,
    options: () => []
  }
)

const emmits = defineEmits(['update:modelValue'])
const _content = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})

const _cascaderProps = computed(() => {
  return { multiple: props.multiple }
})

const copyAdminAuth = () => {
  const vs = _content.value
  const result = {}
  for (let i = 0; i < vs.length; i++) {
    const v = vs[i]
    const r = result[v[0]] || []
    r.push(v[1])
    result[v[0]] = r
  }
  $copyText(JSON.stringify(result))
  $message.success('Auth details copied.')
}

const cascaderFilter = (node, val) => {
  return !!(node && node.text && node.text.toUpperCase().indexOf(val.toUpperCase()) !== -1)
}
</script>

<script lang="ts">
export default {
  name: 'ScpCascader'
}
</script>

<style lang="scss">
.scp-cascader {
  .el-tag--small {
    height: auto !important;
  }

  .el-input__inner {
    height: var(--el-input-inner-height) !important;
  }

  .el-input__wrapper {
    padding-left: 12px !important;
  }

  .el-cascader__tags {
    .el-tag--small {
      .el-tag__content {
        font-size: 0.45rem !important
      }
    }
  }
}

.el-cascader-node {
  height: 1.1rem !important;
  line-height: 1.1rem !important;
}

.cascader-copy-config {
  margin-bottom: 0.2rem;
  margin-left: 0.1rem;
  font-size: 0.4rem;
  color: transparent;
  cursor: pointer;
}

.cascader-copy-config:hover {
  color: var(--scp-text-color-secondary);
}
</style>
