<template>
  <div class="table-box" ref="tableBoxRef">
    <div v-loading="tableCtl.loading && props.enableLoading" class="table-area" :style="fullscreenCtl.fullscreenStyle" @contextmenu.prevent>
      <hot-table ref="hotTableRef"
                 :data="tableCtl.data"
                 :columns="_columns"
                 :colHeaders="_colHeaders"
                 :nestedHeaders="_nestedHeaders"
                 :dropdownMenu="_dropdownMenu"
                 :filters="props.filters"
                 :formulas="props.formulas"
                 :columnSorting="props.columnSorting"
                 :stretchH="props.stretchH"
                 :autoWrapRow="props.autoWrapRow"
                 :manualRowResize="props.manualRowResize"
                 :manualColumnResize="props.manualColumnResize"
                 :rowHeaders="props.rowHeaders"
                 :manualRowMove="props.manualRowMove"
                 :manualColumnMove="_manualColumnMove"
                 :licenseKey="tableCtl.licenseKey"
                 :height="tableCtl.height + 'px'"
                 :width="tableCtl.width"
                 :contextMenu="_contextMenu"
                 :columnSummary="props.columnSummary"
                 :beforeColumnSort="beforeColumnSort"
                 :beforeFilter="beforeFilter"
                 :afterSelectionEnd="afterSelectionEnd"
                 :hiddenColumns="_hiddenColumns"
                 :collapsibleColumns="_collapsibleColumns"
                 :afterChange="afterChangeTrigger"
                 :beforeRemoveRow="beforeRemoveRow"
                 :afterCreateRow="afterCreateRow"
                 :fixedColumnsLeft="props.fixedColumnsLeft"
                 :renderAllRows="props.renderAllRows"
                 :mergeCells="props.mergeCells"
                 :class="props.tableStriped ? 'table-striped' : ''"
      ></hot-table>
      <el-pagination
          ref="paginationRef"
          size="small"
          v-show="props.pagging"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableCtl.currentPage"
          :page-sizes="props.pageSizes"
          :page-size="tableCtl.pageSize"
          layout="total, sizes, ->, prev, pager, next"
          style="margin:10px 5px 0 10px"
          :total="tableCtl.total">
      </el-pagination>
    </div>

    <el-dialog
        title="Save Changes?"
        v-model="tableCtl.confirmVisible"
        :append-to-body="true"
        width="30%">
      <span v-html="tableCtl.confirmMessage"></span>
      <template #footer>
        <span class="dialog-footer">
        <el-button @click="tableCtl.confirmVisible = false">Cancel</el-button>
        <el-button type="primary" @click="saveData" :loading="tableCtl.saving">Confirm</el-button>
      </span>
      </template>
    </el-dialog>

    <el-dialog
        title="Choose Output Columns"
        v-model="tableCtl.downloadVisible"
        :append-to-body="true"
        width="30%">
      <el-radio-group v-model="tableCtl.downloadType" size="small">
        <el-radio-button value="All Columns">All Columns</el-radio-button>
        <el-radio-button value="Specify Columns" :disabled="tableCtl.downloadColumnOpts.length === 0">Specify Columns</el-radio-button>
      </el-radio-group>
      <div v-if="tableCtl.downloadColumnOpts.length === 0" style="margin-top: var(--scp-widget-margin); font-size: 0.5rem;" class="input-tips">
         *当前只可下载所有记录, 下载所有记录后方可执行选列操作
      </div>
      <div style="margin-top: var(--scp-widget-margin)" v-show="tableCtl.downloadType === 'Specify Columns'">
          <el-select v-model="tableCtl.downloadColumns" filterable placeholder="Select..." style="width: calc(100% - 100px)" clearable collapse-tags multiple>
            <el-option
                v-for="item in tableCtl.downloadColumnOpts"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
      </div>
      <template #footer>
        <span class="dialog-footer">
        <el-button @click="tableCtl.downloadVisible = false">Cancel</el-button>
        <el-button type="primary" @click="downloadAllResultAction">Download</el-button>
      </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { HotTable } from '@handsontable/vue3'
import 'handsontable/dist/handsontable.full.css'
import { registerAllModules } from 'handsontable/registry'
import { inject, reactive, ref, onMounted, onUpdated, computed, nextTick, watch } from 'vue'
import html2canvas from 'html2canvas'
import { useRoute } from 'vue-router'

registerAllModules()

const $route = useRoute()
const $axios: any = inject('$axios')
const $isDate: any = inject('$isDate')
const $message: any = inject('$message')
const $deepClone: any = inject('$deepClone')
const $downloadFile: any = inject('$downloadFile')
const $randomString: any = inject('$randomString')
const $selectValidator: any = inject('$selectValidator')
const $renderColumnName: any = inject('$renderColumnName')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $renderColumnAsNumber: any = inject('$renderColumnAsNumber')
const $renderColumnAsPositiveNumber: any = inject('$renderColumnAsPositiveNumber')
const hotTableRef: any = ref()
const tableBoxRef: any = ref()
const paginationRef: any = ref()

// region 变量和computed
export interface Column {
  title?: string,
  data: string,
  type?: string,
  render?: Function,
  renderer?: Function,
  readOnly?: boolean,
  precision?: number,
  editor?: string,
  selectOptions?: Array<string>,
  source?: Function | Array<string>,
  strict?: boolean,
  width?: number,
  numericFormat?: string,
  dateFormat?: string,
  validator?: Function,
  sorting?: boolean,
  columnSorting?: any
}

const props = withDefaults(
  defineProps<{
      pagging?: boolean,
      enableLoading?: boolean,
      downloadUrl?: string,
      lazy?: boolean,
      params?: object,
      showContextMenu?: boolean,
      showTotal?: boolean,
      showTotalIgnoreCols?: Array<string>,
      lastRowBold?: boolean,
      contextMenuItems?: object,
      overwriteContextMenu?: boolean,
      contextMenuItemsReverse?: boolean,
      url: string,
      columns?: Array<Column>,
      nestedHeaders?: Array<any>,
      pageSizes?: Array<number>,
      afterSelect?: Function,
      afterChange?: Function,
      beforeSearch?: Function,
      afterSearch?: Function,
      afterDownload?: Function,
      beforeSave?: Function,
      afterSave?: Function,
      maxHeight?: number,
      rowHeight?: number,
      marginBottom?: number,
      filters?: boolean,
      dropdownMenu?: boolean,
      formulas?: boolean,
      columnSorting?: boolean,
      stretchH?: string,
      autoWrapRow?: boolean,
      manualRowResize?: boolean,
      manualColumnResize?: boolean,
      rowHeaders?: boolean,
      manualRowMove?: boolean,
      manualColumnMove?: boolean,
      columnSummary?: Array<any>,
      mergeCells?: Array<any>,
      hiddenColumns?: object,
      fixedColumnsLeft?: number,
      tableStriped?: boolean,
      primaryKeyId?: Array<string>,
      editable?: boolean,
      renderAllRows?: boolean,
      saveUrl?: string,
      saveValidation?: boolean,
      downloadSpecifyColumn?: boolean,
      precision?: number,
      autoTitle?: boolean
    }>(),
  {
    pagging: true, // 是否显示页码
    enableLoading: true, // 是否开启加载动画, 有些场景, 加载动画需要使用在页面控制
    downloadUrl: undefined, // 下载url, 如果为空, download菜单不可用
    downloadSpecifyColumn: true, // 在执行download all时, 是否可以指定下载列
    lazy: false, // 是否懒加载, 懒加载就需要用户手动触发查询
    params: () => { // 请求url, saveUrl, downloadUrl的参数
      return {}
    },
    showContextMenu: true, // 是否显示右键
    showTotal: false, // 是否显示汇总行
    showTotalIgnoreCols: () => { // 表格列
      return []
    },
    lastRowBold: false, // 最后一行是否加粗, 一般和showTotal一起用
    contextMenuItems: () => { // 自定义右键菜单
      return {}
    },
    overwriteContextMenu: false, // 是否覆盖原有菜单
    contextMenuItemsReverse: true, // 自定义的菜单是否显示在最上方, false则显示在下方
    url: undefined, // 数据请求的url
    columns: () => { // 表格列
      return []
    },
    nestedHeaders: undefined, // 多表头
    pageSizes: () => { // 默认分页方案
      return [10, 20, 50, 100, 200, 500]
    },
    afterSelect: undefined, // 事件钩子, 含义参考方法名
    afterChange: undefined, // 事件钩子, 含义参考方法名
    beforeSearch: undefined, // 事件钩子, 含义参考方法名
    afterSearch: undefined, // 事件钩子, 含义参考方法名
    afterDownload: undefined, // 事件钩子, 含义参考方法名
    beforeSave: undefined, // 事件钩子, 含义参考方法名
    afterSave: undefined, // 事件钩子, 含义参考方法名
    maxHeight: 800, // 表格出现滚动条的高度, 单位px
    rowHeight: 23.4, // 表格行高, 单位px
    marginBottom: 0, // 表格距离外框的高度, 如果没有margin, dropdown可能会显示异常
    filters: true, // 是否在列头显示下拉菜单中的筛选框
    dropdownMenu: true, // 是否在列头显示下拉菜单
    formulas: false, // 是否支持公式, 支持公式在大数据量下, 加载速度会变慢
    columnSorting: true, // 是否支持单击表头排序
    stretchH: 'all', // 列自动填充容器宽度
    autoWrapRow: true, // 自动换行, 在stretchH: 'all'下, 会出现滚动条, 不再进行换行
    manualRowResize: true, // 是否可以手动调整行尺寸
    manualColumnResize: true, // 是否可以手动调整列尺寸
    rowHeaders: true, // 是否显示行表头, 就是1, 2, 3, 4...
    manualRowMove: true, // 是否可以拖动行
    manualColumnMove: true, // 是否可以拖动列
    columnSummary: undefined, // 对列汇总, 没用过
    mergeCells: undefined, // 合并单元格
    hiddenColumns: undefined, // 隐藏列
    fixedColumnsLeft: undefined, // 锁定左侧表头数量
    tableStriped: true, // 是否需要斑马纹显示表格
    primaryKeyId: () => { // 在修改数据时, 列的主键, ROW_ID是Oracle的主键, 一般用这个
      return ['ROW_ID']
    },
    editable: true, // 可以编辑, 如果为false, 可以隐藏save changes和rows相关的菜单
    renderAllRows: false, // 按需渲染表格, false可以增加效率, 但是true可以增加拖拽效率
    saveUrl: undefined, // 保存数据的后台url, 如果不填, 则save changes菜单不可用
    saveValidation: true, // 保存数据时是否进行数据校验, 需要配合单元格校验使用
    precision: undefined, // 数据展示默认精度, 会被Column中的精度覆盖
    autoTitle: true // 是否自动根据data列转移表头, 比如PLANT_CODE转义成Plant Code
  }
)

const tableCtl = reactive({
  height: 150, // 表格高度
  width: '100%', // 表格宽度
  loading: false, // 加载动画
  saveStatus: false, // 当前表格是否被修改, 如果被修改, 则为true, 控制save change按钮显示
  total: 0, // 总行数
  licenseKey: 'non-commercial-and-evaluation',
  pageSize: props.pageSizes[0], // 显示行数
  currentPage: 1, // 当前页
  sortColumn: '', // 记录排序列
  sortOrder: '',
  data: [{}] as Array<object>,
  conditions: [] as Array<object>,
  updatedRows: {},
  removedRows: [] as Array<string>,
  createdRows: [] as Array<any>,
  maxRows: 65535,
  confirmVisible: false,
  downloadVisible: false,
  downloadType: 'All Columns',
  downloadColumns: [],
  downloadColumnOpts: [],
  confirmMessage: '',
  saving: false,
  searching: false
})

const _nestedHeaders = computed(() => {
  if (props.nestedHeaders) {
    const nestedHeaders = [] as Array<any>
    for (let i = 0; i < props.nestedHeaders.length; i++) {
      nestedHeaders.push(props.nestedHeaders[i])
    }
    nestedHeaders.push(props.columns.map(e => e.title || $renderColumnName(e.data)))
    return nestedHeaders
  } else {
    return undefined
  }
})

const _colHeaders = computed(() => {
  return _columns.value.map(e => e.title || $renderColumnName(e.data))
})

const _colOrgTypes = computed(() => {
  return _columns.value.map(e => e.type || 'VARCHAR')
})

const _colOrgFormats = computed(() => {
  return _columns.value.map(e => e.dateFormat || '')
})

const _dropdownMenu = computed(() => {
  // 当禁用sorting的时候, dropdownMenu的筛选会有bug, 所以强制约束, 当禁用sorting时也会禁用dropdownMenu
  if (props.dropdownMenu) {
    if (props.columnSorting) {
      return ['alignment', '---------', 'filter_by_condition', 'filter_operators', 'filter_by_condition2', 'filter_by_value', 'filter_action_bar']
    } else {
      return false
    }
  } else {
    return false
  }
})

const _hiddenColumns = computed(() => {
  if (props.nestedHeaders) {
    return true
  } else {
    return props.hiddenColumns
  }
})

const _collapsibleColumns = computed(() => {
  if (props.nestedHeaders) {
    return true
  } else {
    return undefined
  }
})

const _manualColumnMove = computed(() => {
  if (props.nestedHeaders) {
    return false
  } else {
    return props.manualColumnMove
  }
})

// endregion

// region 右键菜单
const _contextMenu = computed(() => {
  // 如果配置了不显示右键菜单, 则直接返回false
  if (props.showContextMenu === false) {
    return false
  }

  let menu = defaultContentMenu as any
  if (props.overwriteContextMenu === true) {
    menu = { items: {} }
  }
  // 如果用户配置不可编辑, 则移除下列菜单
  if (props.editable === false) {
    delete menu.items.split1
    delete menu.items.save_changes
    delete menu.items.split4
    delete menu.items.row_above
    delete menu.items.row_below
    delete menu.items.split5
    delete menu.items.remove_row
  }

  // 如果禁用了filter, 也不需要显示clear filter
  if (props.filters === false) {
    delete menu.items.clear_filter
  }

  // 添加自定义菜单
  if (props.contextMenuItemsReverse === false) {
    if (props.contextMenuItems) {
      for (const key in props.contextMenuItems) {
        if (props.contextMenuItems.hasOwnProperty(key)) {
          menu.items[key] = props.contextMenuItems[key]
        }
      }
    }
    return menu
  } else {
    const contextMenu = { items: {} }
    if (props.contextMenuItems) {
      for (const key in props.contextMenuItems) {
        if (props.contextMenuItems.hasOwnProperty(key)) {
          contextMenu.items[key] = props.contextMenuItems[key]
        }
      }
    }
    for (const key in menu.items) {
      if (menu.items.hasOwnProperty(key)) {
        contextMenu.items[key] = menu.items[key]
      }
    }
    return contextMenu
  }
})

const defaultContentMenu = {
  items: {
    refresh: {
      name: 'Refresh',
      callback: () => {
        search()
      }
    },
    clear_filter: {
      name: 'Clear filter',
      disabled: function () {
        return tableCtl.conditions.length === 0
      },
      callback: function () {
        clearAndSearch()
      }
    },
    split0: { name: '---------' },
    fullscreen: {
      name: 'Fullscreen',
      disabled: function () {
        return fullscreenCtl.isFullscreen || fullscreenCtl.disableFullscreen
      },
      callback: function () {
        fullscreenCtl.isFullscreen = true // 设置为全屏
        fullscreenCtl.orgHeight = tableCtl.height // 记录全屏以前高度
        tableCtl.height = document.documentElement.clientHeight - (props.pagging ? 40 : 0) // 设置表格高度为全屏高度, 40为分页高度
        fullscreenCtl.fullscreenStyle = 'position:fixed;top:0;left:0;width:100%;background-color:var(--scp-bg-color);z-index:1080;'
        // 全屏前记录宽度
        fullscreenCtl.orgWidth = tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width
        // 设置宽度为100%
        tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width = '100%'
      }
    },
    exit_fullscreen: {
      name: 'Exit fullscreen',
      disabled: function () {
        return fullscreenCtl.isFullscreen === false || fullscreenCtl.disableFullscreen
      },
      callback: function () {
        fullscreenCtl.isFullscreen = false // 设置为非全屏
        tableCtl.height = fullscreenCtl.orgHeight // 从之前保存的高度中设定表格高度
        fullscreenCtl.fullscreenStyle = '' // 移除全屏样式

        // 将宽度设置为全屏前的宽度
        tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width = fullscreenCtl.orgWidth
      }
    },
    split1: { name: '---------' },
    save_changes: {
      name: function () {
        if (tableCtl.saveStatus === false) {
          return 'Save changes'
        }
        return '<b>Save changes</b>'
      },
      disabled: function () {
        return tableCtl.saveStatus === false
      },
      callback: function () {
        preSaveChanges()
      }
    },
    split3: { name: '---------' },
    download_file: {
      name: 'Download all result',
      disabled: function () {
        return typeof props.downloadUrl === 'undefined' || (!props.pagging) || (!props.downloadUrl)
      },
      callback: function () {
        downloadAllResult()
      }
    },
    download_this_page: {
      name: 'Download this page',
      callback: function () {
        downloadThisPage()
      }
    },
    download_this_page_as_image: {
      name: 'Download this page as image',
      callback: function () {
        downloadPageAsImage()
      }
    },
    copy_this_page_as_image: {
      name: 'Copy this page as image',
      callback: function () {
        copyPageAsImage()
      }
    },
    split4: { name: '---------' },
    row_above: {},
    row_below: {},
    split5: { name: '---------' },
    remove_row: {}
  }
}

// endregion

// region search
const _colOrgHeaders = computed(() => {
  return _columns.value.map(e => e.data)
})

const _columns = computed(() => {
  if (props.columns.length > 0) {
    return props.columns.map(e => {
      const c = {} as Column
      c.data = e.data
      c.type = e.type || 'text'
      if (e.readOnly) {
        c.readOnly = e.readOnly
      }

      if (e.title) {
        c.title = e.title
      }

      const precision = e.precision !== undefined ? e.precision : props.precision !== undefined ? props.precision : 0

      if (props.showTotal === true) {
        c.renderer = function (hotInstance, td, row, column, prop, value) {
          // 如果是最后一行, 加粗
          if (row === tableCtl.data.length - 1) {
            td.style.fontWeight = 'bold'
          }
          td.innerHTML = value

          if (e.render) {
            e.render(hotInstance, td, row, column, prop, value)
          } else if (c.type === 'numeric') {
            $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision, false)
          } else if (c.type === 'strict-numeric') {
            $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision, true)
          } else if (c.type === 'strict-positive-numeric') {
            $renderColumnAsPositiveNumber(hotInstance, td, row, column, prop, value, precision, true)
          }
        }
      } else {
        if (e.render) {
          c.renderer = function (hotInstance, td, row, column, prop, value) {
            if (props.lastRowBold) {
              if (row === tableCtl.data.length - 1) {
                td.style.fontWeight = 'bold'
              }
            }
            e.render!(hotInstance, td, row, column, prop, value)
          }
        } else if (c.type === 'numeric') {
          c.renderer = (hotInstance, td, row, column, prop, value) => {
            if (props.lastRowBold) {
              if (row === tableCtl.data.length - 1) {
                td.style.fontWeight = 'bold'
              }
            }
            $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision, false)
          }
        } else if (c.type === 'strict-numeric') {
          c.renderer = (hotInstance, td, row, column, prop, value) => {
            if (props.lastRowBold) {
              if (row === tableCtl.data.length - 1) {
                td.style.fontWeight = 'bold'
              }
            }
            $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision, true)
          }
        } else if (c.type === 'strict-positive-numeric') {
          c.renderer = (hotInstance, td, row, column, prop, value) => {
            if (props.lastRowBold) {
              if (row === tableCtl.data.length - 1) {
                td.style.fontWeight = 'bold'
              }
            }
            $renderColumnAsPositiveNumber(hotInstance, td, row, column, prop, value, precision, true)
          }
        } else {
          if (props.lastRowBold) {
            c.renderer = (hotInstance, td, row, column, prop, value) => {
              if (row === tableCtl.data.length - 1) {
                td.style.fontWeight = 'bold'
              }
              td.innerHTML = value
            }
          }
        }
      }

      if (c.type === 'strict-numeric' || c.type === 'strict-positive-numeric') {
        c.type = 'numeric'
      }

      if (e.editor) {
        c.editor = e.editor
        if (e.editor === 'select') {
          c.validator = (value, callback) => $selectValidator(value, c.selectOptions, callback)
        }
      }
      if (e.selectOptions) {
        c.selectOptions = e.selectOptions
      }
      if (e.source) {
        if (typeof e.source === 'object') {
          c.source = (query, process) => {
            if (query) {
              if (typeof e.source === 'object') {
                process(e.source.filter(e => e.toLowerCase().indexOf(query.toLowerCase()) !== -1))
              }
            } else {
              process(e.source)
            }
          }
        } else {
          c.source = e.source
        }
      }
      if (e.strict !== undefined) {
        c.strict = e.strict
      }
      if (e.width) {
        c.width = e.width
      }
      if (e.numericFormat) {
        c.numericFormat = e.numericFormat
      }
      if (e.dateFormat) {
        c.dateFormat = e.dateFormat
      }
      if (e.validator) {
        c.validator = e.validator
      }
      if (e.type === 'date' && !e.dateFormat) {
        c.dateFormat = 'YYYY/MM/DD'
      }
      if (e.sorting === false) {
        c.columnSorting = {
          indicator: false,
          headerAction: false,
          compareFunctionFactory: function compareFunctionFactory () {
            return function comparator () {
              return 0
            }
          }
        }
      }
      return c
    })
  } else {
    const result = [] as Array<Column>
    // 找每一列第一个非空字段
    const column = {}
    for (let i = 0; i < tableCtl.data.length; i++) {
      const tdata = tableCtl.data[i]
      for (const c in tdata) {
        if (tdata.hasOwnProperty(c)) {
          if (tdata[c] !== undefined && tdata[c] !== null && column[c] === undefined) {
            column[c] = tdata[c]
          } else if (!column.hasOwnProperty(c)) {
            column[c] = undefined
          }
        }
      }
    }
    if (column && Object.keys(column).length > 0) {
      for (const c in column) {
        if (column.hasOwnProperty(c)) {
          const cc = {
            data: c,
            title: props.autoTitle ? $renderColumnName(c) + '' : c,
            type: 'text'
          } as any
          if (column[c] && column[c].length > 50) {
            cc.width = 220
          }
          if (typeof column[c] === 'number') {
            cc.type = 'numeric'
            cc.renderer = (hotInstance, td, row, column, prop, value) => {
              $renderColumnAsNumber(hotInstance, td, row, column, prop, value, props.precision !== undefined ? props.precision : 1, false)
            }
          } else if ($isDate(column[c]) === true) {
            cc.type = 'date'
            cc.dateFormat = 'YYYY/MM/DD'
          }
          result.push(cc)
        }
      }
    } else {
      result.push({
        data: 'NO_DATA',
        type: 'text'
      })
    }
    return result
  }
})

const search = () => {
  tableCtl.currentPage = 1
  searchRaw()
}

const searchRaw = () => {
  // 防止短时间内的重复查询
  if (tableCtl.searching === true) {
    return
  }
  // 从当前页开始查询, 此方法主要用于分页
  resetEditorCache()
  const params = $deepClone(props.params)
  params._pagging = props.pagging
  params._conditions = tableCtl.conditions
  if (props.pagging) {
    params._pageSize = tableCtl.pageSize
    params._currentPage = tableCtl.currentPage
  } else {
    params._maxRows = tableCtl.maxRows
  }
  if (tableCtl.sortColumn) {
    params._sortColumn = tableCtl.sortColumn
    params._sortOrder = tableCtl.sortOrder
  } else {
    delete params._sortColumn
    delete params._sortOrder
  }
  params._colOrgHeaders = _colOrgHeaders.value
  params._colOrgTypes = _colOrgTypes.value
  params._colOrgFormats = _colOrgFormats.value

  tableCtl.loading = true
  if (props.beforeSearch) {
    props.beforeSearch.call(null)
  }

  $axios({
    method: 'post',
    url: props.url,
    data: params
  }).then((body) => {
    if (body.data && body.data.length > 0) {
      tableCtl.data = addTotalRow(body.data)
    } else {
      tableCtl.data = [{}]
    }

    // handsontable 不再支持动态刷新, 必须手动调用以下内容
    hotTableRef.value.hotInstance.updateData(tableCtl.data)

    tableCtl.total = body.total
    if (fullscreenCtl.isFullscreen === false) {
      tableCtl.height = getTableHeight()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    tableCtl.loading = false
    tableCtl.searching = false

    // 回调方法
    if (props.afterSearch) {
      props.afterSearch.call(null)
    }
  })
}

const addTotalRow = (data) => {
  if (props.showTotal === true && _colOrgHeaders.value) {
    const total = {}
    for (let i = 0; i < _colOrgHeaders.value.length; i++) {
      const col = _colOrgHeaders.value[i]
      if (i === 0) {
        total[col] = 'Total'
        continue
      }
      let s = 0
      let isNumber = true
      for (let j = 0; j < data.length; j++) {
        if (!data[j]) {
          continue
        }
        const v = data[j][col]
        if (!v) {
          continue
        }
        if (isNaN(v)) {
          isNumber = false
          break
        } else {
          s += parseFloat(data[j][col])
        }
      }
      if (isNumber && props.showTotalIgnoreCols.indexOf(col) === -1) {
        total[col] = s
      } else {
        total[col] = ''
      }
    }
    data.push(total)
    return data
  } else {
    return data
  }
}

const resetEditorCache = () => {
  tableCtl.saveStatus = false
  tableCtl.updatedRows = {}
  tableCtl.removedRows = []
  tableCtl.createdRows = []
}

const handlePageSizeChange = (val) => {
  tableCtl.pageSize = val
  searchRaw()
}

const handleCurrentChange = (val) => {
  tableCtl.currentPage = val
  searchRaw()
}

const beforeColumnSort = (currentSortConfig, destinationSortConfigs) => {
  if (destinationSortConfigs && destinationSortConfigs.length > 0) {
    const sortedHeader = hotTableRef.value.hotInstance.getColHeader() // 为了应对用户手动调整了列顺序
    const sortColumnName = sortedHeader[destinationSortConfigs[0].column] // 检查用户排序的列名字
    // 如果表中有两个同名的, 那无法正常获取表头, 按照默认用户不会移动表头顺序来判断
    // 也就是说, 如果表头的名字一样, 用户恰巧对表头进行了拖拽, 那排序会失效, 这种概率微乎其微
    if (sortedHeader.indexOf(sortColumnName) !== sortedHeader.lastIndexOf(sortColumnName)) {
      tableCtl.sortColumn = _colOrgHeaders.value[destinationSortConfigs[0].column]
    } else {
      // 如果没有重复名称的表头, 获取这个表头在整个表中的顺序
      // 然后用表头名称找到真正的表头
      // 不填表头, 系统会自动匹配表头, 所以columns为空的情况可能出现
      if (props.columns.length > 0) {
        tableCtl.sortColumn = props.columns.filter(e => (e.title || $renderColumnName(e.data)) === sortColumnName)[0].data
      } else {
        tableCtl.sortColumn = _columns.value.filter(e => (e.title || $renderColumnName(e.data)) === sortColumnName)[0].data
      }
    }
    tableCtl.sortOrder = destinationSortConfigs[0].sortOrder
  } else {
    tableCtl.sortColumn = ''
    tableCtl.sortOrder = ''
  }
  const columnSortPlugin = hotTableRef.value.hotInstance.getPlugin('columnSorting')
  columnSortPlugin.setSortConfig(destinationSortConfigs)
  search()
  return false
}

const beforeFilter = (sortSettings) => {
  const conditions = JSON.parse(JSON.stringify(sortSettings))
  for (let i = 0; i < conditions.length; i++) {
    conditions[i].column = _colOrgHeaders.value[conditions[i].column]
  }
  tableCtl.conditions = conditions
  search()
  return false
}

const clearAndSearch = () => {
  if (hotTableRef.value && hotTableRef.value) {
    tableCtl.sortColumn = ''
    tableCtl.sortOrder = ''
    tableCtl.conditions = []
    hotTableRef.value.hotInstance.getPlugin('filters').clearConditions()
    hotTableRef.value.hotInstance.getPlugin('columnSorting').clearSort()
    // hotTableRef.value.hotInstance.getPlugin('filters').filter()
  }
}

// endregion

// region fullscreen
const fullscreenCtl = reactive({
  isFullscreen: false,
  orgHeight: 0,
  orgWidth: '',
  fullscreenStyle: '',
  disableFullscreen: false
})
// endregion

// region save changes
const afterSelectionEnd = (row, column) => {
  if (props.afterSelect) {
    const r = hotTableRef.value.hotInstance.getSourceDataAtRow(row) || {}
    const c = _colOrgHeaders.value[column]
    props.afterSelect(r, r[c], c, column, row)
  }
}

const beforeRemoveRow = (index, amount, physicalRows) => {
  tableCtl.saveStatus = true
  const ht = hotTableRef.value.hotInstance
  for (let i = 0; i < physicalRows.length; i++) {
    const pk = getPkByRowData(ht.getSourceDataAtRow(physicalRows[i]))
    if (pk && pk.length > 0) {
      tableCtl.removedRows.push(pk)
    }
  }
}

const getPkByRowData = (row) => {
  if (!row) {
    return ''
  }
  const pks = [] as Array<string>
  for (let j = 0; j < props.primaryKeyId.length; j++) {
    pks.push(row[props.primaryKeyId[j]])
  }
  return pks.join(',')
}
const preSaveChanges = () => {
  // 获取新增的行, 没有pk的行都是新增行
  tableCtl.createdRows = []
  const ht = hotTableRef.value.hotInstance
  if (props.saveValidation === true) {
    ht.validateCells((valid) => {
      if (valid) {
        preSaveChangesAction(ht)
      } else {
        $message.error('Invalid input, please check the highlight cell in the table.')
      }
    })
  } else {
    preSaveChangesAction(ht)
  }
}

const preSaveChangesAction = (ht) => {
  const changedRows = [] as Array<string>
  const tableData = ht.getSourceData()
  for (let i = 0; i < tableData.length; i++) {
    const c = tableData[i]
    const pk = getPkByRowData(c)

    if (c && !pk) {
      tableCtl.createdRows.push(c)
      changedRows.push(c) // 记录增加的列
    }

    // 确认待删除的列, 是否仍存在于表格, 如果存在, 则从待删除列中移除
    if (c && pk) {
      const di = tableCtl.removedRows.indexOf(pk)
      if (di !== -1) {
        tableCtl.removedRows.splice(di, 1)
      }

      // 记录修改的列
      if (tableCtl.updatedRows[pk]) {
        changedRows.push(c)
      }
    }
  }

  // 检查待更新的行, 如果此行已经被删除, 那么就从待更新的列表中移除, 无须再执行更新
  for (let i = 0; i < tableCtl.removedRows.length; i++) {
    const pk = tableCtl.removedRows[i]
    if (tableCtl.updatedRows[pk]) {
      delete tableCtl.updatedRows[pk]
    }
  }

  if (props.beforeSave) {
    if (props.beforeSave({
      creates: tableCtl.createdRows,
      updates: tableCtl.updatedRows,
      deletes: tableCtl.removedRows
    },
    changedRows) === false) {
      return
    }
  }

  // 显示更新信息
  const html = ['<ul>']
  let l = tableCtl.createdRows.length
  if (l > 0) {
    html.push('<li style="color:var(--scp-text-color-primary);font-size: 16px;line-height:24px">' + l + (l > 1 ? ' rows' : ' row') + ' will be created</li>')
  }
  l = tableCtl.removedRows.length
  if (l > 0) {
    html.push('<li style="color:var(--scp-text-color-primary);font-size: 16px;line-height:24px">' + l + (l > 1 ? ' rows' : ' row') + ' will be deleted</li>')
  }
  l = Object.keys(tableCtl.updatedRows).length
  if (l > 0) {
    html.push('<li style="color:var(--scp-text-color-primary);font-size: 16px;line-height:24px">' + l + (l > 1 ? ' rows' : ' row') + ' will be updated</li>')
  }
  html.push('</ul>')
  tableCtl.confirmMessage = html.join('')
  tableCtl.confirmVisible = true
}

const afterChangeDefault = (changes) => {
  if (changes) {
    const ht = hotTableRef.value.hotInstance
    let changed = false
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const pk = getPkByRowData(ht.getSourceDataAtRow(row))
        changed = true
        if (pk) {
          let e = tableCtl.updatedRows[pk]
          if (typeof (e) === 'undefined') {
            e = {}
          }
          e[prop] = newValue
          tableCtl.updatedRows[pk] = e
        }
      }
    })
    if (changed) {
      tableCtl.saveStatus = true
    }
  }
}

const saveData = () => {
  tableCtl.saving = true
  const params = $deepClone(props.params)
  params.updatedRows = tableCtl.updatedRows
  params.removedRows = tableCtl.removedRows
  params.createdRows = tableCtl.createdRows

  $axios({
    method: 'post',
    url: props.saveUrl,
    data: params
  }).then((body) => {
    $message(body)

    if (body.type !== 'error') {
      tableCtl.confirmVisible = false

      setTimeout(function () {
        search()
      }, 300)

      if (props.afterSave) {
        props.afterSave.call(null, body)
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    tableCtl.saving = false
  })
}

const afterChangeTrigger = (changes) => {
  if (props.afterChange) {
    props.afterChange(changes)
  } else {
    afterChangeDefault(changes)
  }
}

const afterCreateRow = () => {
  tableCtl.saveStatus = true
  if (fullscreenCtl.isFullscreen === false) {
    let height = tableCtl.height
    height = Math.min(props.maxHeight, height + 25)
    tableCtl.height = height
  }
}
// endregion

// region download
const downloadAllResult = () => {
  if (props.downloadSpecifyColumn === false) {
    downloadAllResultAction()
  } else {
    $axios({
      method: 'post',
      url: '/datagrid/query_avalible_columns_by_url',
      data: {
        url: props.downloadUrl
      }
    }).then((body) => {
      if (body) {
        tableCtl.downloadColumnOpts = JSON.parse(body)
        autocompleteDownloadColumns(JSON.parse(body))
      } else {
        tableCtl.downloadColumnOpts = []
      }
      tableCtl.downloadVisible = true
    }).catch((error) => {
      console.log(error)
    })
  }
}

const autocompleteDownloadColumns = (columnOpts) => {
  const key = 'download.' + $route.path
  const value = JSON.parse(localStorage.getItem(key) || '{}')
  const downloadType = value.downloadType
  const downloadColumns = value.downloadColumns
  if (downloadType) {
    // 判断存储在localstorage的字段定义, 是否失效, 如果失效, 则直接废弃
    let containAll = true
    for (let i = 0; i < downloadColumns.length; i++) {
      if (columnOpts.indexOf(downloadColumns[i]) === -1) {
        containAll = false
        break
      }
    }

    if (containAll) {
      tableCtl.downloadType = downloadType
      tableCtl.downloadColumns = downloadColumns
    } else {
      localStorage.removeItem(key)
    }
  }
}

const saveDownloadColumnsToLocal = () => {
  const key = 'download.' + $route.path
  const value = {
    downloadType: tableCtl.downloadType,
    downloadColumns: tableCtl.downloadColumns
  }

  localStorage.setItem(key, JSON.stringify(value))
}

const downloadAllResultAction = () => {
  tableCtl.downloadVisible = false
  const params = $deepClone(props.params)
  params._conditions = tableCtl.conditions
  params._maxRows = 300000 // download max rows

  // 如果网页禁用选择列下载, 则不向后台传递_outputColumns参数
  // 否则, 哪怕不选, 也会向后台传递一个空对象
  if (props.downloadSpecifyColumn) {
    if (tableCtl.downloadType === 'Specify Columns') {
      params._outputColumns = tableCtl.downloadColumns
    } else {
      params._outputColumns = []
    }
  }

  saveDownloadColumnsToLocal()
  $downloadFile(props.downloadUrl, params, (r) => {
    if (r) {
      if (props.afterDownload) {
        props.afterDownload.call(null)
      }
    }
  })
}

const downloadThisPage = () => {
  hotTableRef.value.hotInstance.getPlugin('exportFile').downloadFile('csv', {
    filename: 'page_data_' + $randomString(4),
    columnHeaders: true,
    rowHeaders: false
  })
}

const downloadPageAsImage = () => {
// 使用html2canvas 转换html为canvas
  // 先将滚动条滚动至最顶端, 然后调整截图区域的宽高, 使其全部可以展示出来, 截图完毕后, 将之前调整的内容全部恢复
  const h2 = document.documentElement.scrollTop
  const h3 = document.body.scrollTop
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0

  hotTableRef.value.hotInstance.deselectCell()

  tableCtl.loading = true
  const ht = tableCtl.height
  const wt = hotTableRef.value.$el.getElementsByClassName('wtHider')[0].style.width
  tableCtl.height = (tableCtl.data.length * props.rowHeight) + 24 + 20
  tableCtl.width = wt
  nextTick(() => {
    html2canvas(hotTableRef.value.$el).then((canvas) => {
      tableCtl.height = ht
      tableCtl.width = '100%'
      document.documentElement.scrollTop = h2
      document.body.scrollTop = h3

      tableCtl.loading = false
      const imgUri = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream') // 获取生成的图片的url

      const saveLink = document.createElement('a')
      saveLink.href = imgUri
      saveLink.download = 'screenshot_' + new Date().getTime() + '.png'
      saveLink.click()
      saveLink.remove()
    })
  })
}

const copyPageAsImage = () => {
  // 使用html2canvas 转换html为canvas
  // 先将滚动条滚动至最顶端, 然后调整截图区域的宽高, 使其全部可以展示出来, 截图完毕后, 将之前调整的内容全部恢复
  const h2 = document.documentElement.scrollTop
  const h3 = document.body.scrollTop
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0

  hotTableRef.value.hotInstance.deselectCell()

  tableCtl.loading = true
  const ht = tableCtl.height
  const wt = hotTableRef.value.$el.getElementsByClassName('wtHider')[0].style.width
  tableCtl.height = (tableCtl.data.length * props.rowHeight) + 24 + 20
  tableCtl.width = wt
  nextTick(() => {
    html2canvas(hotTableRef.value.$el).then(function (canvas) {
      tableCtl.height = ht
      tableCtl.width = '100%'
      document.documentElement.scrollTop = h2
      document.body.scrollTop = h3

      tableCtl.loading = false
      const img = document.createElement('img')
      img.src = canvas.toDataURL('image/png')
      document.getElementsByClassName('table-box')[0].appendChild(img)

      const range = document.createRange()
      range.selectNode(img)
      const selection = window.getSelection()
      if (selection) {
        selection.addRange(range)
      }
      document.execCommand('Copy')
      img.remove()
    })
  })
}
// endregion

const getParents = (element, className) => {
  let returnParentElement = null

  const getParentNode = (element, className) => {
    if (element) {
      if (element && element.classList.contains(className) && element.tagName.toLowerCase() !== 'body') {
        returnParentElement = element
      } else {
        getParentNode(element.parentElement, className)
      }
    }
  }
  getParentNode(element, className)

  return returnParentElement
}

onMounted(() => {
  if (props.lazy === false) {
    search()
  }
  tableCtl.width = (tableBoxRef.value.offsetWidth - 5) + 'px'

  // 在某些情况下, fullscreen无法突破上层元素的约束, 导致无法正常运行全屏, 所以在这里判断, 如果遇到不能全屏的情况, 直接禁用掉fullscreen功能
  const dom = tableBoxRef.value
  const front = getParents(dom, 'front')
  const back = getParents(dom, 'back')

  if (front || back) {
    fullscreenCtl.disableFullscreen = true
  }
})

onUpdated(() => {
  // 覆盖原生页码显示的总数, element ui默认不显示千分位, 老板肯定会不开心的, 所以添加为有千分位的总数
  const paggingDisplay = paginationRef.value.$el.getElementsByClassName('el-pagination__total')
  if (paggingDisplay && paggingDisplay[0]) {
    paggingDisplay[0].innerHTML = 'Total ' + $thousandBitSeparator(tableCtl.total)
  }
})

watch(() => props.maxHeight, () => {
  tableCtl.height = props.maxHeight
  // hotTableRef.value.hotInstance.updateSettings({
  //   height: getTableHeight()
  // })
}, {
  deep: true
})

const getTableHeight = () => {
  let h = Math.min(((tableCtl.data.length * props.rowHeight) + 27 + 20), props.maxHeight)
  if (props.nestedHeaders) {
    h += 20
  }
  h += props.marginBottom
  return h
}

// region getter and setter
const getHotInstance = () => {
  return hotTableRef.value.hotInstance
}

const setLoading = (loading) => {
  tableCtl.loading = loading
}

const getLoading = () => {
  return tableCtl.loading
}

let timer: any = {}
window.addEventListener('resize', () => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    if (hotTableRef.value && hotTableRef.value.hotInstance) {
      hotTableRef.value.hotInstance.updateSettings({
        width: '100%'
      })
    }
  }, 200)
})

const redrawTable = () => {
  hotTableRef.value.hotInstance.updateSettings({})
}

const getData = () => {
  return tableCtl.data
}

const getTotalCount = () => {
  return tableCtl.total
}

const clearData = () => {
  tableCtl.data = [{}]
  tableCtl.total = 0
}

// endregion

defineExpose({
  getHotInstance,
  setLoading,
  getLoading,
  getData,
  redrawTable,
  getTotalCount,
  clearData,
  clearAndSearch,
  search,
  searchRaw
})
</script>

<script lang="ts">
export default {
  name: 'ScpTable'
}
</script>
<style lang="scss" scoped>
.table-box {
  width: 100%;
  box-sizing: border-box;

  .table-area {
    width: 100%;
  }
}

.autocompleteEditor {
  .wtHider {
    position: fixed !important;
  }
}
</style>
