<template>
  <div v-show="_visible">
    <vue3-draggable-resizable :initW="_w" :initH="_h"
                              :draggable="draggable"
                              :resizable="true"
                              :minW="200"
                              :minH="100"
                              :x="positionCtl.x"
                              :y="positionCtl.y"
                              class="scp-draggable-resizable"
                              ref="winRef"
                              @resize-end="(e)=> resizeend(e)">
      <div class="tip-title drag-handle" @mousedown="draggable=true" @mouseout="draggable=false" @mouseup="draggable=false">
        <div class="pull-left">{{ props.title }}</div>
        <div class="pull-right tip-close" @click="_visible = false">
          <font-awesome-icon icon="times"/>
        </div>
        <div class="pull-right tip-close" v-show="!!props.tips">
          <el-tooltip class="item" effect="light" placement="top-end">
            <template #default>
              <font-awesome-icon icon="circle-question"/>
            </template>
            <template #content>
              <div v-html="props.tips"/>
            </template>
          </el-tooltip>
        </div>
      </div>
      <div class="tip-body" style="padding-right: 0;overflow: auto;">
        <slot :width="sizeCtl.w" :height="sizeCtl.h"/>
      </div>
      <div class="tip-footer">
        <slot name="footer">
          <el-popconfirm :title="props.deleteConfirmText"
                         iconColor="var(--scp-text-color-error)"
                         @confirm="del"
                         :confirmButtonType="props.deleteBtnType"
                         confirmButtonText='确定'
                         cancelButtonText='取消'
                         style="margin-left:15px">
            <template #reference>
              <el-button size="small" :type="props.deleteBtnType" v-show="typeof props.delete !== 'undefined'" :disabled="disabled" :loading="deleteLoading" style="float: left">
                {{ props.deleteText }}
              </el-button>
            </template>
          </el-popconfirm>
          <el-button size="small" @click="close" :disabled="disabled">
            {{ props.closeText }}
          </el-button>
          <el-button size="small" @click="draft" :disabled="disabled" v-show="typeof props.draft !== 'undefined'" :loading="draftLoading">
            {{ props.draftText }}
          </el-button>
          <el-button size="small" type="primary" @click="save" :disabled="disabled" v-show="typeof props.save !== 'undefined' && showSave"
                     :loading="saveLoading">
            {{ props.saveText }}
          </el-button>
          <div class="clearfix"/>
        </slot>
      </div>
    </vue3-draggable-resizable>
  </div>
</template>
<script lang="ts" setup>
import Vue3DraggableResizable from 'vue3-draggable-resizable'
import 'vue3-draggable-resizable/dist/Vue3DraggableResizable.css'
import { computed, inject, reactive, ref, watch } from 'vue'

const draggable = ref(false)
const $toPixel: any = inject('$toPixel')

const props = withDefaults(defineProps<{
  modelValue?: boolean,
  w?: string | number,
  h?: string | number,
  title?: string,
  offset?: string,
  resizestop?: Function,
  beforeClose?: Function,

  draft?: Function,
  delete?: Function,
  save?: Function,
  disabled?: boolean,
  draftText?: string,
  saveText?: string,
  showSave?: boolean,
  closeText?: string,
  deleteText?: string,
  deleteBtnType?:'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info' | '' | 'text',
  closeWarningText?: string,
  saveLoading?: boolean,
  draftLoading?: boolean,
  deleteLoading?: boolean,
  deleteConfirmText?: string,
  tips?:string,
  zIndex?: string | number
}>(), {
  modelValue: false,
  w: '1024px',
  h: '400px',
  title: 'No Title',
  offset: '5rem',
  resizestop: undefined,
  beforeClose: undefined,

  draft: undefined,
  delete: undefined,
  save: undefined,
  disabled: false,
  draftText: 'Draft',
  saveText: 'Save',
  showSave: true,
  closeText: 'Close',
  deleteText: 'Delete',
  deleteBtnType: 'danger',
  closeWarningText: '',
  saveLoading: false,
  draftLoading: false,
  deleteLoading: false,
  deleteConfirmText: '确定删除当前项目?',
  tips: '',
  zIndex: 1201
})

const _w = computed(() => {
  return $toPixel(props.w)
})

const _h = computed(() => {
  return $toPixel(props.h)
})

const positionCtl = reactive({
  x: 0,
  y: 0
})

const sizeCtl = reactive({
  w: _w.value,
  h: _h.value
})

const emmits = defineEmits(['update:modelValue'])

const winRef = ref()

watch(() => props.modelValue, () => {
  resetViewPosition()
})
const resetViewPosition = () => {
  if (winRef.value && winRef.value.$el) {
    const el = winRef.value.$el

    let x = (document.documentElement.clientWidth - _w.value) / 2
    let y = (document.documentElement.clientHeight - _h.value - 100) / 2
    x = Math.max(100, x)
    y = Math.max(50, y)
    positionCtl.x = x
    positionCtl.y = 0.8 * y

    let style = el.getAttribute('style')
    style = style.replace('z-index: auto;', '')
    style += ';z-index: ' + props.zIndex
    el.setAttribute('style', style)
  }
}

const resizeend = (e) => {
  sizeCtl.w = e.w
  sizeCtl.h = e.h
  if (props.resizestop) {
    props.resizestop(e.w, e.h)
  }
}

const _visible = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})

const del = () => {
  if (props.delete) {
    props.delete()
  }
}

const draft = () => {
  if (props.draft) {
    props.draft()
  }
}

const save = () => {
  if (props.save) {
    props.save()
  }
}

const close = () => {
  if (props.beforeClose) {
    if (props.beforeClose() !== false) {
      _visible.value = false
    }
  } else {
    _visible.value = false
  }
}

</script>

<script lang="ts">
export default {
  name: 'ScpDraggableResizable'
}
</script>

<style lang="scss">
.scp-draggable-resizable {
  position: fixed !important;
  background-color: var(--scp-bg-color);
  border: 1px solid var(--scp-border-color-lighter) !important;

  .tip-title {
    cursor: move;
    height: 1.283rem;
    line-height: 1.2rem;
    font-size: 0.5416666rem;
    font-weight: bold;
    padding: 0 10px 0 16px;
    background-color: var(--scp-bg-color-fill);

    .tip-close {
      width: 30px;
      height: 30px;
      color: var(--scp-text-color-secondary);
      cursor: pointer;
      text-align: center
    }

    .tip-close:hover {
      opacity: 0.8
    }
  }

  .tip-body {
    padding: 0 2px 0 0;
    height: calc(100% - 80px);
  }

  .tip-footer {
    padding: 5px 15px;
    text-align: right;
    line-height: normal;
  }
}
</style>
