<template>
  <div id="scpPopup">
    <el-dialog v-model="pageCtl.isVisible" style="padding: 0 10px 10px;" width="75%">
      <template #header>
        <span style="font-size: 0.5rem">
          <font-awesome-icon icon="bell"/>
          DSS Reminders - {{ _pageName }}
        </span>
      </template>
      <div style="font-size: 0.5rem;height: 450px">
        <el-carousel trigger="click" class="carousel" :pause-on-hover="true" :motion-blur="true" indicator-position="outside" @change="carouselChange">
          <el-carousel-item class="carousel-content" v-for="(item, index) in pageCtl.content" :key="index">
            <div style="height: inherit; width: 100%" class="html-content" v-html="_itemContent(item.description)"></div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <template #footer>
        <span class="dialog-footer">
        <el-button @click="saveNeverDisplay" v-show="() => { return localStorage.getItem('username') !== null }">Don't Show Again</el-button>
        <el-button @click="() => { pageCtl.isVisible = false }" type="primary">Close</el-button>
      </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import axios from 'axios'
import { computed, onMounted, reactive, watch } from 'vue'

const props = withDefaults(defineProps<{
  modelValue?: string
}>(), {
  modelValue: ''
})

const pageCtl = reactive({
  isVisible: false,
  content: [] as any,
  currentIndex: 0,
  url: ''
})

watch(() => props.modelValue, () => {
  pageCtl.url = props.modelValue
  showPopup()
})

onMounted(() => {
  pageCtl.url = props.modelValue
  showPopup()
})

const showPopup = () => {
  axios({
    method: 'post',
    url: '/popup/query_popup_config',
    data: {
      url: pageCtl.url,
      username: localStorage.getItem('username')
    }
  }).then((body) => {
    if (body.length > 0) {
      pageCtl.isVisible = true
      pageCtl.content = body
    }
  }).catch((error) => {
    console.log(error)
  })
}

const _itemContent = (description) => {
  return description.replace(/<img/g, '<img style="width: inherit;"')
}

const saveNeverDisplay = () => {
  const excludeContent = pageCtl.content[pageCtl.currentIndex]
  pageCtl.content.splice(pageCtl.currentIndex, 1)
  if (pageCtl.content.length === 0) {
    pageCtl.isVisible = false
  }
  axios({
    method: 'post',
    url: '/popup/save_never_display',
    data: {
      excludeContent,
      username: localStorage.getItem('username')
    }
  }).catch((error) => {
    console.log(error)
  })
}

const _pageName = computed(() => {
  const tUrl = pageCtl.url.split('/')[pageCtl.url.split('/').length - 1]
  return tUrl.split('_').map((part, index) => {
    return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
  }).join(' ')
})

const carouselChange = (item) => {
  pageCtl.currentIndex = item
}

</script>

<script lang="ts">
export default {
  name: 'ScpPopup'
}
</script>

<style lang="scss">
#scpPopup {
  .el-dialog__body {
    padding: 5px !important;
  }

  .el-carousel__container {
    height: 420px;
  }
}
</style>

<style scoped>
>>> .el-dialog__headerbtn {
  height: 41px !important;
  border-bottom: 1px solid var(--el-border-color-light);
  top: 0;
}

>>> .el-dialog__footer {
  padding: 0;
}

>>> .el-dialog__header {
  font-size: medium;
  border-bottom: 1px solid var(--el-border-color-light);
  height: 20px !important;
}

.carousel-content {
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.html-content img {
  max-width: 800px; /* 设置图片最大宽度为容器宽度 */
  height: auto; /* 保持图片宽高比 */
}
</style>
