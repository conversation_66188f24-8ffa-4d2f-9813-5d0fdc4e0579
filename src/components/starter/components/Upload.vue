<template>
  <el-button style="margin-left:10px;" @click="showUploadWin" v-show="props.showBtn">
    <font-awesome-icon :icon="props.icon"/> &nbsp;
    {{props.text}}
  </el-button>
  <!-- scp-draggable-resizable在弹出的时候会在按钮位置生成一个1像素高度的div, 这个div会影响布局, 所以需要使用display inline将这个div隐藏在按钮背后 -->
  <scp-draggable-resizable :w="props.w" :h="props.h" v-model="upload.visible" :title="props.title" style="display:inline !important;">
    <el-upload
        style="padding:15px"
        :accept="props.accept"
        :multiple="props.multiple"

        :before-upload="onUploadStart"
        :on-success="onUploadEnd"
        :on-change="onUploadChange"

        :file-list="upload.uploadFileList"
        :headers="upload.uploadHeader"
        :action="upload.uploadUrl"
        :data="props.uploadParams">
      <template #trigger>
        <el-button type="primary" :loading="upload.uploading" style="padding:6px 12px">Select a file</el-button>
      </template>
      <slot name="template">
        <el-button size="small" :loading="upload.downloading" style="padding:6px 12px;margin-left:6px"
                   @click="downloadTemplate" v-show="!!props.downloadTemplateUrl">
          Download Template
        </el-button>
      </slot>
      <slot name="operation"></slot>
      <template #tip>
        <div class="el-upload__tip" style="font-weight: normal">
          <slot name="tip">
            <p>1. Please check the format of the file before uploading.</p>
          </slot>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :type="upload.closeBtnType" @click="upload.visible = false" :loading="upload.uploading">
        {{ upload.closeBtn }}
      </el-button>
    </template>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>
import { inject, reactive } from 'vue'

const $baseUrl = inject('$baseUrl')
const $notify : any = inject('$notify')
const $downloadFile: any = inject('$downloadFile')
const props = withDefaults(defineProps<{
  w?: string,
  h?: string,
  title?: string,
  downloadTemplateUrl?: string,
  downloadTemplateParams?: any,
  uploadUrl: string,
  uploadParams?: any,
  accept?: string,
  multiple?: boolean,
  icon?: string,
  text?: string,
  onUploadEnd?: Function,
  showBtn?: boolean
}>(), {
  w: '800px',
  h: '600px',
  title: 'File Upload',
  downloadTemplateUrl: undefined,
  downloadTemplateParams: {},
  uploadUrl: undefined,
  uploadParams: {},
  accept: '.xlsx',
  multiple: false,
  icon: 'file-upload',
  text: 'Upload',
  onUploadEnd: undefined,
  showBtn: true
})

const upload = reactive({
  visible: false,
  closeBtn: 'Close',
  closeBtnType: '',

  uploading: false,
  downloading: false,

  uploadUrl: $baseUrl + props.uploadUrl,
  uploadHeader: { token: localStorage.getItem('token') },
  uploadFileList: [] as Array<any>
})

const showUploadWin = () => {
  upload.uploadFileList = []
  upload.closeBtn = 'Close'
  upload.closeBtnType = ''
  upload.visible = true
}

const closeUploadWin = () => {
  upload.uploadFileList = []
  upload.closeBtn = 'Close'
  upload.closeBtnType = ''
  upload.visible = false
}

const downloadTemplate = () => {
  upload.downloading = true
  $downloadFile(props.downloadTemplateUrl, props.downloadTemplateParams, () => {
    upload.downloading = false
  })
}

const onUploadStart = () => {
  upload.uploading = true
  upload.closeBtn = 'Uploading'
  upload.closeBtnType = ''
  return true
}

const onUploadChange = (file) => {
  upload.uploadFileList = [file]
}

const onUploadEnd = (res) => {
  if (res.header.status !== 200) {
    $notify({
      title: 'Upload Failed',
      message: res.header.message,
      dangerouslyUseHTMLString: true,
      type: 'error'
    })
    upload.closeBtn = 'Failed'
    upload.closeBtnType = 'danger'
  } else {
    upload.closeBtn = 'Success'
    upload.closeBtnType = 'primary'
    if (props.onUploadEnd) {
      props.onUploadEnd.call(null, res)
    }
  }
  upload.uploading = false
}

defineExpose({
  showUploadWin, closeUploadWin
})
</script>

<script lang="ts">
export default {
  name: 'ScpUpload'
}
</script>
