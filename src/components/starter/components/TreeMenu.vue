<template>
  <div v-loading="treeMenuCtl.loading" :style="$attrs.style">
    <el-row style="margin-bottom: 2px" v-show="_newBtnShow">
      <el-col :span="24" style="display: flex;justify-content: space-between">
        <el-input
            placeholder="Filter"
            v-model="treeMenuCtl.filterText"
            style="width: calc(100% - 90px)"
            clearable>
        </el-input>
        <el-button @click="newClick">
          <font-awesome-icon icon="fa-solid fa-plus"/>
        </el-button>
        <el-button @click="search" style="margin-left: 0 !important;margin-right: 5px">
          <font-awesome-icon icon="fa-solid fa-arrows-rotate"/>
        </el-button>
      </el-col>
    </el-row>
    <el-row style="margin-bottom: 2px" v-show="_newBtnShow === false">
      <el-col :span="19">
        <el-input
            placeholder="Filter"
            v-model="treeMenuCtl.filterText"
            clearable>
        </el-input>
      </el-col>
      <el-col :span="1">
        &nbsp;
      </el-col>
      <el-col :span="4">
        <el-button @click="search">
          <font-awesome-icon icon="fa-solid fa-arrows-rotate"/>
        </el-button>
      </el-col>
    </el-row>
    <el-tree
        ref="treeRef"
        :data="treeMenuCtl.data"
        :props="{value: 'key'}"
        :highlight-current="true"
        :node-key="nodeKey"
        :default-expanded-keys="props.defaultExpandedKeys"
        @node-click="nodeClick"
        :filter-node-method="filterNode">
      <template v-slot="{ node, data }">
        <div class="custom-tree-node">
          <slot :node="node" :data="data">
            <el-popover trigger="hover" :show-after="props.popShowAfter">
              <div>
                <font-awesome-icon icon="question-circle" style="color: var(--scp-text-color-error);font-size: 90%"/>&nbsp;
                {{ node.label }}
              </div>
              <div style="text-align: right">
                <!-- 可以修改的条件是, 必须有nodeModifyClick; 只可以是子节点, 因为Group无法修改 -->
                <el-button size="small" text v-if="!!props.additionAction1 && data.children.length === 0"
                           :title="typeof props.additionAction1Text === 'string'? props.additionAction1Text: props.additionAction1Text(data)"
                           @click="props.additionAction1(data)" style="margin-top: var(--scp-widget-margin);margin-left: 0">
                  <font-awesome-icon :icon="typeof props.additionAction1Icon === 'string'? props.additionAction1Icon: props.additionAction1Icon(data)"
                                     class="tree-operation-hover-btn"/>
                </el-button>
                <el-button size="small" text v-if="!!props.additionAction2 && data.children.length === 0"
                           :title="typeof props.additionAction2Text === 'string'? props.additionAction2Text: props.additionAction2Text(data)"
                           @click="props.additionAction2(data)" style="margin-top: var(--scp-widget-margin);margin-left: 0">
                  <font-awesome-icon :icon="typeof props.additionAction2Icon === 'string'? props.additionAction2Icon: props.additionAction2Icon(data)"
                                     class="tree-operation-hover-btn"/>
                </el-button>
                <el-button size="small" text v-if="!!props.additionAction3 && data.children.length === 0"
                           :title="typeof props.additionAction3Text === 'string'? props.additionAction3Text: props.additionAction3Text(data)"
                           @click="props.additionAction3(data)" style="margin-top: var(--scp-widget-margin);margin-left: 0">
                  <font-awesome-icon :icon="typeof props.additionAction3Icon === 'string'? props.additionAction3Icon: props.additionAction3Icon(data)"
                                     class="tree-operation-hover-btn"/>
                </el-button>
                <!-- 可以修改的条件是, 必须有nodeModifyClick; 只可以是子节点, 因为Group无法修改 -->
                <el-button size="small" text v-if="!!props.nodeModifyClick && data.children.length === 0"
                           @click="props.nodeModifyClick(data)" style="margin-top: var(--scp-widget-margin);margin-left: 0" title="修改">
                  <font-awesome-icon icon="edit"/>
                </el-button>

                <!-- 可以删除的条件是, 必须有nodeDeleteClick; 默认情况下可以删除组或子节点, 除非canDeleteGroup=false -->
                <span v-if="!!props.nodeDeleteClick && (props.canDeleteGroup || data.children.length === 0)">
                  <el-popconfirm :title="typeof props.nodeDeleteConfirmMsg === 'string'? props.nodeDeleteConfirmMsg: props.nodeDeleteConfirmMsg(data)"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="()=>props.nodeDeleteClick(data)"

                                 confirmButtonType="danger"
                                 cancelButtonType="text"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'>
                    <template #reference>
                      <el-button size="small" type="danger" style="margin-top: var(--scp-widget-margin);margin-left: 0" title="删除">
                        <font-awesome-icon icon="times"/>
                      </el-button>
                    </template>
                  </el-popconfirm>
                </span>
              </div>

              <template #reference>
                <span>{{ node.label }}
                  <span style="color:var(--scp-text-color-secondary);font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{ data.subLabel }}</span>
                  <span v-show="data.children.length > 0">({{ data.children.length }})</span>
                </span>
              </template>
            </el-popover>

            <span class="tree-operation">
              <span v-if="!!props.additionAction1 && data.children.length === 0" class="tree-modify" @click.stop="">
                <font-awesome-icon
                    :icon="typeof props.additionAction1Icon === 'string'? props.additionAction1Icon : props.additionAction1Icon(data)"
                    :title="typeof props.additionAction1Text === 'string'? props.additionAction1Text : props.additionAction1Text(data)"
                    @click="props.additionAction1(data)"/>
              </span>
              <span v-if="!!props.additionAction2 && data.children.length === 0" class="tree-modify" @click.stop="">
                <font-awesome-icon
                    :icon="typeof props.additionAction2Icon === 'string'? props.additionAction2Icon : props.additionAction2Icon(data)"
                    :title="typeof props.additionAction2Text === 'string'? props.additionAction2Text : props.additionAction2Text(data)"
                    @click="props.additionAction2(data)"/>
              </span>
              <span v-if="!!props.additionAction3 && data.children.length === 0" class="tree-modify" @click.stop="">
                <font-awesome-icon
                    :icon="typeof props.additionAction3Icon === 'string'? props.additionAction3Icon : props.additionAction3Icon(data)"
                    :title="typeof props.additionAction3Text === 'string'? props.additionAction3Text : props.additionAction3Text(data)"
                    @click="props.additionAction3(data)"/>
              </span>
              <!-- 可以修改的条件是, 必须有nodeModifyClick; 只可以是子节点, 因为Group无法修改 -->
              <span v-if="!!props.nodeModifyClick && data.children.length === 0" class="tree-modify" @click.stop="">
                <font-awesome-icon icon="edit" title="修改" @click="props.nodeModifyClick(data)"/>
              </span>

              <!-- 可以删除的条件是, 必须有nodeDeleteClick; 默认情况下可以删除组或子节点, 除非canDeleteGroup=false -->
              <span v-if="!!props.nodeDeleteClick && (props.canDeleteGroup || data.children.length === 0)" class="tree-delete"
                    @click.stop="">
                <el-popconfirm :title="typeof props.nodeDeleteConfirmMsg === 'string'? props.nodeDeleteConfirmMsg: props.nodeDeleteConfirmMsg(data)"
                               iconColor="var(--scp-text-color-error)"
                               @confirm="()=>props.nodeDeleteClick(data)"
                               confirmButtonType="danger"
                               cancelButtonType="text"
                               confirmButtonText='确定'
                               cancelButtonText='取消'>
                  <template #reference>
                    <font-awesome-icon icon="times" title="删除"/>
                  </template>
                </el-popconfirm>
              </span>
            </span>
          </slot>
        </div>
      </template>
    </el-tree>
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'

const treeRef = ref()
const $axios: any = inject('$axios')

// @ts-ignore
const props = withDefaults(
  defineProps<{
      data?: Array<any>, // 前台传过来的数据
      url?: string,
      lazy?: boolean,
      popShowAfter?: number,
      nodeKey?: string,
      defaultExpandedKeys?: Array<string>,
      newClick?: Function,
      nodeClick?: Function,
      nodeDblclick?: Function,
      nodeDeleteClick?: Function,
      nodeDeleteConfirmMsg?: string | Function,
      canDeleteGroup?: boolean,
      nodeModifyClick?: Function,
      additionAction1?: Function,
      additionAction1Icon?: string | Function,
      additionAction1Text?: string | Function,
      additionAction2?: Function,
      additionAction2Icon?: string | Function,
      additionAction2Text?: string | Function,
      additionAction3?: Function,
      additionAction3Icon?: string | Function,
      additionAction3Text?: string | Function,
    }>(),
  {
    data: () => [],
    url: '',
    lazy: false,
    popShowAfter: 1500,
    nodeKey: 'label',
    defaultExpandedKeys: () => [],
    newClick: undefined,
    nodeClick: undefined,
    nodeDblclick: undefined,
    nodeDeleteClick: undefined,
    nodeDeleteConfirmMsg: '',
    canDeleteGroup: true,
    nodeModifyClick: undefined,
    additionAction1: undefined,
    additionAction1Icon: undefined,
    additionAction1Text: undefined,
    additionAction2: undefined,
    additionAction2Icon: undefined,
    additionAction2Text: undefined,
    additionAction3: undefined,
    additionAction3Icon: undefined,
    additionAction3Text: undefined
  }
)

const treeMenuCtl = reactive({
  loading: false,
  data: [] as Array<any>,
  filterText: '',
  timer: {}
})

onMounted(() => {
  // 如果前台传过来数据, 那么直接显示前台数据, url和lazy将会被忽略
  if (props.data.length > 0) {
    treeMenuCtl.data = props.data
  } else if (props.lazy === false) {
    if (props.url) {
      search()
    }
  }
})

const _newBtnShow = computed(() => {
  return typeof props.newClick !== 'undefined'
})

watch(() => treeMenuCtl.filterText, (val) => {
  treeRef.value.filter(val)
})

const search = () => {
  treeMenuCtl.loading = true
  $axios({
    method: 'post',
    url: props.url
  }).then((body) => {
    setTimeout(() => {
      treeMenuCtl.data = body
    }, 200)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    treeMenuCtl.loading = false
  })
}

const newClick = () => {
  if (props.newClick) {
    props.newClick.call(null)
  }
}

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.toUpperCase().indexOf(value.toUpperCase()) !== -1
}

let clickCnt = 0

const nodeClick = (e) => {
  if (e.children === null || typeof e.children === 'undefined' || e.children.length === 0) {
    clearTimeout(treeMenuCtl.timer)
    clickCnt++
    treeMenuCtl.timer = setTimeout(() => {
      nextTick(() => {
        if (clickCnt === 1) {
          if (props.nodeClick) {
            props.nodeClick(e)
          }
        } else if (clickCnt > 1) {
          if (props.nodeDblclick) {
            props.nodeDblclick(e)
          }
        }
        clickCnt = 0
      })
    }, 200)
  }
}

const setLoading = (loading) => {
  treeMenuCtl.loading = loading
}

defineExpose({
  search, setLoading
})
</script>

<script lang="ts">
export default {
  name: 'ScpTreeMenu'
}
</script>

<style lang="scss" scoped>
.tree-delete, .tree-modify {
  svg {
    font-size: 0.45rem;
    cursor: pointer;
    border: 0;
    padding-left: 6px !important;
    color: transparent;
  }
}
.tree-modify {
  padding-right: 3px !important;
}

.tree-operation:hover {
  .tree-delete {
    svg {
      color: var(--scp-text-color-error);
    }
    svg:hover {
      color: #ff0001;
    }
  }
  .tree-modify {
    svg {
      color: var(--scp-text-color-highlight);
    }
    svg:hover {
      color: var(--scp-text-color-success);
    }
  }
}

.tree-operation-hover-btn {
  font-size: 0.45rem;
}
</style>
