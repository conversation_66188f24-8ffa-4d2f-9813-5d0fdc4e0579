<template>
  <div>
    <md-editor v-model="_content" :theme="$store.state.theme" preview-theme="github" code-theme="github" style="height: 100%"
               :show-code-row-number="true" :scroll-auto="false" v-if="_isProd"/>
    <h2 v-if="!_isProd">开发环境下, 禁用md-editor, 因为这个组件会阻止热部署调试, 严重影响效率. 如有需要, 可以在MdEditor.vue中取消禁用.</h2>
  </div>
</template>

<script lang="ts" setup>
import { MdEditor } from 'md-editor-v3'
import { computed } from 'vue'
import { useStore } from 'vuex'
import '@/assets/css/markdown.css' /* you must replace this file by @md-editor-v3/lib/style.css when md-editor-v3 upgrade */

const $store = useStore()
// @ts-ignore
const props = withDefaults(defineProps<{
  modelValue?: string
}>(), {
  modelValue: ''
})

const _isProd = computed(() => {
  // return window.$env !== 'development'
  return true // 注释掉上一行, 并且取消注释本行, 即可解除禁用状态
})

const emmits = defineEmits(['update:modelValue'])
const _content = computed({
  get: () => {
    return props.modelValue
  },
  set: (val) => {
    emmits('update:modelValue', val)
  }
})
</script>

<script lang="ts">
export default {
  name: 'ScpMdEditor'
}
</script>
