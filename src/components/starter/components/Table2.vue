<!-- 将传入的数据渲染 -->
<template>
  <div class="table-box" ref="tableBoxRef">
    <div v-loading="tableCtl.loading" class="table-area" :style="fullscreenCtl.fullscreenStyle" @contextmenu.prevent>
      <hot-table ref="hotTableRef"
                 :data="tableCtl.data"
                 :columns="_columns"
                 :colHeaders="_colHeaders"
                 :nestedHeaders="_nestedHeaders"
                 :licenseKey="tableCtl.licenseKey"
                 :height="tableCtl.height + 'px'"
                 :width="tableCtl.width"
                 :dropdownMenu="_dropdownMenu"
                 :filters="props.filters"
                 :formulas="props.formulas"
                 :columnSorting="props.columnSorting"
                 :stretchH="props.stretchH"
                 :autoWrapRow="props.autoWrapRow"
                 :manualRowResize="props.manualRowResize"
                 :manualColumnResize="props.manualColumnResize"
                 :rowHeaders="props.rowHeaders"
                 :manualRowMove="props.manualRowMove"
                 :manualColumnMove="props.manualColumnMove"
                 :collapsibleColumns="_collapsibleColumns"
                 :contextMenu="_contextMenu"
                 :columnSummary="props.columnSummary"
                 :afterSelectionEnd="afterSelectionEnd"
                 :hiddenColumns="props.hiddenColumns"
                 :afterChange="afterChangeTrigger"
                 :afterRemoveRow="afterRemoveRowTrigger"
                 :fixedColumnsLeft="props.fixedColumnsLeft"
                 :renderAllRows="props.renderAllRows"
                 :mergeCells="props.mergeCells"
                 class="table-striped"
      ></hot-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { HotTable } from '@handsontable/vue3'
import 'handsontable/dist/handsontable.full.css'
import { registerAllModules } from 'handsontable/registry'
import html2canvas from 'html2canvas'
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'

registerAllModules()

const $randomString: any = inject('$randomString')
const $renderColumnName: any = inject('$renderColumnName')
const $renderColumnAsNumber: any = inject('$renderColumnAsNumber')

// region 变量和computed
const hotTableRef: any = ref()
const tableBoxRef: any = ref()
const rowHeight = 23.4

interface Column {
  title?: string,
  data: string,
  type?: string,
  render?: Function,
  renderer?: Function,
  readOnly?: boolean,
  precision?: number,
  editor?: string,
  selectOptions?: Array<string>,
  source?: Function | Array<string>,
  strict?: boolean,
  width?: number,
  numericFormat?: string,
  dateFormat?: string,
  validator?: Function,
  sorting?: boolean,
  columnSorting?: any
}

const props = withDefaults(
  defineProps<{
      data: Array<object>,
      showTotal?: boolean,
      showTotalPosition?: number,
      showContextMenu?: boolean,
      contextMenuItems?: object,
      overwriteContextMenu?: boolean,
      contextMenuItemsReverse?: boolean,
      columns?: Array<Column>,
      afterSelect?: Function,
      afterChange?: Function,
      afterRemoveRow?: Function,
      onSearch?: Function,
      maxHeight?: number,
      filters?: boolean,
      formulas?: boolean,
      columnSorting?: boolean,
      stretchH?: string,
      autoWrapRow?: boolean,
      manualRowResize?: boolean,
      nestedHeaders?: Array<any>,
      mergeCells?: Array<any>,
      manualColumnResize?: boolean,
      rowHeaders?: boolean,
      manualRowMove?: boolean,
      manualColumnMove?: boolean,
      renderAllRows?: boolean,
      columnSummary?: Array<any>,
      hiddenColumns?: any,
      fixedColumnsLeft?: number
    }>(),
  {
    data: () => {
      return [{}] as Array<object>
    },
    showTotal: false, // 是否显示汇总行
    showTotalPosition: 0,
    showContextMenu: true, // 是否显示右键
    contextMenuItems: () => { // 自定义右键菜单
      return {}
    },
    overwriteContextMenu: false, // 是否覆盖原有菜单
    contextMenuItemsReverse: true, // 自定义的菜单是否显示在最上方, false则显示在下方
    columns: () => { // 表格列
      return []
    },
    afterSelect: undefined, // 事件钩子, 含义参考方法名
    afterChange: undefined, // 事件钩子, 含义参考方法名
    afterRemoveRow: undefined, // 删除行并不会触发afterChange事件, 所以添加了此事件用来监控表格数据变动
    onSearch: undefined,
    maxHeight: 800, // 表格出现滚动条的高度, 单位px
    filters: true, // 是否在列头显示下拉菜单中的筛选框
    formulas: false, // 是否支持公式, 支持公式在大数据量下, 加载速度会变慢
    columnSorting: true, // 是否支持单击表头排序
    stretchH: 'all', // 列自动填充容器宽度
    autoWrapRow: true, // 自动换行, 在stretchH: 'all'下, 会出现滚动条, 不再进行换行
    manualRowResize: true, // 是否可以手动调整行尺寸
    nestedHeaders: undefined, // 多表头
    mergeCells: undefined, // 合并单元格
    manualColumnResize: true, // 是否可以手动调整列尺寸
    rowHeaders: true, // 是否显示行表头, 就是1, 2, 3, 4...
    manualRowMove: true, // 是否可以拖动行
    manualColumnMove: true, // 是否可以拖动列
    renderAllRows: false, // 按需渲染表格, false可以增加效率, 但是true可以增加拖拽效率
    columnSummary: undefined, // 对列汇总, 没用过
    hiddenColumns: undefined, // 隐藏列
    fixedColumnsLeft: undefined // 锁定左侧表头数量
  }
)

const tableCtl = reactive({
  height: 150, // 表格高度
  width: '100%', // 表格宽度
  loading: false, // 加载动画
  licenseKey: 'non-commercial-and-evaluation',
  sortColumn: '', // 记录排序列
  sortOrder: '',
  confirmVisible: false,
  data: [{}] as Array<object>,
  confirmMessage: '',
  saving: false
})

const _collapsibleColumns = computed(() => {
  if (props.nestedHeaders) {
    return true
  } else {
    return undefined
  }
})

const _colOrgHeaders = computed(() => {
  return _columns.value.map(e => e.data)
})

const _dropdownMenu = computed(() => {
  // 当禁用sorting的时候, dropdownMenu的筛选会有bug, 所以强制约束, 当禁用sorting时也会禁用dropdownMenu
  if (props.columnSorting) {
    return ['alignment', '---------', 'filter_by_condition', 'filter_operators', 'filter_by_condition2', 'filter_by_value', 'filter_action_bar']
  } else {
    return false
  }
})
// endregion

// region 右键菜单
const defaultContentMenu = {
  items: {
    refresh: {
      name: 'Refresh',
      callback: function () {
        search()
      }
    },
    clear_filter: {
      name: 'Clear filter',
      callback: function () {
        clearFilter()
      }
    },
    download_this_page: {
      name: 'Download this page',
      callback: function () {
        downloadThisPage()
      }
    },
    download_this_page_as_image: {
      name: 'Download this page as image',
      callback: function () {
        downloadPageAsImage()
      }
    },
    copy_this_page_as_image: {
      name: 'Copy this page as image',
      callback: function () {
        copyPageAsImage()
      }
    },
    split: { name: '---------' },
    fullscreen: {
      name: 'Fullscreen',
      disabled: function () {
        return fullscreenCtl.isFullscreen || fullscreenCtl.disableFullscreen
      },
      callback: function () {
        fullscreenCtl.isFullscreen = true // 设置为全屏
        fullscreenCtl.orgHeight = tableCtl.height // 记录全屏以前高度
        tableCtl.height = document.documentElement.clientHeight
        fullscreenCtl.fullscreenStyle = 'position:fixed;top:0;left:0;width:100%;background-color:#fff;z-index:1080;'

        // 全屏前记录宽度
        fullscreenCtl.orgWidth = tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width
        // 设置宽度为100%
        tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width = '100%'
      }
    },
    exit_fullscreen: {
      name: 'Exit fullscreen',
      disabled: function () {
        return fullscreenCtl.isFullscreen === false || fullscreenCtl.disableFullscreen
      },
      callback: function () {
        fullscreenCtl.isFullscreen = false // 设置为非全屏
        tableCtl.height = fullscreenCtl.orgHeight // 从之前保存的高度中设定表格高度
        fullscreenCtl.fullscreenStyle = '' // 移除全屏样式

        // 将宽度设置为全屏前的宽度
        tableBoxRef.value.children[0].getElementsByClassName('handsontable')[0].style.width = fullscreenCtl.orgWidth
      }
    }
  }
}

const _total = computed(() => {
  return tableCtl.data.length
})

const _columns = computed(() => {
  return props.columns.map(e => {
    const c = {} as Column
    c.data = e.data
    c.type = e.type || 'text'

    if (e.title) {
      c.title = e.title
    }

    const precision = e.precision || 0

    if (props.showTotal === true) {
      c.renderer = (hotInstance, td, row, column, prop, value) => {
        td.innerHTML = value
        if (row === _total.value - 1) {
          td.style.fontWeight = 'bold'
        }
        if (e.render) {
          e.render(hotInstance, td, row, column, prop, value)
        } else if (c.type === 'numeric') {
          $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision)
        } else if (c.type === 'strict-numeric') {
          $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision, true)
        }
      }
    } else {
      if (e.render) {
        c.renderer = e.render
      } else if (c.type === 'numeric') {
        c.renderer = (hotInstance, td, row, column, prop, value) => $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision)
      } else if (c.type === 'strict-numeric') {
        c.renderer = (hotInstance, td, row, column, prop, value) => $renderColumnAsNumber(hotInstance, td, row, column, prop, value, precision, true)
      }
    }
    if (e.width) {
      c.width = e.width
    }
    if (e.numericFormat) {
      c.numericFormat = e.numericFormat
    }
    if (e.dateFormat) {
      c.dateFormat = e.dateFormat
    }
    return c
  })
})

const _colHeaders = computed(() => {
  return _columns.value.map(e => e.title || $renderColumnName(e.data))
})

const _contextMenu = computed(() => {
  // 如果配置了不显示右键菜单, 则直接返回false
  if (props.showContextMenu === false) {
    return false
  }

  let menu = defaultContentMenu as any
  if (props.overwriteContextMenu === true) {
    menu = { items: {} }
  }

  // 如果禁用了filter, 也不需要显示clear filter
  if (props.filters === false) {
    delete menu.items.clear_filter
  }

  // 添加自定义菜单
  if (props.contextMenuItemsReverse === false) {
    if (props.contextMenuItems) {
      for (const key in props.contextMenuItems) {
        if (props.contextMenuItems.hasOwnProperty(key)) {
          menu.items[key] = props.contextMenuItems[key]
        }
      }
    }
    return menu
  } else {
    const contextMenu = { items: {} }
    if (props.contextMenuItems) {
      for (const key in props.contextMenuItems) {
        if (props.contextMenuItems.hasOwnProperty(key)) {
          contextMenu.items[key] = props.contextMenuItems[key]
        }
      }
    }
    for (const key in menu.items) {
      if (menu.items.hasOwnProperty(key)) {
        contextMenu.items[key] = menu.items[key]
      }
    }
    return contextMenu
  }
})

const _nestedHeaders = computed(() => {
  if (props.nestedHeaders) {
    const nestedHeaders = [] as Array<any>
    for (let i = 0; i < props.nestedHeaders.length; i++) {
      nestedHeaders.push(props.nestedHeaders[i])
    }
    nestedHeaders.push(props.columns.map(e => e.title || $renderColumnName(e.data)))
    return nestedHeaders
  } else {
    return undefined
  }
})
// endregion

// region search
const loadData = (source) => {
  if (props.showTotal === true && _colOrgHeaders.value) {
    const total = {}
    const data = source.slice()
    for (let i = props.showTotalPosition; i < _colOrgHeaders.value.length; i++) {
      const col = _colOrgHeaders.value[i]
      if (props.showTotalPosition === i) {
        total[col] = 'Total'
        continue
      }
      let s = 0
      for (let j = 0; j < data.length; j++) {
        s += (isNaN(data[j][col]) || !data[j][col]) ? 0 : parseFloat(data[j][col])
      }
      total[col] = s
    }
    data.push(total)
    tableCtl.data = data
  } else {
    tableCtl.data = source
  }
  hotTableRef.value.hotInstance.loadData(tableCtl.data)
  adjustHeight()
}

watch(() => props.data, (newVal) => {
  loadData(newVal)
}, {
  deep: true // 深度监听的参数
})

let timer: any = {}
window.addEventListener('resize', () => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    if (hotTableRef.value && hotTableRef.value.hotInstance) {
      hotTableRef.value.hotInstance.updateSettings({
        width: '100%'
      })
    }
  }, 200)
})

const getParents = (element, className) => {
  let returnParentElement = null

  const getParentNode = (element, className) => {
    if (element) {
      if (element && element.classList.contains(className) && element.tagName.toLowerCase() !== 'body') {
        returnParentElement = element
      } else {
        getParentNode(element.parentElement, className)
      }
    }
  }
  getParentNode(element, className)

  return returnParentElement
}

onMounted(() => {
  loadData(props.data)

  // 在某些情况下, fullscreen无法突破上层元素的约束, 导致无法正常运行全屏, 所以在这里判断, 如果遇到不能全屏的情况, 直接禁用掉fullscreen功能
  const dom = tableBoxRef.value
  const front = getParents(dom, 'front')
  const back = getParents(dom, 'back')

  if (front || back) {
    fullscreenCtl.disableFullscreen = true
  }
})
const search = () => {
  if (props.onSearch) {
    props.onSearch()
  } else {
    adjustHeight()
  }
}

const adjustHeight = () => {
  nextTick(() => {
    let h0 = (tableCtl.data.length * 26) + 30
    if (props.nestedHeaders) {
      h0 += 30
    }
    tableCtl.height = Math.min(h0, props.maxHeight)
  })
}

const afterSelectionEnd = (row, column) => {
  if (props.afterSelect) {
    const r0 = {}
    const r = hotTableRef.value.hotInstance.getDataAtRow(row)
    let c = ''

    let j = 0
    for (let i = 0; i < _columns.value.length; i++) {
      if (props.hiddenColumns && props.hiddenColumns.columns && props.hiddenColumns.columns.indexOf(i) !== -1) {
        continue
      }
      const key = _columns.value[i].data
      if (column === j) {
        c = key
      }
      r0[key] = r[j++]
    }

    props.afterSelect(r0, r0[c], c, column, row)
  }
}

const clearFilter = () => {
  if (hotTableRef.value) {
    hotTableRef.value.hotInstance.getPlugin('filters').clearConditions()
    hotTableRef.value.hotInstance.getPlugin('filters').filter()
  }
}
// endregion

// region fullscreen
const fullscreenCtl = reactive({
  isFullscreen: false,
  orgHeight: 0,
  orgWidth: '',
  fullscreenStyle: '',
  disableFullscreen: false
})
// endregion

// region download
const downloadThisPage = () => {
  hotTableRef.value.hotInstance.getPlugin('exportFile').downloadFile('csv', {
    filename: 'page_data_' + $randomString(4),
    columnHeaders: true,
    rowHeaders: false
  })
}

const downloadPageAsImage = () => {
// 使用html2canvas 转换html为canvas
  // 先将滚动条滚动至最顶端, 然后调整截图区域的宽高, 使其全部可以展示出来, 截图完毕后, 将之前调整的内容全部恢复
  const h2 = document.documentElement.scrollTop
  const h3 = document.body.scrollTop
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0

  hotTableRef.value.hotInstance.deselectCell()

  tableCtl.loading = true
  const ht = tableCtl.height
  const wt = hotTableRef.value.$el.getElementsByClassName('wtHider')[0].style.width
  tableCtl.height = (tableCtl.data.length * rowHeight) + 24 + 20
  tableCtl.width = wt
  nextTick(() => {
    html2canvas(hotTableRef.value.$el).then((canvas) => {
      tableCtl.height = ht
      tableCtl.width = '100%'
      document.documentElement.scrollTop = h2
      document.body.scrollTop = h3

      tableCtl.loading = false
      const imgUri = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream') // 获取生成的图片的url

      const saveLink = document.createElement('a')
      saveLink.href = imgUri
      saveLink.download = 'screenshot_' + new Date().getTime() + '.png'
      saveLink.click()
      saveLink.remove()
    })
  })
}

const copyPageAsImage = () => {
  // 使用html2canvas 转换html为canvas
  // 先将滚动条滚动至最顶端, 然后调整截图区域的宽高, 使其全部可以展示出来, 截图完毕后, 将之前调整的内容全部恢复
  const h2 = document.documentElement.scrollTop
  const h3 = document.body.scrollTop
  document.documentElement.scrollTop = 0
  document.body.scrollTop = 0

  hotTableRef.value.hotInstance.deselectCell()

  tableCtl.loading = true
  const ht = tableCtl.height
  const wt = hotTableRef.value.$el.getElementsByClassName('wtHider')[0].style.width
  tableCtl.height = (tableCtl.data.length * rowHeight) + 24 + 20
  tableCtl.width = wt
  nextTick(() => {
    html2canvas(hotTableRef.value.$el).then(function (canvas) {
      tableCtl.height = ht
      tableCtl.width = '100%'
      document.documentElement.scrollTop = h2
      document.body.scrollTop = h3

      tableCtl.loading = false
      const img = document.createElement('img')
      img.src = canvas.toDataURL('image/png')
      document.getElementsByClassName('table-box')[0].appendChild(img)

      const range = document.createRange()
      range.selectNode(img)
      const selection = window.getSelection()
      if (selection) {
        selection.addRange(range)
      }
      document.execCommand('Copy')
      img.remove()
    })
  })
}
// endregion

// region event
const afterChangeTrigger = (changes) => {
  if (props.afterChange) {
    props.afterChange(changes, hotTableRef.value.hotInstance)
  }
}

const afterRemoveRowTrigger = (index, amount, physicalRows) => {
  if (props.afterRemoveRow) {
    props.afterRemoveRow(index, amount, physicalRows, hotTableRef.value.hotInstance)
  }
}
// endregion

// region getter and setter
const setLoading = (l) => {
  tableCtl.loading = l
}

const getLoading = () => {
  return tableCtl.loading
}
const getLength = () => {
  return _total.value
}
const getHotInstance = () => {
  return hotTableRef.value.hotInstance
}

defineExpose({
  getHotInstance, setLoading, getLoading, getLength
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'ScpTable2'
}
</script>

<style lang="scss" scoped>
.table-box {
  width: 100%;
  box-sizing: border-box;
  min-height: 50px;

  .table-area {
    overflow: hidden;
    width: 100%;
  }
}
</style>
