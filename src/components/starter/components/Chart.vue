<template>
  <echarts ref="chartRef" :option="_options" :update-options="{notMerge: true}" :theme="_theme" :style="{height: _height + 'px', width: '100%'}"/>
</template>

<script lang="ts" setup>
import { computed, ref, inject, watch } from 'vue'
import { useStore } from 'vuex'

const $store: any = useStore()
const $toPixel: any = inject('$toPixel')
const chartRef = ref()
const props = withDefaults(defineProps<{
  option: object,
  height?: number | string
}>(), {
  option: () => {
    return {}
  },
  height: 320
})

const _theme = computed(() => {
  if ($store.state.theme === 'dark') {
    return 'customed-dark'
  } else {
    return 'customed'
  }
})

const _options = computed(() => {
  return props.option
})

watch(() => props.option, (newVal) => {
  if (newVal) {
    resize()
  }
})

let timer: any = {}
window.addEventListener('resize', () => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    resize()
  }, 200)
})

const _height = computed(() => {
  return $toPixel(props.height)
})

const chart = () => {
  return chartRef.value.chart
}

const resize = () => {
  if (chartRef.value && chartRef.value) {
    chartRef.value.resize()
  }
}

defineExpose({
  chart, resize
})
</script>

<script lang="ts">
export default {
  name: 'ScpChart'
}
</script>

<style lang="scss">
.echarts {
  .vue-echarts-inner {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
