<template>
  <div id="workstation" class="left-sidebar" style="width:100%">
    <div class="widget">
      <div class="widget-body">
        <div class="subscript-container" v-loading="loadingCtl.mainPage" :style="{height: ($store.state.pageHeight - 50) + 'px'}">
          <scp-subscript id="MYSM" ref="mysmRef"/>
          <div class="front" v-contextmenu:contextmenu1>
            <div v-for="(item,index) in pageCtl.activeWorkstations" :key="index">
              <div class="group-header">
                <font-awesome-icon :icon="item.open? 'fa-solid fa-caret-down':'fa-solid fa-caret-right'" @click="item.open = !item.open"/>
                <div class="text" @click="item.open = !item.open" style="cursor: pointer;margin-left: 8px">{{ item.name }}</div>
              </div>
              <div class="group-content" v-show="item.open">
                <div class="tag" :style="{height: _widgetHeight + 'px', width: _widgetWidth + 'px'}" v-for="(item2,index2) in item.children"
                     :key="'sub-' + index2">
                  <div class="tag-header" :style="{fontSize: Math.min(12 * pageCtl.conditions.sizeRatio / 100, 12) + 'px'}">
                    <div style="float: left">{{ item2.title }}</div>
                    <span class="menu" title="Details" @click="showDetails(item2.id, item2.title)" v-if="item2['detailsLength'] > 0">
                      <font-awesome-icon icon="share-alt"/>
                    </span>
                    <span class="menu" title="Quick Access" v-if="item2.linkTo" style="margin-left:4px;">
                      <a :href="item2.linkTo" target="_blank">
                        <font-awesome-icon icon="share"/>
                      </a>
                    </span>
                    <span class="menu" title="Options" style="margin-left:4px;">
                      <el-popover
                          placement="right"
                          width="270"
                          @show="showPopover(item2.id)"
                          trigger="click">
                        <div class="workstation-custom-form" v-loading="loadingCtl.customSettings">
                          <el-row>
                            <el-col :span="8">Group</el-col>
                            <el-col :span="16">
                              <el-autocomplete
                                  class="inline-input"
                                  style="width: 100%"
                                  v-model="pageCtl.customSettings.groupName"
                                  :maxlength="30"
                                  :fetch-suggestions="groupSuggestion"
                                  placeholder="Group"
                                  show-word-limit
                              ></el-autocomplete>
                            </el-col>
                          </el-row>
                          <el-row style="margin: 5px 0;">
                            <el-col :span="8">Order No</el-col>
                            <el-col :span="16">
                              <el-input-number v-model="pageCtl.customSettings.orderNo" controls-position="right" :min="0" :max="1000" style="width: 100%"/>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="8">Font Size(%)</el-col>
                            <el-col :span="16">
                              <el-slider v-model="pageCtl.customSettings.fontSize" :min="50" :max="150" :step="1"></el-slider>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="24">
                              <div style="text-align: right;margin-top: 5px;">
                                <el-button type="primary" @click="saveCustomSettings">Save</el-button>
                              </div>
                            </el-col>
                          </el-row>
                        </div>
                        <template #reference>
                          <el-button>
                            <font-awesome-icon icon="sliders-h"/>
                          </el-button>
                        </template>
                      </el-popover>
                    </span>
                  </div>
                  <div class="tag-body" v-loading="pageCtl.workstationsLoading[item2.id] && mysmRef.getViewType() === 'front'"
                       :style="{position:'static !important',display: 'inherit', width: _widgetWidth + 'px'}">
                    <!-- 显示一个文本 -->
                    <div v-if="getValueType(pageCtl.workstationsValues[item2.id]) === 'string'">
                      <div v-html="pageCtl.workstationsValues[item2.id].value" class="text"
                           :style="{fontSize: customFontSize(item2.id), height: _widgetHeight + 'px', display: 'table-cell',
                                    verticalAlign:'middle', width: _widgetWidth + 'px', color: pageCtl.workstationsValues[item2.id].color ? pageCtl.workstationsValues[item2.id].color : '#3d5170'}">
                      </div>
                    </div>
                    <!-- 显示一个数字 -->
                    <div v-else-if="getValueType(pageCtl.workstationsValues[item2.id]) === 'number'">
                      <div class="text"
                           :style="{fontSize:customFontSize(item2.id), height: _widgetHeight + 'px', lineHeight: _widgetHeight + 'px', color: pageCtl.workstationsValues[item2.id].color ? pageCtl.workstationsValues[item2.id].color : '#3d5170'}">
                        {{ $shortenNumber(pageCtl.workstationsValues[item2.id].value, 1) }}
                      </div>
                    </div>
                    <!-- 显示多个数字 -->
                    <div v-else-if="getValueType(pageCtl.workstationsValues[item2.id]) === 'array'"> <!--arrays-->
                      <div class="text" :style="{height: _widgetHeight + 'px'}">
                        <el-carousel trigger="click" :interval="8000" :style="{height: _widgetHeight + 'px'}" :autoplay="mysmRef.getViewType() === 'front'">
                          <el-carousel-item v-for="(item3, index3) in pageCtl.workstationsValues[item2.id].value" :key="'sub-' + index3">
                            <div class="text" :style="{display: 'table-cell', width: _widgetWidth + 'px', height: _widgetHeight + 'px', verticalAlign: 'middle',
                                          fontSize: customFontSize(item2.id, 0.9),
                                          color: pageCtl.workstationsValues[item2.id].color[index3] ? pageCtl.workstationsValues[item2.id].color[index3] : '#3d5170'}"
                                 v-html="$shortenNumber(item3)"></div>
                          </el-carousel-item>
                        </el-carousel>
                      </div>
                    </div>
                    <!-- 显示图表 -->
                    <div v-else-if="getValueType(pageCtl.workstationsValues[item2.id]) === 'chart'">
                      <div class="text" :style="{height: _widgetHeight + 'px', lineHeight: _widgetHeight + 'px'}">
                        <chart :style="{width:_widgetWidth + 'px',height:_widgetHeight + 'px', display: 'block'}"
                               :option="chartOpts(pageCtl.workstationsValues[item2.id].value)" :autoresize="true"/>
                      </div>
                    </div>
                    <!-- 默认填充,不填充页面会错行 -->
                    <div v-else>
                      <div class="text"
                           :style="{fontSize:_widgetHeight * 0.22 + 'px', height: _widgetHeight + 'px', lineHeight: _widgetHeight + 'px', color: 'transparent'}">
                        --
                      </div>
                    </div>
                    <font-awesome-icon class="icon"
                                       :icon="(pageCtl.workstationsValues[item2.id] && pageCtl.workstationsValues[item2.id].icon) ? pageCtl.workstationsValues[item2.id].icon : 'minus'"
                                       v-show="pageCtl.workstationsValues[item2.id] && pageCtl.workstationsValues[item2.id].icon"
                                       :style="{top: 'calc(50% - ' + (_widgetHeight * 0.15 * 0.5) + 'px)', right: 0.09 * _widgetWidth + 'px', fontSize: _widgetHeight * 0.15, color: pageCtl.workstationsValues[item2.id] && pageCtl.workstationsValues[item2.id].iconColor ? pageCtl.workstationsValues[item2.id].iconColor : '#3d5170'}">
                    </font-awesome-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="back"><!-- 高度已调整 -->
            <div style="border-bottom: 1px dashed var(--scp-border-color)">
              <el-row>
                <el-col :span="6" style="padding: 5px 15px 5px 5px">
                  <el-row style="margin-top: 3px">
                    <el-col :span="8" style="height: 34px; line-height: 34px;font-size: 0.5rem;font-weight: bold">WIDGET SIZE</el-col>
                    <el-col :span="16" style="padding-left: 10px">
                      <el-slider v-model="pageCtl.conditions.sizeRatio" :min="50" :max="100" :step="1"/>
                    </el-col>
                  </el-row>
                  <scp-cascader
                      style="width: 100%;margin-top: 10px"
                      v-model="pageCtl.conditions.filterList"
                      :loading="loadingCtl.filter"
                      size="default"
                      :options="pageCtl.workstationFilterOpts"/>
                </el-col>
                <el-col :span="18" style="padding: 5px">
                  <el-row>
                    <el-col :span="12">
                      <el-input
                          style="height: 75px"
                          type="textarea"
                          :rows="4"
                          placeholder="Material"
                          v-model="pageCtl.conditions.material">
                      </el-input>
                    </el-col>
                    <el-col :span="12" style="padding-left: 10px">
                      <el-input
                          style="height: 75px"
                          type="textarea"
                          :rows="4"
                          placeholder="Vendor Code"
                          v-model="pageCtl.conditions.vendorCode">
                      </el-input>
                    </el-col>
                    <div class="clearfix"/>
                  </el-row>
                </el-col>
              </el-row>
              <div style="text-align: right;margin: 5px 5px 5px 0">
                <el-button @click="mysmRef.toggleView()">Back</el-button>
                <el-button type="primary" @click="saveParameters(true, true)">Save</el-button>
              </div>
              <div class="clearfix"/>
            </div>
            <div style="padding-top: 10px;">
              <el-radio-group v-model="pageCtl.conditions.groupType" size="small" style="float: right" @change="saveParameters(false, true)">
                <el-radio-button value="By Category">By Category</el-radio-button>
                <el-radio-button value="By Author">By Author</el-radio-button>
              </el-radio-group>
              <div v-for="(item,index) in pageCtl.availableWorkstations" :key="index">
                <div class="group-header">
                  <font-awesome-icon icon="fa-solid fa-caret-down"/>
                  <div class="text" style="margin-left: 8px">{{ item.name }}</div>
                </div>
                <div class="group-content" v-show="item.open">
                  <div :class="pageCtl.checkedWidgets[item2.id] ? 'tag active' : 'tag'" v-for="(item2,index2) in item.children" :key="'sub-' + index2">
                    <div class="tag-header" style="font-size: 12px">
                      <div style="float: left;font-size: 6px">{{ item2['author'] ? 'By ' + item2['author'] : '' }}</div>
                      <span class="menu">
                        <el-button title="Options" @click="showWidgetDetails(item2.id)" style="padding-top: 0 !important;">
                          <font-awesome-icon icon="sliders-h"/>
                        </el-button>
                      &nbsp; <el-checkbox v-model="pageCtl.checkedWidgets[item2.id]" title="Active Report"/>
                      </span>
                    </div>
                    <div class="tag-body" style="height:3.7rem;text-align: center; display: table-cell; vertical-align: middle; width:6rem;">
                      {{ item2.title }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="clearfix"/>
          </div>
        </div>
      </div>
    </div>

    <!-- new widget-->
    <scp-draggable-resizable w="70vw" h="600px" v-model="visibleCtl.newWidget" title="New Widget"
                             :save="saveWidget" :save-loading="loadingCtl.saveWidget">
      <template v-slot="{height}">
        <el-row style="padding: 10px 0 0 10px;">
          <el-col :span="6">
            <el-input placeholder="Title" v-model="pageCtl.newForm.title" style="width: var(--scp-input-width) !important;" :maxlength="30"
                      show-word-limit></el-input>
          </el-col>
          <el-col :span="5">
            <el-autocomplete
                class="inline-input"
                style="width: var(--scp-input-width) !important;"
                v-model="pageCtl.newForm.groupName"
                :maxlength="30"
                :fetch-suggestions="groupSuggestion"
                placeholder="Group"
                show-word-limit
            ></el-autocomplete>
          </el-col>
          <el-col :span="8">
            <el-radio-group v-model="pageCtl.newForm.reportType">
              <el-radio-button value="Text/Number">Text/Number</el-radio-button>
              <el-radio-button value="Line">Line</el-radio-button>
              <el-radio-button value="Bar">Bar</el-radio-button>
              <el-radio-button value="Pie">Pie</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>

        <el-row style="padding: 10px 0 5px 10px;">
          <el-col :span="11">
            <el-input placeholder="Link to" v-model="pageCtl.newForm.linkTo" style="width: var(--scp-input-width) !important;" clearable></el-input>
          </el-col>
          <el-col :span="8">
            <el-select placeholder="Share to" v-model="pageCtl.newForm.shareTo" style="width: 100% !important;" multiple collapse-tags filterable
                       clearable>
              <el-option
                  v-for="item in pageCtl.shareTo"
                  :key="item['VALUE']"
                  :label="item['NAME']"
                  :value="item['VALUE']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" style="padding-left: 10px">
            <el-select placeholder="Cache Type" v-model="pageCtl.newForm.cachable" style="width: 100% !important;">
              <el-option
                  v-for="item in [{label: 'Enable Cache', value: 'Y'}, {label: 'Disable Cache', value: 'N'}]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-row>

        <el-row style="border-bottom:1px dashed var(--scp-border-color);" v-loading="loadingCtl.execute">
          <el-col :span="23">
            <el-tabs v-model="pageCtl.newForm.scriptType" type="card">
              <el-tab-pane label="Summary" name="Summary">
                <scp-ace-editor v-model="pageCtl.newForm.scripts" :style="{height: (height - 200) + 'px'}" lang="sql"/>
              </el-tab-pane>
              <el-tab-pane label="Details" name="Details">
                <scp-ace-editor v-model="pageCtl.newForm.detailsScripts" :style="{height: (height - 200) + 'px'}" lang="sql"/>
              </el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col :span="1" style="margin-top: 20px;padding-left: 2px" :style="{height: (height - 200) + 'px', borderLeft:'1px dashed var(--scp-border-color)'}">
            <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
              <el-tag type="info" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('newForm')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Add Global Conditions" placement="right" :show-after="1000">
              <el-tag type="info" style="cursor: pointer;font-size: 11px;padding: 0 9px" @click="addGlobalConditions('newForm')">
                <font-awesome-icon icon="globe-asia"/>
              </el-tag>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="View Supported Icons" placement="right" :show-after="1000">
              <a target="_blank" href="https://fontawesome.com/search?s=solid">
                <el-tag type="info" style="cursor: pointer;font-size: 11px;padding: 0 9px">
                  <font-awesome-icon icon="flag"/>
                </el-tag>
              </a>
            </el-tooltip>
            <div style="height: 5px;"></div>
            <span title="Pick a color">
                <el-color-picker v-model="pageCtl.colorPicker" size="small"/>
            </span>
            <div style="height: 5px;"></div>
            <el-tooltip effect="light" content="Run SQL" placement="right" :show-after="1000">
              <el-tag type="info" style="cursor: pointer;" @click="testSql('newForm')">
                <font-awesome-icon icon="play"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>

    <!-- modify widget-->
    <scp-draggable-resizable w="70vw" h="600px" v-model="visibleCtl.modifyWidget" title="Widget Details"
                             delete-confirm-text="确定删除该组件?" :delete-loading="loadingCtl.deleteWidget" :delete="deleteWidget"
                             :save-loading="loadingCtl.saveWidget" :save="modifyWidget" :show-save="pageCtl.modifyForm.author">
      <template v-slot="{height}">
        <div v-loading="loadingCtl.queryWidget">
          <el-row style="padding: 10px 0 5px 10px;">
            <el-col :span="6">
              <el-input placeholder="Title" v-model="pageCtl.modifyForm.title" style="width: var(--scp-input-width) !important;" :maxlength="30"
                        show-word-limit/>
            </el-col>
            <el-col :span="5">
              <el-autocomplete
                  class="inline-input"
                  style="width: var(--scp-input-width) !important;"
                  v-model="pageCtl.modifyForm.groupName"
                  :maxlength="30"
                  :fetch-suggestions="groupSuggestion"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
            <el-col :span="8">
              <el-radio-group v-model="pageCtl.modifyForm.reportType">
                <el-radio-button value="Text/Number">Text/Number</el-radio-button>
                <el-radio-button value="Line">Line</el-radio-button>
                <el-radio-button value="Bar">Bar</el-radio-button>
                <el-radio-button value="Pie">Pie</el-radio-button>
              </el-radio-group>
            </el-col>
          </el-row>

          <el-row style="padding: 0 0 10px 10px;">
            <el-col :span="11">
              <el-input placeholder="Link to" v-model="pageCtl.modifyForm.linkTo" style="width: var(--scp-input-width) !important;" clearable></el-input>
            </el-col>
            <el-col :span="8">
              <el-select placeholder="Share to" v-model="pageCtl.modifyForm.shareTo" style="width: 100% !important;" multiple collapse-tags filterable
                         clearable>
                <el-option
                    v-for="item in pageCtl.shareTo"
                    :key="item['VALUE']"
                    :label="item['NAME']"
                    :value="item['VALUE']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" style="padding-left: 10px">
              <el-select placeholder="Cache Type" v-model="pageCtl.modifyForm.cachable" style="width: 100% !important;">
                <el-option
                    v-for="item in [{label: 'Enable Cache', value: 'Y'}, {label: 'Disable Cache', value: 'N'}]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>

          <el-row style="border-bottom:1px dashed var(--scp-border-color);" v-loading="loadingCtl.execute">
            <el-col :span="23">
              <el-tabs v-model="pageCtl.modifyForm.scriptType" type="card">
                <el-tab-pane label="Summary" name="Summary">
                  <scp-ace-editor v-model="pageCtl.modifyForm.scripts" :style="{height: (height - 200) + 'px'}" lang="sql"/>
                </el-tab-pane>
                <el-tab-pane label="Details" name="Details">
                  <scp-ace-editor v-model="pageCtl.modifyForm.detailsScripts" :style="{height: (height - 200) + 'px'}" lang="sql"/>
                </el-tab-pane>
              </el-tabs>
            </el-col>
            <el-col :span="1" style="margin-top: 20px;padding-left: 2px" :style="{height: (height - 200) + 'px', borderLeft:'1px dashed var(--scp-border-color)'}">
              <el-tooltip effect="light" content="Format SQL" placement="right" :show-after="1000">
                <el-tag type="info" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatSQL('modifyForm')">
                  <font-awesome-icon icon="align-left"/>
                </el-tag>
              </el-tooltip>
              <div style="height: 5px;"></div>
              <el-tooltip effect="light" content="Add Global Conditions" placement="right" :show-after="1000">
                <el-tag type="info" style="cursor: pointer;font-size: 11px;padding: 0 9px" @click="addGlobalConditions('modifyForm')">
                  <font-awesome-icon icon="globe-asia"/>
                </el-tag>
              </el-tooltip>
              <div style="height: 5px;"></div>
              <el-tooltip effect="light" content="View Supported Icons" placement="right" :show-after="1000">
                <a target="_blank" href="https://fontawesome.com/search?s=solid">
                  <el-tag type="info" style="cursor: pointer;font-size: 11px;padding: 0 9px">
                    <font-awesome-icon icon="flag"/>
                  </el-tag>
                </a>
              </el-tooltip>
              <div style="height: 5px;"></div>
              <span title="Pick a color">
                <el-color-picker v-model="pageCtl.colorPicker" size="small"/>
              </span>
              <div style="height: 5px;"></div>
              <el-tooltip effect="light" content="Run SQL" placement="right" :show-after="1000">
                <el-tag type="info" style="cursor: pointer;" @click="testSql('modifyForm')">
                  <font-awesome-icon icon="play"/>
                </el-tag>
              </el-tooltip>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>

    <!-- execute sql-->
    <scp-draggable-resizable w="800px" h="360px" v-model="visibleCtl.executeResult" title="Execute Result">
      <scp-table2
          ref="executeResultTable"
          :pagging="false"
          v-loading="loadingCtl.execute"
          :column-sorting="false"
          :filters="false"
          :showContextMenu="true"
          :columns="_resultTableHeader"
          :data="pageCtl.resultData"/>
    </scp-draggable-resizable>

    <!-- view details-->
    <scp-draggable-resizable w="70vw" h="480px" v-model="visibleCtl.viewDetails" :title="'View Details - ' + viewDetailsCtl.title">
      <template v-slot="{height}">
        <scp-table
            ref="viewDetailsTableRef"
            url="/home/<USER>/query_details"
            download-url="/home/<USER>/download_details"
            :download-specify-column="false"
            :params="pageCtl.conditions"
            :max-height="height - 120"
            :lazy="true"
            :columns="viewDetailsCtl.columns"/>
      </template>
    </scp-draggable-resizable>

    <!-- new widget contextmenu-->
    <v-contextmenu ref="contextmenu1">
      <v-contextmenu-item @click="showNewWidget">
        New Widget
      </v-contextmenu-item>
      <v-contextmenu-item @click="loadLayout">
        Refresh
      </v-contextmenu-item>
    </v-contextmenu>

    <!-- save warning-->
    <el-dialog
        title="Error"
        :close-on-click-modal="false"
        v-model="visibleCtl.saveWarning"
        width="800px">
      <div style="color:var(--scp-text-color-error)">{{ pageCtl.saveMessage }}</div>
      <hr>
      <table class="displayTable">
        <colgroup>
          <col style="width: 20%"/>
          <col style="width: 35%"/>
          <col style="width: 45%"/>
        </colgroup>
        <thead>
        <tr>
          <th>Widget Type</th>
          <th>Required Fields</th>
          <th>Optional Fields</th>
        </tr>
        </thead>
        <tbody>
        <tr>
          <td>Text/Number</td>
          <td>value1, value2... <span class="tips"><font-awesome-icon icon="info-circle"/> the text you want to display</span></td>
          <td>
            <p>color1, color2... <span class="tips"><font-awesome-icon icon="info-circle"/> text color, corresponds to the text</span></p>
            <p>icon <span class="tips"><font-awesome-icon icon="info-circle"/> displays an icon if needed</span></p>
            <p>icon_color <span class="tips"><font-awesome-icon icon="info-circle"/> icon color</span></p>
          </td>
        </tr>
        <tr>
          <td>Pie/Bar/Line</td>
          <td>
            <p>xAxis (x-Axis)</p>
            <p>yAxis (y-Axis)</p>
          </td>
          <td></td>
        </tr>
        </tbody>
      </table>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="visibleCtl.saveWarning = false">Got it!</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { useStore } from 'vuex'

const mysmRef = ref()
const viewDetailsTableRef = ref()
const $axios: any = inject('$axios')
const $store = useStore()
const $message: any = inject('$message')
const $shortenNumber: any = inject('$shortenNumber')

const pageCtl = reactive({
  activeWorkstations: [] as Array<any>, // 前台组件列表
  availableWorkstations: [], // 后台组件列表
  workstationsValues: {}, // 组件数据对象
  workstationFilterOpts: [],
  colorPicker: '',
  saveMessage: '', // 保存组件的相关提示信息
  shareTo: [],
  activeWidgetsSettings: {}, // 保存用户选择结果, 前台计算
  checkedWidgets: {}, // 用户选择的组件
  workstationsLoading: {}, // 组件加载对象, 前台计算
  resultData: [[]], // SQL测试结果
  selectedWidget: '',
  conditions: {
    id: '', // view details的ID
    filterList: [],
    groupType: 'By Category',
    material: '',
    vendorCode: '',
    sizeRatio: 200,
    checkedWidgets: {}
  },
  customSettings: {
    groupName: '',
    orderNo: 0,
    fontSize: 100
  },
  modifyForm: {
    id: '',
    title: '',
    groupName: '',
    shareTo: [],
    linkTo: '',
    cachable: 'Y',
    reportType: 'Text/Number',
    scripts: '',
    detailsScripts: '',
    author: false,
    scriptType: 'Summary'
  },
  newForm: {
    title: '',
    scriptType: 'Summary',
    groupName: '',
    shareTo: [],
    linkTo: '',
    cachable: 'Y',
    reportType: 'Text/Number',
    scripts: '',
    detailsScripts: ''
  }
})

const loadingCtl = reactive({
  mainPage: false,
  saveWidget: false,
  execute: false,
  customSettings: false,
  queryWidget: false,
  deleteWidget: false,
  testSql: false,
  filter: false
})

const visibleCtl = reactive({
  newWidget: false,
  modifyWidget: false,
  executeResult: false,
  saveWarning: false,
  viewDetails: false
})

// 翻转之后查询条件
const viewDetailsCtl = reactive({
  title: '',
  columns: [{}] as Array<any>
})

// 自定义宽高
const _widgetWidth = computed(() => {
  return 220.0 * pageCtl.conditions.sizeRatio / 100
})

const _widgetHeight = computed(() => {
  return 120.0 * pageCtl.conditions.sizeRatio / 100
})

const _resultTableHeader = computed(() => {
  const result = [] as Array<any>
  const column = pageCtl.resultData[0]
  for (const c in column) {
    if (column.hasOwnProperty(c) === true) {
      result.push({
        data: c,
        type: 'text'
      })
    }
  }
  return result
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/home/<USER>/init_page'
  }).then((body) => {
    pageCtl.workstationFilterOpts = body.cascader
    pageCtl.shareTo = body.shareTo
  }).catch((error) => {
    console.log(error)
  })
}

const loadLayout = () => {
  loadingCtl.mainPage = true
  $axios({
    method: 'post',
    url: '/home/<USER>/query_workstations_layout'
  }).then((body) => {
    pageCtl.activeWorkstations = body.activeWorkstations
    pageCtl.availableWorkstations = body.availableWorkstations

    // 计算loading和settings
    const activeWidgets = body.activeWidgets
    const settings = {}
    const loading = {}
    const checked = {}
    for (let i = 0; i < activeWidgets.length; i++) {
      const id = activeWidgets[i].id
      settings[id] = activeWidgets[i]
      loading[id] = true
      checked[id] = true
    }
    pageCtl.checkedWidgets = checked
    pageCtl.activeWidgetsSettings = settings
    pageCtl.workstationsLoading = loading

    if (body.parameter) {
      pageCtl.conditions = body.parameter
    }

    pageCtl.workstationsValues = {}
    loadingCtl.mainPage = false
    loadValue(activeWidgets)
  }).catch((error) => {
    console.log(error)
  })
}

const loadValue = (widgets) => {
  for (let i = 0; i < widgets.length; i++) {
    const w = widgets[i]
    $axios({
      method: 'post',
      url: '/home/<USER>/query_workstation_value',
      data: {
        id: w.id,
        cachable: w.cachable,
        filterList: pageCtl.conditions.filterList,
        material: pageCtl.conditions.material,
        vendorCode: pageCtl.conditions.vendorCode
      }
    }).then((body) => {
      pageCtl.workstationsValues[w.id] = body
      pageCtl.workstationsLoading[w.id] = false
    }).catch((error) => {
      console.log(error)
    })
  }
}

const addGlobalConditions = (formID) => {
  const pointer = formID === 'modifyForm' ? pageCtl.modifyForm : pageCtl.newForm
  if (pointer.scriptType === 'Details') {
    pointer.detailsScripts += '#GLOBAL_FILTER#\r'
  } else {
    pointer.scripts += '#GLOBAL_FILTER#\r'
  }
}

const testSql = (formID) => {
  const pointer = formID === 'modifyForm' ? pageCtl.modifyForm : pageCtl.newForm
  const scripts = pointer.scriptType === 'Details' ? pointer.detailsScripts : pointer.scripts

  const c = new Date().getTime()
  loadingCtl.execute = true
  $axios({
    method: 'post',
    url: '/home/<USER>/execute_query',
    data: {
      scripts,
      filterList: pageCtl.conditions.filterList,
      material: pageCtl.conditions.material,
      vendorCode: pageCtl.conditions.vendorCode
    }
  }).then((body) => {
    if (body.result === 'success') {
      visibleCtl.executeResult = true
      if (new Date().getTime() - c > 500) {
        pageCtl.resultData = body.data
      } else {
        setTimeout(() => {
          pageCtl.resultData = body.data
        }, 500)
      }
    } else {
      $message.error(body.data)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    if (new Date().getTime() - c > 500) {
      loadingCtl.execute = false
    } else {
      setTimeout(() => {
        loadingCtl.execute = false
      }, 500)
    }
  })
}

const formatSQL = (formID) => {
  const pointer = formID === 'modifyForm' ? pageCtl.modifyForm : pageCtl.newForm
  const scripts = pointer.scriptType === 'Details' ? pointer.detailsScripts : pointer.scripts
  $axios({
    method: 'post',
    url: '/home/<USER>/format_sql',
    data: {
      scripts
    }
  }).then((body) => {
    if (body) {
      if (pointer.scriptType === 'Details') {
        pointer.detailsScripts = body
      } else {
        pointer.scripts = body
      }
    }
  }).catch((error) => {
    console.log(error)
  })
}

const showNewWidget = () => {
  pageCtl.newForm = {
    title: '',
    scriptType: 'Summary',
    groupName: '',
    shareTo: [],
    linkTo: '',
    cachable: 'Y',
    reportType: 'Text/Number',
    scripts: '',
    detailsScripts: ''
  }
  visibleCtl.newWidget = true
}

const saveWidget = () => {
  if (pageCtl.newForm.title && pageCtl.newForm.groupName && pageCtl.newForm.scripts) {
    loadingCtl.saveWidget = true
    $axios({
      method: 'post',
      url: '/home/<USER>/save_widget',
      data: pageCtl.newForm
    }).then((body) => {
      if (body && body.message) {
        pageCtl.saveMessage = body.message
        visibleCtl.saveWarning = true
      } else {
        $message.success('Widget Saved')
        loadLayout()
        visibleCtl.executeResult = false
        visibleCtl.newWidget = false
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      loadingCtl.saveWidget = false
    })
  } else {
    $message.error('Please confirm the input')
  }
}

const modifyWidget = () => {
  if (pageCtl.modifyForm.title && pageCtl.modifyForm.groupName && pageCtl.modifyForm.scripts) {
    loadingCtl.saveWidget = true
    $axios({
      method: 'post',
      url: '/home/<USER>/modify_widget',
      data: pageCtl.modifyForm
    }).then((body) => {
      if (body && body.message) {
        pageCtl.saveMessage = body.message
        visibleCtl.saveWarning = true
      } else {
        $message.success('Widget Modified')
        loadLayout()
        visibleCtl.executeResult = false
        visibleCtl.modifyWidget = false
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      loadingCtl.saveWidget = false
    })
  } else {
    $message.error('Please confirm the input')
  }
}

const deleteWidget = () => {
  loadingCtl.deleteWidget = true
  $axios({
    method: 'post',
    url: '/home/<USER>/delete_widget',
    data: {
      id: pageCtl.modifyForm.id
    }
  }).then(() => {
    $message.success('Widget Deleted')
    visibleCtl.executeResult = false
    visibleCtl.modifyWidget = false
    loadLayout()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.deleteWidget = false
  })
}

const saveParameters = (toggle, message) => {
  pageCtl.conditions.checkedWidgets = pageCtl.checkedWidgets
  $axios({
    method: 'post',
    url: '/home/<USER>/save_parameters',
    data: pageCtl.conditions
  }).then(() => {
    loadLayout()
    if (message) {
      $message.success('Settings Saved')
    }
    if (toggle === true) {
      nextTick(() => {
        mysmRef.value.toggleView()
      })
    }
  }).catch((error) => {
    console.log(error)
  })
}

const showPopover = (id) => {
  const c = pageCtl.activeWidgetsSettings[id]
  const r = {} as any
  for (const k in c) {
    if (c.hasOwnProperty(k)) {
      r[k] = c[k]
    }
  }
  pageCtl.customSettings = r
}

const saveCustomSettings = () => {
  loadingCtl.customSettings = true
  $axios({
    method: 'post',
    url: '/home/<USER>/save_custom_settings',
    data: pageCtl.customSettings
  }).then(() => {
    loadLayout()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.customSettings = false
  })
}

const chartOpts = (data) => {
  const margin = Math.min(pageCtl.conditions.sizeRatio / 100 * 20, 20)
  const lineWidth = Math.min(pageCtl.conditions.sizeRatio / 100 * 5, 5)
  if (data.type === 'pie') {
    const pieData = [] as Array<any>
    for (let i = 0; i < data.xAxis.length; i++) {
      pieData.push({ name: data.xAxis[i], value: data.yAxis[i] })
    }
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      series: {
        color: ['#3dcd58'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        type: 'pie',
        radius: '80%',
        center: ['50%', '50%'],
        data: pieData,
        itemStyle: {
          borderWidth: 1,
          borderColor: '#ffffff'
        }
      }
    }
  } else if (data.type === 'line') {
    return {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}'
      },
      color: '#3dcd58',
      grid: {
        top: margin,
        bottom: margin
      },
      xAxis: {
        type: 'category',
        show: false,
        data: data.xAxis
      },
      yAxis: {
        type: 'value',
        show: false,
        splitLine: { show: false }
      },
      series: [{
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: lineWidth,
        lineStyle: {
          width: lineWidth
        },
        data: data.yAxis
      }]
    }
  } else if (data.type === 'bar') {
    return {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}'
      },
      color: ['#3dcd58'],
      grid: {
        top: margin,
        bottom: margin
      },
      xAxis: {
        type: 'category',
        show: false,
        data: data.xAxis
      },
      yAxis: {
        type: 'value',
        show: false,
        splitLine: { show: false }
      },
      series: [{
        type: 'bar',
        data: data.yAxis
      }]
    }
  }
  return {}
}

const groupSuggestion = (queryString, cb) => {
  const restaurants = pageCtl.activeWorkstations.map(e => {
    return { value: e.name }
  })
  const results = queryString ? restaurants.filter(createFilter(queryString)) : restaurants
  cb(results)
}

const createFilter = (queryString) => {
  return (restaurant) => {
    return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
  }
}

const customFontSize = (id, ratio?) => {
  ratio = ratio || 1
  const size = pageCtl.activeWidgetsSettings[id].fontSize || 100
  return (_widgetHeight.value * 0.22 * size * ratio / 100) + 'px'
}

const showWidgetDetails = (id) => {
  visibleCtl.modifyWidget = true
  loadingCtl.queryWidget = true
  $axios({
    method: 'post',
    url: '/home/<USER>/query_widget_by_id',
    data: {
      id
    }
  }).then((body) => {
    pageCtl.modifyForm.id = body.id
    pageCtl.modifyForm.title = body.title
    pageCtl.modifyForm.groupName = body.groupName
    pageCtl.modifyForm.shareTo = body.shareTo
    pageCtl.modifyForm.linkTo = body.linkTo
    pageCtl.modifyForm.cachable = body.cachable
    pageCtl.modifyForm.reportType = body.reportType
    pageCtl.modifyForm.scripts = body.scripts || ''
    pageCtl.modifyForm.detailsScripts = body.detailsScripts || ''
    pageCtl.modifyForm.author = body.author
    pageCtl.modifyForm.scriptType = 'Summary'
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.queryWidget = false
  })
}

const searchDetails = () => {
  viewDetailsTableRef.value.clearAndSearch()
}

const showDetails = (id, title) => {
  pageCtl.conditions.id = id
  viewDetailsCtl.title = title
  $axios({
    method: 'post',
    url: '/home/<USER>/query_details_header',
    data: {
      id
    }
  }).then((body) => {
    const data = body.data
    if (body.result === 'error') {
      $message.error(data)
    } else {
      const columns = [] as any
      for (let i = 0; i < data.length; i++) {
        columns.push({ data: data[i] })
      }
      viewDetailsCtl.columns = columns
      visibleCtl.viewDetails = true
      if (body.result === 'success') {
        searchDetails()
      }
    }
  }).catch((error) => {
    console.log(error)
  })
}

const getValueType = (obj) => {
  if (obj) {
    if (typeof obj.value === 'string' || obj.value === null || obj.value === undefined) {
      return 'string'
    } else if (typeof obj.value === 'number') {
      return 'number'
    } else if (isNaN(obj.value.length)) {
      return 'chart'
    } else if (!isNaN(obj.value.length)) {
      return 'array'
    }
  }
  return 'default'
}

onMounted(() => {
  initPage()
  loadLayout()
})
</script>

<style lang="scss">
.workstation-custom-form {
  .el-slider__runway {
    margin: 8px 0 !important;

    .el-slider__button-wrapper {
      z-index: 980 !important;
    }
  }

  .el-col {
    color: var(--scp-text-color-secondary) !important;

    input {
      padding: 0 10px !important
    }
  }
}

#workstation {
  .el-color-picker__trigger {
    height: 20px !important;
    width: 30px !important;
    padding: 0 !important;
    border: 0 !important;
    background-color: var(--scp-bg-color-fill-lighter);

    .el-color-picker__color {
      border: 0 !important
    }
  }

  .el-color-picker__icon {
    font-size: 16px;
  }

  .el-color-picker__empty {
    font-size: 16px;
  }

  .el-color-picker__icon:before {
    content: '\e79f' !important;
  }

  .el-color-picker__empty:before {
    content: '\e79f' !important;
  }

  .tag-header {
    .el-button {
      border: 0 !important;
    }

    .el-button--small {
      padding: 4px 0 !important;
      font-size: inherit !important;
    }
  }

  .tag-body {
    .echarts {
      position: initial !important;
    }

    .el-loading-spinner {
      margin-top: -10px !important;

      .circular {
        animation: none !important;
        -webkit-animation: none !important;
        height: 20px !important;
        width: 20px !important;
      }
    }

    .text {
      p {
        margin: 0 0 1% !important;
      }
    }
  }

  .el-cascader--medium input.el-cascader__search-input {
    background-color: #fff;
  }

  .input-display {
    .el-input__inner {
      border: 0 !important;
      padding-left: 2px !important;
      padding-right: 0 !important;
      cursor: text;
      text-align: center;
      color: var(--scp-text-color-primary) !important;
    }

    .el-input__inner:hover {
      border: 0 !important;
      cursor: text;
    }
  }

  .el-carousel__container {
    height: 100% !important;
  }

  .el-carousel__indicators {
    line-height: initial !important;
  }

  .el-carousel__arrow {
    width: 18px !important;
    height: 18px !important;
    font-size: 12px !important;
  }

  .el-tabs__header {
    margin: 0 0 1px !important;

    .el-tabs__item {
      font-size: 0.5rem !important;
      height: 0.55rem !important;
      line-height: 0.55rem !important;
    }
  }

  .el-carousel__button {
    background-color: var(--scp-bg-color-fill) !important;
    width: 20px !important;
  }
}
</style>
<style lang="scss" scoped>
.group-header {
  display: inline-block;
  background-color: var(--scp-bg-color);
  margin-bottom: 10px;

  .text {
    position: absolute;
    display: inline-block;
    padding-right: 10px;
    color: var(--scp-text-color-primary);
    font-size: 0.52rem;
    font-weight: bold;
  }
}

.group-content {
  .tag.active {
    border-color: var(--scp-text-color-highlight) !important;
  }

  .tag {
    position: relative;
    display: inline-block;
    margin: 5px 5px 0.54rem;
    border: 1px solid var(--scp-border-color-lighter);

    .text {
      display: inline-block;
      width: 100%;
      color: inherit;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font: {
        weight: bold;
      }
      text-align: center;
    }

    &:hover {
      box-shadow: 0 0 10px #e5e5e5;
    }

    .tag-header {
      text-align: right;
      width: 100%;
      box-sizing: border-box;
      position: absolute;
      z-index: 980;
      top: -20px;
      right: 0;
      opacity: 0;

      &:hover {
        opacity: 1;
      }

      .menu {
        cursor: pointer;
      }

      * {
        color: var(--scp-text-color-secondary);
      }

      .el-button {
        background-color: transparent !important;
      }

      .el-button:hover {
        background-color: transparent !important;
      }
    }

    .icon {
      position: absolute;
      font: {
        weight: bold;
      }
    }
  }
}

.displayTable {
  width: 100%;

  th, td {
    border: 1px dashed var(--scp-border-color);
    padding: 5px;

    .tips {
      color: var(--scp-text-color-highlight);
    }

    p {
      margin: 0 2px !important;
    }
  }
}
</style>
