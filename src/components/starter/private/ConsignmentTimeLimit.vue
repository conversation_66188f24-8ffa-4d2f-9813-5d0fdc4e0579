<template>
    <div class="left-sidebar">
        <div class="widget">
            <div class="widget-body">
                <scp-table
                        ref="tableRef"
                        :contextMenuItems="pageCtl.menuItems"
                        :columns="pageCtl.columns"
                        :lazy="true"
                        url="/components/consignment_time_limit/query_data"
                        download-url="/components/consignment_time_limit/download_data"
                        save-url="/components/consignment_time_limit/save_data"
                        :primary-key-id="['PLANT_CODE', 'MATERIAL', 'VENDOR_CODE']"
                />

                <scp-upload
                        title="Theo Aging Day Upload"
                        text="Upload Data"
                        ref="uploadRef"
                        w="600px"
                        h="250px"
                        :show-btn="false"
                        upload-url='/components/consignment_time_limit/upload_data'
                        download-template-url="/components/consignment_time_limit/download_template"
                        :on-upload-end="search">
                    <template #tip>
                        <p>1. Please check the format of the file before uploading.</p>
                        <p>2. This file will overwrite the existing data.</p>
                        <p>3. You can ONLY update column CONSIGN_TIME_LIMIT</p>
                    </template>
                </scp-upload>
            </div>
        </div>

    </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { Column } from '@/components/starter/components/Table.vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const tableRef = ref()
const uploadRef = ref()
const showUpload = () => {
  uploadRef.value.showUploadWin()
}

const pageCtl = reactive({
  columns: [{
    data: 'PLANT_CODE'
  }, {
    data: 'MATERIAL'
  }, {
    data: 'VENDOR_CODE'
  }, {
    data: 'CONSIGN_TIME_LIMIT',
    type: 'strict-numeric',
    precision: 1
  }, {
    data: 'CREATE_BY'
  }, {
    data: 'CREATE_DATE'
  }, {
    data: 'UPDATE_BY'
  }, {
    data: 'UPDATE_DATE'
  }] as Array<Column>,
  menuItems: {
    upload_material_owner: {
      name: 'Upload',
      callback: showUpload
    },
    view_split0: { name: '---------' }
  }
})

const search = () => {
  tableRef.value.search()
}

onMounted(() => {
  search()
})

</script>
