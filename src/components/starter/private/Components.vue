<template>
  <div class="body-wrapper">
    <div style="padding: var(--scp-widget-margin) var(--scp-widget-margin) 0 var(--scp-widget-margin)">
      <el-breadcrumb separator="/" class="components-header">
        <el-breadcrumb-item to="/users/my_favourites">
          <font-awesome-icon icon="angle-right"/>&nbsp;&nbsp;My Favourites
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{ $route.query.label }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <router-view/>
    <!-- 公共View Details模块 -->
    <scp-draggable-resizable v-model="$store.state.viewDetails.visible" h="450px" w="60vw"
                             :title="$store.state.viewDetails.title">
      <template v-slot="{ height }">
        <scp-table
            ref="publicViewDetailsRef"
            :max-height="height - 150"
            :lazy="true"
            :params="$store.state.viewDetails.params"
            :url="$store.state.viewDetails.url"
            :download-url="$store.state.viewDetails.durl"
            :columns="$store.state.viewDetails.columns"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { nextTick, ref, watch } from 'vue'

const $route = useRoute()
const $router = useRouter()
const $store: any = useStore()
const goBack = () => {
  $router.replace('/users/my_favourites')
}

const publicViewDetailsRef = ref()
watch(() => $store.state.viewDetails.id, () => {
  if ($store.state.viewDetails.visible) {
    nextTick(() => {
      publicViewDetailsRef.value.clearAndSearch()
    })
  }
})
</script>
<style scoped>
.components-header {
  font-size: 0.5rem;
  margin: 10px 0 10px 0;
}
</style>
