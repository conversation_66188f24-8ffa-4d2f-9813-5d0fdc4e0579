<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box" style="margin-bottom: 5px;">
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.materialOwner"
                       clearable collapse-tags filterable multiple
                       placeholder="Material Owner">
              <el-option
                  v-for="item in pageCtl.materialOwnerList"
                  :key="item['VAL']"
                  :label="item['LABEL']"
                  :value="item['VAL']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <el-button @click="search">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <scp-table
            ref="materialOwnerTableRef"
            :contextMenuItems="pageCtl.menuItems"
            :params="pageCtl.conditions"
            :context-menu-items-reverse="true"
            :columns="pageCtl.columns"
            :lazy="true"
            :editable="false"
            url="/components/material_owner/query_data"
            download-url="/components/material_owner/download_data"/>

        <hr>

        <el-row class="search-box">
          <el-col :span="20">
            <h4 style="margin: 10px 0">Abnormal Material</h4>
          </el-col>
          <el-col :span="4" style="text-align: right;padding-right: 5px">
            <el-popconfirm title="确定要删除数据? 如需要恢复,请重新上传."
                           iconColor="var(--scp-text-color-error)"
                           @confirm="deleteAbnormalMaterial"
                           confirmButtonType="danger"
                           confirmButtonText='Confirm'
                           cancelButtonText='Cancel'>
              <template #reference>
                <el-button :loading="pageCtl.deleting">Delete Abnormal Material</el-button>
              </template>
            </el-popconfirm>
          </el-col>
        </el-row>

        <scp-table
            ref="materialOwnerInvalidTableRef"
            :columns="pageCtl.abnormalColumns"
            :lazy="true"
            url="/components/material_owner/query_invalid_data"
            download-url="/components/material_owner/download_invalid_data"/>
      </div>
    </div>

    <scp-draggable-resizable w="70vw" h="600px" v-model="upload.visible" title="Upload Material Owner">
      <template v-slot="{height}">
        <el-upload
            style="padding:15px"
            accept=".xlsx"
            :multiple="false"
            :before-upload="upload.onUploadStart"
            :on-success="upload.onUploadEnd"
            :headers="upload.header"
            :file-list="upload.fileList"
            :on-change="upload.onUploadChange"
            :action="upload.url">
          <template #trigger>
            <el-button size="small" type="primary" :loading="upload.loading" style="padding:6px 12px">Select a file</el-button>
          </template>
          <a target="_blank" href="/file/MaterialOwnerUploadTemplateV3.xlsx">
            <el-button size="small" style="padding:6px 12px;margin-left:6px">Download Template</el-button>
          </a>
          <template #tip>
            <div class="el-upload__tip">
              <p>1. Please check the format of the file before uploading.</p>
              <p>2. This file will overwrite the data you uploaded before.</p>
            </div>
          </template>
        </el-upload>
        <div style="padding: 5px" v-show="upload.confictData.length > 0">
          <h4 style="color:var(--scp-text-color-error);margin: 10px 0">Confict Material Found</h4>
          <scp-table2
              :max-height="height - 400"
              ref="confictTable"
              :lazy="true"
              :columns="pageCtl.confictTableColumns"
              :data="upload.confictData"/>
        </div>
      </template>
      <template #footer>
        <el-popconfirm title="Overwrite conflicting data?"
                       iconColor="orange"
                       @confirm="applyConflict"
                       confirmButtonType="warning"
                       confirmButtonText='Confirm'
                       cancelButtonText='Cancel'>
          <template #reference>
            <el-button type="primary" :loading="upload.applying" v-show="upload.confictData.length > 0">
              Apply Conflict
            </el-button>
          </template>
        </el-popconfirm>
        <el-button :class="upload.closeBtnClass" @click="upload.visible = false" :loading="upload.loading">
          {{ upload.closeBtn }}
        </el-button>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'

const $axios: any = inject('$axios')
const $baseUrl: any = inject('$baseUrl')
const $message: any = inject('$message')
const materialOwnerTableRef = ref()
const materialOwnerInvalidTableRef = ref()

const upload = reactive({
  fileList: [] as Array<any>,
  closeBtn: 'Close',
  closeBtnClass: 'el-button--default',
  visible: false,
  loading: false,
  header: { token: localStorage.getItem('token') },
  url: $baseUrl + '/components/material_owner/upload_data',
  applying: false,
  confictDataFile: '',
  confictData: [],

  showUploadWin: () => {
    upload.confictDataFile = ''
    upload.confictData = []
    upload.fileList = []
    upload.closeBtn = 'Close'
    upload.closeBtnClass = 'el-button--default'
    upload.visible = true
  },
  onUploadStart: () => {
    upload.loading = true
    upload.closeBtn = 'Uploading'
    upload.closeBtnClass = 'el-button--default'
  },
  onUploadChange: (file) => {
    upload.fileList = [file]
  },
  onUploadEnd: (res) => {
    if (res.header.status !== 200) {
      $message({
        message: res.header.message,
        type: 'error'
      })
      upload.closeBtn = 'Failed'
      upload.closeBtnClass = 'el-button--danger'
    } else {
      // 如果tempFilePath不为空, 则说明有冲突的数据, 则显示冲突提示
      if (res.body && res.body.tempFilePath) {
        upload.confictDataFile = res.body.tempFilePath || ''
        upload.confictData = res.body.conflictList || []
        upload.closeBtn = 'Close'
        upload.closeBtnClass = 'el-button--default'
      } else {
        upload.closeBtn = 'Success'
        upload.closeBtnClass = 'el-button--primary'
        $message.success('Upload Success')
        upload.visible = false
        search()
      }
    }
    upload.loading = false
  }
})

const pageCtl = reactive({
  materialOwnerList: [] as Array<any>,
  conditions: {
    materialOwner: [] as Array<string>
  },
  columns: [{
    data: 'PLANT_CODE'
  }, {
    data: 'CLAIM_DATA'
  }, {
    data: 'CLAIM_TYPE'
  }, {
    data: 'COMMENTS'
  }, {
    data: 'OWNER'
  }, {
    data: 'OWNER_NAME'
  }],
  abnormalColumns: [{
    data: 'PLANT_CODE'
  }, {
    data: 'CLAIM_DATA'
  }, {
    data: 'CLAIM_TYPE'
  }, {
    data: 'COMMENTS'
  }, {
    data: 'OWNER'
  }, {
    data: 'OWNER_NAME'
  }],
  menuItems: {
    upload_material_owner: {
      name: 'Upload material owner',
      callback: upload.showUploadWin
    },
    view_split0: { name: '---------' }
  },
  confictTableColumns: [{
    title: 'Plant Code',
    data: 'PLANT_CODE'
  }, {
    title: 'Claim Data',
    data: 'CLAIM_DATA'
  }, {
    title: 'Claim Type',
    data: 'CLAIM_TYPE'
  }, {
    title: 'ORG Owner',
    data: 'ORG_OWNER'
  }, {
    title: 'ORG Owner Name',
    data: 'ORG_OWNER_NAME'
  }, {
    title: 'New Owner',
    data: 'NEW_OWNER'
  }, {
    title: 'New Owner Name',
    data: 'NEW_OWNER_NAME'
  }],
  deleting: false
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/components/material_owner/init_page'
  }).then((body) => {
    pageCtl.materialOwnerList = body.materialOwnerList
    if (pageCtl.materialOwnerList.filter(e => e.VAL === localStorage.getItem('username')).length > 0) {
      pageCtl.conditions.materialOwner = [localStorage.getItem('username') || '']
    }
    search()
  }).catch((error) => {
    console.log(error)
  })
}

const search = () => {
  materialOwnerTableRef.value.search()
  materialOwnerInvalidTableRef.value.search()
}

const applyConflict = () => {
  upload.applying = true
  $axios({
    method: 'post',
    url: '/components/material_owner/upload_data_apply_conflict',
    data: {
      confictDataFile: upload.confictDataFile
    }
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success('Upload Success')
      upload.visible = false
      search()
    }
  }).finally(() => {
    upload.applying = false
  })
}

const deleteAbnormalMaterial = () => {
  pageCtl.deleting = true
  $axios({
    method: 'post',
    url: '/components/material_owner/delete_abnormal_material'
  }).then((body) => {
    $message.success('Abnormal material deleted!')
    search()
  }).finally(() => {
    pageCtl.deleting = false
  })
}

onMounted(() => {
  initPage()
})

</script>
