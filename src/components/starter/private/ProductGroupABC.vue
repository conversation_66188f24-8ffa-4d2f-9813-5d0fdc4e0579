<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['MR3_PRODUCT_GROUP_ABC']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <scp-table
            ref="reportTableRef"
            :lazy="true"
            :context-menu-items="pageCtl.contextItems"
            :columns="colColumns"
            save-url="/components/product_group/save_product_group_data"
            url="/components/product_group/query_product_group_data"
            download-url="/components/product_group/download_product_group_data"
            :params="pageCtl.conditions"
            :after-save="afterSave"
        />

        <scp-upload
            title="Product Group Upload"
            text="Upload Product Group"
            ref="reportUploadRef"
            :show-btn="false"
            w="600px"
            h="250px"
            upload-url='/components/product_group/upload_report'
            download-template-url="/components/product_group/download_product_group_template"
            :on-upload-end="onUploadEnd">
          <template #tip>
            <p>1. Please check the file format before uploading.</p>
          </template>
        </scp-upload>
      </div>
    </div>

    <scp-draggable-resizable w="900px" h="700px" v-model="pageCtl.errorListVisible" title="Execute Result">
      <p style="margin: 8px 2px 0; color: #d32f2f; font-weight: bold; font-size: 12px;">
        【上传失败】下方表格中的物料标识已被占用。
      </p>
      <p style="margin: 8px 2px; font-size: 12px; color: #333;">
        如需更新，请先联系对应的负责人
        <span style="color: #1976d2; font-weight: bold;">COLUMN_OWNER</span>，
        在其协助清除原有标识后，再进行上传操作。
      </p>
      <scp-table2
          ref="executeResultTable"
          :max-height="550"
          :pagging="[0, 50 , 100]"
          :column-sorting="false"
          :filters="false"
          :showContextMenu="true"
          :columns="errorColColumns"
          :data="pageCtl.errorListData"/>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')
const reportTableRef = ref()
const reportUploadRef = ref()

const searchRef = ref()

const showUploadWin = () => {
  reportUploadRef.value.showUploadWin()
}

const pageCtl = reactive({
  filterOpts: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    }
  },
  loading: {
    report1: false
  },
  contextItems: {
    view_details: {
      name: 'Upload Product Group Target',
      callback: showUploadWin
    },
    view_split0: { name: '---------' }
  },
  errorListVisible: false,
  errorListData: []
})

const errorColColumns = reactive([
  { data: 'MATERIAL' },
  { data: 'PLANT_CODE' },
  { data: 'ERROR_COLUMN' },
  { data: 'COLUMN_OWNER' },
  { data: 'ORIGINAL_CONTENT' },
  { data: 'NEW_CONTENT' }
])

const colColumns = reactive([
  { data: 'MATERIAL' },
  { data: 'PLANT_CODE' },
  { data: 'PRODUCT_GROUP_A' },
  { data: 'PRODUCT_GROUP_B' },
  { data: 'PRODUCT_GROUP_C' },
  { data: 'PRODUCT_GROUP_D' },
  { data: 'PRODUCT_GROUP_E' }
])

const onUploadEnd = (params) => {
  searchReport1()
  if (params.body.errorList && params.body.errorList.length > 0) {
    pageCtl.errorListData = params.body.errorList
    pageCtl.errorListVisible = true
  }
}

const afterSave = (params) => {
  if (params.errorList && params.errorList.length > 0) {
    pageCtl.errorListData = params.errorList
    pageCtl.errorListVisible = true
  }
}

const searchReport1 = () => {
  reportTableRef.value.search()
}

const initPage = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/components/product_group/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(
    pageCtl.loading.report1 = false
  )
}

const search = () => {
  searchReport1()
}

onMounted(() => {
  initPage()
})
</script>

<style scoped>
.el-upload__tip p {
  margin-bottom: 2px !important
}
</style>
