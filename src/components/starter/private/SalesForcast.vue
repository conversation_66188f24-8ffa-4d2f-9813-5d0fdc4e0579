<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-header" style="padding: 10px 10px 15px 10px">
        <el-row>
          <el-col :span="3">
            <el-select v-model="pageCtl.selectedVersion" placeholder="Forcast Version"
                       :loading="pageCtl.fcstVersionLoading">
              <el-option
                  v-for="item in pageCtl.fcstVersion"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <scp-upload
                title="Sales FCST Upload"
                text="Upload Sales Forcast Data"
                w="700px"
                h="450px"
                upload-url='/demand/fcst/upload_fcst'
                :upload-params="{uploadVersion: pageCtl.uploadVersion}"
                :download-template-params="{ uploadVersion: pageCtl.uploadVersion }"
                download-template-url="/demand/fcst/download_template"
                :on-upload-end="initPage">
              <template #operation>
                <el-select v-model="pageCtl.uploadVersion" placeholder="Forcast Version" style="margin-left:6px">
                  <el-option
                      v-for="item in pageCtl.uploadVersions"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template #tip>
                <div class="el-upload__tip">
                  <p style="margin-bottom:5px">
                    <el-radio v-model="pageCtl.uploadMode" value="INSERT-IGNORE">
                      Import and <b>ignore</b> existing data &nbsp;&nbsp;&nbsp;&nbsp; [INSERT-IGNORE]
                    </el-radio>
                  </p>
                  <p style="margin-bottom:5px">
                    <el-radio v-model="pageCtl.uploadMode" value="MERGE">Import and
                      <b>merge</b> existing data &nbsp;&nbsp;&nbsp;&nbsp; [MERGE]
                    </el-radio>
                  </p>
                  <p style="margin-bottom:5px">
                    <el-radio v-model="pageCtl.uploadMode" value="REPLACE-ALL">
                      <b>Delete</b> existing data and re-import &nbsp;[REPLACE-ALL]
                    </el-radio>
                  </p>
                </div>
                <hr>
                <p>1. Please check the format of the file before uploading.</p>
                <p>2. This file will overwrite the data you uploaded before.</p>
                <p>3. You CANNOT access the data that does not belong to you.</p>
                <p>4. {{ pageCtl.privilegeTips }}.</p>
                <p>5. Your "Customer Code" can be "SECI-OG", "Plant-OG" or exact customer code.</p>
              </template>
            </scp-upload>
          </el-col>
        </el-row>
      </div>
      <div class="widget-body">
        <scp-table
            ref="forcastTable"
            :lazy="true"
            :params="{
              fcst_version: pageCtl.selectedVersion
            }"
            :columns="pageCtl.forcastTableColumns"
            :contextMenuItems="pageCtl.forcastTableMenuItems"
            :editable="false"
            url="/demand/fcst/query_fcst_data"
            download-url="/demand/fcst/download_fcst_data"
            :afterChange="forcastAfterChange"
            :page-sizes="[20, 50, 100, 200, 500]"
            :hiddenColumns="{columns: [0]}"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')

const forcastTable = ref()

const pageCtl = reactive(
  {
    tables: [],
    bindTo: '',
    uploadVersion: '',
    uploadVersions: [],
    privilegeTips: '',
    fcstVersionLoading: false,
    fcstVersion: [],
    uploadMode: 'MERGE',
    selectedVersion: '',
    forcastSourceUpdated: {},
    forcastTableColumns: [],
    forcastTableMenuItems: {
      save_changes: {
        name: () => {
          if (JSON.stringify(pageCtl.forcastSourceUpdated) === '{}') {
            return 'Save changes'
          }
          return '<b>Save changes</b>'
        },
        disabled: () => {
          return JSON.stringify(pageCtl.forcastSourceUpdated) === '{}'
        },
        callback: () => {
          saveForcastSource()
        }
      }
    }
  }
)

const initPage = () => {
  pageCtl.fcstVersionLoading = true
  $axios({
    method: 'post',
    url: '/demand/fcst/init_page'
  }).then((body) => {
    if (body.fcstVersion[0]) {
      pageCtl.selectedVersion = body.fcstVersion[0].value
      queryTableColumns()
    }
    if (body.authedSalesOrgs) {
      if (body.authedSalesOrgs === '*') {
        pageCtl.privilegeTips = 'You are the administrator and have access to all data'
      } else {
        pageCtl.privilegeTips = 'You have privileges to access the data of ' + body.authedSalesOrgs
      }
    } else {
      pageCtl.privilegeTips = 'You are not allowed to access any data, please contact your manager'
    }
    pageCtl.fcstVersion = body.fcstVersion
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.fcstVersionLoading = false
  })
}

const queryTableColumns = () => {
  const columns:any = []
  columns.push({
    data: 'ROW_ID'
  })
  columns.push({
    data: 'MATERIAL',
    title: 'Material'
  })
  columns.push({
    data: 'SALES_ORGANIZATION',
    title: 'Sales Organization'
  })
  columns.push({
    data: 'CUSTOMER_CODE',
    title: 'Customer Code'
  })
  columns.push({
    data: 'SALES_GROUP',
    title: 'Sales Group'
  })
  columns.push({
    data: 'NET_NET_PRICE_RMB',
    title: 'Net Net Price RMB',
    type: 'numeric',
    precision: 1
  })
  columns.push({
    data: 'NET_NET_PRICE_HKD',
    title: 'Net Net Price HKD',
    type: 'numeric',
    precision: 1
  })
  $axios({
    method: 'post',
    url: '/demand/fcst/query_fcst_columns',
    data: {
      fcst_version: pageCtl.selectedVersion
    }
  }).then((body) => {
    for (let i = 0; i < body.length; i++) {
      columns.push({
        data: body[i].data,
        title: body[i].title,
        type: 'numeric',
        render: renderNumber
      })
    }
    pageCtl.forcastTableColumns = columns
    pageCtl.forcastSourceUpdated = {}
    forcastTable.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const renderNumber = (hotInstance, td, row, column, prop, value) => {
  td.innerHTML = value
}

const saveForcastSource = () => {
  $axios({
    method: 'post',
    url: '/demand/fcst/save_forcast_source',
    data: {
      forcastSourceUpdated: pageCtl.forcastSourceUpdated
    }
  }).then(() => {
    $message.success('Changes Saved')
    setTimeout(() => {
      queryTableColumns()
    }, 300)
  }).catch((error) => {
    console.log(error)
  })
}
const forcastAfterChange = (changes) => {
  if (changes) {
    const ht = forcastTable.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const rowid = ht.getDataAtRow(row)[0]
        if (rowid) {
          let e = pageCtl.forcastSourceUpdated[rowid]
          if (typeof (e) === 'undefined') {
            e = {}
          }
          e[prop] = newValue
          pageCtl.forcastSourceUpdated[rowid] = e
        }
      }
    })
  }
}

onMounted(() => {
  initPage()

  const date = new Date()
  const version:any = []
  for (let startYear = date.getFullYear() - 1, startMonth = 1; startYear < date.getFullYear() || (startYear === date.getFullYear() && startMonth <= date.getMonth() + 1);) {
    version.push(startYear + '' + (startMonth < 10 ? '0' + startMonth : startMonth))
    if (startMonth < 12) {
      startMonth++
    } else {
      startYear++
      startMonth = 1
    }
  }
  pageCtl.uploadVersions = version.reverse()
  pageCtl.uploadVersion = pageCtl.uploadVersions[0]
})

watch(() => pageCtl.selectedVersion, (newVal, oldVal) => {
  if (oldVal && newVal) {
    queryTableColumns()
  }
})

</script>
