<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <scp-md-preview v-model="content" v-loading="loading" id="wechatBotView" style="overflow:hidden;" :style="{height: height + 'px'}"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import { inject, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const content = ref('<p style="font-size:10rem;text-align: center;padding-top:10%"><b>No Data</b></p>')
const loading = ref(false)
const height = ref(0)

const $route = useRoute()
const $axios: any = inject('$axios')

onMounted(() => {
  const tid = $route.query.tid
  const p = $route.query.p
  if (tid) {
    height.value = window.innerHeight - 80
    loading.value = true
    $axios({
      method: 'post',
      url: '/components/wechat_bot_presetation/query_report',
      data: {
        id: tid,
        params: p
      }
    }).then((body) => {
      if (body && body.endsWith('**No Data**') === false) {
        content.value = body
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      loading.value = false
    })
  }
})
</script>

<style lang="scss">
#wechatBotView {

}
</style>
