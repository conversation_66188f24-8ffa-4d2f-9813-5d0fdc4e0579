<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-header" style="height: 1.1rem;line-height: 1.1rem">
        <el-row>
          <el-col :span="3">
            <el-select v-model="pageCtl.selectedVersion" size="small" placeholder="SIOP Version" :loading="pageCtl.siopVersionLoading">
              <el-option
                  v-for="item in pageCtl.siopVersion"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <scp-upload
                title="Siop Raw Data Upload"
                text="Upload SIOP Raw Data"
                w="600px"
                h="250px"
                upload-url='/components/siop/upload_siop'
                :upload-params="{uploadVersion: pageCtl.uploadVersion}"
                download-template-url="/components/siop/download_template"
                :download-template-params="{ uploadVersion: pageCtl.uploadVersion }"
                :on-upload-end="initPage">
              <template #operation>
                <el-select v-model="pageCtl.uploadVersion" placeholder="Forcast Version" style="width: 150px ;margin-left:6px">
                  <el-option
                      v-for="item in pageCtl.uploadVersions"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template #tip>
                <p>1. Please check the format of the file before uploading.</p>
                <p>2. pageCtl file will overwrite the data you uploaded before(Current Version).</p>
              </template>
            </scp-upload>
          </el-col>
          <el-col :span="15"/>
          <el-col :span="2">
            <el-popconfirm :title="'确定刷新SIOP物化视图? 同一时间只能有一个用户执行刷新操作'"
                           iconColor="var(--scp-text-color-error)"
                           @confirm="refreshClick"
                           confirmButtonType="danger"
                           confirmButtonText='确定'
                           cancelButtonText='取消'>
              <template #reference>
                <el-button type="danger" style="width: 100%" :loading="pageCtl.refreshing">
                  Refresh SIOP
                </el-button>
              </template>
            </el-popconfirm>
          </el-col>
        </el-row>
      </div>
      <div class="widget-body">
        <scp-table
          ref="siopTableRef"
          :columns="pageCtl.siopTableColumns"
          :lazy="true"
          :params="_siopTableParams"
          url="/components/siop/query_siop_data"
          download-url="/components/siop/download_siop_data"
          save-url="/components/siop/save_siop_data"
          :hiddenColumns="{columns: [0]}"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'
const $message: any = inject('$message')
const $axios: any = inject('$axios')

const siopTableRef = ref()

const pageCtl = reactive({
  uploadVersion: '',
  uploadVersions: [],
  siopVersionLoading: false,
  refreshing: false,
  siopVersion: [],
  selectedVersion: '',
  siopTableColumns: []
})

watch(() => pageCtl.selectedVersion, (newVal, oldVal) => {
  if (oldVal && newVal) {
    queryTableColumns()
  }
})

const refreshClick = () => {
  pageCtl.refreshing = true
  $axios({
    method: 'post',
    url: '/components/siop/refresh_siop_data'
  }).then((body) => {
    $message.success('Successfully Refreshed SIOP View')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.refreshing = false
  })
}

const initPage = () => {
  pageCtl.siopVersionLoading = true
  $axios({
    method: 'post',
    url: '/components/siop/init_page'
  }).then((body) => {
    if (body.siopVersion[0]) {
      pageCtl.selectedVersion = body.siopVersion[0]
      queryTableColumns()
    }
    pageCtl.siopVersion = body.siopVersion
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.siopVersionLoading = false
  })
}

const queryTableColumns = () => {
  const columns = [] as any
  columns.push({ data: 'ROW_ID' })
  columns.push({ data: 'PRODUCT_LINE' })
  columns.push({ data: 'SIOP_SCOPE' })
  columns.push({ data: 'LOCAL_PRODUCT_LINE' })
  columns.push({ data: 'LOCAL_PRODUCT_FAMILY' })
  columns.push({ data: 'LOCAL_PRODUCT_SUB_FAMILY' })
  columns.push({ data: 'ACC_LABEL' })
  $axios({
    method: 'post',
    url: '/components/siop/query_siop_columns',
    data: {
      selectedVersion: pageCtl.selectedVersion
    }
  }).then((body) => {
    for (let i = 0; i < body.length; i++) {
      columns.push({
        data: body[i].data,
        title: body[i].title,
        type: 'numeric'
      })
    }
    pageCtl.siopTableColumns = columns
    siopTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  initPage()

  const date = new Date()
  const version = [] as any
  for (let startYear = date.getFullYear() - 1, startMonth = 1; startYear < date.getFullYear() || (startYear === date.getFullYear() && startMonth <= date.getMonth() + 1);) {
    version.push(startYear + '' + (startMonth < 10 ? '0' + startMonth : startMonth))
    if (startMonth < 12) {
      startMonth++
    } else {
      startYear++
      startMonth = 1
    }
  }
  pageCtl.uploadVersions = version.reverse()
  pageCtl.uploadVersion = pageCtl.uploadVersions[0]
})

const _siopTableParams = computed(() => {
  return {
    selectedVersion: pageCtl.selectedVersion
  }
})

</script>
<style scoped>
  .el-upload__tip p {
    margin-bottom: 2px !important
  }
</style>
