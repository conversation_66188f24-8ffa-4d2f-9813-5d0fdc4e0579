<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box" style="margin-bottom: 5px;">
          <el-col :span="5">
            <el-select v-model="pageCtl.selectedVersion" placeholder="Slice Version" :loading="pageCtl.sliceVersionLoading">
              <el-option
                  v-for="item in pageCtl.sliceVersion"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-upload
                title="Slice Raw Data Upload"
                text="Upload Slice Raw Data"
                w="600px"
                h="250px"
                upload-url='/components/slice/upload_slice'
                :upload-params="{uploadVersion: pageCtl.uploadVersion}"
                :download-template-params="{ uploadVersion: pageCtl.uploadVersion }"
                download-template-url="/components/slice/download_template"
                :on-upload-end="initPage">
              <template #operation>
                <el-select v-model="pageCtl.uploadVersion" placeholder="Forcast Version" style="margin-left:6px">
                  <el-option
                      v-for="item in pageCtl.uploadVersions"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template #tip>
                <p>1. Please check the format of the file before uploading.</p>
                <p>2. This file will overwrite the data you uploaded before.</p>
              </template>
            </scp-upload>
          </el-col>
        </el-row>
        <scp-table
            ref="sliceTableRef"
            :columns="pageCtl.sliceTableColumns"
            :lazy="true"
            :params="_sliceTableParams"
            url="/components/slice/query_manual_slice_data"
            download-url="/components/slice/download_manual_slice_data"
            save-url="/components/slice/save_manual_slice_data"
            :hiddenColumns="{columns: [0]}"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const sliceTableRef = ref()

const _sliceTableParams = computed(() => {
  return {
    selectedVersion: pageCtl.selectedVersion
  }
})

const pageCtl = reactive({
  uploadVersion: '',
  uploadVersions: [] as any,
  sliceVersionLoading: false,
  sliceVersion: [],
  selectedVersion: '',
  sliceTableColumns: [] as any
})

const initPage = () => {
  pageCtl.sliceVersionLoading = true
  $axios({
    method: 'post',
    url: '/components/slice/init_page'
  }).then((body) => {
    if (body.sliceVersion[0]) {
      pageCtl.selectedVersion = body.sliceVersion[0]
      queryTableColumns()
    }
    pageCtl.sliceVersion = body.sliceVersion
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.sliceVersionLoading = false
  })
}

const queryTableColumns = () => {
  const columns: any = []
  columns.push({ data: 'ROW_ID' })
  columns.push({ data: 'HFM_CODE' })
  columns.push({ data: 'CLASS' })
  columns.push({ data: 'PLANT_TYPE' })
  columns.push({ data: 'CURRENCY_TYPE' })
  columns.push({ data: 'CURRENCY' })
  $axios({
    method: 'post',
    url: '/components/slice/query_slice_columns',
    data: {
      selectedVersion: pageCtl.selectedVersion
    }
  }).then((body) => {
    for (let i = 0; i < body.length; i++) {
      columns.push({
        data: body[i].data,
        title: body[i].title,
        type: 'numeric'
      })
    }
    pageCtl.sliceTableColumns = columns
    sliceTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

watch(() => pageCtl.selectedVersion, (newVal, oldVal) => {
  if (oldVal && newVal) {
    queryTableColumns()
  }
})

onMounted(() => {
  initPage()

  const date = new Date()
  const version:string[] = []
  for (let startYear = date.getFullYear() - 1, startMonth = 1; startYear < date.getFullYear() || (startYear === date.getFullYear() && startMonth <= date.getMonth() + 1);) {
    version.push(startYear + '' + (startMonth < 10 ? '0' + startMonth : startMonth))
    if (startMonth < 12) {
      startMonth++
    } else {
      startYear++
      startMonth = 1
    }
  }
  pageCtl.uploadVersions = version.reverse()
  pageCtl.uploadVersion = pageCtl.uploadVersions[0]
})

</script>

<style scoped>
.el-upload__tip p {
  margin-bottom: 2px !important
}
</style>
