<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body" id="materialRisk">
        <el-row>
          <el-col :span="24" style="text-align: right;margin-bottom: 5px;padding-right:var(--scp-widget-margin)">
            <el-button @click="pageCtl.visible.save=true">
              <font-awesome-icon icon="plus"/>
            </el-button>
          </el-col>
        </el-row>
        <el-row v-show="pageCtl.rules.length === 0">
          <el-col :span="24">
            <h1 style="text-align: center;font-size: 1.2rem; color: var(--scp-text-color-lighter);">No Defined Rules</h1>
          </el-col>
        </el-row>
        <el-row v-show="pageCtl.rules.length > 0">
          <el-col :span="6" v-for="(item, index) in pageCtl.rules" :key="index"
                  style="padding-right: var(--scp-widget-margin);margin-bottom: var(--scp-widget-margin)">
            <el-card class="rule-card" disabled="disabled" :data-value="index + ''" shadow="hover" @dblclick="showModifyRuleWin(item.ID)"
                     draggable="true" @dragstart="onRuleDragStart" @dragover="onRuleDragOver" @drop="onRuleDragDrop">
              <el-row>
                <el-col :span="3" class="rule-card-no">
                  <b style="font-size: 0.85rem">{{ index + 1 }}</b>
                </el-col>
                <el-col :span="17" class="rule-card-body">
                  <div>
                    <p v-if="!!item.FILTER_CHAIN.rule1" :style="{width: pageCtl.cardWidth + 'px'}" class="rule-card-display">
                      {{ item.FILTER_CHAIN.rule1 }}
                    </p>
                    <div style="display: flex;align-items: center;">
                      <p v-if="!!item.FILTER_CHAIN.rule2" :style="{maxWidth: (!!item.FILTER_CHAIN.suffix? pageCtl.cardWidth - 20 : pageCtl.cardWidth) + 'px'}"
                         class="rule-card-display">
                        {{ item.FILTER_CHAIN.rule2 }}
                      </p>
                      <b v-if="!!item.FILTER_CHAIN.suffix">&nbsp;&nbsp;{{ item.FILTER_CHAIN.suffix }}</b>
                    </div>
                  </div>
                </el-col>
                <el-col :span="4" class="rule-card-level" style="background-color: var(--scp-bg-color-fill-lighter);">
                  <b style="padding-top: 8px;">{{ item.RISK_LEVEL }}</b>
                  <el-switch v-model="item['STATUS']" @change="modifyRuleStatus"/>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
        <hr>
        <el-row>
          <el-col :span="24" style="text-align: right;margin-bottom: 5px;padding-right:calc(var(--scp-widget-margin) - 3px)">
            <el-button @click="searchMaterial">
              <font-awesome-icon icon="search"/>
            </el-button>
            <el-button @click="recreateView" :loading="pageCtl.loading.recreate" v-show="false">
              <font-awesome-icon icon="refresh"/>
            </el-button>
          </el-col>
        </el-row>
        <scp-table
            ref="materialTable"
            :lazy="true"
            url="/system/material_risk/query_material_list_by_rule"
            download-url="/system/material_risk/download_material_list_by_rule"/>

        <scp-draggable-resizable w="1024px" h="520px" v-model="pageCtl.visible.save" :title="pageCtl.modifyId? 'Modify Rule' : 'Add Rule'"
                                 :save="saveRule" save-text="Save" :save-loading="pageCtl.loading.save"
                                 :delete="pageCtl.modifyId? deleteRule : undefined" :delete-loading="pageCtl.loading.delete">
          <div class="filter-cascader-win">
            <el-row style="height: 100%;">
              <el-col :span="12" class="left-slide">
                <el-row style="margin-bottom: 8px">
                  <el-col :span="18">
                    <el-select ref="fieldRef" v-model="pageCtl.prepare.fields" placeholder="筛选字段" style="width: calc(100% - 15px)" default-first-option
                               @change="clearDropdown"
                               filterable collapse-tags clearable multiple collapse-tags-tooltip :reserve-keyword="false" :multiple-limit="3">
                      <el-option
                          v-for="item in pageCtl.fieldOpts"
                          :key="item.name"
                          :label="item.name"
                          :value="item.name">
                        <span style="float: left">{{ item.name }}</span>
                        <span style="float: right;color: var(--scp-text-color-lighter);font-size: 0.4rem;">{{ item.type }}</span>
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="pageCtl.prepare.operator" placeholder="操作符" style="width: calc(100% - 15px)">
                      <el-option
                          v-for="item in _operatorOpts"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>

                <el-row style="margin-bottom: 8px" v-if="pageCtl.prepare.operator.indexOf('NULL') === -1">
                  <el-col :span="24">
                    <el-input v-model="pageCtl.prepare.text" v-if="pageCtl.prepare.operator==='IN' || pageCtl.prepare.operator==='NOT IN'" type="textarea"
                              :placeholder="pageCtl.valuePlaceHolder" @keydown="preventTextareaTab"
                              style="width: calc(100% - 15px);height: auto !important;" :rows="16"/>
                    <el-select v-model="pageCtl.prepare.value" v-if="pageCtl.prepare.operator!=='IN' && pageCtl.prepare.operator!=='NOT IN'" ref="valueRef"
                               :remote-method="(keyword) => changeSearch(keyword)" filterable clearable remote :loading="pageCtl.loading.value"
                               :remote-show-suffix="true" :allow-create="_fieldsTypes[0] === 'NUMBER'"
                               @focus="()=>focusSearch(null)" multiple :multiple-limit="800" collapse-tags
                               :placeholder="pageCtl.loading.value? 'Loading...' : pageCtl.valuePlaceHolder"
                               style="width: calc(100% - 15px);height: auto !important;">
                      <el-option
                          v-for="item in pageCtl.valueOpts"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                    <div class="clearfix"/>
                  </el-col>
                </el-row>

                <el-row style="padding-top: 8px">
                  <el-col :span="24" style="text-align: right;padding-right: 15px">
                    <el-button @click="addToRule">
                      <font-awesome-icon icon="search-plus"/>
                    </el-button>
                  </el-col>
                </el-row>

              </el-col>
              <el-col :span="12" class="right-slide">
                <div style="font-weight: bold;padding-bottom: 0.5rem;" v-show="pageCtl.filterChain.length > 0">
                  当满足下列条件时：
                </div>
                <div class="filter-cascader-card-box" :key="'parent_' + index" v-for="(item, index) in pageCtl.filterChain">
                  <div class="filter-cascader-card-box-and">
                    <span :class="item.joiner === 'AND'? 'and': 'or'" v-if="index > 0" @click="switchJoiner(item)">{{ item.joiner }}</span>
                    <!-- 如果当前element不是OR, 但是下一个是OR, 则显示括号 -->
                    <span class="bracket" v-if="item.nextOr && item.currentOr === false">(</span>
                    <el-card shadow="never" class="existing-card" @dblclick="prepareModify(item, index)" :data-value="index + ''"
                      draggable="true" @dragstart.stop="onFilterDragStart" @dragover.stop="onFilterDragOver" @drop.stop="onFilterDragDrop">
                      <div class="existing-card-content" v-html="displayFilter(item.fields, item.operator, item.value, item.text)"/>
                      <font-awesome-icon icon="times" class="existing-card-close" @click="()=>delFilter(index)"/>
                    </el-card>
                    <!-- 如果当前element是OR, 但是下一个不是OR, 则显示后半个括号 -->
                    <span class="bracket" v-if="item.nextOr === false && item.currentOr">)</span>
                  </div>
                </div>
                <div v-show="pageCtl.filterChain.length > 0">
                  <span style="font-weight: bold;padding-top: 0.5rem;">Material Shortage Risk 定义为：&nbsp;</span>
                  <el-select v-model="pageCtl.shortageRiskLevel" placeholder="Shortage Risk Level" filterable style="width: 115px">
                    <el-option
                        v-for="item in ['Low', 'Medium', 'High']"
                        :key="item"
                        :value="item"
                        :label="item"/>
                  </el-select>
                </div>
              </el-col>
            </el-row>
          </div>
        </scp-draggable-resizable>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpDraggableResizable from '@/components/starter/components/DraggableResizable.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $deepClone: any = inject('$deepClone')
const $message: any = inject('$message')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const materialTable = ref()
const fieldRef = ref()
const valueRef = ref()

const pageCtl = reactive({
  cardWidth: (document.body.clientWidth * 0.25 - 40) * 17 / 24,
  modifyId: '',
  filterChain: [],
  shortageRiskLevel: 'High',
  timeout: {},
  rules: [],
  visible: {
    save: false
  },
  loading: {
    save: false,
    value: false,
    modify: false,
    delete: false,
    recreate: false
  },
  fieldOpts: [
    { name: 'AVAILABILITY_CHECK', type: 'VARCHAR2' },
    { name: 'COV_RANGE_END_ORDER', type: 'VARCHAR2' },
    { name: 'EXISTING_IN_BOM', type: 'VARCHAR2' },
    { name: 'LT_RANGE', type: 'VARCHAR2' },
    { name: 'MATERIAL', type: 'VARCHAR2' },
    { name: 'NUM_OF_WHERE_USE_RANGE', type: 'VARCHAR2' },
    { name: 'PLANNING_ALIVE_STATUS', type: 'VARCHAR2' },
    { name: 'PLANT_CODE', type: 'VARCHAR2' },
    { name: 'RES_COV_STK_LA_RANGE', type: 'VARCHAR2' },
    { name: 'STARS_CODE', type: 'VARCHAR2' },
    { name: 'VENDOR_NAME', type: 'VARCHAR2' },
    { name: 'XBOARD', type: 'VARCHAR2' }
  ], // 字段的opts
  valueOpts: [], // 字段对应value的opts
  predModifyIndex: -1,
  prepare: {
    fields: [],
    operator: '' as String,
    value: [],
    text: ''
  },
  valuePlaceHolder: '请选择需要筛选的字段'
})

const initPage = () => {
  searchRules()
  searchMaterial()
}

// 添加条件至准备区
const addToRule = (enableWarning) => {
  if (enableWarning === undefined) {
    enableWarning = true
  }
  if (pageCtl.prepare.fields.length === 0) {
    if (enableWarning) {
      $message.error('请选择需要筛选的字段')
    }
    return
  }
  if (pageCtl.prepare.operator === '') {
    if (enableWarning) {
      $message.error('请选择操作符')
    }
    return
  }

  if (pageCtl.prepare.operator.indexOf('NULL') === -1) {
    if (pageCtl.prepare.operator.indexOf('IN') !== -1 && pageCtl.prepare.text === '') {
      if (enableWarning) {
        $message.error('请输入需要查询的值')
      }
      return
    } else if (pageCtl.prepare.operator.indexOf('IN') === -1 && pageCtl.prepare.value.length === 0) {
      if (enableWarning) {
        $message.error('请输入需要查询的值')
      }
      return
    }
  }

  const element = {
    fields: pageCtl.prepare.fields,
    types: $deepClone(_fieldsTypes.value),
    operator: pageCtl.prepare.operator,
    value: $deepClone(pageCtl.prepare.value),
    text: pageCtl.prepare.text
  }

  // 替换元素
  // 当pageCtl.predModifyIndex !== -1, 说明用户双击了修改卡片
  // pageCtl.predModifyIndex < pageCtl.filterChain.length, 判断数组是否越界, 没其他意思
  if (pageCtl.predModifyIndex !== -1 && pageCtl.predModifyIndex < pageCtl.filterChain.length) {
    pageCtl.filterChain[pageCtl.predModifyIndex].fields = element.fields
    pageCtl.filterChain[pageCtl.predModifyIndex].types = element.types
    pageCtl.filterChain[pageCtl.predModifyIndex].operator = element.operator
    pageCtl.filterChain[pageCtl.predModifyIndex].value = element.value
    pageCtl.filterChain[pageCtl.predModifyIndex].text = element.text

    pageCtl.predModifyIndex = -1
  } else {
    pageCtl.filterChain.push({
      joiner: 'AND',
      ...element
    })
  }

  rebuildFilterChain()

  pageCtl.prepare.fields = []
  pageCtl.prepare.value = []
  pageCtl.prepare.text = ''
  pageCtl.prepare.operator = ''
}

const rebuildFilterChain = () => {
  for (let i = 0; i < pageCtl.filterChain.length; i++) {
    const element = pageCtl.filterChain[i]
    element.currentOr = (element.joiner === 'OR')
    element.nextOr = false
    if (i < pageCtl.filterChain.length - 1) {
      const nextElemnt = pageCtl.filterChain[i + 1]
      element.nextOr = (nextElemnt.joiner === 'OR')
    }
  }
}

const switchJoiner = (item) => {
  if (item.joiner === 'AND') {
    item.joiner = 'OR'
  } else if (item.joiner === 'OR') {
    item.joiner = 'AND'
  }
  rebuildFilterChain()
}

const filterDragCtl = reactive({
  dragIndex: 0
})

const onFilterDragStart = (e) => {
  filterDragCtl.dragIndex = e.target.dataset.value
}

// 覆盖虚拟列的DragOver事件, 否则无法获取@drop事件
const onFilterDragOver = (e) => {
  e.preventDefault()
}

// 松开拖拽, 将前后两个列的index进行互换, 实现拖拽功能
const onFilterDragDrop = (e) => {
  let element = e.target
  let maxLevel = 5
  while (element.dataset.value === undefined && element.parentElement && (maxLevel--) > 0) {
    element = element.parentElement
  }
  if (element && element.dataset.value !== undefined) {
    const array = pageCtl.filterChain
    const targetIdx = parseInt(element.dataset.value)
    const currentIdx = parseInt(filterDragCtl.dragIndex)

    const targetElement = array[targetIdx]
    array[targetIdx] = array[currentIdx]
    array[currentIdx] = targetElement
  }

  if (pageCtl.filterChain.length > 0) {
    pageCtl.filterChain[0].joiner = 'AND'
    rebuildFilterChain()
  }
}

const searchRules = () => {
  $axios({
    method: 'post',
    url: '/system/material_risk/search_rules',
    data: {}
  }).then((body) => {
    pageCtl.rules = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.value = false
  })
}

const saveRule = () => {
  addToRule(false)
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/system/material_risk/save_rule',
    data: {
      id: pageCtl.modifyId,
      chain: JSON.stringify(pageCtl.filterChain),
      level: pageCtl.shortageRiskLevel
    }
  }).then(() => {
    pageCtl.visible.save = false
    searchRules()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const deleteRule = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/system/material_risk/delete_rule',
    data: {
      id: pageCtl.modifyId
    }
  }).then(() => {
    pageCtl.visible.save = false
    searchRules()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const clearDropdown = () => {
  fieldRef.value.dropMenuVisible = false
}

// 动态计算用户选中的Field对应的类型
const _fieldsTypes = computed(() => {
  const fields = pageCtl.prepare.fields
  const types = []
  for (let i = 0; i < fields.length; i++) {
    const element = pageCtl.fieldOpts.filter(e => e.name === fields[i]) as any
    if (element.length > 0) {
      types.push(element[0].type)
    } else {
      types.push('VARCHAR')
    }
  }
  return types
})

// 可用操作符根据用户填写的字段变化而变化
const _operatorOpts = computed(() => {
  const selectFields = pageCtl.prepare.fields as Array<string>
  if (selectFields.length > 1) {
    return [{
      label: '在列表中',
      value: 'IN'
    }, {
      label: '不在列表中',
      value: 'NOT IN'
    }]
  } else if (selectFields.length === 1) {
    const element = pageCtl.fieldOpts.filter(e => e.name === selectFields[0]) as any
    if (element.length > 0) {
      if (element[0].type === 'VARCHAR2') {
        return [{
          label: '等于',
          value: '='
        }, {
          label: '不等于',
          value: '!='
        }, {
          label: '在列表中',
          value: 'IN'
        }, {
          label: '不在列表中',
          value: 'NOT IN'
        }, {
          label: '包含',
          value: 'LIKE'
        }, {
          label: '不包含',
          value: 'NOT LIKE'
        }, {
          label: '以..开始',
          value: 'START WITH'
        }, {
          label: '为空',
          value: 'IS NULL'
        }, {
          label: '不为空',
          value: 'IS NOT NULL'
        }]
      } else if (element[0].type === 'NUMBER' || element[0].type === 'DATE') {
        return [{
          label: '等于',
          value: '='
        }, {
          label: '不等于',
          value: '!='
        }, {
          label: '在列表中',
          value: 'IN'
        }, {
          label: '不在列表中',
          value: 'NOT IN'
        }, {
          label: '大于',
          value: '>'
        }, {
          label: '大于等于',
          value: '>='
        }, {
          label: '小于',
          value: '<'
        }, {
          label: '小于等于',
          value: '<='
        }, {
          label: '为空',
          value: 'IS NULL'
        }, {
          label: '不为空',
          value: 'IS NOT NULL'
        }]
      }
    }
  }
  return []
})

// 用户选择的字段不同, 操作符默认值和提示信息也不同
watch(() => pageCtl.prepare.fields, (newVal) => {
  pageCtl.prepare.operator = ''
  pageCtl.prepare.value = []
  pageCtl.prepare.text = ''
  pageCtl.valueOpts = []
  newVal = newVal || []
  if (newVal.length === 1) {
    const element = pageCtl.fieldOpts.filter(e => e.name === newVal[0]) as any
    if (element.length > 0) {
      if (element[0].type === 'VARCHAR2') {
        pageCtl.valuePlaceHolder = '请输入查询内容'
      } else if (element[0].type === 'NUMBER') {
        pageCtl.valuePlaceHolder = '请输入合法数字, 否则将不会返回任何数据'
      } else if (element[0].type === 'DATE') {
        pageCtl.valuePlaceHolder = '请输入YYYY/MM/DD格式日期, 否则将不会返回任何数据'
      }
    }
    pageCtl.prepare.operator = '='
  } else if (newVal.length > 1) {
    pageCtl.prepare.operator = 'IN'
    pageCtl.valuePlaceHolder = '请输入一组数据, 多个数据列请使用TAB键分割'
  } else {
    pageCtl.valuePlaceHolder = '请选择需要筛选的字段'
  }
})

// 默认情况下, 在textarea中无法使用tab, 而我们多个查询条件需要用tab分割
// 所以用这个方法来实现tab的输入
const preventTextareaTab = (e) => {
  if (e && e.key === 'Tab') {
    pageCtl.prepare.text += '\t'
    if (e.preventDefault) {
      e.preventDefault()
    } else {
      window.event.returnValue = false
    }
  }
}

// 只有用户选择了VARCHAR和DATE类型的字段, 并且运算符不为IN的时候, 才可以启动联想
// 因为IN的textarea组件不支持联想功能
const changeSearch = (keywords) => {
  if (keywords) {
    focusSearch(keywords)
  }
}

const focusSearch = (keywords) => {
  if (pageCtl.prepare.fields.length === 1 && _fieldsTypes.value[0] !== 'NUMBER') {
    keywords = keywords || ''
    clearTimeout(pageCtl.timeout)
    pageCtl.loading.value = true
    pageCtl.timeout = setTimeout(() => {
      $axios({
        method: 'post',
        url: '/system/material_risk/query_suggestion_by_keywords',
        data: {
          keywords,
          field: pageCtl.prepare.fields[0],
          type: _fieldsTypes.value[0]
        }
      }).then((body) => {
        pageCtl.valueOpts = body || []
        // valueRef.value.dropMenuVisible = true
      }).catch((error) => {
        console.log(error)
      }).finally(() => {
        pageCtl.loading.value = false
      })
    }, 650)
  }
}

// 以相对友好的方式在准备区显示用户已经配置好的Filter
const displayFilter = (fields, operator, value, text) => {
  const result = []
  if (fields.length === 1) {
    result.push(fields[0])
  } else {
    result.push('(' + fields.join(',') + ')')
  }
  result.push('<span class="filter-cascader-highlight-operator">' + operator + '</span>')

  if (operator === 'IN' || operator === 'NOT IN') {
    let vs = text.split('\n').filter(e => !!e)
    let suffix = ''
    if (vs.length > 3) {
      suffix = ', +' + $thousandBitSeparator(vs.length - 3)
      vs = vs.slice(0, 3)
    }
    const temp = []
    for (let i = 0; i < vs.length; i++) {
      let vs2 = vs[i].split('\t')
      while (vs2.length < fields.length) {
        vs2.push('NULL')
      }
      if (vs2.length > fields.length) {
        vs2 = vs2.slice(0, fields.length)
      }
      if (vs2.length > 1) {
        temp.push('(' + vs2.join(',') + ')')
      } else {
        temp.push(vs2[0])
      }
    }
    result.push('(' + temp.join(',') + suffix + ')')
  } else {
    let vs = value
    let suffix = ''
    if (value.length > 3) {
      suffix = ', +' + $thousandBitSeparator(value.length - 3)
      vs = value.slice(0, 3)
    }
    result.push(vs.join(', ') + suffix)
  }
  return result.join(' ')
}

const prepareModify = (item, index) => {
  pageCtl.predModifyIndex = index
  nextTick(() => {
    pageCtl.prepare.fields = $deepClone(item.fields)
    nextTick(() => {
      pageCtl.prepare.operator = item.operator
      nextTick(() => {
        pageCtl.prepare.value = $deepClone(item.value)
        pageCtl.prepare.text = item.text + ''
      })
    })
  })
}

// 删除Filter中的某一个条目
const delFilter = (index) => {
  pageCtl.filterChain.splice(index, 1)
  rebuildFilterChain()
}

onMounted(() => {
  initPage()
  window.onresize = () => {
    pageCtl.cardWidth = (document.body.clientWidth * 0.25 - 40) * 17 / 24
  }
})

const ruleDragCtl = reactive({
  dragIndex: 0
})

const onRuleDragStart = (e) => {
  ruleDragCtl.dragIndex = e.target.dataset.value
}

// 覆盖虚拟列的DragOver事件, 否则无法获取@drop事件
const onRuleDragOver = (e) => {
  e.preventDefault()
}

// 松开拖拽, 将前后两个列的index进行互换, 实现拖拽功能
const onRuleDragDrop = (e) => {
  let element = e.target
  let maxLevel = 8
  while (element.dataset.value === undefined && element.parentElement && (maxLevel--) > 0) {
    element = element.parentElement
  }
  if (element && element.dataset.value !== undefined) {
    const array = pageCtl.rules
    const targetIdx = parseInt(element.dataset.value)
    const currentIdx = parseInt(ruleDragCtl.dragIndex)
    // 拖拽插入
    array.splice(targetIdx, 0, array.splice(currentIdx, 1)[0])
  }
  modifyRuleStatus()
}

const modifyRuleStatus = () => {
  $axios({
    method: 'post',
    url: '/system/material_risk/modify_rule_status',
    data: {
      rule: pageCtl.rules.map(e => {
        return { ID: e.ID, STATUS: e.STATUS }
      })
    }
  })
}

// 关闭窗口就清空
watch(() => pageCtl.visible.save, (newVal) => {
  if (newVal === false) {
    pageCtl.modifyId = ''
    pageCtl.filterChain = []
  }
})

const showModifyRuleWin = (id) => {
  if (pageCtl.loading.modify) {
    return
  }
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/system/material_risk/query_rule_by_id',
    data: {
      id
    }
  }).then((body) => {
    pageCtl.modifyId = id
    pageCtl.filterChain = body
    pageCtl.visible.save = true
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

const searchMaterial = () => {
  materialTable.value.search()
}

const recreateView = () => {
  pageCtl.loading.recreate = true
  $axios({
    method: 'post',
    url: '/system/material_risk/recreate_material_risk_level_view'
  }).then(() => {
    $message.success('View Recreated!')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.recreate = false
  })
}
</script>

<style lang="scss">
#materialRisk {
  .existing-card-tooltips {
    font-size: 0.45rem;
    font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
  }

  .filter-cascader-highlight-operator {
    color: #d73a49;
    font-weight: bold;
  }

  .filter-cascader-win {
    padding: 10px;
    height: calc(100% - 20px);
    overflow: auto;

    .left-slide {
      border-right: 1px dotted var(--scp-border-color-lighter);
      width: calc(100% - 400px);
      max-width: calc(100% - 400px);
      flex: none;
    }

    .right-slide {
      font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
      height: 100%;
      padding: 0 var(--scp-widget-margin);
      overflow: auto;
      width: 400px;
      max-width: 400px;
      flex: none;
    }

    .filter-cascader-card-box {
      display: flex;
      align-content: center;
      justify-content: left;
      flex-wrap: wrap;
      margin-bottom: 12px;
      cursor: pointer;

      .filter-cascader-card-box-or {
        justify-content: left;
        flex-wrap: wrap;
        display: flex;

        .existing-card {
          width: 280px;
        }

        .existing-card:not(:last-child) {
          margin-bottom: 5px;
        }
      }

      .filter-cascader-card-box-and {
        justify-content: space-between;
        flex-wrap: wrap;
        display: flex;

        .existing-card {
          width: 320px;
        }
      }

      .bracket {
        user-select: none;
        font-family: Consolas, Courier New, monospace;
        font-size: 1.3rem;
        line-height: 1;
      }

      .or, .and {
        user-select: none;
        color: #d73a49;
        line-height: 34px;
        text-align: center;
        font-weight: bold;
        padding: 0 5px;
      }

    }

    .existing-card {
      display: flex;

      .el-card__body {
        font-size: 0.45rem;
        padding: 5px;
        height: 24px;
        line-height: 24px;
        width: 100%;
        display: flex;
        align-items: center;

        .existing-card-content {
          width: calc(100% - 20px);
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .existing-card-close {
          color: var(--scp-text-color-lighter);
          width: 10px;
          cursor: pointer;
        }

        .existing-card-close:hover {
          color: var(--scp-text-color-error);
        }
      }
    }

    .el-select-tags-wrapper.has-prefix {
      margin-left: 4px !important;
    }
  }

  .rule-card {
    .el-card__body {
      padding: 0;

      .rule-card-no {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .rule-card-body {
        border-left: 1px dotted var(--scp-border-color-lighter);
        border-right: 1px dotted var(--scp-border-color-lighter);
        padding: var(--scp-widget-margin);
        height: 2.5rem;
        display: flex;
        align-items: center;
        cursor: pointer;

        .rule-card-display {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .rule-card-level {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
      }
    }
  }
}
</style>
