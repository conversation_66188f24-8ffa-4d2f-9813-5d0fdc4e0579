<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-header" style="padding: 10px 10px 15px 10px">
        <div style="width: 100%">
          <el-select v-model="pageCtl.selectedVersion" :placeholder="pageCtl.versionLoading? 'Loading...' : 'Report Version'"
                     :loading="pageCtl.versionLoading" style="width: 210px">
            <el-option
                v-for="item in pageCtl.reportVersion"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
          <scp-upload
              title="OTDS Official Report Upload"
              text="Upload OTDS Official Report"
              w="600px"
              h="250px"
              upload-url='/components/otds/upload_report'
              :upload-params="{uploadVersion: pageCtl.uploadVersion}"
              download-template-url="/components/otds/download_otds_template"
              :on-upload-end="initPage">
            <template #operation>
              <el-select v-model="pageCtl.uploadVersion" placeholder="Forcast Version" style="margin-left:6px;width: 120px">
                <el-option
                    v-for="item in pageCtl.uploadVersions"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </template>
            <template #tip>
              <p>1. Please check the format of the file before uploading.</p>
              <p>2. This file will overwrite the data you uploaded before.</p>
            </template>
          </scp-upload>
          <el-popconfirm title="确定立刻将数据同步至OTDS Report? 任务执行时, 请不要关闭窗口"
                         iconColor="var(--scp-text-color-error)"
                         @confirm="syncReport"
                         confirmButtonType="danger"
                         confirmButtonText='确定'
                         cancelButtonText='取消'>
            <template #reference>
              <el-button :loading="pageCtl.syncLoading" style="float: right;margin-left:10px;">
                <font-awesome-icon icon="sync"/>&nbsp;
                Sync Report Immediately
              </el-button>
            </template>
          </el-popconfirm>
        </div>
      </div>
      <div class="widget-body">
        <scp-table
            ref="reportTableRef"
            :lazy="true"
            :params="{
              report_version: pageCtl.selectedVersion
            }"
            :columns="pageCtl.reportColumns"
            :editable="false"
            url="/components/otds/query_otds_report_data"
            download-url="/components/otds/download_otds_data"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const reportTableRef = ref()

const pageCtl = reactive({
  uploadVersion: '',
  uploadVersions: [],
  versionLoading: false,
  reportVersion: [],
  selectedVersion: '',
  reportColumns: [
    { data: 'WEEK' },
    { data: 'SALES_ORDER_NUMBER' },
    { data: 'SALES_ORDER_ITEM' },
    { data: 'HIGHER_LEVEL_ITEM' },
    { data: 'STOCK_INDENT' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'ONTIME' },
    { data: 'REA_SOBLOCK' },
    { data: 'REA_PUR' },
    { data: 'REA_VENDOR' },
    { data: 'REA_INV' },
    { data: 'REA_GRPAGE' },
    { data: 'REA_DEL_CREN_BLOCK' },
    { data: 'REA_WAREHOUSE' },
    { data: 'REA_DEL_CREAT' },
    { data: 'REA_CUST_BLOCK' },
    { data: 'REA_FINANCE' },
    { data: 'REA_OTHER' },
    { data: 'REA_CUSTOVERBLK' },
    { data: 'REA_DEL_GIBLOCK' },
    { data: 'MATERIAL' },
    { data: 'ENTITY' },
    { data: 'ITEM_CATEGORY' },
    { data: 'MAT_PRC_GRP' },
    { data: 'PRODUCT_LINE' },
    { data: 'COMPLETE_DELIV_IND' },
    { data: 'DEL_GROUP' },
    { data: 'PLANT_CODE' },
    { data: 'SALES_DISTRICT' },
    { data: 'CUSTOMER_GROUP' },
    { data: 'VENDOR_CODE' },
    { data: 'PR_NUMBER' },
    { data: 'MO_NUMBER' },
    { data: 'SO04_CRD', type: 'date' },
    { data: 'CREATED_DATE', type: 'date' },
    { data: 'DELIVERY_RELEASE_DATE', type: 'date' },
    { data: 'SALES_ORDER_RELEASE', type: 'date' },
    { data: 'FIRST_DELIVERY_DATE', type: 'date' },
    { data: 'MATERIAL_AVAILIBALE_DATE', type: 'date' },
    { data: 'CONFIRMED_DATE', type: 'date' },
    { data: 'PURCHASE_GR_DATE', type: 'date' },
    { data: 'DELIVERY_CREATE_DATE', type: 'date' },
    { data: 'DELIVERY_PACKING_DATE', type: 'date' },
    { data: 'DELIVERY_PICKING_DATE', type: 'date' },
    { data: 'MO_CONFRIMED_FINISH_DATE', type: 'date' },
    { data: 'PURCHASE_CREATE_DATE', type: 'date' },
    { data: 'MO_SCHEDULE_FINISH_DATE', type: 'date' },
    { data: 'PO_STATISTIC_DATE', type: 'date' },
    { data: 'DELIVERY_GOODS_ISSUE_DATE', type: 'date' },
    { data: 'SOLD_TO' },
    { data: 'BASE_UNIT' },
    { data: 'NUM_DOC' },
    { data: 'ORDER_QTY' },
    { data: 'INVOICED_QUANTITY' },
    { data: 'INVOICED_AMOUNT' },
    { data: 'DELAY_DAYS' },
    { data: 'ORDER_TYPE' },
    { data: 'BUSINESS_UNIT' },
    { data: 'LOCAL_BUSINESS_UNIT' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'MRP_CONTROLLER' },
    { data: 'REGION' },
    { data: 'IMPORT_VENDOR' },
    { data: 'VENDOR_NAME' },
    { data: 'CLUSTER_NAME' },
    { data: 'GRA_TYPE' }
  ],
  syncLoading: false
})

const initPage = () => {
  pageCtl.versionLoading = true
  $axios({
    method: 'post',
    url: '/components/otds/init_page'
  }).then((body) => {
    if (body.reportVersion[0]) {
      if (pageCtl.selectedVersion === body.reportVersion[0].value) {
        setTimeout(() => {
          reportTableRef.value.search()
        }, 200)
      } else {
        pageCtl.selectedVersion = body.reportVersion[0].value
      }
    }
    pageCtl.uploadVersions = body.uploadVersions
    pageCtl.reportVersion = body.reportVersion
    pageCtl.uploadVersion = pageCtl.uploadVersions[0]
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.versionLoading = false
  })
}

const syncReport = () => {
  pageCtl.syncLoading = true
  $axios({
    method: 'post',
    url: '/components/otds/refresh_otds_official_report_mv'
  }).then((body) => {
    $message.success(body + '')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.syncLoading = false
  })
}

onMounted(() => {
  initPage()
})

watch(() => pageCtl.selectedVersion, () => {
  nextTick(() => {
    reportTableRef.value.search()
  })
})
</script>

<style scoped>
.el-upload__tip p {
  margin-bottom: 2px !important
}
</style>
