<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <div style="text-align:right;margin-bottom: 10px" v-show="_showBtn">
          <el-popconfirm title="确定需要执行计算吗, 这可能需要一点时间?"
                         iconColor="orange"
                         @confirm="calculate"
                         confirmButtonType="warning"
                         confirmButtonText='确定'
                         cancelButtonText='取消'
                         style="margin-left:5px">
            <template #reference>
              <el-button style="padding:5px 10px 5px 10px;" :type="pageCtl.calcResult" :loading="pageCtl.loading">
                <font-awesome-icon :icon="pageCtl.calcIcon" style="font-size: 80%"/>&nbsp;
                {{ pageCtl.settings['BUTTON_NAME'] }}
              </el-button>
            </template>

          </el-popconfirm>
        </div>
        <scp-datagrid ref="tableRef"
                      :bindTo="pageCtl.bindTo"
                      :lazy="true"
                      :enable-upload="pageCtl.settings['enableUpload']"
                      :upload-module="pageCtl.settings['uploadModule'] || ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA']"
                      :default-sort-column="pageCtl.settings['sortColumn'] || ''"
                      :default-sort-order="pageCtl.settings['sortOrder'] || ''"
                      :afterChanged="afterChanged || null"
                      :numericPrecision="pageCtl.settings['numericPrecision']"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const $route = useRoute()
const $router = useRouter()
const $message : any = inject('$message')
const $axios: any = inject('$axios')
const tableRef = ref()
const pageCtl = reactive({
  settings: {
    BUTTON_ACTION: null,
    BUTTON_NAME: null,
    enableUpload: false,
    uploadModule: ['INSERT-IGNORE', 'MERGE', 'REPLACE-ALL', 'REPLACE-MY-DATA'],
    sortColumn: '',
    sortOrder: '',
    numericPrecision: 0,
    ds: '02',
    actionUrl: ''
  },
  bindTo: '',
  loading: false,
  calcResult: '' as any,
  calcIcon: 'sync'
})

const initTables = () => {
  $axios({
    method: 'post',
    url: '/system/query_component_by_id',
    data: {
      id: $route.query.id
    }
  }).then((body) => {
    body.enableUpload = (body.enableUpload === 'Y')
    pageCtl.settings = body
    pageCtl.bindTo = body.bindTo
    nextTick(() => {
      tableRef.value.updateTable()
    })
  }).catch((error) => {
    console.log(error)
  })
}

const afterChanged = () => {
  if (pageCtl.settings.actionUrl) {
    $axios({
      method: 'post',
      url: pageCtl.settings.actionUrl
    }).then(() => {
      setTimeout(() => {
        tableRef.value.search()
      }, 1000)
    }).catch((error) => {
      console.log(error)
    })
  }
}

const calculate = () => {
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: pageCtl.settings.BUTTON_ACTION
  }).then(() => {
    $message.success('Job finished')
    pageCtl.calcResult = 'success'
    pageCtl.calcIcon = 'check'
    setTimeout(() => {
      tableRef.value.search()
    }, 1000)
  }).catch((error) => {
    pageCtl.calcResult = 'danger'
    pageCtl.calcIcon = 'exclamation'
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const _showBtn = computed(() => {
  return (!!pageCtl.settings.BUTTON_NAME) && (!!pageCtl.settings.BUTTON_ACTION)
})

onMounted(() => {
  if ($route.query.id) {
    initTables()
  } else {
    $router.push('/users/my_favourites')
  }
})
</script>
