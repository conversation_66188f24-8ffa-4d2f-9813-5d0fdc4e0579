<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-header" style="padding: 10px 10px 15px 10px">
        <div style="width: 100%">
          <el-select v-model="pageCtl.selectedVersion" :placeholder="pageCtl.versionLoading? 'Loading...' : 'Target Version'"
                     :loading="pageCtl.versionLoading" style="width: 210px">
            <el-option
                v-for="item in pageCtl.reportVersion"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
          <scp-upload
              title="CLO Target Upload"
              text="Upload CLO Target"
              ref="reportUploadRef"
              :show-btn="false"
              w="600px"
              h="250px"
              upload-url='/components/clo/upload_report'
              download-template-url="/components/clo/download_clo_template"
              :on-upload-end="initPage">
            <template #operation>
            </template>
            <template #tip>
              <p>1. Please check the file format before uploading.</p>
              <p>2. Uploading will only replace the data for year {{$dateFormatter(new Date(), 'yyyy')}}.</p>
            </template>
          </scp-upload>
        </div>
      </div>
      <div class="widget-body">
        <scp-table
            ref="reportTableRef"
            :lazy="true"
            :context-menu-items="pageCtl.contextItems"
            :params="{
              version: pageCtl.selectedVersion
            }"
            :columns="colColumns"
            save-url="/components/clo/save_clo_data"
            url="/components/clo/query_clo_report_data"
            download-url="/components/clo/download_clo_data"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')
const reportTableRef = ref()
const reportUploadRef = ref()

const showUploadWin = () => {
  reportUploadRef.value.showUploadWin()
}

const pageCtl = reactive({
  versionLoading: false,
  reportVersion: [],
  selectedVersion: '',
  contextItems: {
    view_details: {
      name: 'Upload CLO Target',
      callback: showUploadWin
    },
    view_split0: { name: '---------' }
  }
})

const initPage = () => {
  pageCtl.versionLoading = true
  $axios({
    method: 'post',
    url: '/components/clo/init_page'
  }).then((body) => {
    if (body.reportVersion[0]) {
      if (pageCtl.selectedVersion !== body.reportVersion[0]) {
        pageCtl.selectedVersion = body.reportVersion[0]
      } else {
        reportTableRef.value.search()
      }
    }
    pageCtl.reportVersion = body.reportVersion
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.versionLoading = false
  })
}

const colColumns = reactive([
  { data: 'MATERIAL' },
  { data: 'COMPETITIVE_LT_CRITERIA', type: 'numeric' },
  { data: 'COMMENTS' },
  { data: 'BEST_CLO_LT_IN_THE_MARKET_WD' },
  { data: 'NAME_OF_COMPETITOR_WITH_BEST_CLO_LT' },
  { data: 'CURRENT_CLO_LT' },
  { data: 'PLANT_CODE' }
])

onMounted(() => {
  initPage()
})

watch(() => pageCtl.selectedVersion, () => {
  nextTick(() => {
    reportTableRef.value.search()
  })
})
</script>

<style scoped>
.el-upload__tip p {
  margin-bottom: 2px !important
}
</style>
