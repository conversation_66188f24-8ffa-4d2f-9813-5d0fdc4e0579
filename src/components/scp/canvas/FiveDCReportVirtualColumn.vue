<!--
变量命名解释

1. 不可拆分的原子参数, 比如SOH, AMU, 变量命名为variant
2. 多个variant进行相加之后的结果, 变量命名为field, 在表达式中, field可以看作是最小操作单元
3. 操作符, 加减除这些, 变量命名为operator
4. 括号, (), 变量命名为bracket
5. field, operator, bracket统称为token

-->

<template>
  <!-- region 操作区-->
  <div class="five-dc-virtual-columns">
    <el-row style="margin-bottom: 8px">
      <el-col :span="15">
        <scp-cascader
            placeholder-text="Select one or more columns"
            :show-copy="false"
            style="width: calc(100% - 15px)"
            v-model="pageCtl.selectedVariant"
            :options="pageCtl.variantOpts"/>
      </el-col>
      <el-col :span="9">
        <el-button-group>
          <el-button @click="postField" :disabled="_isPostFieldDisabled">
            <font-awesome-icon :icon="['fas', 'paper-plane']"/>
          </el-button>
        </el-button-group>
        <el-divider direction="vertical"/>
        <el-button-group>
          <el-button :disabled="_isPostOperatorDisabled" @click="postOperator('+')"><b>+</b></el-button>
          <el-button :disabled="_isPostOperatorDisabled" @click="postOperator('-')"><b>-</b></el-button>
          <el-button :disabled="_isPostOperatorDisabled" @click="postOperator('/')"><b>/</b></el-button>
        </el-button-group>
        <el-divider direction="vertical"/>
        <el-button @click="postBracket" :disabled="_isPostBracketDisabled"><b>( )</b></el-button>
        <el-divider direction="vertical"/>
        <el-button @click="toggleCaseWhen" :type="pageCtl.caseWhenActive === 'Y'? 'primary': ''">
          <el-tooltip class="item" :show-after='500' effect="light" placement="bottom" content="使用Case When">
            <font-awesome-icon style="font-size: 0.45rem" :icon="['fas', 'tablets']"/>
          </el-tooltip>
        </el-button>
      </el-col>
    </el-row>
    <!-- endregion -->

    <div style="height: calc(100% - 220px)">
      <!-- region 条件块 -->
      <div v-show="pageCtl.caseWhenActive === 'Y'">
        <div class="five-dc-virtual-columns-card-box">
          <el-card shadow="never" style="border: none" class="existing-card">
            <b class="five-dc-virtual-columns-operator" style="font-size: 0.5rem">当满足&nbsp;<span style="font-size: 0.7rem">(</span></b>
          </el-card>
          <el-card v-for="(item, index) in pageCtl.postedConditions" :key="'field_' + index" shadow="never"
                   class="existing-card" :class="cardClass(item, index, 'postedConditions')"
                   data-type="postedConditions"
                   :data-value="index + ''"
                   :draggable="item.type===TYPE_FIELD"
                   @dragstart.stop="(e)=>onFieldDragStart(e,'postedConditions')"
                   @dragover.stop="onFieldDragOver"
                   @drop.stop="onFieldDragDrop">
            <el-tooltip class="item" :show-after='item.type===TYPE_FIELD? 1000: 100' effect="light" placement="bottom">
              <template #content>
                <span v-if="item.type===TYPE_FIELD" v-html="displayField(item)"/>
                <span v-else-if="item.type===TYPE_OPERATOR">单击切换符号</span>
                <span v-else-if="item.type===TYPE_BRACKET">单击删除括号</span>
              </template>
              <div v-if="item.type===TYPE_FIELD" class="existing-card-content" v-html="displayField(item)" @click="selectField(index, 'postedConditions')"/>
              <div v-else-if="item.type===TYPE_OPERATOR && index <  pageCtl.postedConditions.length - 1" class="existing-card-content no-delete-btn"
                   v-html="displayOperator(item)" @click="()=>toogleOperator(index, item, pageCtl.postedConditions)"/>
              <div v-else-if="item.type===TYPE_OPERATOR" class="existing-card-content" v-html="displayOperator(item)"/>
              <div v-else-if="item.type===TYPE_BRACKET" class="existing-card-content" v-html="displayBracket(item)"
                   @click="delBracket(index, pageCtl.postedConditions)"/>
            </el-tooltip>

            <span v-if="item.type===TYPE_FIELD">
              <font-awesome-icon icon="times" class="existing-card-close" @click="()=>delField(index, pageCtl.postedConditions)"/>
            </span>
            <span v-if="item.type===TYPE_OPERATOR && index ===  pageCtl.postedConditions.length - 1">
              <font-awesome-icon icon="times" class="existing-card-close" @click="()=>delOperator(index, pageCtl.postedConditions)"/>
            </span>
          </el-card>
          <el-card shadow="never" style="border: none" class="existing-card">
            <b class="five-dc-virtual-columns-operator" style="font-size: 0.5rem"><span style="font-size: 0.7rem">)</span>&nbsp;&nbsp;</b>
            <el-input
                v-model="pageCtl.caseWhenCompareValue"
                type="number"
                placeholder="Value"
                :style="{width: pageCtl.caseWhenComparer === 'IS NULL' || pageCtl.caseWhenComparer === 'IS NOT NULL'? '75px': '180px'}"
                :disabled="pageCtl.caseWhenComparer === 'IS NULL' || pageCtl.caseWhenComparer === 'IS NOT NULL'">
              <template #prepend>
                <el-select v-model="pageCtl.caseWhenComparer" placeholder="Comparer" style="width: 75px">
                  <el-option
                      v-for="item in [{
                        label: '=',
                        value: '='
                      }, {
                        label: '<>',
                        value: '!='
                      }, {
                        label: '>',
                        value: '>'
                      }, {
                        label: '>=',
                        value: '>='
                      }, {
                        label: '<',
                        value: '<'
                      }, {
                        label: '<=',
                        value: '<='
                      }, {
                        label: '为空',
                        value: 'IS NULL'
                      }, {
                        label: '不为空',
                        value: 'IS NOT NULL'
                      }]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-input>
          </el-card>
        </div>
        <div style="text-align: right">
          <el-check-tag :checked="pageCtl.editConditions" @change="activeEditor('condition')">
            <span v-html="pageCtl.editConditions? '正在编辑': '单击激活'"></span>
          </el-check-tag>
        </div>
        <el-divider border-style="dashed" content-position="left">时输出下列值</el-divider>
      </div>
      <!-- endregion -->

      <!-- region 输出块 -->
      <el-row style="margin-bottom: 8px">
        <el-col :span="24">
          <el-radio-group v-model="pageCtl.aggFunc" size="small">
            <el-radio-button label="sum" value="sum"/>
            <el-radio-button label="count" value="count"/>
            <el-radio-button label="avg" value="avg"/>
            <el-radio-button label="max" value="max"/>
            <el-radio-button label="min" value="min"/>
            <el-radio-button label="median" value="median"/>
            <el-radio-button label="std.dev." value="stddev"/>
          </el-radio-group>
        </el-col>
      </el-row>
      <div class="five-dc-virtual-columns-card-box">
        <el-card v-for="(item, index) in pageCtl.postedTokens" :key="'field_' + index" shadow="never"
                 class="existing-card" :class="cardClass(item, index, 'postedTokens')"
                 data-type="postedTokens"
                 :data-value="index + ''"
                 :draggable="item.type===TYPE_FIELD"
                 @dragstart.stop="(e)=>onFieldDragStart(e, 'postedTokens')"
                 @dragover.stop="onFieldDragOver"
                 @drop.stop="onFieldDragDrop">
          <el-tooltip class="item" :show-after='item.type===TYPE_FIELD? 1000: 100' effect="light" placement="bottom">
            <template #content>
              <span v-if="item.type===TYPE_FIELD" v-html="displayField(item)"/>
              <span v-else-if="item.type===TYPE_OPERATOR">单击切换符号</span>
              <span v-else-if="item.type===TYPE_BRACKET">单击删除括号</span>
            </template>
            <div v-if="item.type===TYPE_FIELD" class="existing-card-content" v-html="displayField(item)" @click="selectField(index, 'postedTokens')"/>
            <div v-else-if="item.type===TYPE_OPERATOR && index <  pageCtl.postedTokens.length - 1" class="existing-card-content no-delete-btn"
                 v-html="displayOperator(item)" @click="()=>toogleOperator(index, item, pageCtl.postedTokens)"/>
            <div v-else-if="item.type===TYPE_OPERATOR" class="existing-card-content" v-html="displayOperator(item)"/>
            <div v-else-if="item.type===TYPE_BRACKET" class="existing-card-content" v-html="displayBracket(item)"
                 @click="delBracket(index, pageCtl.postedTokens)"/>
          </el-tooltip>

          <span v-if="item.type===TYPE_FIELD">
            <font-awesome-icon icon="times" class="existing-card-close" @click="()=>delField(index, pageCtl.postedTokens)"/>
          </span>
          <span v-if="item.type===TYPE_OPERATOR && index ===  pageCtl.postedTokens.length - 1">
            <font-awesome-icon icon="times" class="existing-card-close" @click="()=>delOperator(index, pageCtl.postedTokens)"/>
          </span>
        </el-card>
        <el-card shadow="never" style="width: 200px;border: none" class="existing-card" v-if="pageCtl.postedTokens.length > 0">
          <b class="five-dc-virtual-columns-operator">AS</b>&nbsp;&nbsp;<el-input v-model="pageCtl.alias" placeholder="Alias"/>
        </el-card>
      </div>
      <div style="text-align: right" v-if="pageCtl.caseWhenActive === 'Y'">
        <el-check-tag :checked="pageCtl.editTokens" @change="activeEditor('value')">
          <span v-html="pageCtl.editTokens? '正在编辑': '单击激活'"></span>
        </el-check-tag>
      </div>
      <!-- endregion -->
    </div>

    <!-- region 表达式展示 -->
    <el-divider border-style="dashed" content-position="left">Debug</el-divider>
    <scp-ace-editor v-model="_displaySQL" :readonly="true" :wrap="true" lang="sql" :show-gutter="false" :show-line-numbers="false"
                    style="height: 160px; border: 1px solid var(--scp-border-color-lighter);"/>
    <!-- endregion -->
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $axios: any = inject('$axios')
const $randomString: any = inject('$randomString')

const TYPE_FIELD = 'field'
const TYPE_OPERATOR = 'operator'
const TYPE_BRACKET = 'bracket'

const pageCtl = reactive({
  variantOpts: [],
  selectedVariant: [],
  selectedFieldIndex: [],
  editConditions: false,
  editTokens: true,

  postedConditions: [],
  postedTokens: [],
  alias: '',
  caseWhenActive: 'N',
  caseWhenComparer: '=',
  caseWhenCompareValue: '',
  aggFunc: 'sum',
  id: $randomString(8)
})

const getPostedElements = () => {
  if (pageCtl.editTokens) {
    return pageCtl.postedTokens
  } else if (pageCtl.editConditions) {
    return pageCtl.postedConditions
  } else {
    return []
  }
}

const toggleCaseWhen = () => {
  if (pageCtl.caseWhenActive === 'Y') {
    pageCtl.caseWhenActive = 'N'
    activeEditor('value')
  } else {
    pageCtl.caseWhenActive = 'Y'
    activeEditor('condition')
  }
}

const postField = () => {
  if (pageCtl.selectedVariant.length > 0) {
    getPostedElements().push({
      type: TYPE_FIELD,
      value: pageCtl.selectedVariant
    })
    pageCtl.selectedVariant = []
    pageCtl.selectedFieldIndex = []
  }
}

const cardClass = (item, index, cardType) => {
  if (item.type === TYPE_OPERATOR) {
    return 'existing-card-operator'
  }
  if (item.type === TYPE_BRACKET) {
    return 'existing-card-bracket'
  }
  if (item.type === TYPE_FIELD && index <= Math.max(...pageCtl.selectedFieldIndex) && index >= Math.min(...pageCtl.selectedFieldIndex)) {
    if (cardType === 'postedTokens' && pageCtl.editTokens) {
      return 'highlight'
    }
    if (cardType === 'postedConditions' && pageCtl.editConditions) {
      return 'highlight'
    }
  }
  return ''
}

const postBracket = () => {
  const min = Math.min(...pageCtl.selectedFieldIndex)
  const max = Math.max(...pageCtl.selectedFieldIndex)
  getPostedElements().splice(min, 0, {
    type: TYPE_BRACKET,
    value: '('
  })
  getPostedElements().splice(max + 2, 0, {
    type: TYPE_BRACKET,
    value: ')'
  })
  pageCtl.selectedFieldIndex = []
}

const selectField = (index, fieldType) => {
  if ((pageCtl.editTokens && fieldType === 'postedTokens') || (pageCtl.editConditions && fieldType === 'postedConditions')) {
    const i = pageCtl.selectedFieldIndex.indexOf(index)
    if (i === -1) {
      pageCtl.selectedFieldIndex.push(index)
    } else {
      pageCtl.selectedFieldIndex.splice(i, 1)
    }
  }
}

const postOperator = (operator) => {
  getPostedElements().push({
    type: TYPE_OPERATOR,
    value: operator
  })
}

const delField = (index, elements) => {
  const emptyElement = { type: 'NONE', value: '' }
  const preElement = index > 0 ? elements[index - 1] : emptyElement
  const nextElement = index < elements.length - 1 ? elements[index + 1] : emptyElement

  // 不管怎么样都先删除被点击的元素, 使用deleteElement的目的是占位
  elements[index] = emptyElement

  if (preElement.type === TYPE_OPERATOR) { // 如果前一个字符是操作符, 则删除
    elements[index - 1] = emptyElement
  } else if (nextElement.type === TYPE_OPERATOR) { // 如果前一个字符不是操作符, 后一个是, 则删除后一个
    elements[index + 1] = emptyElement
  }

  // 统一删除空元素
  let i = elements.length
  while (i--) {
    if (elements[i].type === 'NONE') {
      elements.splice(i, 1)
    }
  }
  rebuildElements(pageCtl.postedTokens)
  rebuildElements(pageCtl.postedConditions)
}

const delOperator = (index, elements) => {
  elements.splice(index, 1)
}

const delBracket = (index, elements) => {
  const currentBracket = elements[index].value
  let index2 = -1
  let cnt = 1
  if (currentBracket === ')') {
    // 如果用户点击的是), 按照index向前找, 找到一个) +1, 找到一个( -1, 当结果为0的时候, 即结束
    for (let i = index - 1; i >= 0; i--) {
      if (elements[i].type === TYPE_BRACKET) {
        if (elements[i].value === ')') {
          cnt++
        } else if (elements[i].value === '(') {
          cnt--
        }
      }
      if (cnt === 0) {
        index2 = i
        break
      }
    }
  } else if (currentBracket === '(') {
    // 如果用户点击的是), 按照index向后找, 找到一个( +1, 找到一个) -1, 当结果为0的时候, 即结束
    for (let i = index - 1; i >= 0; i--) {
      if (elements[i].type === TYPE_BRACKET) {
        if (elements[i].value === ')') {
          cnt--
        } else if (elements[i].value === '(') {
          cnt++
        }
      }
      if (cnt === 0) {
        index2 = i
        break
      }
    }
  }
  elements.splice(index, 1)
  elements.splice(index2, 1)
}

const rebuildElements = (elements) => {
  let deleted = false
  // 删除()这种括号布局的
  let i = 0
  while (i < elements.length - 1) {
    if (elements[i].type === TYPE_BRACKET && elements[i + 1].type === TYPE_BRACKET && elements[i].value === '(' && elements[i + 1].value === ')') {
      elements.splice(i, 2) // 删除当前元素和下一个元素
      deleted = true
    } else {
      i++ // 如果当前元素和下一个元素不相等，继续遍历
    }
  }

  // 删除两个符号相邻的
  i = 0
  while (i < elements.length - 1) {
    if (elements[i].type === TYPE_OPERATOR && elements[i + 1].type === TYPE_OPERATOR) {
      elements.splice(i, 1) // 删除当前元素和下一个元素
      deleted = true
    } else {
      i++ // 如果当前元素和下一个元素不相等，继续遍历
    }
  }

  if (elements.length > 0 && elements[0].type === TYPE_OPERATOR) {
    elements.splice(i, 1) // 删除第一个操作符
    deleted = true
  }

  // 清空用户选择
  pageCtl.selectedFieldIndex = []

  // 递归调用, 直到数组中无法再删除异常元素为止
  while (deleted) {
    deleted = rebuildElements(elements)
  }

  return deleted
}

const toogleOperator = (index, item, elements) => {
  if (item.value === '+') {
    elements[index].value = '-'
  } else if (item.value === '-') {
    elements[index].value = '/'
  } else if (item.value === '/') {
    elements[index].value = '+'
  }
}

const displayField = (element) => {
  const display = []
  for (const item of element.value) {
    display.push(item[1])
  }
  return display.join(' <b class="five-dc-virtual-columns-operator">+</b> ')
}

const displayOperator = (element) => {
  return '<b class="five-dc-virtual-columns-operator">' + element.value + '</b>'
}

const displayBracket = (element) => {
  return '<b class="five-dc-virtual-columns-operator">' + element.value + '</b>'
}
const columnDragCtl = reactive({
  dragIndex: 0,
  sourceType: ''
})

const onFieldDragStart = (e, sourceType) => {
  columnDragCtl.dragIndex = e.target.dataset.value
  columnDragCtl.sourceType = sourceType
}

// 覆盖虚拟列的DragOver事件, 否则无法获取@drop事件
const onFieldDragOver = (e) => {
  e.preventDefault()
}

// 松开拖拽, 将前后两个列的index进行互换, 实现拖拽功能
const onFieldDragDrop = (e) => {
  let element = e.target
  let maxLevel = 5
  while (element.dataset.value === undefined && element.parentElement && (maxLevel--) > 0) {
    element = element.parentElement
  }
  if (element && element.dataset.value !== undefined) {
    const targetArray = pageCtl[element.dataset.type]
    const sourceArray = pageCtl[columnDragCtl.sourceType]

    const targetIdx = parseInt(element.dataset.value)
    const sourceIdx = parseInt(columnDragCtl.dragIndex)

    if (targetArray[targetIdx].type === TYPE_FIELD) {
      const element = targetArray[targetIdx]
      targetArray[targetIdx] = sourceArray[sourceIdx]
      sourceArray[sourceIdx] = element
    }
  }
}

const activeEditor = (editorType) => {
  if (editorType === 'value') {
    pageCtl.editTokens = !pageCtl.editTokens
    pageCtl.editConditions = !pageCtl.editTokens
  } else if (editorType === 'condition') {
    pageCtl.editConditions = !pageCtl.editConditions
    pageCtl.editTokens = !pageCtl.editConditions
  }
  pageCtl.selectedFieldIndex = []
}

const _isPostBracketDisabled = computed(() => {
  return pageCtl.selectedFieldIndex.length < 2
})

const _isPostFieldDisabled = computed(() => {
  if (pageCtl.selectedVariant.length === 0) {
    return true
  }
  if (getPostedElements().length === 0) {
    return false
  }
  return getPostedElements()[getPostedElements().length - 1].type !== TYPE_OPERATOR
})

const _isPostOperatorDisabled = computed(() => {
  if (getPostedElements().length === 0) {
    return true
  }
  return getPostedElements()[getPostedElements().length - 1].type === TYPE_OPERATOR
})

const _displaySQL = computed(() => {
  const sql = []
  sql.push(pageCtl.aggFunc.toUpperCase() + '(')
  if (pageCtl.caseWhenActive === 'Y' && pageCtl.postedConditions.length > 0) {
    sql.push('CASE WHEN')
    for (let i = 0; i < pageCtl.postedConditions.length; i++) {
      if (i === pageCtl.postedConditions.length - 1 && pageCtl.postedConditions[i].type === TYPE_OPERATOR) {
        continue
      }
      const c = pageCtl.postedConditions[i].value
      if (typeof c === 'string') {
        sql.push(c)
      } else if (typeof c === 'object') {
        const temp = c.map(e => '"' + e[1] + '"')
        if (temp.length > 1) {
          sql.push('(' + temp.join(' + ') + ')')
        } else {
          sql.push(temp[0])
        }
      }
    }
    sql.push(pageCtl.caseWhenComparer)
    if (pageCtl.caseWhenComparer !== 'IS NOT NULL' && pageCtl.caseWhenComparer !== 'IS NULL') {
      sql.push('\'' + pageCtl.caseWhenCompareValue + '\'')
    }
    sql.push('THEN')
  }
  if (pageCtl.postedTokens.length > 0) {
    for (let i = 0; i < pageCtl.postedTokens.length; i++) {
      if (i === pageCtl.postedTokens.length - 1 && pageCtl.postedTokens[i].type === TYPE_OPERATOR) {
        continue
      }
      const c = pageCtl.postedTokens[i].value
      if (typeof c === 'string') {
        sql.push(c)
      } else if (typeof c === 'object') {
        const temp = c.map(e => '"' + e[1] + '"')
        if (temp.length > 1) {
          sql.push('(' + temp.join(' + ') + ')')
        } else {
          sql.push(temp[0])
        }
      }
    }
  }
  sql.push(')')
  sql.push('AS')
  if (pageCtl.alias) {
    sql.push('"' + pageCtl.alias + '"')
  } else {
    if (pageCtl.postedTokens.length === 1 && pageCtl.postedTokens[0].value.length === 1) {
      sql.push('"' + pageCtl.postedTokens[0].value[0][1] + '"')
    } else {
      sql.push('"ALIAS"')
    }
  }

  let result = sql.join(' ')
  result = result.replace(/\( /g, '(')
  result = result.replace(/ \)/g, ')')

  if ((pageCtl.caseWhenActive === 'N' || pageCtl.postedConditions.length === 0) && pageCtl.postedTokens.length === 0) {
    return ''
  } else {
    return result
  }
})

onMounted(() => {
  $axios({
    method: 'post',
    url: '/canvas/five_dc_report/init_page'
  }).then((body) => {
    pageCtl.variantOpts = body.report1ColumnOpts
  })
})

const getOptions = () => {
  return {
    id: pageCtl.id,
    conditions: pageCtl.postedConditions,
    tokens: pageCtl.postedTokens,
    alias: pageCtl.alias,
    caseWhenActive: pageCtl.postedConditions.length === 0 ? 'N' : pageCtl.caseWhenActive,
    caseWhenComparer: pageCtl.caseWhenComparer,
    caseWhenCompareValue: pageCtl.caseWhenCompareValue,
    aggFunc: pageCtl.aggFunc,
    sql: _displaySQL.value + ''
  }
}

const setOptions = (options) => {
  pageCtl.id = options.id
  pageCtl.postedConditions = options.conditions || []
  pageCtl.postedTokens = options.tokens || []
  pageCtl.alias = options.alias || ''
  pageCtl.caseWhenActive = options.caseWhenActive || 'N'
  pageCtl.caseWhenComparer = options.caseWhenComparer || '='
  pageCtl.caseWhenCompareValue = options.caseWhenCompareValue || ''
  pageCtl.aggFunc = options.aggFunc || 'sum'
}

const resetOptions = () => {
  pageCtl.id = $randomString(8)
  pageCtl.postedConditions = []
  pageCtl.postedTokens = []
  pageCtl.alias = ''
  pageCtl.caseWhenActive = 'N'
  pageCtl.caseWhenComparer = '='
  pageCtl.caseWhenCompareValue = ''
  pageCtl.aggFunc = 'sum'
}

defineExpose({
  getOptions,
  setOptions,
  resetOptions
})
</script>

<script lang="ts">
export default {
  name: 'FiveDcReportVirtualColumn'
}
</script>

<style lang="scss">
.five-dc-virtual-columns {
  width: 100%;
  height: 100%;

  .el-divider--horizontal {
    margin: 0 0 calc(var(--scp-widget-margin) * 2) 0 !important;

    .el-divider__text {
      color: #d73a49 !important;
      font-weight: bold !important;
    }
  }

  .el-check-tag {
    background-color: transparent !important;
    font-size: 0.45rem !important;
    font-weight: normal !important;
  }
}

.five-dc-virtual-columns-operator {
  user-select: none;
  color: #d73a49 !important;
  margin: 0 2px !important;
  text-align: center;
  font-weight: bold;
}

.five-dc-virtual-columns-card-box {
  display: flex;
  justify-content: left;
  align-content: flex-start;
  flex-wrap: wrap;
  margin-bottom: 12px;
  gap: 8px 0;

  .existing-card-operator {
    width: auto !important;
    border: none;

    .existing-card-content {
      width: 100% !important;
    }

    .five-dc-virtual-columns-operator {
      font-size: 0.7rem;
    }
  }

  .existing-card-bracket {
    width: 10px !important;
    border: none;

    .el-card__body {
      padding: 2px 0 !important;
    }

    .existing-card-content {
      width: 100% !important;
    }

    .five-dc-virtual-columns-operator {
      font-size: 0.7rem;
    }
  }

  .existing-card.highlight {
    background-color: var(--scp-bg-color-highlight) !important;
    color: #fff !important;
  }

  .existing-card {
    display: flex;
    max-width: 240px;
    height: 28px;
    cursor: pointer;

    .el-card__body {
      .el-tooltip__trigger {
        width: 100%;
      }

      font-size: 0.45rem;
      padding: 2px 8px;
      height: 24px;
      line-height: 24px;
      width: 100%;
      display: flex;
      align-items: center;

      .existing-card-content {
        user-select: none;
        max-width: 216px;
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .existing-card-close {
        color: var(--scp-text-color-lighter);
        width: 10px;
        cursor: pointer;
        margin-left: 4px;
      }

      .existing-card-close:hover {
        color: var(--scp-text-color-error);
      }
    }
  }
}
</style>
