<template>
  <div class="left-sidebar" id="fiveDCReport">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['MATERIAL_MASTER_V']" :after-apply="()=>search()"/>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.valueType" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Moving Average Price', 'Net Net Price']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['report1Date', 'report2DateRange']"/>

            <el-button @click="()=>{pageCtl.visible.report1ColumnConfig = !pageCtl.visible.report1ColumnConfig}"
                       size="small" style="margin-left: 10px" v-if="pageCtl.isAdmin">
              <font-awesome-icon icon="fa-sliders"/>
            </el-button>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container setting-form" :style="{height: ($store.state.pageHeight - 90) + 'px'}">
              <scp-subscript id="FDCRC" ref="fdcrcRef"/>
              <div class="front">
                <el-col>
                  <el-col :span="24" v-show="pageCtl.visible.tabCtl==='Table'" class="report1-table">
                    <scp-table
                        ref="report1TableRef"
                        url="/canvas/five_dc_report/query_report1"
                        download-url="/canvas/five_dc_report/download_report1"
                        :fixed-columns-left="Math.min(_report1PivotBy.length + 1, 6)"
                        :after-change="afterReport1TableChange"
                        :precision="0"
                        :auto-title="false"
                        :columns="pageCtl.report1TableColumn"
                        :params="pageCtl.conditions"
                        :after-select="report1TableSelected"
                        :context-menu-items="pageCtl.report1TableContextItems"
                        :lazy="true"/>
                  </el-col>

                  <el-col :span="24" v-loading="pageCtl.loading.report1TreeTable" v-show="pageCtl.visible.tabCtl==='Tree Table'">
                    <el-table
                        :data="pageCtl.report1TreeTableData.data"
                        style="width: 100%;"
                        row-key="id"
                        lazy
                        :load="report1TreeTableLoad"
                        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                        @sort-change="report1TreeTableSortChange"
                        @row-dblclick="report1TreeTableRowDblick"
                        class="tree-table">
                      <el-table-column prop="category" :label="_report1PivotBy[0]">
                        <template v-slot="scope">
                          <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                            {{ scope.row.category }}
                          </el-tooltip>
                        </template>
                      </el-table-column>
                      <el-table-column v-for="item in pageCtl.report1TreeTableData.columns" :key="item"
                                       :prop="item" :label="item" sortable :sort-method="(a, b) => report1TreeTableSort(a, b, item)">
                        <template v-slot="scope">
                          <div style="text-align: right" v-contextmenu:report1TreeTableContext @contextmenu.prevent="report1TreeTableRightClick"
                               :data-value="item + ',' + scope.row.category + ',' + scope.row.parent.join(',')">
                            {{ $thousandBitSeparator(scope.row[item], 0) }}
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>

                  <el-col :span="16" v-loading="pageCtl.loading.report1PieChart" v-show="pageCtl.visible.tabCtl==='Pie Chart'">
                    <chart ref="report1PieChartRef" :height="400" :option="_report1PieChartOptions"/>
                  </el-col>

                  <el-col :span="16" v-loading="pageCtl.loading.report1TreemapChart" v-show="pageCtl.visible.tabCtl==='Treemap Chart'">
                    <chart ref="report1TreemapChartRef" :height="400" :option="_report1TreemapChartOptions"/>
                  </el-col>

                  <el-divider/>

                  <el-col :span="24" v-loading="pageCtl.loading.report2">
                    <chart ref="report2Ref" :height="400" :option="_report2Options"/>
                  </el-col>
                </el-col>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box" style="width: calc(90% - 16px)">
                  <div class="box-title">Specific Date Report Settings</div>
                  <el-row>
                    <el-col :span="24" class="content">
                      <el-radio-group v-model="pageCtl.conditions.report1ReportType">
                        <el-radio-button value="Table">Table</el-radio-button>
                        <el-radio-button value="Tree Table">Tree Table</el-radio-button>
                        <el-radio-button value="Pie Chart">Pie Chart</el-radio-button>
                        <el-radio-button value="Treemap Chart">Treemap Chart</el-radio-button>
                      </el-radio-group>
                      <span class="input-tips">结果希望展示的形式</span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-date-picker
                          style="width: 308px"
                          v-model="pageCtl.conditions.report1Date"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          type="date"
                          :clearable="false"
                          placeholder="Date">
                      </el-date-picker>
                      <span class="input-tips">查看某天的数据</span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-select v-model="pageCtl.conditions.report1PivotBy" placeholder="Pivot By" multiple :multiple-limit="16"
                                 collapse-tags filterable clearable collapse-tags-tooltip style="width: 308px">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                      <span class="input-tips">Pivot By</span>
                    </el-col>
                    <el-col :span="20" class="content">
                      <div style="width: 100%;min-height: 32px;overflow: auto;"
                           :style="{border: pageCtl.conditions.report1Table.selectedVirtualColumns.length > 0 ? 'none' : '1px dashed var(--scp-border-color-lighter)',
                                maxHeight: virtualColumnCtl.visible.expand ? '9999px' : '240px'}">
                        <el-card shadow="never" class="column-card" v-for="(item, index) in pageCtl.conditions.report1Table.selectedVirtualColumns"
                                 :key="item.id"
                                 @dblclick="editVirtualColumn(item, index)"
                                 draggable="true" @dragstart="onVirtualColumnDragStart" @dragover="onVirtualColumnDragOver" @drop="onVirtualColumnDragDrop"
                                 :data-value="index + ''"
                                 :class="pageCtl.conditions.report1ReportType.indexOf(' Chart') !== -1 && item.id===pageCtl.conditions.report1ChartValueId? 'active-virtual-column': ''"
                                 :title="pageCtl.conditions.report1ReportType.indexOf(' Chart') !== -1 && item.id===pageCtl.conditions.report1ChartValueId? '在Pie/Treemap Chart模式下, 只有第一列会被展示, 单击选择需要显示的列': undefined">
                          <div class="column-card-content" v-html="item.sql" @click="selectChartValue(item.id)"/>
                          <font-awesome-icon icon="times" class="column-card-close" @click="()=>delVirtualColumn(index)"/>
                        </el-card>
                      </div>
                    </el-col>
                    <el-col :span="4" class="content">
                      <span class="input-tips">
                        显示列 ({{ pageCtl.conditions.report1Table.selectedVirtualColumns.length }})
                        <el-tooltip :show-after="1000" effect="light" placement="bottom" content="新增展示列">
                          <font-awesome-icon icon="add" class="column-card-add" @click="showCreateVirtualColumnWin"/>
                        </el-tooltip>
                        <el-tooltip :show-after="1000" effect="light" placement="bottom" content="新增展示列">
                          <font-awesome-icon :icon="virtualColumnCtl.visible.expand? ['fas', 'chevron-up']: ['fas', 'chevron-down']"
                                             class="column-card-add" @click="()=>virtualColumnCtl.visible.expand = !virtualColumnCtl.visible.expand"/>
                        </el-tooltip>
                      </span>
                    </el-col>
                  </el-row>
                  <div class="box-title">Trend Report Settings</div>
                  <el-row>
                    <el-col :span="24" class="content">
                      <div style="width: 320px;float:left">
                        <el-date-picker
                            v-model="pageCtl.conditions.report2DateRange"
                            type="daterange"
                            unlink-panels
                            style="width: calc(308px - 10px)"
                            :clearable="false"
                            range-separator="to"
                            format="YYYY/MM/DD"
                            value-format="YYYY/MM/DD"
                            start-placeholder="Start month"
                            end-placeholder="End month">
                        </el-date-picker>
                      </div>
                      <span class="input-tips">查看某段时间的数据</span>
                    </el-col>
                    <el-col :span="20" class="content">
                      <el-select v-model="pageCtl.conditions.report2SelectedValues" placeholder="Display Values" multiple :multiple-limit="16"
                                 collapse-tags filterable clearable style="width: 318px">
                        <el-option
                            v-for="item in pageCtl.conditions.report1Table.selectedVirtualColumns"
                            :key="item.id"
                            :label="item.alias || item.tokens[0].value[0].join(' / ')"
                            :value="item.id">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="4" class="content">
                      <span class="input-tips">趋势图最多显示16组数据</span>
                    </el-col>
                  </el-row>
                  <el-divider/>
                  <div style="text-align: right;margin-right:5px;">
                    <el-button @click="fdcrcRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="fdcrcRef.toggleView();search()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 编辑虚拟列 -->
    <scp-draggable-resizable w="1000px" h="640px" v-model="virtualColumnCtl.visible.creation" :title="virtualColumnCtl.winMode  + ' Display Column'"
                             :save="saveVirtualColumn" draft-text="Save As" :draft="virtualColumnCtl.winMode==='Modify'? saveAsVirtualColumn : undefined">
      <template v-slot="{height}">
        <div style="padding: 5px;" :style="{height: (height - 100) + 'px'}">
          <five-dc-report-virtual-column ref="virtualColumnsRef"/>
        </div>
      </template>
    </scp-draggable-resizable>

    <!-- 维护自定义列 -->
    <scp-draggable-resizable w="90vw" h="680px" v-model="pageCtl.visible.report1ColumnConfig" title="Column Settings">
      <el-row style="margin-bottom: 8px">
        <el-col :span="24">
          <scp-datagrid bindTo="FIVE_DC_COLUMN_CONFIG" :default-sort-column="['TABLE_NAME','DISPLAY_COLUMN']" :max-height="500" :page-sizes="[500]"/>
        </el-col>
      </el-row>
    </scp-draggable-resizable>

    <v-contextmenu ref="report1TreeTableContext">
      <v-contextmenu-item @click="viewReport1TableDetails">
        View Details
      </v-contextmenu-item>
      <v-contextmenu-item @click="downloadReport1TreeTable">
        Download this page
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import ScpDraggableResizable from '@/components/starter/components/DraggableResizable.vue'
import ScpTable from '@/components/starter/components/Table.vue'
import ScpSearch from '@/components/starter/components/Search.vue'
import ScpDatagrid from '@/components/starter/components/Datagrid.vue'
import { ViewDetailConfig } from '@/assets/js/function'
import { useStore } from 'vuex'
import FiveDcReportVirtualColumn from '@/components/scp/canvas/FiveDCReportVirtualColumn.vue'

const $axios: any = inject('$axios')
const $store = useStore()
const $renderColumnName: any = inject('$renderColumnName')
const $deepClone: any = inject('$deepClone')
const $message: any = inject('$message')
const $downloadFile: any = inject('$downloadFile')
const $randomString: any = inject('$randomString')
const $viewDetails: any = inject('$viewDetails')
const $shortenNumber: any = inject('$shortenNumber')
const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $toolbox: any = inject('$echarts.toolbox')

const virtualColumnsRef = ref()
const report1TableRef = ref()
const report1TreeTableContext = ref()
const report1PieChartRef = ref()
const report1TreemapChartRef = ref()
const searchRef = ref()
const fdcrcRef = ref()
const report2Ref = ref()

// region 控制自定义列的新增/删除/拖拽

const virtualColumnCtl = reactive({
  winMode: '', // 弹出窗模式 Add or Modify
  dragIndex: '', // 正在拖拽的虚拟列的index
  editIndex: 0, // 正在编辑的虚拟列的index
  creationOpts: {
    value1: [],
    operator: ['+', '-', '/']
  }, // 创建虚拟列所需的opts
  visible: {
    creation: false,
    expand: false
  } // 控制元素可见性
})

// 根据配置的内容, 生成虚拟列
const validateInput = () => {
  const options = virtualColumnsRef.value.getOptions()
  if (options.tokens.length === 0) {
    $message.error('请选择需要显示的列')
    return false
  }
  if ((options.tokens.length > 2 || options.tokens[0].value.length > 1) && (!options.alias.trim())) {
    $message.error('请为复杂字段指定一个别名')
    return false
  }

  return true
}

// modify & save
const saveVirtualColumn = () => {
  if (validateInput()) {
    if (virtualColumnCtl.winMode === 'Add') {
      saveAsVirtualColumn()
    } else {
      pageCtl.conditions.report1Table.selectedVirtualColumns[virtualColumnCtl.editIndex] = virtualColumnsRef.value.getOptions()
      virtualColumnCtl.visible.creation = false
    }
  }
}

// save as
const saveAsVirtualColumn = () => {
  if (validateInput()) {
    pageCtl.conditions.report1Table.selectedVirtualColumns.push(virtualColumnsRef.value.getOptions())
    virtualColumnsRef.value.resetOptions()
    if (virtualColumnCtl.winMode === 'Modify') {
      virtualColumnCtl.visible.creation = false
    }
  }
}

// 删除虚拟列
const delVirtualColumn = (index) => {
  if (pageCtl.conditions.report1Table.selectedVirtualColumns.length === 1) {
    $message.error('不能删除最后一列')
    return
  }

  // 如果删除掉的这一列, 正好是需要显示的列, 那么需要重新赋值一列
  const deleteID = pageCtl.conditions.report1Table.selectedVirtualColumns[index].id
  if (deleteID === pageCtl.conditions.report1ChartValueId) {
    for (let i = 0; i < pageCtl.conditions.report1Table.selectedVirtualColumns.length; i++) {
      if (pageCtl.conditions.report1Table.selectedVirtualColumns[i].id !== deleteID) {
        pageCtl.conditions.report1ChartValueId = pageCtl.conditions.report1Table.selectedVirtualColumns[i].id
        break
      }
    }
  }

  pageCtl.conditions.report1Table.selectedVirtualColumns.splice(index, 1)
}

// 编辑已存在的虚拟列
const editVirtualColumn = (item, index) => {
  virtualColumnCtl.winMode = 'Modify'
  virtualColumnCtl.editIndex = index
  virtualColumnsRef.value.setOptions(item)
  virtualColumnCtl.visible.creation = true
}

const selectChartValue = (id) => {
  pageCtl.conditions.report1ChartValueId = id
}

// 弹出创建虚拟列的窗口
const showCreateVirtualColumnWin = () => {
  virtualColumnCtl.winMode = 'Add'
  virtualColumnsRef.value.resetOptions()
  virtualColumnCtl.visible.creation = true
}

// 拖动虚拟列时, 记录拖动的列, 在当前列表中的index
const onVirtualColumnDragStart = (e) => {
  virtualColumnCtl.dragIndex = e.target.dataset.value
}

// 覆盖虚拟列的DragOver事件, 否则无法获取@drop事件
const onVirtualColumnDragOver = (e) => {
  e.preventDefault()
}

// 松开拖拽, 将前后两个列的index进行互换, 实现拖拽功能
const onVirtualColumnDragDrop = (e) => {
  let element = e.target
  let maxLevel = 5
  while (element.dataset.value === undefined && element.parentElement && (maxLevel--) > 0) {
    element = element.parentElement
  }
  if (element && element.dataset.value !== undefined) {
    const array = pageCtl.conditions.report1Table.selectedVirtualColumns
    const targetIdx = parseInt(element.dataset.value)
    const currentIdx = parseInt(virtualColumnCtl.dragIndex)
    array.splice(targetIdx, 0, array.splice(currentIdx, 1)[0])
  }
}

// endregion

// region report1Table
const viewReport1TableDetails = () => {
  if (_pivotColumns.value.indexOf(pageCtl.conditions.report1Table.selectedCell.displayColumn) !== -1) {
    $message.error('请选择有值的列进行view details')
    return
  }
  $viewDetails({
    url: '/canvas/five_dc_report/query_report1_table_details',
    durl: '/canvas/five_dc_report/download_report1_table_details',
    params: pageCtl.conditions,
    title: 'View Details [' + pageCtl.conditions.report1Table.selectedCell.values.join(', ') + ']'
  } as ViewDetailConfig)
}

const downloadReport1TreeTable = () => {
  $downloadFile('/canvas/five_dc_report/download_report1_tree_table', pageCtl.conditions)
}

const report1TableSelected = (r, rc, c) => {
  pageCtl.conditions.report1Table.selectedCell.displayColumn = c
  const s = []
  for (const i in _report1PivotBy.value) {
    s.push(r[_report1PivotBy.value[i]])
  }
  pageCtl.conditions.report1Table.selectedCell.values = s
}

const afterReport1TableChange = (changes) => {
  if (changes) {
    const ht = report1TableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'COMMENTS') {
          const kl = []
          const list = $deepClone(_report1PivotBy.value)
          list.sort((e1, e2) => e1.localeCompare(e2))
          for (let i = 0; i < list.length; i++) {
            kl.push(r[list[i]])
          }
          const key = kl.join('#')
          pageCtl.report1TableCommentsUpdate[key] = newValue
        }
      }
    })
  }
}

const saveReport1TableComments = () => {
  $axios({
    method: 'post',
    url: '/canvas/five_dc_report/save_report1_comments',
    data: {
      update: pageCtl.report1TableCommentsUpdate
    }
  }).then(() => {
    pageCtl.report1TableCommentsUpdate = {}
    $message.success('Comments saved.')
  })
}

// endregion

// region report1TreeTable
const report1TreeTableSortCache = {}
const report1TreeTableSortChange = (e) => {
  report1TreeTableSortCache[e.prop] = e.order
}

const report1TreeTableRowDblick = (row) => {
  // 双击treetable的时候, 需要先重置view details保存的内容
  pageCtl.conditions.report1Table.selectedCell = {
    displayColumn: '',
    values: []
  }
  pageCtl.conditions.report1TreeTableSelectedValues = $deepClone(row.parent) || []
  pageCtl.conditions.report1TreeTableSelectedValues.push(row.category)
  searchReport2(true)
}

const report1TreeTableSort = (a, b, category) => {
  const beforeOrder = report1TreeTableSortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const report1TreeTableRightClick = (e) => {
  const cs = e.target.dataset.value.split(',')
  pageCtl.conditions.report1Table.selectedCell.displayColumn = cs.shift()
  const t = cs.shift()
  const s = []
  for (const i in _report1PivotBy.value) {
    if (cs[i]) {
      s.push(cs[i])
    }
  }
  s.push(t)
  pageCtl.conditions.report1Table.selectedCell.values = s
}

const report1TreeTableLoad = (
  row: any,
  treeNode: any,
  resolve: (date: any[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/five_dc_report/query_report1_tree_table_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}
// endregion

// region report1PieChart
const _report1PieChartOptions = computed(() => {
  const legend = pageCtl.report1PieChartData.map(e => e.name)
  return {
    title: {
      text: pageCtl.report1PieChartTitle
    },
    legend: $legend({ type: 'pie', data: legend }),
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div style="width:7.5rem;">')
        tip.push(params.marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value, 1))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span></div>')
        return tip.join('')
      }
    },
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data: pageCtl.report1PieChartData
      }
    ]
  }
})
// endregion

// region report1Treemap
const _report1TreemapChartOptions = computed(() => {
  return {
    title: {
      text: pageCtl.report1TreemapChartTitle
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip: any = []
        tip.push('<div style="width: 11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')
        const treePathInfo = params.treePathInfo || []
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            tip.push('<div>')
            tip.push('Share [' + parentNode.name + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc; margin-top: 5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: 'Total',
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: 1,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1TreemapChartData,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})
// endregion

// region report2
const _report2Options = computed(() => {
  const series = []
  for (const y in pageCtl.report2Data.yAxis) {
    series.push({
      name: y,
      type: 'line',
      smooth: false,
      data: pageCtl.report2Data.yAxis[y]
    })
  }
  return {
    title: {
      text: pageCtl.report2ChartTitle
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:12.5rem;">')
          tip.push(params[i].marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value, 1))
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({}),
    grid: $grid(),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report2Data.xAxis,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        scale: true,
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }
    ],
    series
  }
})

const viewReport2Details = () => {
  // 右键view details的时候, 需要先重置treetable保存的内容
  pageCtl.conditions.report1TreeTableSelectedValues = []
  searchReport2(true)
}
// endregion

const pageCtl = reactive({
  isAdmin: false,
  filterOpts: [],
  report1TableCommentsUpdate: {},
  report1TableColumn: [],
  report1TableContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport1TableDetails,
      disabled: function () {
        return _pivotColumns.value.indexOf(pageCtl.conditions.report1Table.selectedCell.displayColumn) !== -1
      }
    },
    view_charts: {
      name: 'View charts',
      callback: viewReport2Details
    },
    save_comments: {
      name: function () {
        if (Object.keys(pageCtl.report1TableCommentsUpdate).length === 0) {
          return 'Save comments'
        }
        return '<b>Save comments</b>'
      },
      disabled: function () {
        return Object.keys(pageCtl.report1TableCommentsUpdate).length === 0
      },
      callback: saveReport1TableComments
    },
    view_split0: { name: '---------' }
  },
  report1TreeTableData: {
    columns: [],
    data: []
  },
  report1PieChartData: [],
  report1PieChartTitle: '',
  report1TreemapChartData: [],
  report1TreemapChartTitle: '',
  report2Data: {
    xAxis: [],
    yAxis: {}
  },
  report2ChartTitle: '',
  visible: {
    allColumn: false,
    report1ColumnConfig: false,
    tabCtl: ''
  },
  loading: {
    report1TreeTable: false,
    report1PieChart: false,
    report1TreemapChart: false,
    report2: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    report1ChartValueId: '',
    report1ReportType: 'Table',
    report1PivotBy: ['CLUSTER_NAME', 'ENTITY', 'BU', 'LOCAL_BU'],
    report2DateRange: [],
    report2SelectedValues: ['jai46afk'],
    valueType: 'Moving Average Price',
    report1Date: '',
    report1Table: {
      selectedVirtualColumns: [
        {
          id: 'jai46afk',
          conditions: [],
          tokens: [
            {
              type: 'field',
              value: [
                [
                  'Inventory',
                  'SOH'
                ]
              ]
            }
          ],
          alias: '',
          caseWhenActive: 'N',
          caseWhenComparer: '=',
          caseWhenCompareValue: '',
          aggFunc: 'sum',
          sql: 'SUM("SOH") AS "SOH"'
        }
      ],
      selectedCell: {
        displayColumn: '',
        values: []
      } // 用户当前选中的单元格, 用来view details
    },
    report1TreeTableSelectedValues: []
  }
})

onMounted(() => {
  initPage()
  searchRef.value.loadAndClick()
})

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)

  pageCtl.conditions.report1Date = $dateFormatter(now, 'yyyy/MM/dd')
  pageCtl.conditions.report2DateRange = [$dateFormatter(start, 'yyyy/MM/dd'), $dateFormatter(end, 'yyyy/MM/dd')]

  if (pageCtl.conditions.report1ChartValueId === '' && pageCtl.conditions.report1Table.selectedVirtualColumns.length > 0) {
    pageCtl.conditions.report1ChartValueId = pageCtl.conditions.report1Table.selectedVirtualColumns[0].id
  }

  $axios({
    method: 'post',
    url: '/canvas/five_dc_report/init_page'
  }).then((body) => {
    pageCtl.isAdmin = body.isAdmin
    pageCtl.filterOpts = body.filterOpts
    virtualColumnCtl.creationOpts.value1 = body.report1ColumnOpts
  })
}

const search = () => {
  // 点击查询时, 清空选择
  pageCtl.conditions.report1Table.selectedCell = {
    displayColumn: '',
    values: []
  }
  pageCtl.conditions.report1TreeTableSelectedValues = []
  searchReport1()
  searchReport2()
}

const searchReport1 = () => {
  pageCtl.visible.tabCtl = pageCtl.conditions.report1ReportType
  pageCtl.visible.report1ColumnConfig = false
  nextTick(() => {
    searchReport1Action()
  })
}

const searchReport1Action = () => {
  if (pageCtl.conditions.report1ReportType === 'Table') {
    const tableColumns = []
    for (let i = 0; i < _report1PivotBy.value.length; i++) {
      tableColumns.push({
        data: _report1PivotBy.value[i],
        title: $renderColumnName(_report1PivotBy.value[i]),
        type: 'text'
      })
    }
    tableColumns.push({
      data: 'COMMENTS',
      title: 'Comments',
      type: 'text'
    })
    for (let i = 0; i < pageCtl.conditions.report1Table.selectedVirtualColumns.length; i++) {
      const col = pageCtl.conditions.report1Table.selectedVirtualColumns[i].alias || pageCtl.conditions.report1Table.selectedVirtualColumns[i].tokens[0].value[0][1]
      tableColumns.push({
        data: col,
        title: $renderColumnName(col),
        type: 'numeric'
      })
    }
    pageCtl.report1TableColumn = tableColumns

    report1TableRef.value.search()
  } else if (pageCtl.conditions.report1ReportType === 'Tree Table') {
    pageCtl.loading.report1TreeTable = true
    $axios({
      method: 'post',
      url: '/canvas/five_dc_report/query_report1',
      data: pageCtl.conditions
    }).then((body) => {
      pageCtl.report1TreeTableData.columns = body.columns
      pageCtl.report1TreeTableData.data = body.data
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report1TreeTable = false
    })
  } else if (pageCtl.conditions.report1ReportType === 'Pie Chart') {
    pageCtl.loading.report1PieChart = true
    $axios({
      method: 'post',
      url: '/canvas/five_dc_report/query_report1',
      data: pageCtl.conditions
    }).then((body) => {
      pageCtl.report1PieChartData = body
      const id = pageCtl.conditions.report2SelectedValues[0]
      const s = pageCtl.conditions.report1Table.selectedVirtualColumns.filter(e => e.id === id)[0]
      const subTitle = s.alias || s.tokens[0].value[0][1]
      pageCtl.report1PieChartTitle = 'Share of [' + subTitle.trim() + '] by [' + _report1PivotBy.value[0] + ']'
      nextTick(() => {
        report1PieChartRef.value.resize()
      })
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report1PieChart = false
    })
  } else if (pageCtl.conditions.report1ReportType === 'Treemap Chart') {
    pageCtl.loading.report1TreemapChart = true
    $axios({
      method: 'post',
      url: '/canvas/five_dc_report/query_report1',
      data: pageCtl.conditions
    }).then((body) => {
      pageCtl.report1TreemapChartData = body
      const id = pageCtl.conditions.report2SelectedValues[0]
      const s = pageCtl.conditions.report1Table.selectedVirtualColumns.filter(e => e.id === id)[0]
      const subTitle = s.alias || s.tokens[0].value[0][1]
      pageCtl.report1TreemapChartTitle = 'Share of [' + subTitle.trim() + ']'
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      setTimeout(() => {
        report1TreemapChartRef.value.resize()
        pageCtl.loading.report1TreemapChart = false
      }, 1200)
    })
  }
}

const searchReport2 = (viewDetails?) => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/five_dc_report/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    if (body) {
      pageCtl.report2Data = body
      const id = pageCtl.conditions.report2SelectedValues[0]
      const s = pageCtl.conditions.report1Table.selectedVirtualColumns.filter(e => e.id === id)[0]
      const subTitle = s.alias || s.tokens[0].value[0][1]
      let subsubtile = ' '
      if (viewDetails) {
        let selectedCell = pageCtl.conditions.report1Table.selectedCell.values
        if (selectedCell.length === 0) {
          selectedCell = pageCtl.conditions.report1TreeTableSelectedValues
        }
        if (selectedCell.length > 1) {
          subsubtile = ' [' + (selectedCell[0] || ' ') + ', +' + (selectedCell.length - 1) + ']'
        } else {
          subsubtile = ' [' + (selectedCell[0] || ' ') + ']'
        }
      }
      if (pageCtl.conditions.report2SelectedValues.length > 1) {
        pageCtl.report2ChartTitle = 'Trends of [' + subTitle + ', +' + (pageCtl.conditions.report2SelectedValues.length - 1) + ']' + subsubtile
      } else {
        pageCtl.report2ChartTitle = 'Trends of [' + subTitle + ']' + subsubtile
      }
    } else {
      pageCtl.report2Data = {
        xAxis: [],
        yAxis: {}
      }
      pageCtl.report2ChartTitle = ''
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'MATERIAL', 'VENDOR_NAME')
})

const _report1PivotBy = computed(() => {
  if (pageCtl.conditions.report1PivotBy.length > 0) {
    return pageCtl.conditions.report1PivotBy
  } else {
    return ['ENTITY']
  }
})

watch(() => pageCtl.conditions.report1Table.selectedVirtualColumns, (newVal) => {
  const availible = newVal.map(e => e.id)
  pageCtl.conditions.report2SelectedValues = pageCtl.conditions.report2SelectedValues.filter(e => availible.includes(e))
}, {
  deep: true
})
</script>

<style lang="scss">
#fiveDCReport {
  .report1-table {
    .wtHolder {
      position: static !important;
    }
  }

  .column-card {
    display: inline-flex;
    margin-right: 5px;
    margin-bottom: 5px;
    max-width: 49%;
    cursor: pointer;

    .el-card__body {
      font-size: 0.45rem;
      padding: 5px 8px;
      width: 100%;
      display: flex;
      align-items: center;

      .filter-cascader-highlight-operator {
        color: #d73a49;
        font-weight: bold;
      }

      .filter-cascader-highlight-field {
      }

      .column-card-close {
        color: var(--scp-text-color-lighter);
        width: 20px;
        cursor: pointer;
      }

      .column-card-close:hover {
        color: var(--scp-text-color-error);
      }

      .column-card-content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        word-break: break-all;
      }
    }
  }

  .setting-form {
    .content {
      display: flex;
      align-items: flex-start;
    }

    .content {
      margin-bottom: 10px;
    }

    .el-row {
      margin-bottom: 12px;
    }

    .input-tips {
      padding-left: 10px;
      font-style: italic;
      color: var(--scp-text-color-secondary);

      .column-card-add {
        width: 20px;
        cursor: pointer;
      }

      .column-card-add:hover {
        color: var(--scp-text-color-highlight);
      }
    }
  }

  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    border-bottom: 0;

    table {
      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              margin-left: 10px;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__tags {
                max-width: calc(100% - 20px) !important;

                span {
                  .el-tag:nth-child(2) {
                    font-size: .4rem;
                    color: var(--scp-text-color-secondary);
                  }

                  .el-tag {
                    max-width: calc(100% - 18px) !important;
                    padding: 0 !important;
                    margin-left: 0 !important;
                    font-size: 0.45rem;
                    color: var(--scp-text-color-primary);

                    .el-tag__close {
                      display: none !important;
                    }
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-input__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }

  .active-virtual-column {
    background-color: var(--scp-bg-color-highlight);
    color: #fff;
  }
}
</style>
