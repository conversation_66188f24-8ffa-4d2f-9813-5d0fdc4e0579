<template>
  <div class="left-sidebar" id="myStory">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.componentList" size="small" multiple collapse-tags collapse-tags-tooltip filterable clearable>
              <el-option
                  v-for="item in pageCtl.componentOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="MATERIAL_MASTER_FILTER_V"
                        :filter-base="['MATERIAL_MASTER_V']" :after-apply="()=>search()"/>
          </el-col>
          <el-col :span="6">
            <el-button-group>
              <el-button @click="()=>search()">
                <font-awesome-icon icon="search"/>
              </el-button>
              <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Story Management">
                <el-button @click="openStoryVariant">
                  <font-awesome-icon icon="code-branch"/>
                </el-button>
              </el-tooltip>
              <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Story Catalog" v-if="_storyActived">
                <el-button @click="openStoryCatalog">
                  <font-awesome-icon icon="comment-dots"/>
                </el-button>
              </el-tooltip>
            </el-button-group>
            <span v-if="_storyActived===false" class="story-title">No Story Selected</span>
            <span v-else class="story-title">
              Current Story: {{ pageCtl.currentStory }}
            </span>
          </el-col>
        </el-row>

        <el-drawer v-model="pageCtl.visible.menu" :size="420" :show-close="false" class="my-story-menu" v-if="_storyActived">
          <template #header>
            <div style="text-align: center">
              {{ pageCtl.currentStory }}
            </div>
          </template>
          <el-card shadow="hover" @click="goToAnchor('myStory')" style="font-size: 1rem;text-align: center" class="my-story-top-card">
            <font-awesome-icon icon="fa-solid fa-turn-up"/>
          </el-card>
          <el-card shadow="hover" @click="goToAnchor('myStoryComponent_' + item.PAGE_ID)" v-for="item in pageCtl.activedPageList" :key="item"
                   class="my-story-card">
            <div class="my-story-card-title">
              <font-awesome-icon icon="chart-bar" style="font-size: 0.45rem" v-if="item.PAGE_TYPE === 'BUILT_IN'"/>&nbsp;
              <font-awesome-icon icon="pencil-alt" style="font-size: 0.45rem" v-else/>&nbsp;
              {{ item.PAGE_TITLE }}
            </div>
            <div class="my-story-card-author">{{ item.CREATE_BY }}</div>
          </el-card>
        </el-drawer>

        <div class="my-story-mini-menu" v-if="_storyActived">
          <div class="my-story-mini-menu-icon">
            <font-awesome-icon icon="chevron-left"/>
          </div>
          <div class="my-story-mini-menu-btn">
            <el-button text @click="goToAnchor('myStory')">
              <font-awesome-icon icon="fa-solid fa-turn-up"/>
            </el-button>
          </div>
          <div class="my-story-mini-menu-btn">
            <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Page Up(Ctrl+↑)">
              <el-button text @click="pageUp">
                <font-awesome-icon icon="chevron-up"/>
              </el-button>
            </el-tooltip>
          </div>
          <div class="my-story-mini-menu-btn">
            <el-button text @click="pageCtl.visible.menu=true">
              <font-awesome-icon icon="list"/>
            </el-button>
          </div>
          <div class="my-story-mini-menu-btn">
            <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Page Down(Ctrl+↓)">
              <el-button text @click="pageDown">
                <font-awesome-icon icon="chevron-down"/>
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div id="myStoryArea">
          <div class="my-story-component" v-for="item in pageCtl.activedPageList.map(e=>e.PAGE_ID)" :key="item" :id="'myStoryComponent_' + item">
            <my-story-performance-result :ref="(el)=>setItemRef(el, item)" v-if="item === 'Performance Result'" :conditions="pageCtl.conditions"/>
            <my-story-business-metrics :ref="(el)=>setItemRef(el, item)" v-if="item === 'Business Metrics'" :conditions="pageCtl.conditions"/>
            <my-story-otds :ref="(el)=>setItemRef(el, item)" v-if="item === 'OTDS'" :conditions="pageCtl.conditions"/>
            <my-story-bol :ref="(el)=>setItemRef(el, item)" v-if="item === 'Back Order'" :conditions="pageCtl.conditions"/>
            <my-story-mycp :ref="(el)=>setItemRef(el, item)" v-if="item === 'MyCP'" :conditions="pageCtl.conditions"/>
            <my-story-otdm :ref="(el)=>setItemRef(el, item)" v-if="item === 'OTDM'" :conditions="pageCtl.conditions"/>
            <my-story-clo :ref="(el)=>setItemRef(el, item)"  v-if="item === 'CLO'" :conditions="pageCtl.conditions"/>
            <my-story-nor :ref="(el)=>setItemRef(el, item)"  v-if="item === 'NOR'" :conditions="pageCtl.conditions"/>
            <my-story-inventory :ref="(el)=>setItemRef(el, item)" v-if="item === 'Inventory'" :conditions="pageCtl.conditions"/>
            <my-story-mpt :ref="(el)=>setItemRef(el, item)" v-if="item === 'MPT'" :conditions="pageCtl.conditions"/>
            <my-story-manual-input :ref="(el)=>setItemRef(el, item)" v-if="pageCtl.componentOpts.indexOf(item) === -1" :page-id="item"/>
            <my-story-5dc v-if="item === '5DC'" :conditions="pageCtl.conditions"/>
            <my-story-otc v-if="item === 'OTC'" :conditions="pageCtl.conditions"/>
            <my-story-uhs v-if="item === 'UHS'" :conditions="pageCtl.conditions"/>
          </div>
          <div style="height: 10rem"></div>
        </div>

        <my-story-variant ref="myStoryVariantRef" :bind-to="pageCtl.conditions" :click-native="search"/>
        <my-story-catalog ref="myStoryCatalogRef" :click-native="search"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import MyStoryOtds from '@/components/scp/canvas/mystory/MyStoryOTDS'
import MyStoryBol from '@/components/scp/canvas/mystory/MyStoryBOL'
import MyStoryMycp from '@/components/scp/canvas/mystory/MyStoryMyCP'
import MyStoryNor from '@/components/scp/canvas/mystory/MyStoryNOR'
import MyStoryOtdm from '@/components/scp/canvas/mystory/MyStoryOTDM'
// @ts-ignore
import MyStory5dc from '@/components/scp/canvas/mystory/MyStory5DC'
import MyStoryInventory from '@/components/scp/canvas/mystory/MyStoryInventory'
import MyStoryMpt from '@/components/scp/canvas/mystory/MyStoryMPT'
import MyStoryOtc from '@/components/scp/canvas/mystory/MyStoryOTC'
import MyStoryClo from '@/components/scp/canvas/mystory/MyStoryCLO'
import MyStoryUhs from '@/components/scp/canvas/mystory/MyStoryUHS'
import MyStoryCatalog from '@/components/scp/canvas/mystory/MyStoryCatalog'
import MyStoryManualInput from '@/components/scp/canvas/mystory/MyStoryManualInput.vue'
import MyStoryPerformanceResult from '@/components/scp/canvas/mystory/MyStoryPerformanceResult.vue'
import MyStoryBusinessMetrics from '@/components/scp/canvas/mystory/MyStoryBusinessMetrics.vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { useRoute, useRouter } from 'vue-router'
import MyStoryVariant from '@/components/scp/canvas/mystory/MyStoryVariant.vue'
import { useStore } from 'vuex'

const $route = useRoute()
const $router = useRouter()
const $store = useStore()
const $message = inject('$message')
const $axios: any = inject('$axios')
const searchRef: any = ref()
const componentRefs = {}
const myStoryVariantRef: any = ref()
const myStoryCatalogRef: any = ref()
const setItemRef = (el, key) => {
  if (el) {
    componentRefs[key] = el
  }
}

interface Page {
  PAGE_ID: string,
  PAGE_TITLE: string,
  PAGE_TYPE: string,
  CREATE_BY: string
}

onMounted(() => {
  document.addEventListener('keydown', hotKeyHandler)
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', hotKeyHandler)
})

const hotKeyHandler = (event) => {
  if (event.ctrlKey && event.key === 'ArrowUp') {
    pageUp()
  } else if (event.ctrlKey && event.key === 'ArrowDown') {
    pageDown()
  }
}

const pageCtl = reactive({
  currentStory: '',
  currentStoryElement: {},
  // componentOpts: ['OTDS', 'Back Order', 'MyCP Commitment', 'PO NOR', 'OTDM', '5DC', 'Inventory', 'MPT', 'OTC', 'CLO', 'UHS'],
  componentOpts: ['Performance Result', 'Business Metrics', 'OTDS', 'Back Order', 'MyCP', 'OTDM', 'CLO', 'MPT', 'NOR', 'Inventory'],
  activedPageList: [] as Array<Page>,
  visible: {
    menu: false
  },
  conditions: {
    storyID: '',
    // componentList: ['OTDS', 'Back Order', 'MyCP', 'OTDM', 'CLO', 'NOR', 'Inventory'],
    componentList: ['Performance Result', 'Business Metrics', 'CLO'],
    $scpFilter: {
      cascader: [],
      filter: []
    }
  }
})

// search(null)或search() 使用当前激活的故事查询, 如果当前没有激活故事, 则执行默认查询
// search({}) 取消激活当前故事线, 执行默认查询
// search({key: 'xxx', label: 'xxxx'}) 指定激活某个故事线
const search = (e?) => {
  if (e) {
    pageCtl.currentStory = e.label || ''
    pageCtl.currentStoryElement = e
  }

  if (Object.keys(pageCtl.currentStoryElement).length > 0) {
    const element = pageCtl.currentStoryElement
    myStoryCatalogRef.value.setBindTo(element.key, element.label)
    $axios({
      method: 'post',
      url: '/canvas/my_story/query_page_list',
      data: {
        variantId: element.key
      }
    }).then((body) => {
      pageCtl.activedPageList = body.filter(e => pageCtl.conditions.componentList.indexOf(e.PAGE_ID) !== -1 || e.PAGE_TYPE !== 'BUILT_IN')
      searchAction(element)
    }).catch((error) => {
      console.log(error)
    })
  } else {
    pageCtl.activedPageList = pageCtl.conditions.componentList.map(e => {
      return { PAGE_ID: e } as Page
    })
    searchAction()
  }
}

// 触发所有组件的查询
const searchAction = (e?) => {
  nextTick(() => {
    for (let i = 0; i < pageCtl.activedPageList.length; i++) {
      const element = pageCtl.activedPageList[i]
      const key = element.PAGE_ID
      if (componentRefs[key]) {
        componentRefs[key].apply(e)
      }
    }
  })
}

// 页面高度发生变化, 触发全局缩放事件
watch(() => $store.state.pageHeight, () => {
  nextTick(() => {
    for (let i = 0; i < pageCtl.activedPageList.length; i++) {
      const element = pageCtl.activedPageList[i]
      const key = element.PAGE_ID
      if (componentRefs[key]) {
        componentRefs[key].resize()
      }
    }
  })
})

const openStoryVariant = () => {
  myStoryVariantRef.value.showVariant()
  myStoryCatalogRef.value.closeCatalog()
}

const openStoryCatalog = () => {
  myStoryCatalogRef.value.showCatalog()
}

const goToAnchor = (id) => {
  pageCtl.visible.menu = false
  const element = document.getElementById(id)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const pageUp = () => {
  const allComponent = document.getElementsByClassName('my-story-component')
  for (let i = allComponent.length - 1; i >= 0; i--) {
    const elementPositionY = allComponent[i].getBoundingClientRect().y
    if (elementPositionY < 0) {
      goToAnchor(allComponent[i].id)
      return
    }
  }
  goToAnchor('myStory')
}

const pageDown = () => {
  const allComponent = document.getElementsByClassName('my-story-component')
  for (let i = 0; i < allComponent.length; i++) {
    const elementPositionY = allComponent[i].getBoundingClientRect().y
    if (elementPositionY > 50) {
      goToAnchor(allComponent[i].id)
      break
    }
  }
}

const _storyActived = computed(() => {
  return !!pageCtl.currentStory
})
</script>

<style lang="scss">
#myStory {
  .el-drawer__header {
    margin-bottom: 0.85rem;
    font-weight: 700;
    font-size: 0.65rem;
    color: var(--scp-text-color-primary)
  }

  .el-drawer__body {
    border-top: 1px dotted var(--scp-border-color-lighter)
  }

  .story-title {
    margin-left: 20px;
    font-style: italic;
    color: var(--scp-text-color-secondary);
    font-weight: bold;
  }

  .my-story-mini-menu:hover {
    width: 48px;
    background-color: rgba(0, 0, 0, .8);

    .my-story-mini-menu-btn {
      display: block;
    }

    .my-story-mini-menu-icon {
      display: none;
    }
  }

  .my-story-mini-menu {
    border-radius: 4px 0 0 4px;
    position: fixed;
    top: 200px;
    right: 8px;
    z-index: 2000;
    height: 104px;
    width: 12px;
    padding: 10px 0 5px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: white;
    background-color: rgba(0, 0, 0, .55);
    transition: all 0.3s;

    .my-story-mini-menu-btn {
      text-align: center;
      width: 48px;
      display: none;
      cursor: pointer;
      font-size: 0.65rem;
      height: 26px;

      .el-button.is-text {
        color: white;
      }

      .el-button.is-text:hover, .el-button.is-text:focus {
        background-color: transparent;
        color: var(--scp-text-color-highlight)
      }
    }
  }

  .my-story-menu {
    .el-card {
      cursor: pointer;
      margin-bottom: var(--scp-widget-margin);
    }

    .my-story-card {
      .el-card__body {
        padding: 15px 15px 5px 15px;
      }

      .my-story-card-title {
        font-size: 0.55rem;
      }

      .my-story-card-author {
        text-align: right;
        font-style: italic;
        color: rgb(34, 34, 34);
        font-family: "Segoe UI", "system-ui";
        font-size: 9px;
      }
    }
  }
}

#myStoryArea {
  .my-story-component {
    margin-bottom: var(--scp-widget-margin);
  }

  .my-story-header {
    font-size: 0.68rem;
    margin: 0 0 6px 0;
    color: #3FA82A;
    font-weight: bold;

    svg {
      margin-top: var(--scp-widget-margin);
    }
  }
}
</style>
