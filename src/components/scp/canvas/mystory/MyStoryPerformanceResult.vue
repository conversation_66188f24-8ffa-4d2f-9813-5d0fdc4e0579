<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / Performance Result
  </div>

  <el-row class="my-story-performance-result" :style="{height: _height + 'px'}" style="overflow: auto;margin-bottom: var(--scp-widget-margin)">
    <el-col :span="6" class="subscript-container subscript-container-left column-container">
      <scp-subscript id="MSPRE1" ref="mspre1Ref"/>
      <div class="front">
        <div class="sub-title">On Time Delivery</div>
        <div style="display: flex; flex-direction: column; height: calc(100% - 1rem);overflow: auto">
          <my-story-performance-result-card
              v-loading="pageCtl.loading.report1"
              card-type="KPI"
              title="OTDS"
              :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
              :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
              :data="pageCtl.data.otds"/>

          <my-story-performance-result-card
              v-loading="pageCtl.loading.report1"
              card-type="KPI"
              title="OTDM"
              :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
              :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
              :data="pageCtl.data.otdm"/>

          <my-story-performance-result-card
              v-loading="pageCtl.loading.report1"
              card-type="KPI"
              title="CLO Competitive Index"
              sub-title2="Spot Level"
              :data="pageCtl.data.clo"/>
        </div>
      </div>
      <div class="back"><!-- 高度已调整 -->
        <div class="box" style="padding: 5px;width: 70%">
          <div class="box-title">Report Settings</div>
          <el-row>
            <el-col :span="4">Month</el-col>
            <el-col :span="16">
              <el-date-picker
                  style="width: var(--scp-input-width)"
                  v-model="pageCtl.conditions.month"
                  format="YYYYMM"
                  value-format="YYYYMM"
                  type="month"
                  :clearable="false"
                  placeholder="Month">
              </el-date-picker>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">Display</el-col>
            <el-col :span="16">
              <el-select v-model="pageCtl.conditions.display" placeholder="Display" filterable
                           style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['ENTITY', 'CLUSTER_NAME', 'BU']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
            </el-col>
          </el-row>
          <el-divider/>
          <el-row>
            <el-col :span="8">Show <b>OTDS</b> as</el-col>
            <el-col :span="16">
              <el-radio-group v-model="pageCtl.conditions.otdsType">
                <el-radio value="ONTIME_PERCENTAGE">OnTime%</el-radio>
                <el-radio value="FAIL_LINES">Delay Lines%</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">Show <b>OTDM</b> as</el-col>
            <el-col :span="16">
              <el-radio-group v-model="pageCtl.conditions.otdmType">
                <el-radio value="ONTIME_PERCENTAGE">OnTime%</el-radio>
                <el-radio value="FAIL_LINES">Delay Lines%</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>

          <div class="box-footer">
            <el-button @click="mspre1Ref.toggleView()">Back</el-button>
            <el-button type="primary" @click="mspre1Ref.toggleView();search()">Search</el-button>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="12" class="subscript-container column-container"
            style="flex: 0 0 calc(50% - var(--scp-widget-margin)) !important; max-width: calc(50% - var(--scp-widget-margin)) !important;">
      <scp-subscript id="EMPTY"/>
      <div class="sub-title">Reliable Confirmation</div>
      <el-row style="height: 100%">
        <el-col :span="12" style="height: 100%">
          <div style="display: flex; flex-direction: column; height: calc(100% - 1rem);overflow: auto;">
            <my-story-performance-result-card
                v-loading="pageCtl.loading.report1"
                card-type="KPI"
                title="On Time Confirm(OPM)"
                :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                :data="pageCtl.data.otc"
              />

            <my-story-performance-result-card
                v-loading="pageCtl.loading.report1"
                card-type="PI"
                title="Accuracy of Commit. in MyCP"
                :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                :data="pageCtl.data.mycp"/>
          </div>
        </el-col>

        <el-col :span="12">
          <div style="display: flex; flex-direction: column; height: calc(100% - 1rem);overflow: auto;">
            <my-story-performance-result-card
                v-loading="pageCtl.loading.report1"
                card-type="PI"
                title="SO NOR(OPM)"
                :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                :data="pageCtl.data.sonor"/>

            <my-story-performance-result-card
                v-loading="pageCtl.loading.report1"
                card-type="PI"
                title="Effective PO NORO%"
                :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                :data="pageCtl.data.ponoro"/>
          </div>
        </el-col>
      </el-row>
    </el-col>

    <el-col :span="6" class="subscript-container subscript-container-right column-container">
      <scp-subscript id="EMPTY"/>
      <div class="sub-title">Inventory</div>
      <div style="display: flex; flex-direction: column; height: 100%;">
        <my-story-performance-result-card
            v-loading="pageCtl.loading.report1"
            card-type="KPI"
            title="DIN 12(Manual)"
            :sub-title2="pageCtl.conditions.monthName"
            :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
            sub-value1="56"
            :sub-value2="renderValue(pageCtl.data.nydin.value2)"
            :sub-value2-color="pageCtl.data.nydin.value2 < 56 ? 'green' : 'red'"
            :sub-value3="renderValue(pageCtl.data.nydin.value3)"
            :sub-value3-color="pageCtl.data.nydin.value3 < 56 ? 'green' : 'red'"/>

        <my-story-performance-result-card
            v-loading="pageCtl.loading.report1"
            card-type="KPI"
            title="Net Yearly DIN(Manual)"
            :sub-title2="pageCtl.conditions.monthName"
            :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
            sub-value1="56"
            :sub-value2="renderValue(pageCtl.data.nydin.value2)"
            :sub-value2-color="pageCtl.data.nydin.value2 < 56 ? 'green' : 'red'"
            :sub-value3="renderValue(pageCtl.data.nydin.value3)"
            :sub-value3-color="pageCtl.data.nydin.value3 < 56 ? 'green' : 'red'"/>

        <el-row>
          <el-col :span="15">&nbsp;</el-col>
          <el-col :span="4" style="background-color: #359c23;margin-right: var(--scp-widget-margin);" class="kpi-tips">KPI</el-col>
          <el-col :span="4" style="background-color: var(--scp-bg-color-highlight);" class="kpi-tips">PI</el-col>
        </el-row>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import MyStoryPerformanceResultCard from '@/components/scp/canvas/mystory/MyStoryPerformanceResultCard.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $deepClone: any = inject('$deepClone')
const $convertDateStr: any = inject('$convertDateStr')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')

const mspre1Ref = ref()

const pageCtl = reactive({
  conditions: {
    $scpFilter: {},
    month: '',
    monthName: '',
    display: 'ENTITY',
    otdsType: 'ONTIME_PERCENTAGE', // FAIL_LINES OR ONTIME_PERCENTAGE
    otdmType: 'ONTIME_PERCENTAGE' // FAIL_LINES OR ONTIME_PERCENTAGE
  },
  loading: {
    report1: false
  },
  data: {
    otds: [],
    otdm: [],
    clo: [],
    otc: [],
    mycp: [],
    sonor: [],
    ponoro: [],
    din12: {
      value2: 58,
      value3: 58
    },
    nydin: {
      value2: 58,
      value3: 58
    }
  }
})

const renderValue = (value, unit?) => {
  if (isNaN(value)) {
    return '--'
  } if (value === 0) {
    return '0'
  } else {
    return value + (unit || '')
  }
}

const initPage = () => {
  const start = new Date()
  let startMonth: any = start.getMonth() + 1
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  if (!pageCtl.conditions.month) {
    pageCtl.conditions.month = start.getFullYear() + '' + startMonth
  }
  search()
}

const search = () => {
  pageCtl.conditions.monthName = $firstCharUpperCase($convertDateStr(pageCtl.conditions.month).split('-')[0])
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_performance_result/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    if (pageCtl.conditions.otdsType === 'FAIL_LINES') {
      pageCtl.data.otds = body.otds.map(e => {
        e.value2Color = 'none'
        e.value3Color = 'none'
        e.value2 = renderValue(e.value2, '')
        e.value3 = renderValue(e.value3, '')

        e.children.map(e2 => {
          e2.value2Color = 'none'
          e2.value3Color = 'none'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    } else {
      pageCtl.data.otds = body.otds.map(e => {
        e.value2Color = e.value2 >= 97.5 ? 'green' : 'red'
        e.value3Color = e.value3 >= 97.5 ? 'green' : 'red'
        e.value2 = renderValue(e.value2, '%')
        e.value3 = renderValue(e.value3, '%')

        e.children.map(e2 => {
          e2.value2Color = e2.value2 >= 97.5 ? 'green' : 'red'
          e2.value3Color = e2.value3 >= 97.5 ? 'green' : 'red'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    }

    if (pageCtl.conditions.otdmType === 'FAIL_LINES') {
      pageCtl.data.otdm = body.otdm.map(e => {
        e.value2Color = 'none'
        e.value3Color = 'none'
        e.value2 = renderValue(e.value2, '')
        e.value3 = renderValue(e.value3, '')

        e.children.map(e2 => {
          e2.value2Color = 'none'
          e2.value3Color = 'none'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    } else {
      pageCtl.data.otdm = body.otdm.map(e => {
        e.value2Color = e.value2 >= 90 ? 'green' : 'red'
        e.value3Color = e.value3 >= 90 ? 'green' : 'red'
        e.value2 = renderValue(e.value2, '%')
        e.value3 = renderValue(e.value3, '%')

        e.children.map(e2 => {
          e2.value2Color = e2.value2 >= 90 ? 'green' : 'red'
          e2.value3Color = e2.value3 >= 90 ? 'green' : 'red'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    }

    pageCtl.data.clo = body.clo.map(e => {
      e.value2Color = e.value2 >= 95 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 95 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        return e2
      })
      return e
    })

    pageCtl.data.mycp = body.mycp.map(e => {
      e.value2Color = e.value2 >= 95 ? 'green' : 'red'
      e.value3Color = e.value3 >= 95 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 95 ? 'green' : 'red'
        e2.value3Color = e2.value3 >= 95 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    pageCtl.data.ponoro = body.ponoro.map(e => {
      e.value2Color = e.value2 < 5 ? 'green' : 'red'
      e.value3Color = e.value3 < 5 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 < 5 ? 'green' : 'red'
        e2.value3Color = e2.value3 < 5 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    pageCtl.data.sonor = body.sonor.map(e => {
      e.value2Color = e.value2 < 2 ? 'green' : 'red'
      e.value3Color = e.value3 < 2 ? 'green' : 'red'
      e.value2 = renderValue(e.value2)
      e.value3 = renderValue(e.value3)

      e.children.map(e2 => {
        e2.value2Color = e2.value2 < 2 ? 'green' : 'red'
        e2.value3Color = e2.value3 < 2 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2)
        e2.value3 = renderValue(e2.value3)
        return e2
      })
      return e
    })
    pageCtl.data.otc = body.otc.map(e => {
      e.value2Color = e.value2 >= 97.7 ? 'green' : 'red'
      e.value3Color = e.value3 >= 97.7 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 97.7 ? 'green' : 'red'
        e2.value3Color = e2.value3 >= 97.7 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'PERF_RESULT'
const $store = useStore()
const commentRef: any = ref()

const _height = computed(() => {
  return Math.max($store.state.pageHeight - 272, 200)
})

const resize = () => {
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryPerformanceResult'
}
</script>

<style lang="scss">
.my-story-performance-result {
  margin-top: calc(var(--scp-widget-margin) + var(--scp-widget-margin));

  .column-container {
    display: flex;
    justify-items: center;
    flex-direction: column;
    padding-bottom: calc(var(--scp-widget-margin) + var(--scp-widget-margin));
    height: 100%;
    margin-bottom: 0 !important;

    .sub-title {
      text-align: center;
      font-size: 0.55rem;
      padding: 0;
      font-weight: bold;
      margin-bottom: var(--scp-widget-margin);
      color: var(--scp-text-color-secondary);
    }
  }

  .kpi-tips {
    text-align: center;
    color: #fff;
    font-weight: bold;
  }
}
</style>
