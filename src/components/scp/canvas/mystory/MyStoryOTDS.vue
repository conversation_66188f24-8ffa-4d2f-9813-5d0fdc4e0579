<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / OTDS
  </div>

  <el-row class="my-story-otds">
    <el-col :span="24">
      <div class="subscript-container tree-table-box" @contextmenu.prevent="" v-loading="pageCtl.loading.report1 || pageCtl.loading.init"
        :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSOTD1" ref="msotd1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data.data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category">
              <template #header>
                <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                           collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="ytdOtds" :label="pageCtl.report1Data.ytdTitle + ' %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'ytdOtds')"
                             v-if="pageCtl.conditions.report1Display.indexOf('YTD_OTDS') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.ytdOtds < pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdOtds }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="mtdOtds" :label="pageCtl.report1Data.mtdTitle + ' %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'mtdOtds')"
                             v-if="pageCtl.conditions.report1Display.indexOf('MTD_OTDS') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.mtdOtds < pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.mtdOtds }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="otds" :label="pageCtl.report1Data.wkTitle + ' %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'otds')"
                             v-if="pageCtl.conditions.report1Display.indexOf('OTDS') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.otds < pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.otds }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="lwOtds" :label="pageCtl.report1Data.wk1Title + ' %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'lwOtds')"
                             v-if="pageCtl.conditions.report1Display.indexOf('LAST_WEEK_OTDS') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.lwOtds < pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.lwOtds }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="vip" :label="pageCtl.report1Data.wkTitle + ' VIP %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'vip')"
                             v-if="pageCtl.conditions.report1Display.indexOf('VIP') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.vip < pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.vip }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="ontimeLine" :label="pageCtl.report1Data.wkTitle + ' OnTime'" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'ontimeLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('VIP') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.ontimeLine) }}
              </template>
            </el-table-column>
            <el-table-column prop="delayLine" :label="pageCtl.report1Data.wkTitle + ' Delay'" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'delayLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('DELAY_LINE') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.delayLine) }}
              </template>
            </el-table-column>
            <el-table-column prop="rca" :label="pageCtl.report1Data.wkTitle + ' RCA %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'rca')"
                             v-if="pageCtl.conditions.report1Display.indexOf('RCA') !== -1"/>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="4">OTDS Filter</el-col>
              <el-col :span="8">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Display</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.displayOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">OTDS Week</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Week" placeholder="OTDS Week"
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.weekOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Warning Baseline</el-col>
              <el-col :span="8">
                <el-input-number v-model="pageCtl.conditions.report1WarningValue" size="small" :precision="1" :step="0.1" :max="100" :min="40"
                                 style="width: var(--scp-input-width) !important;"/>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msotd1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msotd1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="10">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSOTD2" ref="msotd2Ref"/>
        <div class="front">
          <chart ref="report2Ref" :height="_part2Height" :option="_report2Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2Category" placeholder="Category" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Stack by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2StackBy" placeholder="Stack by" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.stackOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Top</el-col>
              <el-col :span="16">
                <el-input-number :min="0" v-model="pageCtl.conditions.report2TopStart" style="width: 45%" @change="resetTopStartEnd"/>
                ~
                <el-input-number :min="0" v-model="pageCtl.conditions.report2TopEnd" style="width: 45%" @change="resetTopStartEnd"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msotd2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msotd2Ref.toggleView();searchReport2()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="14">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSOTD3" ref="msotd3Ref"/>
        <div class="front">
          <chart ref="report3Ref" :height="_part2Height" :option="_report3Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in _report3CategoryOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Value Type</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3ValueType" placeholder="Value Type" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['ONTIME_RATE', 'FAILED_LINES']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Time Range</el-col>
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.report3DateRange"
                    type="monthrange"
                    unlink-panels
                    range-separator="~"
                    format="YYYY/MM"
                    value-format="YYYY/MM"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :clearable="false"
                    prefix-icon=null
                    style="width: calc(100% - 1.333rem) !important;">
                </el-date-picker>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msotd3Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msotd3Ref.toggleView();searchReport3()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onBeforeMount, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const msotd1Ref: any = ref()
const msotd2Ref: any = ref()
const msotd3Ref: any = ref()
const report2Ref: any = ref()
const report3Ref: any = ref()
const $deepClone: any = inject('$deepClone')

const pageCtl = reactive({
  report1Data: {
    data: [],
    mtdTitle: 'MTD OTDS',
    ytdTitle: 'YTD OTDS',
    wkTitle: 'OTDS',
    wk1Title: 'W-1 OTDS'
  },
  report2Data: { yAxis: [], total: [], series: [] },
  report3Data: { yAxis: [], xAxis: [], legend: [] },
  filterOpts: [],
  stackOpts: [],
  displayOpts: ['YTD_OTDS', 'MTD_OTDS', 'LAST_WEEK_OTDS', 'OTDS', 'VIP', 'ONTIME_LINE', 'DELAY_LINE', 'RCA'],
  weekOpts: [],
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {},
    filterList: [
      ['ORDER_TYPE', 'KA'],
      ['ORDER_TYPE', 'KL'],
      ['ORDER_TYPE', 'TA'],
      ['ORDER_TYPE', 'KB'],
      ['ORDER_TYPE', 'FD'],
      ['ORDER_TYPE', 'OR'],
      ['ORDER_TYPE', 'YDP'],
      ['ORDER_TYPE', 'ZDP'],
      ['ORDER_TYPE', 'ZFD'],
      ['ORDER_TYPE', 'ZN'],
      ['ORDER_TYPE', 'ZNDP'],
      ['ORDER_TYPE', 'ZOR'],
      ['ORDER_TYPE', 'ZORN'],
      ['ORDER_TYPE', 'ZPOR'],
      ['CUSTOMER_GROUP', 'BA'],
      ['CUSTOMER_GROUP', 'BK'],
      ['CUSTOMER_GROUP', 'C1'],
      ['CUSTOMER_GROUP', 'C2'],
      ['CUSTOMER_GROUP', 'C3'],
      ['CUSTOMER_GROUP', 'C4'],
      ['CUSTOMER_GROUP', 'C5'],
      ['CUSTOMER_GROUP', 'C6'],
      ['CUSTOMER_GROUP', 'CS'],
      ['CUSTOMER_GROUP', 'D1'],
      ['CUSTOMER_GROUP', 'D2'],
      ['CUSTOMER_GROUP', 'D3'],
      ['CUSTOMER_GROUP', 'D4'],
      ['CUSTOMER_GROUP', 'D5'],
      ['CUSTOMER_GROUP', 'D6'],
      ['CUSTOMER_GROUP', 'D7'],
      ['CUSTOMER_GROUP', 'D8'],
      ['CUSTOMER_GROUP', 'D9'],
      ['CUSTOMER_GROUP', 'DS'],
      ['CUSTOMER_GROUP', 'E1'],
      ['CUSTOMER_GROUP', 'EC'],
      ['CUSTOMER_GROUP', 'EM'],
      ['CUSTOMER_GROUP', 'EP'],
      ['CUSTOMER_GROUP', 'EU'],
      ['CUSTOMER_GROUP', 'G1'],
      ['CUSTOMER_GROUP', 'G2'],
      ['CUSTOMER_GROUP', 'G3'],
      ['CUSTOMER_GROUP', 'G4'],
      ['CUSTOMER_GROUP', 'G5'],
      ['CUSTOMER_GROUP', 'G6'],
      ['CUSTOMER_GROUP', 'G7'],
      ['CUSTOMER_GROUP', 'GE'],
      ['CUSTOMER_GROUP', 'GR'],
      ['CUSTOMER_GROUP', 'HT'],
      ['CUSTOMER_GROUP', 'I1'],
      ['CUSTOMER_GROUP', 'I2'],
      ['CUSTOMER_GROUP', 'I3'],
      ['CUSTOMER_GROUP', 'I4'],
      ['CUSTOMER_GROUP', 'I5'],
      ['CUSTOMER_GROUP', 'I6'],
      ['CUSTOMER_GROUP', 'I7'],
      ['CUSTOMER_GROUP', 'I8'],
      ['CUSTOMER_GROUP', 'IT'],
      ['CUSTOMER_GROUP', 'KA'],
      ['CUSTOMER_GROUP', 'KI'],
      ['CUSTOMER_GROUP', 'MA'],
      ['CUSTOMER_GROUP', 'MP'],
      ['CUSTOMER_GROUP', 'OG'],
      ['CUSTOMER_GROUP', 'OH'],
      ['CUSTOMER_GROUP', 'OS'],
      ['CUSTOMER_GROUP', 'OT'],
      ['CUSTOMER_GROUP', 'SU'],
      ['CUSTOMER_GROUP', 'T1'],
      ['CUSTOMER_GROUP', 'T2'],
      ['CUSTOMER_GROUP', 'T3'],
      ['CUSTOMER_GROUP', 'U1'],
      ['CUSTOMER_GROUP', 'U2'],
      ['CUSTOMER_GROUP', 'U3'],
      ['CUSTOMER_GROUP', 'U4'],
      ['CUSTOMER_GROUP', 'U5'],
      ['CUSTOMER_GROUP', 'V1'],
      ['CUSTOMER_GROUP', 'W1'],
      ['CUSTOMER_GROUP', 'W2'],
      ['CUSTOMER_GROUP', 'W3'],
      ['CUSTOMER_GROUP', 'X1'],
      ['CUSTOMER_GROUP', 'X2'],
      ['CUSTOMER_GROUP', 'X3'],
      ['CUSTOMER_GROUP', 'X4'],
      ['CUSTOMER_GROUP', 'X5'],
      ['CUSTOMER_GROUP', 'X6'],
      ['CUSTOMER_GROUP', 'X7'],
      ['CUSTOMER_GROUP', 'X8'],
      ['CUSTOMER_GROUP', 'Z1'],
      ['CUSTOMER_GROUP', 'Z2'],
      ['CUSTOMER_GROUP', 'Z3'],
      ['CUSTOMER_GROUP', 'Z4'],
      ['CUSTOMER_GROUP', 'Z5'],
      ['CUSTOMER_GROUP', 'Z6'],
      ['PLANT_CODE', 'AX02'],
      ['PLANT_CODE', 'E001'],
      ['PLANT_CODE', 'I001'],
      ['PLANT_CODE', 'I003'],
      ['PLANT_CODE', 'M001'],
      ['PLANT_CODE', 'N001'],
      ['PLANT_CODE', 'N005'],
      ['PLANT_CODE', 'N006'],
      ['PLANT_CODE', 'O001'],
      ['PLANT_CODE', 'R001'],
      ['PLANT_CODE', 'X001'],
      ['PLANT_CODE', 'XA01'],
      ['PLANT_CODE', 'SP01'],
      ['PLANT_CODE', 'SP02']
    ],
    report1Week: '',
    report1Display: ['YTD_OTDS', 'MTD_OTDS', 'LAST_WEEK_OTDS', 'OTDS', 'VIP', 'ONTIME_LINE', 'DELAY_LINE', 'RCA'],
    report1WarningValue: 97,
    report1Categories: ['BU', 'PRODUCT_LINE'],
    report1SelectedValues: [],
    report2TopStart: 0,
    report2TopEnd: 15,
    report2Category: 'MATERIAL',
    report2StackBy: '',
    report3Category: '[TOTAL]',
    report3DateRange: [],
    report3ValueType: 'ONTIME_RATE',
    tooltips: []
  },
  pivotOpts: [],
  visible: {
    win: false
  }
})

const initPage = () => {
  initReport3Filter()
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_otds/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.weekOpts = body.weekOpts
    pageCtl.stackOpts = body.stackOpts
    if (pageCtl.conditions.report1Week === '') {
      pageCtl.conditions.report1Week = body.currentWeek
    }
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const initReport3Filter = () => {
  const end = new Date()
  const start = new Date()
  start.setMonth(end.getMonth() - 2)
  let startMonth: any = start.getMonth()
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  let endMonth: any = end.getMonth() + 1
  if (endMonth < 10) {
    endMonth = '0' + endMonth
  }
  pageCtl.conditions.report3DateRange = [start.getFullYear() + '/' + startMonth, end.getFullYear() + '/' + endMonth]
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_otds/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_otds/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_otds/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1RowDblick = (row) => {
  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
}

const report1SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const _report2Opts = computed(() => {
  const series = []
  const legend = []
  for (const key in pageCtl.report2Data.series) {
    legend.push(key)
    series.push({
      name: key,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: pageCtl.report2Data.series[key]
    })
  }
  series.push({
    name: 'Total',
    type: 'line',
    symbolSize: 0,
    lineStyle: {
      width: 0
    },
    label: {
      show: true,
      position: 'right',
      valueAnimation: true
    },
    data: pageCtl.report2Data.total
  })
  return {
    title: {
      text: 'DELAY LINE TOP ' +
          (pageCtl.conditions.report2TopStart === 0 ? pageCtl.conditions.report2TopEnd : pageCtl.conditions.report2TopStart + ' ~ ' + pageCtl.conditions.report2TopEnd) +
          ' ' + pageCtl.conditions.report2Category +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    grid: $grid(),
    legend: $legend({ data: legend }),
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report2Data.yAxis,
      axisLabel: {
        interval: 0
      }
    },
    series
  }
})

const _report3CategoryOpts = computed(() => {
  const result = ['[TOTAL]']
  for (let i = 0; i < pageCtl.pivotOpts.length; i++) {
    if (pageCtl.pivotOpts[i].indexOf('WEEK') === -1) {
      result.push(pageCtl.pivotOpts[i])
    }
  }
  return result
})

const report3ValueRender = (val) => {
  if (pageCtl.conditions.report3ValueType === 'ONTIME_RATE') {
    return val + '%'
  } else {
    return $thousandBitSeparator(val)
  }
}

const _report3Opts = computed(() => {
  const series = []

  for (const y in pageCtl.report3Data.yAxis) {
    const s = {
      name: y,
      data: pageCtl.report3Data.yAxis[y],
      type: 'line',
      label: {}
    }
    if (Object.keys(pageCtl.report3Data.yAxis).length < 3) {
      s.label = {
        show: true,
        position: 'top',
        formatter: (param) => {
          return report3ValueRender(param.data)
        }
      }
    }
    series.push(s)
  }
  series.sort((e1, e2) => e1.name.localeCompare(e2.name))
  const opts = {
    title: {
      text: 'ONTIME TRENDS'
    },
    grid: $grid(),
    legend: $legend(),
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (params.hasOwnProperty(i)) {
            const value = params[i].value || 0
            tip.push('<div style="width:9.5rem;">')
            const marker = params[i].marker

            tip.push(marker)
            tip.push(params[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push(report3ValueRender(value))
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      axisLabel: {},
      type: 'category',
      data: pageCtl.report3Data.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      }
    },
    series
  }
  if (pageCtl.conditions.report3ValueType === 'ONTIME_RATE') {
    // @ts-ignore
    opts.visualMap = {
      show: false,
      dimension: Object.keys(pageCtl.report3Data.yAxis).length === 1 ? 1 : 0,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.conditions.report1WarningValue,
        color: '#c12e34'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    }
  }
  return opts
})

const resetTopStartEnd = () => {
  if (pageCtl.conditions.report2TopStart >= pageCtl.conditions.report2TopEnd) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 15
  } else if (pageCtl.conditions.report2TopEnd > pageCtl.conditions.report2TopStart + 50) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 50
  }
}

interface OTDSTable {
  id: string,
  category: string
  ytdOtds: string
  mtdOtds: string
  otds: string,
  lwOtds: string,
  vip: string,
  ontimeLine: string,
  delayLine: string,
  rca: string,
  parent: Array<string>,
  hasChildren?: boolean
  children?: OTDSTable[]
}

const load = (
  row: OTDSTable,
  treeNode: any,
  resolve: (date: OTDSTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_otds/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'OTDS'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report2Ref.value.resize()
  report3Ref.value.resize()
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryOtds'
}
</script>

<style lang="scss">
.my-story-otds {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
