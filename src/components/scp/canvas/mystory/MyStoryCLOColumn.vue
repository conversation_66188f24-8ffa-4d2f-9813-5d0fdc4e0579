<template>
  <div :data-value="JSON.stringify(props.row)" style="cursor: pointer;">
    <div style="display: flex;justify-content: left;align-items:center;" :style="{width: props.width + 'px'}">
      <!-- C<PERSON><PERSON> Fail显示文本 -->
      <div style="height:19px;margin-right: 2px;padding-right: 2px;color: #fff;font-size: 80%;overflow: hidden"
           :style="{width: props.row[props.name + 'Percent'] + '%',backgroundColor: parseReport1BgColor(props.row)}" v-if="props.row.category !== 'Total' && props.name !== 'ontimeLines'">
        {{ props.row[props.name + 'Percent'] < 2 * $shortenNumber(props.row[props.name], 1).length || props.row[props.name] === 0 ? '' : $shortenNumber(props.row[props.name], 1) }}
      </div>
      <div style="height:19px;margin-right: 2px;padding-right: 2px;color: #fff;font-size: 80%;overflow: hidden"
           :style="{width: props.row[props.name + 'Percent'] + '%',backgroundColor: parseReport1BgColor(props.row)}" v-if="props.row.category !== 'Total' && props.name === 'ontimeLines'">
        {{ props.row[props.name + 'Percent'] < 2 * $shortenNumber(props.row[props.name + 'Percent'], 1).length ? '' : $shortenNumber(props.row[props.name + 'Percent'], 1) }}
      </div>
      <div style="font-size: 80%;" :style="{color: parseReport1Color(props.row)}" v-if="props.row.category !== 'Total' && props.name !== 'ontimeLines'">
        {{ props.row[props.name + 'Percent'] }}
      </div>
      <div v-if="props.row.category === 'Total'">
        {{ $shortenNumber(props.row[props.name], 1) }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject } from 'vue'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $toFixed: any = inject('$toFixed')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

// @ts-ignore
const props = withDefaults(defineProps<{
  row?: object,
  name?: string,
  width?: number
}>(), {
  title: '',
  row: () => {
    return {}
  },
  width: 220
})

const parseReport1BgColor = (row) => {
  const category = row.parent && row.parent[0] ? row.parent[0] : row.category
  if (category === 'MTS D+1' || category === 'MTO <=1W') {
    return '#61a3f1'
  } else if (category === 'MTS <=2W' || category === 'MTO <=1M') {
    return '#e6a23c'
  } else if (category === 'MTS >2W' || category === 'MTO >1M') {
    return '#f56c6c'
  } else if (category === 'Others') {
    return '#bbb'
  }
  return '#61a3f1'
}

const parseReport1Color = (row) => {
  const category = row.parent && row.parent[0] ? row.parent[0] : row.category
  if (category === 'MTS D+1' || category === 'MTO <=1W') {
    return '#61a3f1'
  } else if (category === 'MTS <=2W' || category === 'MTO <=1M') {
    return '#e6a23c'
  } else if (category === 'MTS >2W' || category === 'MTO >1M') {
    return '#f56c6c'
  }
  return '#303133'
}
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryCloColumn'
}
</script>
