<template>
  <div class="my-story-comment" v-loading="pageCtl.loading.load" v-if="!!pageCtl.parentComponent" :style="{height: pageCtl.expend? 'auto': '200px'}">
    <!--评论列表-->

    <div>
      <div style="font-weight: bold;margin-bottom: 0.2rem">{{ pageCtl.comments.length }} comments · <span
          style="color: var(--scp-text-color-lighter)">{{ _repliesCnt }} replies</span>
          &nbsp;<font-awesome-icon class="expand-icon" :icon="pageCtl.expend? ['fas', 'chevron-up']: ['fas', 'chevron-down']"
                                   @click="pageCtl.expend = !pageCtl.expend"/>
      </div>

      <div class="comment-body">
        <div class="comment-item" v-for="item in pageCtl.comments" :key="item">
          <div style="padding: 10px;">
            <div style="display: flex;">
                <span>
                  <el-avatar :size="20" :src="'https://scp-dss.cn.schneider-electric.com/avatar/' + item.USER_ID + '.jpg'">
                    <img :src="item.IS_MAINTAINER === 'Y' ? '/img/avatar-admin.png': '/img/avatar.png'" alt="">
                  </el-avatar>
                </span>
              <span style="margin-left: 10px">
                    {{ item.USER_NAME }}
                    <span style="margin-left: 10px;color: var(--scp-text-color-secondary);font-size: 90%">{{ item.CREATE_DATE }}</span>
              </span>
              <span style="margin-left: auto;color: var(--scp-text-color-secondary);font-size: 90%">{{ item.REPLIES_CNT }} replies</span>
              <span style="cursor: pointer;margin-left: 10px" class="hover-icon-primary" @click="openReply(item)">
                  <el-tooltip :show-after="1000" effect="light" placement="top" content="Reply">
                    <font-awesome-icon icon="fa-reply"/>
                  </el-tooltip>
              </span>
              <span style="cursor: pointer;margin-left: 10px" class="hover-icon-primary" @click="editComment(item)">
                  <el-tooltip :show-after="1000" effect="light" placement="top" content="Edit">
                    <font-awesome-icon :icon="pageCtl.loading.edit ? 'spinner' : 'edit'" :spin="pageCtl.loading.edit"/>
                  </el-tooltip>
              </span>

              <el-popconfirm title="确定删除评论?"
                             iconColor="var(--scp-text-color-error)"
                             @confirm="deleteComment(item)"
                             confirmButtonType="danger"
                             confirmButtonText='确定'
                             cancelButtonText='取消'
                             style="margin-left:5px">
                <template #reference>
                    <span style="cursor: pointer;margin-left: 10px" class="hover-icon" v-if="item.IS_ME">
                      <font-awesome-icon :icon="pageCtl.loading.delete ? 'spinner' : 'fa-trash-alt'" :spin="pageCtl.loading.delete"/>
                    </span>
                </template>
              </el-popconfirm>
            </div>
            <scp-md-preview v-model="item.CONTENT" :editor-id="'editor_' + item.COMMENT_ID"/>
          </div>
          <!--回复列表-->
          <div style="padding: 12px 10px 0 10px;background-color: var(--scp-bg-color-fill);border-radius: 0 0 6px 6px" v-if="item.REPLIES.length > 0">
            <div style="padding-bottom: 7px;" v-for="reply in item.REPLIES" :key="reply.REPLY_ID">
              <div class="reply-top" style="display: flex;">
                  <span>
                    <el-avatar :size="20" :src="'https://scp-dss.cn.schneider-electric.com/avatar/' + reply.USER_ID + '.jpg'">
                      <img :src="reply.IS_MAINTAINER === 'Y' ? '/img/avatar-admin.png': '/img/avatar.png'" alt="">
                    </el-avatar>
                  </span>
                <span style="margin-left: 10px">
                      {{ reply.USER_NAME }}
                      <span style="margin-left: 10px;color: var(--scp-text-color-secondary);font-size: 90%">{{ reply.CREATE_DATE }}</span>
                  </span>
                <span style="margin-left: auto">&nbsp;</span>
                <span style="cursor: pointer;margin-left: 10px" @click="openReply(reply, true)">
                    <el-tooltip :show-after="1000" effect="light" placement="top" content="Reply">
                      <font-awesome-icon icon="fa-reply"/>
                    </el-tooltip>
                  </span>
                <span style="cursor: pointer;margin-left: 10px" class="hover-icon" @click="deleteReply(reply)" v-if="reply.IS_ME">
                    <el-tooltip :show-after="1000" effect="light" placement="top" content="Delete">
                      <font-awesome-icon :icon="pageCtl.loading.delete ? 'fa-spinner' : 'fa-trash-alt'" :spin="pageCtl.loading.delete"/>
                    </el-tooltip>
                  </span>
              </div>
              <div class="reply-content">
                <scp-md-preview v-model="reply.CONTENT" :editor-id="'reply_' + reply.REPLY_ID"/>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--操作区-->
      <div class="input-comment">
        <div v-show="pageCtl.comments.length > 0">
          <el-link style="font-size: 0.45rem;" @click="openComment">More Comments</el-link>
          <el-divider direction="vertical"/>
          <el-link style="font-size: 0.45rem;" @click="viewChangeLogs">Change Logs</el-link>
        </div>
        <el-empty description="No Comments" v-show="pageCtl.comments.length == 0" :image-size="80">
          <el-button @click="openComment">New Comment</el-button>
        </el-empty>
      </div>
    </div>

    <scp-draggable-resizable h="700" w="1000" v-model="pageCtl.visible.create" title="New Comment"
                             :save="()=>sendComment()" :save-loading="pageCtl.loading.create" save-text="Submit">
      <template v-slot="{height}">
        <scp-ck-editor v-model="pageCtl.content.comment" placeholder="Say something..." :height="height - 100"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="700" w="1000" v-model="pageCtl.visible.edit" title="Edit Comment"
                             :save="modifyComment" :save-loading="pageCtl.loading.edit" save-text="Save">
      <template v-slot="{height}">
        <scp-ck-editor v-model="pageCtl.content.editContent" placeholder="Say something..." :height="height - 100"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="650" w="900" v-model="pageCtl.visible.reply" title="New Reply"
                             :save="sendReply" :save-loading="pageCtl.loading.create" save-text="Submit">
      <template v-slot="{height}">
        <scp-ck-editor v-model="pageCtl.content.reply" :height="height - 120"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import ScpDraggableResizable from '@/components/starter/components/DraggableResizable.vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import { ViewDetailConfig } from '@/assets/js/function'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $deepClone: any = inject('$deepClone')
const $viewDetails: any = inject('$viewDetails')
const $delHtmlTag: any = inject('$delHtmlTag')
const $randomString: any = inject('$randomString')

const pageCtl = reactive({
  comments: [] as Array<any>, // 控制Comments和Reply列表
  visible: {
    create: false, // 创建窗口
    edit: false, // 编辑窗口
    reply: false // 回复窗口
  },
  expend: false,
  content: {
    comment: '',
    editId: '',
    editContent: '',
    reply: ''
  },
  loading: {
    load: false,
    create: false,
    edit: false,
    delete: false
  },
  replyParent: {},
  bindTo: '',
  parentComponent: ''
})

const viewChangeLogs = () => {
  $viewDetails({
    url: '/canvas/my_story/query_change_logs',
    params: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent
    },
    title: 'Change logs [' + pageCtl.parentComponent + ']'
  } as ViewDetailConfig)
}

// region 评论部分
const openComment = () => {
  pageCtl.visible.create = !pageCtl.visible.create
}

const sendComment = () => {
  const content = pageCtl.content.comment
  if (!content) {
    $message.error('Please input some comments.')
    return
  }
  pageCtl.loading.create = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/send_comment',
    data: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent,
      content
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.content.comment = ''
      pageCtl.loading.create = false
    })
  })
}

const editComment = (item) => {
  pageCtl.loading.edit = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/query_comment_by_id',
    data: {
      commentId: item.COMMENT_ID
    }
  }).then((body) => {
    pageCtl.content.editContent = body
    pageCtl.content.editId = item.COMMENT_ID
    pageCtl.visible.edit = true
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.edit = false
  })
}

const modifyComment = () => {
  pageCtl.loading.edit = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/modify_comment',
    data: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent,
      commentId: pageCtl.content.editId,
      content: pageCtl.content.editContent
    }
  }).then(() => {
    pageCtl.visible.edit = false
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.loading.edit = false
    })
  })
}

const deleteComment = (item) => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/delete_comment',
    data: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent,
      commentId: item.COMMENT_ID
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.loading.delete = false
    })
  })
}
// endregion

// region 评论的回复
const openReply = (item, hasPrefix?) => {
  pageCtl.replyParent = $deepClone(item)
  if (hasPrefix) {
    const spliter = '<span style="display:none">$&#&$</span>'
    let prefix = '<p style="font-style: italic;margin-bottom:8px"><span style="font-weight: bold;">回复@' + item.USER_NAME + '</span>&nbsp;&nbsp;'
    let content = $delHtmlTag(item.CONTENT)
    if (content.indexOf('回复@') === 0) {
      const cs = content.split(spliter)
      if (cs.length > 0) {
        cs.splice(0, 1)
        content = cs.join('')
      }
    }
    if (content.length > 0) {
      if (content.length > 10) {
        prefix += content.substring(0, 10) + '...'
      } else {
        prefix += content
      }
      prefix += '</p>' + spliter // $&#&$分隔符, 用来再次回复的时候分割用
      pageCtl.replyParent.prefix = prefix
    }
  }
  pageCtl.visible.reply = !pageCtl.visible.reply
}
const sendReply = () => {
  const item = pageCtl.replyParent
  let content = pageCtl.content.reply
  if (!content) {
    $message.error('Please input the reply.')
    return
  }
  if (item.prefix) {
    content = item.prefix + content
  }
  pageCtl.loading.create = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/send_reply',
    data: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent,
      commentId: item.COMMENT_ID,
      content
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.visible.reply = false
    pageCtl.content.reply = ''
    queryComments(() => {
      pageCtl.content.reply = ''
      pageCtl.loading.create = false
    })
  })
}

const deleteReply = (reply) => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/delete_reply',
    data: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent,
      replyId: reply.REPLY_ID
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.loading.delete = false
    })
  })
}

// endregion

const queryComments = (afterQuery?: Function) => {
  pageCtl.loading.load = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/query_comments',
    data: {
      bindTo: pageCtl.bindTo,
      parentComponent: pageCtl.parentComponent
    }
  }).then((body) => {
    pageCtl.comments = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.load = false
    pageCtl.visible.create = false
    if (afterQuery) {
      afterQuery.apply(null)
    }
  })
}

const _repliesCnt = computed(() => {
  let result = 0
  for (let i = 0; i < pageCtl.comments.length; i++) {
    result += pageCtl.comments[i].REPLIES.length
  }
  return result
})

const setBindTo = (bindTo, parentComponent) => {
  pageCtl.bindTo = bindTo
  pageCtl.parentComponent = parentComponent
  queryComments()
}

onMounted(() => {

})
defineExpose({
  setBindTo
})
</script>

<script lang="ts">
export default {
  name: 'MyStoryComment'
}
</script>

<style lang="scss">
.my-story-comment {
  margin-bottom: var(--scp-widget-margin);
  overflow: auto;
  background-color: #fff;
  padding: var(--scp-widget-margin);
  border: 1px solid var(--scp-border-color);

  .expand-icon {
    cursor: pointer;
    font-weight: bold;
    color: var(--scp-text-color-lighter);
  }

  .expand-icon:hover {
    color: var(--scp-text-color-highlight);
  }

  .md-editor-preview-wrapper {
    padding: 0 !important;
  }

  .hover-icon:hover {
    color: var(--scp-text-color-error);
  }

  .hover-icon-primary:hover {
    color: var(--scp-text-color-highlight);
  }

  .input-comment {
    .el-textarea__inner {
      width: 100%;
      height: 115px;
      border: 0;
      border-radius: 6px;
      outline: none;
      resize: none;
      background-color: var(--scp-bg-color-fill-lighter);
    }

    .el-empty {
      --el-empty-padding: 0 0 10px 0 !important;
    }

    .el-empty__description {
      display: none;
    }
  }

  .comment-body {
    display: flex;
    flex-wrap: wrap;

    .comment-item {
      border-radius: 6px;
      border: 1px solid var(--scp-text-color-lighter);
      margin-bottom: 0.4rem;
      display: flex;
      width: calc(33% - var(--scp-widget-margin));
      flex-direction: column;
      margin-right: var(--scp-widget-margin);
      height: fit-content;
    }
  }

  .input-reply {
    margin-top: 5px;
    height: 120px;
  }

  .input-reply textarea {
    height: 85px;
    width: 100%;
    border: 0 solid;
    outline: none;
    resize: none;
    border-radius: 6px;
    background-color: var(--scp-bg-color-fill-lighter);
  }

  .reply-top {
    display: flex;
  }

  .reply-content {
    .md-editor {
      background-color: transparent !important;
    }

    span {
      background-color: transparent !important;
    }
  }

  .ck.ck-toolbar {
    border: 0 !important;
  }

  .ck-content {
    border-width: 1px 0 !important;
  }
}
</style>
