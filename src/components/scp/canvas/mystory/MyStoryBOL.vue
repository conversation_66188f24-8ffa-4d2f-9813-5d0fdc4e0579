<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / Back Order
  </div>
  <el-row class="my-story-bol">
    <el-col :span="24">
      <div class="subscript-container tree-table-box" @contextmenu.prevent="" v-loading="pageCtl.loading.report1 || pageCtl.loading.init"
        :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSBOL1" ref="msbol1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data"
              style="width: 100%"
              row-key="id"
              lazy
              :load="load"
              :height="_part1Height"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category">
              <template #header>
                <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                           collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable :loading="pageCtl.loading.init">
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="bol" label="BOL" sortable :sort-method="(a, b) => report1Sort(a, b, 'bol')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BOL') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.bol, 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="bolVip" label="BOL-VIP" sortable :sort-method="(a, b) => report1Sort(a, b, 'bolVip')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BOL_VIP') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.bolVip, 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="bov" label="BOV" sortable :sort-method="(a, b) => report1Sort(a, b, 'bov')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BOV') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.bov, 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="bovVip" label="BOV-VIP" sortable :sort-method="(a, b) => report1Sort(a, b, 'bovVip')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BOV_VIP') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.bovVip, 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="backlogValue" label="BACKLOG VALUE" sortable :sort-method="(a, b) => report1Sort(a, b, 'backlogValue')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BACKLOG_VALUE') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.backlogValue, 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="backlogLine" label="BACKLOG LINE" sortable :sort-method="(a, b) => report1Sort(a, b, 'backlogLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BACKLOG_LINE') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.backlogLine, 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="bolPercent" label="BOL%" sortable :sort-method="(a, b) => report1Sort(a, b, 'bolPercent')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BOL%') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.bolPercent, 1) }}
              </template>
            </el-table-column>
            <el-table-column prop="bovPercent" label="BOV%" sortable :sort-method="(a, b) => report1Sort(a, b, 'bovPercent')"
                             v-if="pageCtl.conditions.report1Display.indexOf('BOV%') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.bovPercent, 1) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="4">BOL Filter</el-col>
              <el-col :span="8">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Display</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.displayOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Value Type</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1ValueType" placeholder="Value Type" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['OPEN_SO_W_O_GI', 'OPEN_SO_W_O_DEL']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msbol1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msbol1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="10">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSBOL2" ref="msbol2Ref"/>
        <div class="front">
          <chart ref="report2Ref" :height="_part2Height" :option="_report2Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2Category" placeholder="Category" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Stack by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2StackBy" placeholder="Stack by" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.stackOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Top</el-col>
              <el-col :span="16">
                <el-input-number :min="0" v-model="pageCtl.conditions.report2TopStart" style="width: 45%" @change="resetTopStartEnd"/>
                ~
                <el-input-number :min="0" v-model="pageCtl.conditions.report2TopEnd" style="width: 45%" @change="resetTopStartEnd"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msbol2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msbol2Ref.toggleView();searchReport2()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="14">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3 || pageCtl.loading.init">
        <scp-subscript id="EMPTY"/>
        <chart ref="report3Ref" :height="_part2Height" :option="pageCtl.report3Opt"/>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $deepClone: any = inject('$deepClone')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const msbol1Ref: any = ref()
const msbol2Ref: any = ref()
const report2Ref: any = ref()
const report3Ref: any = ref()

const pageCtl = reactive({
  filterOpts: [],
  stackOpts: [],
  displayOpts: ['BOL', 'BOL_VIP', 'BOV', 'BOV_VIP', 'BACKLOG_VALUE', 'BACKLOG_LINE', 'BOL%', 'BOV%'],
  report1Data: [],
  report2Data: { yAxis: [], total: [], series: [] },
  report3Opt: {},
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    report1Display: ['BOL', 'BOL_VIP', 'BOV', 'BOV_VIP', 'BACKLOG_VALUE', 'BACKLOG_LINE', 'BOL%', 'BOV%'],
    $scpFilter: {},
    filterList: [],
    report1ValueType: 'OPEN_SO_W_O_GI',
    report1Categories: ['DELAY_DEPTH', 'CLUSTER_NAME', 'BU', 'ENTITY', 'PRODUCT_LINE'],
    report1SelectedValues: [],
    report2Category: 'MATERIAL',
    report2StackBy: '',
    report2TopStart: 0,
    report2TopEnd: 15
  },
  pivotOpts: [],
  visible: {
    win: false
  },
  selectedParentLevel: '', // report3 select parent path
  selectedCurrentLevel: '' // report3 select path
})

const initPage = () => {
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_bol/init_page'
  }).then((body) => {
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.filterOpts = body.filterOpts
    pageCtl.stackOpts = body.stackOpts
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []

  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_bol/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_bol/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_bol/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Opt = parseReport3Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1RowDblick = (row: BOLTable) => {
  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
}

const report1SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const parseReport3Data = (raw) => {
  return {
    color: ['#c12e34'],
    legend: $legend({
      data: ['0-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M', '3-6M', '>6M']
    }),
    grid: $grid(),
    title: {
      text: 'BOL TREND' +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : ''),
      left: 'left'
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        const x = params[0].name
        tip.push('<div>')
        tip.push(x)
        tip.push('<br>')
        let total = 0
        let marker = ''
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const seriesName = params[i].seriesName

          const value = params[i].value || 0
          total = total + value

          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push(' : ')
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span>')
          tip.push('</div>')
          marker = params[i].marker
        }
        tip.push('<div style="width:7.5rem;">')
        tip.push(marker)
        tip.push('Total')
        tip.push(' : ')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      type: 'category',
      data: raw.xAxis,
      boundaryGap: 0
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series: [{
      name: '0-3D',
      data: raw.yAxis1,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#4f8f15'
      }
    }, {
      name: '3-7D',
      data: raw.yAxis2,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#ccb601'
      }
    }, {
      name: '7-14D',
      data: raw.yAxis3,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#e2a705'
      }
    }, {
      name: '14-30D',
      data: raw.yAxis4,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#dc7318'
      }
    }, {
      name: '1-2M',
      data: raw.yAxis5,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#d45e21'
      }
    }, {
      name: '2-3M',
      data: raw.yAxis6,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#cb4a2a'
      }
    }, {
      name: '3-6M',
      data: raw.yAxis7,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#c63930'
      }
    }, {
      name: '>6M',
      data: raw.yAxis8,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#c12e34'
      }
    }]
  }
}

const resetTopStartEnd = () => {
  if (pageCtl.conditions.report2TopStart >= pageCtl.conditions.report2TopEnd) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 15
  } else if (pageCtl.conditions.report2TopEnd > pageCtl.conditions.report2TopStart + 50) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 50
  }
}

const _report2Opts = computed(() => {
  const series = []
  const legend = []
  for (const key in pageCtl.report2Data.series) {
    legend.push(key)
    series.push({
      name: key,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: pageCtl.report2Data.series[key]
    })
  }
  series.push({
    name: 'Total',
    type: 'line',
    symbolSize: 0,
    lineStyle: {
      width: 0
    },
    label: {
      show: true,
      position: 'right',
      valueAnimation: true
    },
    data: pageCtl.report2Data.total
  })
  return {
    title: {
      text: 'DELAY LINE TOP ' +
          (pageCtl.conditions.report2TopStart === 0 ? pageCtl.conditions.report2TopEnd : pageCtl.conditions.report2TopStart + ' ~ ' + pageCtl.conditions.report2TopEnd) +
          ' ' + pageCtl.conditions.report2Category +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report2Data.yAxis,
      axisLabel: {
        interval: 0
      }
    },
    series
  }
})

interface BOLTable {
  id: string,
  category: string
  bol: string
  bolVip: string
  bov: string,
  bovVip: string,
  backlogValue: string,
  backlogLine: string,
  bolPercent: string,
  bovPercent: string,
  parent: Array<string>,
  hasChildren?: boolean
  children?: BOLTable[]
}

const load = (
  row: BOLTable,
  treeNode: any,
  resolve: (date: BOLTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_bol/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'BOL'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report2Ref.value.resize()
  report3Ref.value.resize()
}

// 下面代码非必要, 不要动
// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage() // 每个子页面初始函数
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 获取父组件的参数
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的方法
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryBol'
}
</script>

<style lang="scss">
.my-story-bol {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
