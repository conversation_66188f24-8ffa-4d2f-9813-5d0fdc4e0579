<template>
  <div class="my-story-business-metrics-merged-card">
    <el-card style="margin: 0 0.125rem 0.125rem 0.125rem">
      <template #header>
        <div class="card-header">
          <span>Business Metrics</span>
        </div>
      </template>

      <!-- Multi-level Headers -->
      <div class="multi-level-headers">
        <!-- First Level Headers -->
        <div class="header-row first-level-header">
          <div class="header-cell entity-header border-right-white">
            Entity
          </div>
          <div class="header-cell group-header border-right-white">
            Order Intake
          </div>
          <div class="header-cell group-header border-right-white">
            Sales
          </div>
          <div class="header-cell group-header border-right-white">
            FCST
          </div>
          <div class="header-cell group-header">
            Backlog
          </div>
        </div>

        <!-- Second Level Headers -->
        <div class="header-row second-level-header">
          <div class="header-cell entity-header border-right-white">
            <!-- Empty for Entity column -->
          </div>
          <div class="sub-headers-group border-right-white">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
          <div class="sub-headers-group border-right-white">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
          <div class="sub-headers-group border-right-white">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
          <div class="sub-headers-group">
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} MTD
            </div>
            <div class="sub-header-cell border-right-thin">
              {{ monthName }} Y ON Y
            </div>
            <div class="sub-header-cell">
              WEIGHT
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-table
            :data="flattenedData"
            style="width: 100%; table-layout: fixed;"
            row-key="id"
            :show-header="false"
            @expand-change="mergedCardExpandChange"
            border
            lazy
            :load="load"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        class="tree-table"
        @row-dblclick="report1RowDblick"
        @sort-change="report1SortChange"
        @contextmenu.prevent=""
        size="small">

        <!-- Entity Column -->
        <el-table-column prop="entity" label="Entity" width="100" fixed="left">
          <template #default="scope">
              <span :style="{
                color: 'var(--scp-text-color-primary)',
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }">
                {{ scope.row.entity }}
              </span>
          </template>
        </el-table-column>

        <!-- Order Intake Columns -->
        <el-table-column label="Order Intake MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.orderIntake?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.orderIntake?.mtd || '--' }}
              <sup v-if="scope.row.orderIntake?.arrowValue !== undefined && scope.row.orderIntake?.arrowValue !== null && scope.row.orderIntake?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.orderIntake?.arrowColor)}">
                {{ formatArrowValue(scope.row.orderIntake?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Order Intake Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.orderIntake?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.orderIntake?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Order Intake Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.orderIntake?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.orderIntake?.weight || '--' }}
            </p>
          </template>
        </el-table-column>

        <!-- Sales Columns -->
        <el-table-column label="Sales MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.sales?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.sales?.mtd || '--' }}
              <sup v-if="scope.row.sales?.arrowValue !== undefined && scope.row.sales?.arrowValue !== null && scope.row.sales?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.sales?.arrowColor)}">
                {{ formatArrowValue(scope.row.sales?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Sales Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.sales?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.sales?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Sales Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.sales?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.sales?.weight || '--' }}
            </p>
          </template>
        </el-table-column>

        <!-- FCST Columns -->
        <el-table-column label="FCST MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.fcst?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.fcst?.mtd || '--' }}
              <sup v-if="scope.row.fcst?.arrowValue !== undefined && scope.row.fcst?.arrowValue !== null && scope.row.fcst?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.fcst?.arrowColor)}">
                {{ formatArrowValue(scope.row.fcst?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="FCST Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.fcst?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.fcst?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="FCST Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.fcst?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.fcst?.weight || '--' }}
            </p>
          </template>
        </el-table-column>

        <!-- Backlog Columns -->
        <el-table-column label="Backlog MTD" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.backlog?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.backlog?.mtd || '--' }}
              <sup v-if="scope.row.backlog?.arrowValue !== undefined && scope.row.backlog?.arrowValue !== null && scope.row.backlog?.arrowValue !== 0"
                   class="arrow-percentage"
                   :style="{color: getArrowColor(scope.row.backlog?.arrowColor)}">
                {{ formatArrowValue(scope.row.backlog?.arrowValue) }}
              </sup>
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Backlog Y ON Y" min-width="100">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.backlog?.yoyColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.backlog?.yoy || '--' }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="Backlog Weight" min-width="80">
          <template #default="scope">
            <p :style="{
                color: convertValueColor(scope.row.backlog?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
              {{ scope.row.backlog?.weight || '--' }}
            </p>
          </template>
        </el-table-column>
        </el-table>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, ref } from 'vue'

interface CardData {
  rowType: string;
  value1: string;
  value1Color?: string;
  value2: string | number;
  value2Color?: string;
  value3: string | number;
  value3Color?: string;
  value4?: string | number;
  value4Color?: string;
  arrow?: string;
  arrowColor?: string;
  arrowValue?: number;
  children?: CardData[];
}

interface MergedCardData {
  id: string;
  entity: string;
  rowType: string;
  orderIntake?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  sales?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  fcst?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  backlog?: {
    mtd: string;
    mtdColor?: string;
    yoy: string;
    yoyColor?: string;
    weight: string;
    weightColor?: string;
    arrowValue?: number;
    arrowColor?: string;
  };
  children?: MergedCardData[];
}

const props = withDefaults(defineProps<{
  monthName: string;
  orderIntakeData?: Array<CardData>;
  salesData?: Array<CardData>;
  fcstData?: Array<CardData>;
  backlogData?: Array<CardData>;
}>(), {
  monthName: '',
  orderIntakeData: () => [],
  salesData: () => [],
  fcstData: () => [],
  backlogData: () => []
})

// 新增一个 ref 来存储加载条件
const loadConditions = ref({
  expandCategory: '',
  parentName: [],
  expandValue: ''
})

// 新增一个方法来加载子数据
const load = (tree: any, treeNode: any, resolve: (data: any[]) => void) => {
  // 设置加载条件
  loadConditions.value.expandCategory = tree.category
  loadConditions.value.parentName = tree.parentName || []
  loadConditions.value.expandValue = tree.category

  // 调用 API 获取子数据
  const $axios = inject('$axios')
  $axios({
    method: 'post',
    url: '/demand/my_story_business_metrics/query_report1_sub',
    data: {
      ...loadConditions.value,
      parentName: loadConditions.value.parentName,
      expandValue: loadConditions.value.expandValue
    }
  }).then((body) => {
    // 解析返回的数据并调用 resolve 函数
    resolve(parseReport1Data(body))
  }).catch((error) => {
    console.log(error)
  })
}

// 解析返回的数据（根据实际返回的数据结构进行调整）
const parseReport1Data = (body: any) => {
  // 假设 body 是一个数组，每个元素包含子节点数据
  return body.map((item: any) => ({
    id: item.id,
    entity: item.entity,
    orderIntake: {
      mtd: item.orderIntake?.mtd,
      mtdColor: item.orderIntake?.mtdColor,
      yoy: item.orderIntake?.yoy,
      yoyColor: item.orderIntake?.yoyColor,
      weight: item.orderIntake?.weight,
      weightColor: item.orderIntake?.weightColor,
      arrowValue: item.orderIntake?.arrowValue,
      arrowColor: item.orderIntake?.arrowColor
    },
    sales: {
      mtd: item.sales?.mtd,
      mtdColor: item.sales?.mtdColor,
      yoy: item.sales?.yoy,
      yoyColor: item.sales?.yoyColor,
      weight: item.sales?.weight,
      weightColor: item.sales?.weightColor,
      arrowValue: item.sales?.arrowValue,
      arrowColor: item.sales?.arrowColor
    },
    fcst: {
      mtd: item.fcst?.mtd,
      mtdColor: item.fcst?.mtdColor,
      yoy: item.fcst?.yoy,
      yoyColor: item.fcst?.yoyColor,
      weight: item.fcst?.weight,
      weightColor: item.fcst?.weightColor,
      arrowValue: item.fcst?.arrowValue,
      arrowColor: item.fcst?.arrowColor
    },
    backlog: {
      mtd: item.backlog?.mtd,
      mtdColor: item.backlog?.mtdColor,
      yoy: item.backlog?.yoy,
      yoyColor: item.backlog?.yoyColor,
      weight: item.backlog?.weight,
      weightColor: item.backlog?.weightColor,
      arrowValue: item.backlog?.arrowValue,
      arrowColor: item.backlog?.arrowColor
    },
    children: item.children || [] // 确保有子节点
  }))
}

// Merge data from four card types
const mergedData = computed(() => {
  const entityMap = new Map<string, MergedCardData>()

  // Helper function to process card data
  const processCardData = (data: CardData[], cardType: string) => {
    data.forEach(item => {
      const entityKey = item.value1
      if (!entityMap.has(entityKey)) {
        entityMap.set(entityKey, {
          id: entityKey,
          entity: entityKey,
          rowType: item.rowType,
          children: [] // 确保有子节点
        })
      }

      const mergedItem = entityMap.get(entityKey)!
      const cardData = {
        mtd: item.value2?.toString() || '--',
        mtdColor: item.value2Color,
        yoy: item.value3?.toString() || '--',
        yoyColor: item.value3Color,
        weight: item.value4?.toString() || '--',
        weightColor: item.value4Color,
        arrowValue: item.arrowValue,
        arrowColor: item.arrowColor
      }

      if (cardType === 'orderIntake') {
        mergedItem.orderIntake = cardData
      } else if (cardType === 'sales') {
        mergedItem.sales = cardData
      } else if (cardType === 'fcst') {
        mergedItem.fcst = cardData
      } else if (cardType === 'backlog') {
        mergedItem.backlog = cardData
      }

      // Process children
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          const childKey = `${entityKey}_${child.value1}`
          let childItem = mergedItem.children?.find(c => c.id === childKey)
          if (!childItem) {
            childItem = {
              id: childKey,
              entity: child.value1,
              rowType: child.rowType,
              children: [] // 确保有子节点
            }
            mergedItem.children?.push(childItem)
          }

          const childCardData = {
            mtd: child.value2?.toString() || '--',
            mtdColor: child.value2Color,
            yoy: child.value3?.toString() || '--',
            yoyColor: child.value3Color,
            weight: child.value4?.toString() || '--',
            weightColor: child.value4Color,
            arrowValue: child.arrowValue,
            arrowColor: child.arrowColor
          }

          if (cardType === 'orderIntake') {
            childItem.orderIntake = childCardData
          } else if (cardType === 'sales') {
            childItem.sales = childCardData
          } else if (cardType === 'fcst') {
            childItem.fcst = childCardData
          } else if (cardType === 'backlog') {
            childItem.backlog = childCardData
          }
        })
      }
    })
  }

  // Process all four card types
  processCardData(props.orderIntakeData, 'orderIntake')
  processCardData(props.salesData, 'sales')
  processCardData(props.fcstData, 'fcst')
  processCardData(props.backlogData, 'backlog')

  return Array.from(entityMap.values())
})

// Function to flatten tree data structure
const flattenTreeData = (data: MergedCardData[]): MergedCardData[] => {
  const result: MergedCardData[] = []

  const flatten = (items: MergedCardData[]) => {
    items.forEach(item => {
      // Add the current item (without children property to avoid tree structure)
      const flatItem = { ...item }
      delete flatItem.children
      result.push(flatItem)

      // If item has children, recursively flatten them
      if (item.children && item.children.length > 0) {
        flatten(item.children)
      }
    })
  }

  flatten(data)
  return result
}

// Flattened data for direct display
const flattenedData = computed(() => {
  return flattenTreeData(mergedData.value)
})

const getArrowColor = (arrowColor) => {
  if (arrowColor === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (arrowColor === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-warning)'
  }
}

const formatArrowValue = (value) => {
  if (value === undefined || value === null || value === '' || value === 0) {
    return ''
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) {
    return ''
  }

  const sign = numValue >= 0 ? '+' : ''
  let displayValue = numValue
  if (Math.abs(numValue) >= 1000) {
    displayValue = (numValue / 1000).toFixed(1) + 'K'
  } else {
    displayValue = numValue.toFixed(1)
  }

  return `${sign}${displayValue}%`
}

const convertValueColor = (colorName) => {
  if (colorName === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (colorName === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-primary)'
  }
}
</script>

<script lang="ts">
export default {
  name: 'MyStoryBusinessMetricsMergedCard'
}
</script>

<style lang="scss">
.my-story-business-metrics-merged-card {
  .el-card__header {
    padding: 0.25rem !important;
    text-align: center;
    font-size: 0.55rem !important;
    font-weight: bold;
    color: #fff;
    background-color: var(--scp-bg-color-highlight) !important;
  }

  .el-card__body {
    color: #fff;
    padding: 0.15rem 0 !important;
    background-color: var(--scp-bg-color-highlight) !important;
  }

  .multi-level-headers {
    .header-row {
      display: flex;
      width: 100%;

      &.first-level-header {
        .header-cell {
          text-align: center;
          font-size: 0.5rem;
          font-weight: bold;
          padding: 0.25rem 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .entity-header {
          width: 100px;
          flex-shrink: 0;
        }

        .group-header {
          flex: 1;
          min-width: 280px;
        }
      }

      &.second-level-header {
        .entity-header {
          width: 100px;
          flex-shrink: 0;
        }

        .sub-headers-group {
          flex: 1;
          display: flex;
          min-width: 280px;

          .sub-header-cell {
            flex: 1;
            text-align: center;
            font-size: 0.45rem;
            font-weight: normal;
            padding: 0.2rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .border-right-white {
      border-right: 1px solid #fff;
    }

    .border-right-thin {
      border-right: 1px solid rgba(255, 255, 255, 0.3);
    }
  }

  .el-card__footer {
    padding: var(--scp-widget-margin);

    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: 0 !important;
    }

    .el-table__indent {
      display: none !important;
    }

    .el-table__placeholder {
      display: none !important;
    }

    .el-table__empty-block {
      min-height: auto !important;
    }

    .el-table__empty-text {
      line-height: 0.94rem !important;
    }

    .el-table--small .el-table__cell {
      padding: 0 !important;
      overflow: visible !important;
    }

    .el-table .cell {
      line-height: 21px !important;
      overflow: visible !important;
    }
  }

  .arrow-percentage {
    font-size: 0.35rem !important;
    font-weight: normal !important;
    line-height: 1;
    margin-left: 2px;
    vertical-align: super;
  }
}
</style>
