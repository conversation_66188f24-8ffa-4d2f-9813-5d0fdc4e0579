<template>
  <el-collapse v-model="pageCtl.activeNames">
    <el-collapse-item title="OTC" name="OTC">
      <template v-slot:title>
        <el-breadcrumb separator="/" class="component-header" style="margin-left: 5px;">
          <el-breadcrumb-item>
            <b>
              <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard</b>
          </el-breadcrumb-item>
          <el-breadcrumb-item>OTC</el-breadcrumb-item>
        </el-breadcrumb>
      </template>

      <el-row id="myStoryOTC">
        <el-col :span="24">
          <div class="subscript-container">
            <scp-subscript id="EMPTY"/>
            <h1>OTC</h1>
          </div>
        </el-col>
      </el-row>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts" setup>
import { onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

const pageCtl = reactive({
  activeNames: ['OTC'],
  visible: {
    win: false
  }
})

onMounted(() => {

})
</script>

<script lang="ts">
export default {
  name: 'MyStoryOtc'
}
</script>

<style lang="scss">
#myStoryOTC {

}
</style>
