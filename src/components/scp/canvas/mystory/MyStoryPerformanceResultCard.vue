<template>
  <div class="my-story-performance-result-card" :class="props.cardType === 'PI'? 'my-story-performance-result-card-pi':'my-story-performance-result-card-kpi'">
    <el-card style="margin: 0 0.5rem 0.5rem 0.5rem">
      <template #header>
        <div class="card-header">
          <span>{{ props.title }}</span>
        </div>
      </template>
      <el-row v-if="!!props.subTitle3">
        <el-col :span="8" class="sub-sub-title border-right-white">
          {{ props.subTitle1 }}
        </el-col>
        <el-col :span="8" class="sub-sub-title border-right-white">
          {{ props.subTitle2 }}
        </el-col>
        <el-col :span="8" class="sub-sub-title">
          {{ props.subTitle3 }}
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="8" class="sub-sub-title border-right-white">
          {{ props.subTitle1 }}
        </el-col>
        <el-col :span="16" class="sub-sub-title border-right-white">
          {{ props.subTitle2 }}
        </el-col>
      </el-row>
      <template #footer>
        <el-table
            :data="props.data"
            style="width: 100%;"
            row-key="id"
            :show-header="false">
          <el-table-column prop="value1" label="value1">
            <template #default="scope">
              <span :style="{color:convertValueColor(scope.row.value1Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }">
                {{ scope.row.value1 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="value2" label="value2">
            <template #default="scope">
              <p :style="{color:convertValueColor(scope.row.value2Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                 style="text-align: center">
                {{ scope.row.value2 }}
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="value3" label="value3" v-if="!!props.subTitle3">
            <template #default="scope">
              <p :style="{color:convertValueColor(scope.row.value3Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                 style="text-align: center">
                {{ scope.row.value3 }}
                <font-awesome-icon v-if="!!scope.row.arrow" :icon="getArrow(scope.row.arrow)"
                                   :style="{color: getArrowColor(scope.row.arrowColor)}"
                                   style="margin-bottom: 1px;"/>
              </p>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive } from 'vue'

const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const pageCtl = reactive({
  conditions: {}
})

interface CardData {
  rowType: string,
  value1: string,
  value1Color?: string,
  value2: string | number,
  value2Color?: string,
  value3: string | number,
  value3Color?: string,
  arrow?: string,
  arrowColor?: string
}

// @ts-ignore
const props = withDefaults(defineProps<{
  cardType: string,
  title: string,
  subTitle1?: string,
  subTitle2: string,
  subTitle3: string,
  data?: Array<CardData>
}>(), {
  cardType: 'PI',
  title: '',
  subTitle1: 'Target',
  subTitle2: '',
  subTitle3: '',
  data: () => []
})

const getArrowColor = (arrowColor) => {
  if (arrowColor === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (arrowColor === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-warning)'
  }
}

const getArrow = (arrow) => {
  if (arrow === 'up') {
    return 'up-long'
  } else if (arrow === 'down') {
    return 'down-long'
  } else {
    return 'right-long'
  }
}

const convertValueColor = (colorName) => {
  if (colorName === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (colorName === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-primary)'
  }
}
</script>

<script lang="ts">
</script>
export default {
name: 'MyStoryPerformanceResultCard'
}

<style lang="scss">
.my-story-performance-result-card {
  .border-right-white {
    border-right: 1px solid #fff;
  }

  .el-card__header {
    padding: 0.25rem !important;
    text-align: center;
    font-size: 0.55rem !important;
    font-weight: bold;
    color: #fff;
  }

  .el-card__body {
    color: #fff;
    padding: 0.15rem 0 !important;
  }

  .sub-sub-title {
    text-align: center;
    font-size: 0.5rem;
    font-weight: bold;
  }

  .el-card__footer {
    padding: var(--scp-widget-margin);

    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: 0 !important;
    }

    .el-table__indent {
      display: none !important;
    }

    .el-table__placeholder {
      display: none !important;
    }

    .el-table__empty-block {
      min-height: auto !important;
    }

    .el-table__empty-text {
      line-height: 0.94rem !important;
    }

    .el-table--small .el-table__cell {
      padding: 0 !important;
    }
    .el-table .cell {
      line-height: 21px !important;
    }
  }
}

.my-story-performance-result-card-pi {
  .el-card__header {
    background-color: var(--scp-bg-color-highlight) !important;
  }

  .el-card__body {
    background-color: var(--scp-bg-color-highlight) !important;
  }
}

.my-story-performance-result-card-kpi {
  .el-card__header {
    background-color: #359c23 !important;
  }

  .el-card__body {
    background-color: #359c23 !important;
  }
}
</style>
