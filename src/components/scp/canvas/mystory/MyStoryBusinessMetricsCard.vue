<template>
  <div class="my-story-business-metrics-card" :class="props.cardType === 'PI'? 'my-story-business-metrics-card-pi':'my-story-business-metrics-card-kpi'">
    <el-card style="margin: 0 0.125rem 0.125rem 0.125rem">
      <template #header>
        <div class="card-header">
          <span>{{ props.title }}</span>
        </div>
      </template>
      <el-row v-if="!!props.subTitle4">
        <el-col :span="6" class="sub-sub-title border-right-white">
          {{ props.subTitle1 }}
        </el-col>
        <el-col :span="6" class="sub-sub-title border-right-white">
          {{ props.subTitle2 }}
        </el-col>
        <el-col :span="6" class="sub-sub-title border-right-white">
          {{ props.subTitle3 }}
        </el-col>
        <el-col :span="6" class="sub-sub-title">
          {{ props.subTitle4 }}
        </el-col>
      </el-row>
      <el-row v-else-if="!!props.subTitle3">
        <el-col :span="8" class="sub-sub-title border-right-white">
          {{ props.subTitle1 }}
        </el-col>
        <el-col :span="8" class="sub-sub-title border-right-white">
          {{ props.subTitle2 }}
        </el-col>
        <el-col :span="8" class="sub-sub-title">
          {{ props.subTitle3 }}
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="8" class="sub-sub-title border-right-white">
          {{ props.subTitle1 }}
        </el-col>
        <el-col :span="16" class="sub-sub-title">
          {{ props.subTitle2 }}
        </el-col>
      </el-row>
      <template #footer>
        <el-table
            :data="props.data"
            style="width: 100%;"
            row-key="id"
            :show-header="false">
          <el-table-column prop="value1" label="value1">
            <template #default="scope">
              <span :style="{color:convertValueColor(scope.row.value1Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }">
                {{ scope.row.value1 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="value2" label="value2">
            <template #default="scope">
              <p :style="{color:convertValueColor(scope.row.value2Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                 style="text-align: center">
                {{ scope.row.value2 }}<sup v-if="scope.row.arrowValue !== undefined && scope.row.arrowValue !== null && scope.row.arrowValue !== 0"
                class="arrow-percentage"
                :style="{color: getArrowColor(scope.row.arrowColor)}">{{ formatArrowValue(scope.row.arrowValue) }}</sup>

              </p>
            </template>
          </el-table-column>
          <el-table-column prop="value3" label="value3" v-if="!!props.subTitle3 || !!props.subTitle4">
            <template #default="scope">
              <p :style="{color:convertValueColor(scope.row.value3Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                 style="text-align: center; overflow: visible;">
                {{ scope.row.value3 }}
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="value4" label="value4" v-if="!!props.subTitle4">
            <template #default="scope">
              <p :style="{color:convertValueColor(scope.row.value4Color), fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                    fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem' }"
                 style="text-align: center; overflow: visible;">
                {{ scope.row.value4 }}
              </p>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive } from 'vue'

const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const pageCtl = reactive({
  conditions: {}
})

interface CardData {
  rowType: string,
  value1: string,
  value1Color?: string,
  value2: string | number,
  value2Color?: string,
  value3: string | number,
  value3Color?: string,
  value4?: string | number,
  value4Color?: string,
  arrow?: string,
  arrowColor?: string,
  arrowValue?: number
}

// @ts-ignore
const props = withDefaults(defineProps<{
  cardType: string,
  title: string,
  subTitle1?: string,
  subTitle2: string,
  subTitle3: string,
  subTitle4: string,
  data?: Array<CardData>
}>(), {
  cardType: 'PI',
  title: '',
  subTitle1: 'Entity',
  subTitle2: '',
  subTitle3: '',
  subTitle4: '',
  data: () => []
})

const getArrowColor = (arrowColor) => {
  if (arrowColor === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (arrowColor === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-warning)'
  }
}

const formatArrowValue = (value) => {
  if (value === undefined || value === null || value === '' || value === 0) {
    return ''
  }

  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) {
    return ''
  }

  // Determine sign based on the value itself
  const sign = numValue >= 0 ? '+' : '' // Negative numbers already have the minus sign

  // For display, we might want to format large numbers
  let displayValue = numValue
  if (Math.abs(numValue) >= 1000) {
    displayValue = (numValue / 1000).toFixed(1) + 'K'
  } else {
    displayValue = numValue.toFixed(1)
  }

  return `${sign}${displayValue}%`
}

const convertValueColor = (colorName) => {
  if (colorName === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (colorName === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-primary)'
  }
}
</script>

<script lang="ts">
</script>
export default {
  name: 'MyStoryBusinessMetricsCard'
}

<style lang="scss">
.my-story-business-metrics-card {
  .border-right-white {
    border-right: 1px solid #fff;
  }

  .el-card__header {
    padding: 0.25rem !important;
    text-align: center;
    font-size: 0.55rem !important;
    font-weight: bold;
    color: #fff;
  }

  .el-card__body {
    color: #fff;
    padding: 0.15rem 0 !important;
  }

  .sub-sub-title {
    text-align: center;
    font-size: 0.5rem;
    font-weight: bold;
  }

  .el-card__footer {
    padding: var(--scp-widget-margin);

    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: 0 !important;
    }

    .el-table__indent {
      display: none !important;
    }

    .el-table__placeholder {
      display: none !important;
    }

    .el-table__empty-block {
      min-height: auto !important;
    }

    .el-table__empty-text {
      line-height: 0.94rem !important;
    }

    .el-table--small .el-table__cell {
      padding: 0 !important;
      overflow: visible !important;
    }
    .el-table .cell {
      line-height: 21px !important;
      overflow: visible !important;
    }
  }

  .arrow-percentage {
    font-size: 0.35rem !important;
    font-weight: normal !important;
    line-height: 1;
    margin-left: 2px;
    vertical-align: super;
  }
}

.my-story-business-metrics-card-pi {
  .el-card__header {
    background-color: var(--scp-bg-color-highlight) !important;
  }

  .el-card__body {
    background-color: var(--scp-bg-color-highlight) !important;
  }
}

.my-story-business-metrics-card-kpi {
  .el-card__header {
    background-color: #359c23 !important;
  }

  .el-card__body {
    background-color: #359c23 !important;
  }
}
</style>
