<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / Inventory
  </div>

  <el-row class="my-story-inventory">
    <el-col :span="24">
      <div class="subscript-container tree-table-box" @contextmenu.prevent="" v-loading="pageCtl.loading.report1 || pageCtl.loading.init"
        :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSINV1" ref="msinv1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category" :min-width="260">
              <template #header>
                <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                           collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column :prop="item.key" :label="item.title" sortable :sort-method="(a, b) => report1Sort(a, b, item.key)"
                             v-for="item in _report1Columns" :min-width="item.width" :key="item.key">
              <template v-slot="scope">
                    <span v-if="item.key.indexOf('PERCENT') === -1">
                      {{ $thousandBitSeparator(scope.row[item.key], 0) }}
                    </span>
                <span v-else>
                      {{ $thousandBitSeparator(scope.row[item.key], 1) }}
                    </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="4">Inventory Filter</el-col>
              <el-col :span="8">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Display</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.availableColumns"
                      :key="item.key"
                      :label="item.title"
                      :value="item.key">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Qty / Value</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1ValueType" placeholder="Value Type"
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['Qty', 'Value']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msinv1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msinv1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="10">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSINV2" ref="msinv2Ref"/>
        <div class="front">
          <chart ref="report2Ref" :height="_part2Height" :option="_report2Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2Category" placeholder="Category" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Sort By</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2SortBy" placeholder="Sort By" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.availableColumns"
                      :key="item.key"
                      :label="item.title"
                      :value="item.key">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Qty / Value</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2ValueType" placeholder="Value Type"
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['Qty', 'Value']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Stack by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2StackBy" placeholder="Stack by" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.stackOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Top</el-col>
              <el-col :span="16">
                <el-input-number :min="0" v-model="pageCtl.conditions.report2TopStart" style="width: 45%" @change="resetTopStartEnd"/>
                ~
                <el-input-number :min="0" v-model="pageCtl.conditions.report2TopEnd" style="width: 45%" @change="resetTopStartEnd"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msinv2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msinv2Ref.toggleView();searchReport2()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="14">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSINV3" ref="msinv3Ref"/>
        <div class="front">
          <chart ref="report3Ref" :height="_part2Height" :option="_report3Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Value Type</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3Category" placeholder="Value Type" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in _report3CategoryOpts"
                      :key="item.key"
                      :label="item.title"
                      :value="item.key">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Qty / Value</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3ValueType" placeholder="Value Type" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['Qty', 'Value']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Time Range</el-col>
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.report3DateRange"
                    type="daterange"
                    unlink-panels
                    range-separator="~"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :clearable="false"
                    prefix-icon=null
                    style="width: calc(100% - 1.333rem) !important;">
                </el-date-picker>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msinv3Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msinv3Ref.toggleView();searchReport3()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $shortenNumber: any = inject('$shortenNumber')
const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const msinv1Ref: any = ref()
const msinv2Ref: any = ref()
const msinv3Ref: any = ref()
const report2Ref: any = ref()
const report3Ref: any = ref()
const $deepClone: any = inject('$deepClone')

const pageCtl = reactive({
  report1Data: [],
  report2Data: { yAxis: [], total: [], series: [] },
  report3Data: { yAxis: [], xAxis: [], legend: [] },
  filterOpts: [],
  stackOpts: [],
  availableColumns: [
    { title: 'Gross Inv', key: 'GROSS_INV', width: 120 },
    { title: 'Finish Goods', key: 'FG', width: 120 },
    { title: 'Semi-FG', key: 'SEMI_FG', width: 120 },
    { title: 'Raw Material', key: 'RM', width: 120 },
    { title: 'GIT', key: 'GIT', width: 120 },
    { title: 'WIP', key: 'WIP', width: 120 },
    { title: 'UHS-Gross Inv', key: 'GROSS_UHS', width: 120 },
    { title: 'UHS-Theo.Prov', key: 'THEO_PROVISION', width: 120 },
    { title: 'UHS-Excess', key: 'EXCESS', width: 120 },
    { title: 'UHS-Missing', key: 'MISSING', width: 120 },
    { title: 'UHS-Spec.Event', key: 'SPECIAL', width: 130 },
    { title: 'Gross UHS%', key: 'GROSS_PERCENT', width: 120 },
    { title: 'Net UHS%', key: 'NET_PERCENT', width: 120 },
    { title: 'Entity Top20', key: 'TOP20_ENTITY', width: 130 },
    { title: 'Mat.Owner Top20', key: 'TOP20_PLANNER', width: 140 },
    { title: 'Block-FG', key: 'BLOCK_FG', width: 120 },
    { title: 'Block-RM', key: 'BLOCK_RM', width: 120 },
    { title: 'Aging > 2Y', key: 'AGING_GT_2Y', width: 120 },
    { title: 'Aging <= 2Y', key: 'AGING_LT_2Y', width: 120 },
    { title: 'Aging <= 1Y', key: 'AGING_LT_1Y', width: 120 },
    { title: 'UD <= 1W', key: 'UD_LT_7_CD', width: 120 },
    { title: 'UD <= 1M', key: 'UD_LT_30_CD', width: 120 },
    { title: 'UD > 1M', key: 'UD_GT_30_CD', width: 120 },
    { title: 'Fulfill SO M0', key: 'UNRESTRICT_M0_SO', width: 130 },
    { title: 'Fulfill SO M1', key: 'UNRESTRICT_M1_SO', width: 130 },
    { title: 'Fulfill SO >M1', key: 'UNRESTRICT_GT_M1_SO', width: 130 },
    { title: 'Non-Block Fulfill SO M0', key: 'NONBLOCK_M0_SO', width: 180 },
    { title: 'Non-Block Fulfill SO M1', key: 'NONBLOCK_M1_SO', width: 180 },
    { title: 'Non-Block Fulfill SO >M1', key: 'NONBLOCK_GT_M1_SO', width: 180 },
    { title: 'Vendor Consignment', key: 'VENDOR_CONSIGN', width: 180 }],
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {},
    filterList: [['MATERIAL_TYPE', 'DIEN-SERVICE'], ['MATERIAL_TYPE', 'Others'], ['MATERIAL_TYPE', 'ERSA-SPARE'], ['MATERIAL_TYPE', 'HALB-SEMI_FG'],
      ['MATERIAL_TYPE', 'HAWA-FG'], ['MATERIAL_TYPE', 'ROH-RM'], ['MATERIAL_TYPE', 'VERP-PACKAGING'], ['MATERIAL_TYPE', 'WIP_BLANK_MATERIAL']],
    report1ValueType: 'Value',
    report1Display: ['GROSS_INV', 'FG', 'UNRESTRICT_M0_SO', 'UNRESTRICT_M1_SO', 'UNRESTRICT_GT_M1_SO', 'UD_LT_7_CD', 'UD_LT_30_CD', 'UD_GT_30_CD'],
    report1Categories: ['CLUSTER_NAME', 'ENTITY'],
    report1SelectedValues: [],
    report2TopStart: 0,
    report2TopEnd: 15,
    report2Category: 'MATERIAL',
    report2StackBy: 'CLUSTER_NAME',
    report2SortBy: 'GROSS_INV',
    report2ValueType: 'Value',
    report3Category: 'GROSS_INV',
    report3DateRange: [],
    report3ValueType: 'Value',
    tooltips: []
  },
  pivotOpts: [],
  visible: {
    win: false
  }
})

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 7 * 9)
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_inventory/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.stackOpts = body.stackOpts
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_inventory/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_inventory/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_inventory/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1RowDblick = (row) => {
  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
}

const report1SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const _report1Columns = computed(() => {
  const result = []
  for (let i = 0; i < pageCtl.conditions.report1Display.length; i++) {
    const key = pageCtl.conditions.report1Display[i]
    for (let j = 0; j < pageCtl.availableColumns.length; j++) {
      const column = pageCtl.availableColumns[j]
      if (column.key === key) {
        result.push(column)
        break
      }
    }
  }
  return result
})

const _report2SortByName = computed(() => {
  for (let j = 0; j < pageCtl.availableColumns.length; j++) {
    const column = pageCtl.availableColumns[j]
    if (column.key === pageCtl.conditions.report2SortBy) {
      return column.title
    }
  }
  return pageCtl.conditions.report2SortBy
})

const _report3CategoryName = computed(() => {
  for (let j = 0; j < pageCtl.availableColumns.length; j++) {
    const column = pageCtl.availableColumns[j]
    if (column.key === pageCtl.conditions.report3Category) {
      return column.title
    }
  }
  return pageCtl.conditions.report3Category
})

const _report2Opts = computed(() => {
  const series = []
  const legend = []
  for (const key in pageCtl.report2Data.series) {
    legend.push(key)
    series.push({
      name: key,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: pageCtl.report2Data.series[key]
    })
  }
  series.push({
    name: 'Total',
    type: 'line',
    symbolSize: 0,
    lineStyle: {
      width: 0
    },
    label: {
      show: true,
      position: 'right',
      valueAnimation: true,
      formatter: (a) => {
        return $shortenNumber(a.data)
      }
    },
    data: pageCtl.report2Data.total
  })
  return {
    title: {
      text: _report2SortByName.value + ' TOP ' +
          (pageCtl.conditions.report2TopStart === 0 ? pageCtl.conditions.report2TopEnd : pageCtl.conditions.report2TopStart + ' ~ ' + pageCtl.conditions.report2TopEnd) +
          ' ' + pageCtl.conditions.report2Category +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as Array<string>
        tip.push(params[0].name)
        for (let i = 0; i < params.length; i++) {
          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(params[i].data) || 0)
          tip.push('</span>')
          tip.push('</div>')
        }
        return tip.join('')
      }
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    grid: $grid(),
    legend: $legend({ data: legend }),
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report2Data.yAxis,
      axisLabel: {
        interval: 0
      }
    },
    series
  }
})

const _report3CategoryOpts = computed(() => {
  const result = []
  for (let i = 0; i < pageCtl.availableColumns.length; i++) {
    const key = pageCtl.availableColumns[i].key
    if (key.indexOf('AGING') === -1 && key.indexOf('_SO') === -1) {
      result.push(pageCtl.availableColumns[i])
    }
  }
  return result
})

const report3ValueRender = (val) => {
  if (pageCtl.conditions.report3ValueType === 'ONTIME_RATE') {
    return val + '%'
  } else {
    return $shortenNumber(val, 1)
  }
}

const _report3Opts = computed(() => {
  return {
    title: {
      text: _report3CategoryName.value + ' TRENDS' +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    grid: $grid(),
    legend: $legend(),
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (params.hasOwnProperty(i)) {
            const value = params[i].value || 0
            tip.push('<div style="width:9.5rem;">')
            const marker = params[i].marker

            tip.push(marker)
            tip.push(params[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push(report3ValueRender(value))
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      axisLabel: {},
      type: 'category',
      data: pageCtl.report3Data.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series: {
      name: _report3CategoryName.value,
      data: pageCtl.report3Data.yAxis,
      type: 'line'
    }
  }
})

const resetTopStartEnd = () => {
  if (pageCtl.conditions.report2TopStart >= pageCtl.conditions.report2TopEnd) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 15
  } else if (pageCtl.conditions.report2TopEnd > pageCtl.conditions.report2TopStart + 50) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 50
  }
}

const load = (
  row: any,
  treeNode: any,
  resolve: (date: any[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_inventory/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'Inventory'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report2Ref.value.resize()
  report3Ref.value.resize()
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryInventory'
}
</script>

<style lang="scss">
.my-story-inventory {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
