<template>
  <div class="my-story-kpi-merged-card">
    <el-card style="margin: 0 0.125rem 0.125rem 0.125rem">
      <template #header>
        <div class="card-header">
          <span>KPI Metrics</span>
        </div>
      </template>

      <!-- Multi-level Headers -->
      <div class="multi-level-headers">
        <!-- First Level Headers -->
        <div class="header-row first-level-header">
          <div class="header-cell entity-header border-right-white">
            Entity
          </div>
          <div class="header-cell group-header border-right-white">
            OTDS
          </div>
          <div class="header-cell group-header border-right-white">
            OTDM
          </div>
          <div class="header-cell group-header border-right-white">
            CLO Competitive Index
          </div>
          <div class="header-cell group-header">
            On Time Confirm(OPM)
          </div>
        </div>

        <!-- Second Level Headers -->
        <div class="header-row second-level-header">
          <div class="header-cell entity-header border-right-white">
            <!-- Empty for entity column -->
          </div>
          <div class="header-cell sub-header-group border-right-white">
            <div class="sub-header border-right-gray">{{ monthName }} MTD</div>
            <div class="sub-header border-right-gray">{{ monthName }} YTD</div>
            <div class="sub-header">WEIGHT</div>
          </div>
          <div class="header-cell sub-header-group border-right-white">
            <div class="sub-header border-right-gray">{{ monthName }} MTD</div>
            <div class="sub-header border-right-gray">{{ monthName }} YTD</div>
            <div class="sub-header">WEIGHT</div>
          </div>
          <div class="header-cell sub-header-group border-right-white">
            <div class="sub-header">Spot Level</div>
          </div>
          <div class="header-cell sub-header-group">
            <div class="sub-header border-right-gray">{{ monthName }} MTD</div>
            <div class="sub-header border-right-gray">{{ monthName }} YTD</div>
            <div class="sub-header">WEIGHT</div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-table
            :data="flattenedData"
            style="width: 100%; table-layout: fixed;"
            row-key="id"
            :show-header="false"
            size="small">
          <el-table-column prop="entity" label="Entity" width="100" fixed="left">
            <template #default="scope">
              <span :style="{
                color: 'var(--scp-text-color-primary)',
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }">
                {{ scope.row.entity }}
              </span>
            </template>
          </el-table-column>

          <!-- OTDS Columns -->
          <el-table-column label="OTDS MTD" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otds?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otds?.mtd || '--' }}
                <sup v-if="scope.row.otds?.arrowValue !== undefined && scope.row.otds?.arrowValue !== null && scope.row.otds?.arrowValue !== 0"
                    class="arrow-percentage"
                    :style="{color: getArrowColor(scope.row.otds?.arrowColor)}">
                  {{ formatArrowValue(scope.row.otds?.arrowValue) }}
                </sup>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="OTDS YTD" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otds?.ytdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otds?.ytd || '--' }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="OTDS Weight" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otds?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otds?.weight || '--' }}
              </p>
            </template>
          </el-table-column>

          <!-- OTDM Columns -->
          <el-table-column label="OTDM MTD" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otdm?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otdm?.mtd || '--' }}
                <sup v-if="scope.row.otdm?.arrowValue !== undefined && scope.row.otdm?.arrowValue !== null && scope.row.otdm?.arrowValue !== 0"
                    class="arrow-percentage"
                    :style="{color: getArrowColor(scope.row.otdm?.arrowColor)}">
                  {{ formatArrowValue(scope.row.otdm?.arrowValue) }}
                </sup>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="OTDM YTD" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otdm?.ytdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otdm?.ytd || '--' }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="OTDM Weight" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otdm?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otdm?.weight || '--' }}
              </p>
            </template>
          </el-table-column>

          <!-- CLO Columns -->
          <el-table-column label="CLO Spot Level" min-width="120">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.clo?.spotLevelColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.clo?.spotLevel || '--' }}
              </p>
            </template>
          </el-table-column>

          <!-- OTC Columns -->
          <el-table-column label="OTC MTD" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otc?.mtdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otc?.mtd || '--' }}
                <sup v-if="scope.row.otc?.arrowValue !== undefined && scope.row.otc?.arrowValue !== null && scope.row.otc?.arrowValue !== 0"
                    class="arrow-percentage"
                    :style="{color: getArrowColor(scope.row.otc?.arrowColor)}">
                  {{ formatArrowValue(scope.row.otc?.arrowValue) }}
                </sup>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="OTC YTD" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otc?.ytdColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otc?.ytd || '--' }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="OTC Weight" min-width="100">
            <template #default="scope">
              <p :style="{
                color: convertValueColor(scope.row.otc?.weightColor),
                fontWeight: scope.row.rowType === 'root' ? 'bold':'normal',
                fontSize: scope.row.rowType === 'root' ? '0.65rem':'0.5rem'
              }" style="text-align: center; margin: 0;">
                {{ scope.row.otc?.weight || '--' }}
              </p>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface CardData {
  rowType: string,
  value1: string,
  value1Color?: string,
  value2: string | number,
  value2Color?: string,
  value3: string | number,
  value3Color?: string,
  value4?: string | number,
  value4Color?: string,
  arrow?: string,
  arrowColor?: string,
  arrowValue?: number,
  children?: CardData[]
}

interface MergedKpiData {
  id: string,
  entity: string,
  rowType: string,
  otds?: {
    mtd: string,
    mtdColor?: string,
    ytd: string,
    ytdColor?: string,
    weight: string,
    weightColor?: string,
    arrowValue?: number,
    arrowColor?: string
  },
  otdm?: {
    mtd: string,
    mtdColor?: string,
    ytd: string,
    ytdColor?: string,
    weight: string,
    weightColor?: string,
    arrowValue?: number,
    arrowColor?: string
  },
  clo?: {
    spotLevel: string,
    spotLevelColor?: string
  },
  otc?: {
    mtd: string,
    mtdColor?: string,
    ytd: string,
    ytdColor?: string,
    weight: string,
    weightColor?: string,
    arrowValue?: number,
    arrowColor?: string
  },
  children?: MergedKpiData[]
}

const props = withDefaults(defineProps<{
  monthName: string,
  otdsData?: Array<CardData>,
  otdmData?: Array<CardData>,
  cloData?: Array<CardData>,
  otcData?: Array<CardData>
}>(), {
  monthName: '',
  otdsData: () => [],
  otdmData: () => [],
  cloData: () => [],
  otcData: () => []
})

// Merge data from four KPI card types
const mergedData = computed(() => {
  const entityMap = new Map<string, MergedKpiData>()

  // Helper function to process card data
  const processCardData = (data: CardData[], cardType: string) => {
    data.forEach(item => {
      const entityKey = item.value1
      if (!entityMap.has(entityKey)) {
        entityMap.set(entityKey, {
          id: entityKey,
          entity: entityKey,
          rowType: item.rowType,
          children: []
        })
      }

      const mergedItem = entityMap.get(entityKey)!

      if (cardType === 'otds') {
        mergedItem.otds = {
          mtd: item.value2?.toString() || '--',
          mtdColor: item.value2Color,
          ytd: item.value3?.toString() || '--',
          ytdColor: item.value3Color,
          weight: item.value4?.toString() || '--',
          weightColor: item.value4Color,
          arrowValue: item.arrowValue,
          arrowColor: item.arrowColor
        }
      } else if (cardType === 'otdm') {
        mergedItem.otdm = {
          mtd: item.value2?.toString() || '--',
          mtdColor: item.value2Color,
          ytd: item.value3?.toString() || '--',
          ytdColor: item.value3Color,
          weight: item.value4?.toString() || '--',
          weightColor: item.value4Color,
          arrowValue: item.arrowValue,
          arrowColor: item.arrowColor
        }
      } else if (cardType === 'clo') {
        mergedItem.clo = {
          spotLevel: item.value2?.toString() || '--',
          spotLevelColor: item.value2Color
        }
      } else if (cardType === 'otc') {
        mergedItem.otc = {
          mtd: item.value2?.toString() || '--',
          mtdColor: item.value2Color,
          ytd: item.value3?.toString() || '--',
          ytdColor: item.value3Color,
          weight: item.value4?.toString() || '--',
          weightColor: item.value4Color,
          arrowValue: item.arrowValue,
          arrowColor: item.arrowColor
        }
      }

      // Process children
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          const childKey = `${entityKey}_${child.value1}`
          let childItem = mergedItem.children?.find(c => c.id === childKey)
          if (!childItem) {
            childItem = {
              id: childKey,
              entity: child.value1,
              rowType: child.rowType
            }
            mergedItem.children?.push(childItem)
          }

          if (cardType === 'otds') {
            childItem.otds = {
              mtd: child.value2?.toString() || '--',
              mtdColor: child.value2Color,
              ytd: child.value3?.toString() || '--',
              ytdColor: child.value3Color,
              weight: child.value4?.toString() || '--',
              weightColor: child.value4Color,
              arrowValue: child.arrowValue,
              arrowColor: child.arrowColor
            }
          } else if (cardType === 'otdm') {
            childItem.otdm = {
              mtd: child.value2?.toString() || '--',
              mtdColor: child.value2Color,
              ytd: child.value3?.toString() || '--',
              ytdColor: child.value3Color,
              weight: child.value4?.toString() || '--',
              weightColor: child.value4Color,
              arrowValue: child.arrowValue,
              arrowColor: child.arrowColor
            }
          } else if (cardType === 'clo') {
            childItem.clo = {
              spotLevel: child.value2?.toString() || '--',
              spotLevelColor: child.value2Color
            }
          } else if (cardType === 'otc') {
            childItem.otc = {
              mtd: child.value2?.toString() || '--',
              mtdColor: child.value2Color,
              ytd: child.value3?.toString() || '--',
              ytdColor: child.value3Color,
              weight: child.value4?.toString() || '--',
              weightColor: child.value4Color,
              arrowValue: child.arrowValue,
              arrowColor: child.arrowColor
            }
          }
        })
      }
    })
  }

  // Process all four card types
  processCardData(props.otdsData, 'otds')
  processCardData(props.otdmData, 'otdm')
  processCardData(props.cloData, 'clo')
  processCardData(props.otcData, 'otc')

  return Array.from(entityMap.values())
})

// Function to flatten tree data structure
const flattenTreeData = (data: MergedKpiData[]): MergedKpiData[] => {
  const result: MergedKpiData[] = []

  const flatten = (items: MergedKpiData[]) => {
    items.forEach(item => {
      // Add the current item (without children property to avoid tree structure)
      const flatItem = { ...item }
      delete flatItem.children
      result.push(flatItem)

      // If item has children, recursively flatten them
      if (item.children && item.children.length > 0) {
        flatten(item.children)
      }
    })
  }

  flatten(data)
  return result
}

// Flattened data for direct display
const flattenedData = computed(() => {
  return flattenTreeData(mergedData.value)
})

const getArrowColor = (arrowColor) => {
  if (arrowColor === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (arrowColor === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-warning)'
  }
}

const formatArrowValue = (value) => {
  if (value === undefined || value === null || value === '' || value === 0) {
    return ''
  }

  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) {
    return ''
  }

  // Determine sign based on the value itself
  const sign = numValue >= 0 ? '+' : '' // Negative numbers already have the minus sign

  // For display, we might want to format large numbers
  let displayValue = numValue
  if (Math.abs(numValue) >= 1000) {
    displayValue = (numValue / 1000).toFixed(1) + 'K'
  } else {
    displayValue = numValue.toFixed(1)
  }

  return `${sign}${displayValue}%`
}

const convertValueColor = (colorName) => {
  if (colorName === 'red') {
    return 'var(--scp-text-color-error)'
  } else if (colorName === 'green') {
    return '#359c23'
  } else {
    return 'var(--scp-text-color-primary)'
  }
}
</script>

<script lang="ts">
export default {
  name: 'MyStoryKpiMergedCard'
}
</script>

<style lang="scss">
.my-story-kpi-merged-card {
  .el-card__header {
    padding: 0.25rem !important;
    text-align: center;
    font-size: 0.55rem !important;
    font-weight: bold;
    color: #fff;
    background-color: #359c23 !important;
  }

  .el-card__body {
    color: #fff;
    padding: 0 !important;
    background-color: #359c23 !important;
  }

  .multi-level-headers {
    .header-row {
      display: flex;
      width: 100%;

      &.first-level-header {
        .header-cell {
          font-size: 0.5rem;
          font-weight: bold;
          text-align: center;
          padding: 0.15rem 0;

          &.entity-header {
            width: 120px;
            flex-shrink: 0;
          }

          &.group-header {
            flex: 1;
          }
        }
      }

      &.second-level-header {
        .header-cell {
          font-size: 0.45rem;
          font-weight: normal;

          &.entity-header {
            width: 120px;
            flex-shrink: 0;
          }

          &.sub-header-group {
            flex: 1;
            display: flex;

            .sub-header {
              flex: 1;
              text-align: center;
              padding: 0.1rem 0;
            }
          }
        }
      }
    }
  }

  .border-right-white {
    border-right: 1px solid #fff;
  }

  .border-right-gray {
    border-right: 1px solid rgba(255, 255, 255, 0.3);
  }

  .el-card__footer {
    padding: var(--scp-widget-margin);

    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: 0 !important;
    }

    .el-table__indent {
      display: none !important;
    }

    .el-table__placeholder {
      display: none !important;
    }

    .el-table__empty-block {
      min-height: auto !important;
    }

    .el-table__empty-text {
      line-height: 0.94rem !important;
    }

    .el-table--small .el-table__cell {
      padding: 0 !important;
      overflow: visible !important;
    }

    .el-table .cell {
      line-height: 21px !important;
      overflow: visible !important;
    }
  }

  .arrow-percentage {
    font-size: 0.35rem !important;
    font-weight: normal !important;
    line-height: 1;
    margin-left: 2px;
    vertical-align: super;
  }
}
</style>
