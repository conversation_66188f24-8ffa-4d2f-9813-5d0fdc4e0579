<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / CLO
  </div>

  <el-row class="my-story-clo">
    <el-col :span="24">
      <div class="subscript-container tree-table-box" @contextmenu.prevent="" v-loading="pageCtl.loading.report1 || pageCtl.loading.init"
        :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSCLO1" ref="msclo1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category">
              <template #header>
                <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important;width: var(--scp-input-width) !important;"
                           placeholder="Pivot Columns" collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="meetLinesPercent" label="CLO Meet Value" sortable :sort-method="(a, b) => report1Sort(a, b, 'cloMeet')"
                             v-if="pageCtl.conditions.report1Display.indexOf('CLO_MEET') !== -1" :width="_report1ColumnWidth">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    CLO Meet: {{ $thousandBitSeparator(scope.row.meetLines, 1) }} ({{ $thousandBitSeparator(scope.row.meetLinesPercent, 1) }}%)
                    <br>CLO Fail: {{ $thousandBitSeparator(scope.row.failLines, 1) }} ({{ $thousandBitSeparator(scope.row.failLinesPercent, 1) }}%)
                  </template>
                  <my-story-clo-column :row="scope.row" :width="_report1ColumnWidth" name="meetLines"/>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="noOfMaterial" label="No. of Material" sortable :sort-method="(a, b) => report1Sort(a, b, 'noOfMaterial')"
                             v-if="pageCtl.conditions.report1Display.indexOf('NO_OF_MATERIAL') !== -1" :width="_report1ColumnWidth">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    No. of Material: {{ $thousandBitSeparator(scope.row.noOfMaterial, 1) }} ({{ $thousandBitSeparator(scope.row.noOfMaterialPercent, 1) }}%)
                  </template>
                  <my-story-clo-column :row="scope.row" :width="_report1ColumnWidth" name="noOfMaterial"/>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="noOfLine" label="No. of Line" sortable :sort-method="(a, b) => report1Sort(a, b, 'noOfLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('NO_OF_LINE') !== -1" :width="_report1ColumnWidth">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    No. of Line: {{ $thousandBitSeparator(scope.row.noOfLine, 1) }} ({{ $thousandBitSeparator(scope.row.noOfLinePercent, 1) }}%)
                  </template>
                  <my-story-clo-column :row="scope.row" :width="_report1ColumnWidth" name="noOfLine"/>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="noOfValue" label="No. of Value" sortable :sort-method="(a, b) => report1Sort(a, b, 'noOfValue')"
                             v-if="pageCtl.conditions.report1Display.indexOf('NO_OF_VALUE') !== -1" :width="_report1ColumnWidth">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    No. of Value: {{ $thousandBitSeparator(scope.row.noOfValue, 0) }} ({{ $thousandBitSeparator(scope.row.noOfValuePercent, 1) }}%)
                  </template>
                  <my-story-clo-column :row="scope.row" :width="_report1ColumnWidth" name="noOfValue"/>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="otds" label="OTDS" sortable :sort-method="(a, b) => report1Sort(a, b, 'otds')"
                             v-if="pageCtl.conditions.report1Display.indexOf('OTDS') !== -1" :width="_report1ColumnWidth">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    OnTime: {{ $thousandBitSeparator(scope.row.ontimeLines, 1) }} ({{ $thousandBitSeparator(scope.row.ontimeLinesPercent, 1) }}%)
                    <br>Delay: {{ $thousandBitSeparator(scope.row.delayLines, 1) }} ({{ $thousandBitSeparator(scope.row.delayLinesPercent, 1) }}%)
                  </template>
                  <my-story-clo-column :row="scope.row" :width="_report1ColumnWidth" name="ontimeLines"/>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="better" label="Shorten" sortable :sort-method="(a, b) => report1Sort(a, b, 'better')" width="90"
                             v-if="pageCtl.conditions.report1Display.indexOf('SHORTEN') !== -1">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    {{ $thousandBitSeparator(scope.row.better, 0) }}
                  </template>
                  <span>
                    {{ $shortenNumber(scope.row.better, 1) }}
                  </span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="worse" label="Extend" sortable :sort-method="(a, b) => report1Sort(a, b, 'worse')" width="90"
                             v-if="pageCtl.conditions.report1Display.indexOf('EXTEND') !== -1">
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="500" placement="top-end">
                  <template #content>
                    {{ $thousandBitSeparator(scope.row.worse, 0) }}
                  </template>
                  <span>
                    {{ $shortenNumber(scope.row.worse, 1) }}
                  </span>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="4">CLO Filter</el-col>
              <el-col :span="8">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Display</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.displayOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">CLO Date</el-col>
              <el-col :span="8">
                <el-date-picker
                    style="width: var(--scp-input-width)"
                    v-model="pageCtl.conditions.report1CLODate"
                    align="right"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    type="date"
                    :clearable="false"
                    placeholder="Date">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">SO Time Range</el-col>
              <el-col :span="8">
                <el-date-picker
                    style="width: calc(100% - 35px)"
                    size="small"
                    v-model="pageCtl.conditions.report1SOTimeRange"
                    type="monthrange"
                    unlink-panels
                    format="YYYYMM"
                    value-format="YYYYMM"
                    range-separator="to"
                    :clearable="false"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :picker-options="{}">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Reference Date</el-col>
              <el-col :span="8">
                <el-date-picker
                    style="width: var(--scp-input-width)"
                    v-model="pageCtl.conditions.report1ReferenceDate"
                    align="right"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    type="date"
                    :clearable="false"
                    placeholder="Date">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Target Date</el-col>
              <el-col :span="8">
                <el-date-picker
                    style="width: var(--scp-input-width)"
                    v-model="pageCtl.conditions.report1TargetDate"
                    align="right"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    type="date"
                    :clearable="false"
                    placeholder="Date">
                </el-date-picker>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msclo1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msclo1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="9">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSCLO2" ref="msclo2Ref"/>
        <div class="front">
          <chart ref="report2Ref" :height="_part2Height" :option="_report2Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="10">Category</el-col>
              <el-col :span="14">
                <el-select v-model="pageCtl.conditions.report2Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10">Merge Threshold</el-col>
              <el-col :span="14">
                <el-input-number v-model="pageCtl.conditions.report2MergeThreshold" style="width: var(--scp-input-width) !important;" :min="0" :max="100"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msclo2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msclo2Ref.toggleView();searchReport2()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="15">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSCLO3" ref="msclo3Ref"/>
        <div class="front">
          <chart ref="report3Ref" :height="_part2Height" :option="_report3Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">SO Time Range</el-col>
              <el-col :span="18">
                <el-date-picker
                    style="width: calc(100% - 35px)"
                    size="small"
                    v-model="pageCtl.conditions.report1SOTimeRange"
                    type="monthrange"
                    unlink-panels
                    format="YYYYMM"
                    value-format="YYYYMM"
                    range-separator="to"
                    :clearable="false"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :picker-options="{}">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">CLO Time Range</el-col>
              <el-col :span="18">
                <el-date-picker
                    style="width: calc(100% - 35px)"
                    size="small"
                    v-model="pageCtl.conditions.report3DateRange"
                    type="daterange"
                    unlink-panels
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    range-separator="to"
                    :clearable="false"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :picker-options="{}">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Display by</el-col>
              <el-col :span="18">
                <el-select v-model="pageCtl.conditions.report3DateType" style="width: calc(100% - 15px)">
                  <el-option v-for="item in ['Day', 'Week', 'Month']"
                             :key="item"
                             :label="item"
                             :value="item"/>
                </el-select>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msclo3Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msclo3Ref.toggleView();searchReport3()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'
import MyStoryCloColumn from '@/components/scp/canvas/mystory/MyStoryCLOColumn.vue'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $scaleLabel: any = inject('$echarts.label.scaleLable')
const $toFixed: any = inject('$toFixed')
const $shortenNumber: any = inject('$shortenNumber')
const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')

const msclo1Ref: any = ref()
const msclo2Ref: any = ref()
const msclo3Ref: any = ref()
const report2Ref: any = ref()
const report3Ref: any = ref()
const $deepClone: any = inject('$deepClone')

const pageCtl = reactive({
  report1Data: [],
  report2Data: [],
  report3Data: {},
  report2Title: '',
  report3Title: '',
  filterOpts: [],
  displayOpts: ['CLO_MEET', 'NO_OF_MATERIAL', 'NO_OF_LINE', 'NO_OF_VALUE', 'OTDS', 'SHORTEN', 'EXTEND'],
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {},
    filterList: [
      ['T0_ACTIVENESS', 'Active'],
      ['T0_ACTIVENESS', 'Others'],
      ['T0_DES_EXCEPTION', 'SER-Services'],
      ['T0_DES_EXCEPTION', 'GSC-Global SC (Strategy/Projects)'],
      ['T0_DES_EXCEPTION', 'LAU-Launch'],
      ['T0_DES_EXCEPTION', 'MTS-Must be Stocked'],
      ['T0_DES_EXCEPTION', 'LTB-Last Time Buy'],
      ['T0_DES_EXCEPTION', 'OFO-Order for Order'],
      ['T0_DES_EXCEPTION', 'CST-Customer Stock'],
      ['T0_DES_EXCEPTION', 'MTO-Must be Non-Stocked'],
      ['T0_DES_EXCEPTION', 'PRO-Promotion'],
      ['T0_DES_EXCEPTION', 'Others'],
      ['T0_DES_EXCEPTION', 'SPI-Supplier issue'],
      ['T0_DES_EXCEPTION', 'VCS-Vendor Consignment Stock'],
      ['T0_DES_EXCEPTION', 'UNKNOWN'],
      ['T0_DES_EXCEPTION', 'MRK-Marketing request'],
      ['T0_DES_EXCEPTION', 'MSK-Maintenance stock'],
      ['T0_DES_EXCEPTION', 'RED-Red Alert / Quota']
    ],
    report1Display: ['CLO_MEET', 'NO_OF_MATERIAL', 'NO_OF_LINE', 'NO_OF_VALUE', 'OTDS', 'SHORTEN', 'EXTEND'],
    report1CLODate: '',
    report1Categories: ['T0_STOCKING_POLICY_LT_GROUP', 'T0_BU'],
    report1SelectedValues: [],
    report1SelectedLabel: 'NO. OF MATERIAL',
    report1SOTimeRange: [],
    report1ReferenceDate: '',
    report1TargetDate: '',
    report2Category: 'T0_ENTITY',
    report2MergeThreshold: 10,
    report3DateRange: [],
    report3DateType: 'Week',
    report3ValueType: 'Net Net Price',
    report3SelectedValues: ''
  },
  pivotOpts: []
})

const initPage = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  pageCtl.conditions.report1SOTimeRange = [$dateFormatter(new Date(year, month - 6, 1), 'yyyyMM'), $dateFormatter(new Date(year, month - 1, 1), 'yyyyMM')]
  pageCtl.conditions.report1CLODate = $dateFormatter(new Date(), 'yyyy/MM/dd')
  pageCtl.conditions.report1ReferenceDate = $dateFormatter(today, 'yyyy/MM/dd')
  pageCtl.conditions.report1TargetDate = $dateFormatter(new Date(year, month, day - 14), 'yyyy/MM/dd')

  initReport3Filter()
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_clo/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.pivotOpts = body.pivotOpts
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const initReport3Filter = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  pageCtl.conditions.report3DateRange = [$dateFormatter(new Date(year, month - 2, 1), 'yyyy/MM/dd'), $dateFormatter(today, 'yyyy/MM/dd')]
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  pageCtl.conditions.report1SelectedLabel = ''
  pageCtl.conditions.report3SelectedValues = ''
  pageCtl.conditions.report3ValueType = 'Net Net Price'
  pageCtl.report2Title = ''
  pageCtl.report3Title = ''
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_clo/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    report1TotalMap = null
    pageCtl.report1Data = parseReport1Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

let report1TotalMap = null as any
const parseReport1Data = (data, hasTotal?) => {
  let totalMap = {} as any
  if (report1TotalMap !== null) {
    totalMap = report1TotalMap
  } else {
    for (let i = 0; i < data.length; i++) {
      const row = data[i]
      if (row.category === 'Total') {
        totalMap = data[i]
      }
    }
    totalMap.ontimeLines = totalMap.ontimeLines + totalMap.delayLines === 0 ? 0 : totalMap.ontimeLines / (totalMap.ontimeLines + totalMap.delayLines) * 100
    report1TotalMap = totalMap
  }

  const result = []
  for (let i = 0; i < data.length; i++) {
    const row = data[i]
    if (row.category !== 'Total') {
      const item = {} as any
      item.category = row.category
      for (const key in row) {
        if (key !== 'category') {
          item[key] = row[key]
          item[key + 'Percent'] = $toFixed(totalMap[key] ? row[key] / totalMap[key] * 100 : 0, 1)

          item.meetLinesPercent = (totalMap.meetLines + totalMap.failLines) !== 0 ? $toFixed(row.meetLines / (totalMap.meetLines + totalMap.failLines) * 100, 1) : 0
          item.failLinesPercent = (totalMap.meetLines + totalMap.failLines) !== 0 ? $toFixed(row.failLines / (totalMap.meetLines + totalMap.failLines) * 100, 1) : 0
          item.ontimeLinesPercent = (row.ontimeLines + row.delayLines) !== 0 ? $toFixed(row.ontimeLines / (row.ontimeLines + row.delayLines) * 100, 1) : 0
          item.delayLinesPercent = (row.ontimeLines + row.delayLines) !== 0 ? $toFixed(row.delayLines / (row.ontimeLines + row.delayLines) * 100, 1) : 0
        }
      }

      result.push(item)
    }
  }

  if (hasTotal === true || typeof hasTotal === 'undefined') {
    result.push(totalMap)
  }
  return result
}

const _report1ColumnWidth = computed(() => {
  const totalWidth = document.documentElement.clientWidth
  const length = pageCtl.conditions.report1Display.length
  return length === 0 ? 0 : (totalWidth - 400) / 5
})

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_clo/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    if (pageCtl.conditions.report1SelectedValues.length > 0) {
      pageCtl.report2Title = '[' + pageCtl.conditions.report1SelectedValues.join(', ') + ']'
    }
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_clo/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    if (pageCtl.conditions.report1SelectedValues.length > 0) {
      pageCtl.report3Title = '[' + pageCtl.conditions.report1SelectedValues.join(', ') + ']'
    }
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1RowDblick = (row, column) => {
  pageCtl.conditions.report3SelectedValues = JSON.stringify(row)
  switch (column.label.toUpperCase()) {
    case 'NO. OF MATERIAL':
      pageCtl.conditions.report3ValueType = 'Material Count'
      pageCtl.conditions.report1SelectedLabel = column.label.toUpperCase()
      break
    case 'NO. OF LINE':
      pageCtl.conditions.report3ValueType = 'SO Line'
      pageCtl.conditions.report1SelectedLabel = column.label.toUpperCase()
      break
    case 'OTDS':
    case 'NO. OF VALUE':
    default:
      pageCtl.conditions.report3ValueType = 'Net Net Price'
      pageCtl.conditions.report1SelectedLabel = 'CLO MEET VALUE'
  }

  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
}

const report1SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const _report2Opts = computed(() => {
  const data = pageCtl.report2Data
  return {
    title: {
      text: (pageCtl.conditions.report1SelectedLabel || 'CLO MEET VALUE') + ' BY ' + pageCtl.conditions.report2Category + ' ' + pageCtl.report2Title
    },
    toolbox: $toolbox({}),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width:11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($thousandBitSeparator(params.value, 0))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ type: 'pie' }),
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data,
        minShowLabelAngle: 10,
        label: {
          normal: {
            show: true,
            formatter: function (params) {
              return params.name + ' (' + params.percent + '%)'
            }
          }
        }
      }
    ]
  }
})

const _report3Opts = computed(() => {
  const series = []
  const data = pageCtl.report3Data.series || []
  const meetColor = {
    '1. D+1': '#2c821d',
    '2. <1W': '#37861b',
    '3. 1-2W': '#468b17',
    '4. 2-4W': '#579214',
    '5. 1-2M': '#6c9b11',
    '6. 2-3M': '#81a20d',
    '7. 3-6M': '#afb106',
    '8. 6-12M': '#cab500',
    '9. >1Y': '#ddb600'
  }
  const failColor = {
    '1. D+1': '#e3b102',
    '2. <1W': '#e2a107',
    '3. 1-2W': '#e28f0f',
    '4. 2-4W': '#db6e1b',
    '5. 1-2M': '#d45e21',
    '6. 2-3M': '#ce4e27',
    '7. 3-6M': '#c8412d',
    '8. 6-12M': '#c43730',
    '9. >1Y': '#c12e34'
  }
  for (let i = 0; i < data.length; i++) {
    series.push({
      name: data[i].name,
      stack: 'total',
      type: 'bar',
      barWidth: '60%',
      data: data[i].meetVal,
      itemStyle: {
        color: meetColor[data[i].name],
        borderColor: meetColor[data[i].name]
      }
    })
  }
  for (let i = 0; i < data.length; i++) {
    series.push({
      name: data[i].name,
      stack: 'total',
      type: 'bar',
      barWidth: '60%',
      data: data[i].failVal,
      itemStyle: {
        color: failColor[data[i].name],
        borderColor: failColor[data[i].name]
      }
    })
  }
  series.push({
    name: 'Percent(%)',
    type: 'line',
    yAxisIndex: 1,
    data: pageCtl.report3Data.percent,
    itemStyle: {
      color: '#c43730',
      borderColor: '#c43730'
    },
    label: {
      show: true,
      valueAnimation: true,
      formatter: function (params) {
        return $scaleLabel(params, pageCtl.report3Data.xAxis.length, 15)
      }
    }
  })
  return {
    title: {
      text: (pageCtl.conditions.report1SelectedLabel || 'CLO MEET VALUE') + ' TRENDS' + ' ' + pageCtl.report3Title
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const pdata = {}
        for (let i = 0; i < params.length; i++) {
          const pt = pdata[params[i].seriesName] || []
          pt.push(params[i])
          pdata[params[i].seriesName] = pt
        }
        const tip = [] as any
        tip.push('<div style="width:11.5rem;">')
        tip.push(params[0].name)
        tip.push('<br>')
        let mtotal = 0
        let ftotal = 0
        for (const key in pdata) {
          const pt = pdata[key]
          if (pt.length > 0) {
            tip.push('<div style="display: flex;justify-content: space-between">')
            tip.push('<div style="text-align: left;width: 4.5rem">')
            tip.push(pt[0].marker)
            tip.push(pt[0].seriesName)
            tip.push('</div>')
            tip.push('<div style="text-align: left;width: 4rem">')
            tip.push('M: ')
            mtotal += pt[0].data
            tip.push($shortenNumber(pt[0].data))
            tip.push('</div>')
            tip.push('<div style="text-align: left;width: 3rem">')
            tip.push('F: ')
            if (pt.length > 1) {
              ftotal += pt[1].data
              tip.push($shortenNumber(pt[1].data * -1))
            } else {
              tip.push(0)
            }
            tip.push('</div>')
            tip.push('</div>')
          }
        }
        tip.push('<div style="display: flex;justify-content: space-between">')
        tip.push('<div style="text-align: left;width: 4.5rem">')
        tip.push('Total')
        tip.push('</div>')
        tip.push('<div style="text-align: left;width: 4rem">')
        tip.push('M: ')
        tip.push($shortenNumber(mtotal))
        tip.push('</div>')
        tip.push('<div style="text-align: left;width: 3rem">')
        tip.push('F: ')
        tip.push($shortenNumber(ftotal * -1))
        tip.push('</div>')
        tip.push('</div>')
        tip.push('</div>')

        return tip.join('')
      }
    },
    grid: $grid(),
    toolbox: $toolbox({ opts: ['no-details'] }),
    legend: $legend(),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report3Data.xAxis,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [{
      type: 'value',
      splitLine: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      min: pageCtl.report3Data.minPercent,
      splitLine: {
        show: false
      }
    }],
    series
  }
})

interface CLOTable {
  id: string,
  category: string
  meetLines: string
  failLines: string
  ontimeLines: string,
  delayLines: string,
  noOfMaterial: string,
  noOfLine: string,
  noOfValue: string,
  parent: Array<string>,
  hasChildren?: boolean
  children?: CLOTable[]
}

const load = (
  row: CLOTable,
  treeNode: any,
  resolve: (date: CLOTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_clo/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(parseReport1Data(body, false))
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'CLO'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report2Ref.value.resize()
  report3Ref.value.resize()
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryClo'
}
</script>

<style lang="scss">
.my-story-clo {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }

  .el-table tr:hover {
    td {
      * {
        color: #fff !important;
      }
    }
  }
}
</style>
