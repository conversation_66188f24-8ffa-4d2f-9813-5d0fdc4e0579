<template>
  <div id="myStoryCatalog">
    <scp-draggable-resizable v-model="pageCtl.visible.win" w="1200px" h="680px" title="Story Catalog" :save="savePageOrder" save-text="Confirm">
      <template v-slot="{height}">
        <el-row :style="{height:(height - 85) + 'px'}">
          <el-col :span="6" style="padding:5px;height: 100%;overflow-y: auto;border-right: 1px dotted var(--scp-border-color-lighter);"
                  v-loading="pageCtl.loading.init">
            <el-card shadow="hover" @click="selectPage(item)" v-for="item in pageCtl.pageList" :key="item.PAGE_ID"
                     class="my-story-card" :class="pageCtl.selectedPage.PAGE_ID === item.PAGE_ID ? 'selected': ''">
              <div class="my-story-card-title">
                <font-awesome-icon icon="share" style="font-size: 0.45rem"/>&nbsp;
                {{ item.PAGE_TITLE }}
                <span v-if="item.PAGE_TYPE === 'MANUAL' && item.IS_OWNER === 'Y'" style="font-size: 0.4rem;float: right;">
                  <font-awesome-icon icon="edit"/>
                </span>
              </div>
              <div class="my-story-card-author">{{ item.AUTHOR_NAME }}</div>
            </el-card>
          </el-col>
          <el-col :span="1">
            <div class="catalog-btn-area" :style="{height:(height - 100) + 'px'}" v-if="Object.keys(pageCtl.selectedPage).length > 0">
              <el-tooltip effect="light" placement="right" :show-after="500" content="Move Up">
                <div>
                  <el-button text @click="moveUp">
                    <font-awesome-icon icon="chevron-up"/>
                  </el-button>
                </div>
              </el-tooltip>
              <el-tooltip effect="light" placement="right" :show-after="500" content="Move Down">
                <div>
                  <el-button text @click="moveDown">
                    <font-awesome-icon icon="chevron-down"/>
                  </el-button>
                </div>
              </el-tooltip>
            </div>
            <div v-else>&nbsp;</div>
          </el-col>
          <el-col :span="17" style="border-left: 1px dotted var(--scp-border-color-lighter);padding: 5px" v-loading="pageCtl.loading.query">
            <el-row>
              <el-col :span="24" style="margin-bottom: 4px;">
                <el-input v-model="pageCtl.newPage.title" placeholder="Page Title"/>
              </el-col>
              <el-col :span="24">
                <scp-ck-editor v-model="pageCtl.newPage.content" placeholder="Say something..." :height="height - 180"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" style="text-align: right;padding-right: 10px;">
                <el-popconfirm :title="'确定刪除当前页('+pageCtl.selectedPage.PAGE_TITLE+')?'"
                               iconColor="var(--scp-text-color-error)"
                               @confirm="deletePage"
                               confirmButtonType="danger"
                               confirmButtonText='确定'
                               cancelButtonText='取消'
                               style="margin-left:5px">
                  <template #reference>
                    <el-button v-show="!!pageCtl.newPage.id && pageCtl.selectedPage.IS_OWNER==='Y'" :loading="pageCtl.loading.delete">Delete</el-button>
                  </template>
                </el-popconfirm>
                <el-button @click="modifyPage" v-show="!!pageCtl.newPage.id && pageCtl.selectedPage.IS_OWNER==='Y'" :loading="pageCtl.loading.modify">Modify</el-button>
                <el-button @click="savePage" type="primary">Create</el-button>
              </el-col>
            </el-row>
            <el-divider style="margin: 20px 0 10px 0;" border-style="dotted"/>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import { useRoute } from 'vue-router'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $route = useRoute()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const treeRef = ref()

interface Page {
  PAGE_ID: string,
  PAGE_TITLE: string
  PAGE_TYPE: string,
  AUTHOR_NAME: string
  CREATE_DATE: string,
  IS_OWNER: string
}

const pageCtl = reactive({
  bindTo: '',
  bindToName: '',
  selectedPage: {},
  pageList: [] as Array<Page>,
  visible: {
    win: false
  },
  loading: {
    init: false,
    delete: false,
    save: false,
    query: false,
    modify: false
  },
  newPage: {
    id: '',
    title: '',
    content: ''
  }
})

const selectPage = (page) => {
  pageCtl.selectedPage = page
  queryPage(page)
}

const initPage = () => {
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_catalog/init_catalog_page',
    data: {
      bindTo: pageCtl.bindTo
    }
  }).then((body) => {
    pageCtl.pageList = body.pageList
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const showCatalog = () => {
  pageCtl.visible.win = true
  pageCtl.selectedPage = {}
  initPage()
}

const closeCatalog = () => {
  pageCtl.visible.win = false
}

const savePageOrder = () => {
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_catalog/save_page_order',
    data: {
      bindTo: pageCtl.bindTo,
      pageList: pageCtl.pageList.map(e => e.PAGE_ID)
    }
  }).then(() => {
    initPage()
    if (props.clickNative) {
      props.clickNative({
        key: pageCtl.bindTo,
        label: pageCtl.bindToName
      })
    }
  }).catch((error) => {
    console.log(error)
  })
}

const deletePage = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_catalog/delete_page',
    data: {
      pageId: pageCtl.newPage.id
    }
  }).then(() => {
    $message.success('Page deleted.')
    pageCtl.newPage = {
      id: '',
      title: '',
      content: ''
    }
    initPage()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const savePage = () => {
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_catalog/save_page',
    data: {
      bindTo: pageCtl.bindTo,
      title: pageCtl.newPage.title,
      content: pageCtl.newPage.content
    }
  }).then(() => {
    initPage()
    pageCtl.newPage = {
      id: '',
      title: '',
      content: ''
    }
    $message.success('Page saved.')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const modifyPage = () => {
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_catalog/modify_page',
    data: {
      id: pageCtl.newPage.id,
      title: pageCtl.newPage.title,
      content: pageCtl.newPage.content
    }
  }).then(() => {
    initPage()
    $message.success('Page modified.')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

const queryPage = (page) => {
  if (page.PAGE_TYPE !== 'MANUAL') {
    pageCtl.newPage = {
      id: '',
      title: '',
      content: ''
    }
    return
  }
  pageCtl.loading.query = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_catalog/query_page_by_id',
    data: {
      pageId: pageCtl.selectedPage.PAGE_ID
    }
  }).then((body) => {
    pageCtl.newPage.id = body.PAGE_ID
    pageCtl.newPage.title = body.PAGE_TITLE
    pageCtl.newPage.content = body.CONTENT
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.query = false
  })
}

const moveUp = () => {
  const pageMap = {}
  const idList = pageCtl.pageList.map(e => {
    pageMap[e.PAGE_ID] = e
    return e.PAGE_ID
  })
  const currentIndex = idList.indexOf(pageCtl.selectedPage.PAGE_ID)
  if (currentIndex > 0) {
    const element = idList.splice(currentIndex, 1)[0]
    idList.splice(currentIndex - 1, 0, element)
  }
  pageCtl.pageList = idList.map(e => pageMap[e])
}

const moveDown = () => {
  const pageMap = {}
  const idList = pageCtl.pageList.map(e => {
    pageMap[e.PAGE_ID] = e
    return e.PAGE_ID
  })
  const currentIndex = idList.indexOf(pageCtl.selectedPage.PAGE_ID)
  if (currentIndex < pageCtl.pageList.length - 1) {
    const element = idList.splice(currentIndex, 1)[0]
    idList.splice(currentIndex + 1, 0, element)
  }
  pageCtl.pageList = idList.map(e => pageMap[e])
}

const setBindTo = (bindTo, bindToName) => {
  pageCtl.visible.win = false
  pageCtl.bindTo = bindTo
  pageCtl.bindToName = bindToName
}

// @ts-ignore
const props = withDefaults(defineProps<{
  clickNative?: Function
}>(), {
  clickNative: null
})

onMounted(() => {

})

defineExpose({
  showCatalog, setBindTo, closeCatalog
})
</script>

<script lang="ts">
export default {
  name: 'MyStoryCatelog'
}
</script>

<style lang="scss">
#myStoryCatalog {
  .el-card {
    cursor: pointer;
    margin-bottom: var(--scp-widget-margin);
  }

  .my-story-card {
    .el-card__body {
      padding: 15px 15px 8px 15px;
    }

    .my-story-card-title {
      font-size: 0.55rem;
    }

    .my-story-card-author {
      text-align: right;
      font-style: italic;
      color: rgb(34, 34, 34);
      font-family: "Segoe UI", "system-ui";
      font-size: 9px;
    }
  }

  .my-story-card.selected {
    .el-card__body {
      color: white;
      background-color: var(--scp-bg-color-highlight);

      .my-story-card-author {
        color: white;
      }
    }
  }

  .catalog-btn-area {
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    justify-content: center;
  }
}
</style>
