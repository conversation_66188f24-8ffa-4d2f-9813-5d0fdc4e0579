<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / {{ pageCtl.title }}
  </div>

  <el-row class="my-story-manual-input" v-loading="pageCtl.loading.init" :id="'mpage_' + props.pageId">
    <el-col :span="24" :style="{height: ($store.state.pageHeight - 270) + 'px'}" class="my-story-manual-input-content">
      <scp-md-preview v-model="pageCtl.content" :editor-id="'manual_input_' + props.pageId" style="height:calc(100% - 10px);overflow: auto"/>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { inject, nextTick, reactive, ref } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $store = useStore()

const pageCtl = reactive({
  title: '',
  content: '',
  loading: {
    init: false
  }
})

const initPage = () => {
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/query_manual_input_by_id',
    data: {
      pageId: props.pageId
    }
  }).then((body) => {
    pageCtl.content = body.CONTENT
    pageCtl.title = body.PAGE_TITLE
    nextTick(() => {
      setTimeout(() => {
        resize()
      }, 100)
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const commentRef: any = ref()

const resize = () => {
  const container = document.getElementById('mpage_' + props.pageId)
  const maxHeight = container.offsetHeight - 60
  const imgs = container.getElementsByTagName('img')
  for (let i = 0; i < imgs.length; i++) {
    imgs[i].style.maxHeight = maxHeight + 'px'
  }
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  pageId?: any
}>(), {
  pageId: ''
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  if (e) {
    initPage()
    commentRef.value.setBindTo(e.key, props.pageId)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryManualInput'
}
</script>

<style lang="scss">
.my-story-manual-input {
  margin-bottom: var(--scp-widget-margin);

  .my-story-manual-input-content {
    border: 1px solid var(--scp-border-color);
  }
}
</style>
