<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / NOR
  </div>

  <el-row class="my-story-nor">
    <el-col :span="14">
      <div class="subscript-container subscript-container-left tree-table-box" @contextmenu.prevent=""
           v-loading="pageCtl.loading.report1 || pageCtl.loading.init" :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSNOR1" ref="msnor1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data.data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category">
              <template #header>
                <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                           collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="ytdSonor" :label="pageCtl.report1Data.ytdTitle + ' SO NOR %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'ytdSonor')"
                             v-if="pageCtl.conditions.report1Display.indexOf('YTD_SO_NOR') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.ytdSonor > pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdSonor }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="mtdSonor" :label="pageCtl.report1Data.mtdTitle + ' SO NOR %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'mtdSonor')"
                             v-if="pageCtl.conditions.report1Display.indexOf('MTD_SO_NOR') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.mtdSonor > pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.mtdSonor }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="ytdSonoro" :label="pageCtl.report1Data.ytdTitle + ' SO NORO %'" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'ytdSonoro')"
                             v-if="pageCtl.conditions.report1Display.indexOf('YTD_SO_NORO') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.ytdSonoro > pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdSonoro }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="ytdSonor2" :label="pageCtl.report1Data.ytdTitle + ' SO NOR >=2%'" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'ytdSonor2')"
                             v-if="pageCtl.conditions.report1Display.indexOf('YTD_SO_NOR_2') !== -1">
              <template v-slot="scope">
                <span :style="{color: scope.row.ytdSonor2 > pageCtl.conditions.report1RocWarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdSonor2 }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="ytdSonor5" :label="pageCtl.report1Data.ytdTitle + ' SO NOR >=5%'" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'ytdSonor5')"
                             v-if="pageCtl.conditions.report1Display.indexOf('YTD_SO_NOR_5') !== -1">
              <template v-slot="scope">
                <span :style="{color: scope.row.ytdSonor5 > pageCtl.conditions.report1RocWarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdSonor5 }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="ytdSonor7" :label="pageCtl.report1Data.ytdTitle + ' SO NOR >=7%'" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'ytdSonor7')"
                             v-if="pageCtl.conditions.report1Display.indexOf('YTD_SO_NOR_7') !== -1">
              <template v-slot="scope">
                <span :style="{color: scope.row.ytdSonor7 > pageCtl.conditions.report1RocWarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdSonor7 }}
                    </span>
              </template>
            </el-table-column>

          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">NOR Filter</el-col>
              <el-col :span="16">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Display</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.displayOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msnor1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msnor1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="10">
      <div class="subscript-container subscript-container-right tree-table-box" @contextmenu.prevent=""
           v-loading="pageCtl.loading.report2 || pageCtl.loading.init" :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSNOR2" ref="msnor2Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report2Data.data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load2"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report2SortChange"
              @row-dblclick="report2RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category">
              <template #header>
                <el-select v-model="pageCtl.conditions.report2Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                           collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                  <el-option
                      v-for="item in poOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="ytdPonoro" :label="pageCtl.report2Data.ytdTitle + ' PO NORO%'" sortable
                             :sort-method="(a, b) => report2Sort(a, b, 'ytdPonoro')"
                             v-if="pageCtl.conditions.report2Display.indexOf('YTD_PO_NORO') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.ytdPonoro > pageCtl.conditions.report2WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.ytdPonoro }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="mtdPonoro" :label="pageCtl.report2Data.mtdTitle + ' PO NORO%'" sortable
                             :sort-method="(a, b) => report2Sort(a, b, 'mtdPonoro')"
                             v-if="pageCtl.conditions.report2Display.indexOf('MTD_PO_NORO') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.mtdPonoro > pageCtl.conditions.report2WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.mtdPonoro }}
                    </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">NORO Filter</el-col>
              <el-col :span="16">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterPoList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterPoOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Display</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.display2Opts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msnor2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msnor2Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="10">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report3 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSNOR3" ref="msnor3Ref"/>
        <div class="front">
          <chart ref="report3Ref" :height="_part2Height" :option="_report3Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3Category" placeholder="Category" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.poOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Stack by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3StackBy" placeholder="Stack by" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in report3StackOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msnor3Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msnor3Ref.toggleView();searchReport3()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="14">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report4 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSNOR4" ref="msnor4Ref"/>
        <div class="front">
          <chart ref="report4Ref" :height="_part2Height" :option="_report4Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report4Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in poOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Time Range</el-col>
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.report4DateRange"
                    type="monthrange"
                    unlink-panels
                    range-separator="~"
                    format="YYYYMM"
                    value-format="YYYYMM"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :clearable="false"
                    prefix-icon=null
                    style="width: calc(100% - 1.333rem) !important;">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Display by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report4DateType" style="width: calc(100% - 15px)">
                  <el-option v-for="item in ['Week', 'Month']"
                             :key="item"
                             :label="item"
                             :value="item"/>
                </el-select>
              </el-col>
            </el-row>

            <div class="box-footer">
              <el-button @click="msnor4Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msnor4Ref.toggleView();searchReport4()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onBeforeMount, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'

const $toFixed: any = inject('$toFixed')
const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')
const msnor1Ref: any = ref()
const msnor2Ref: any = ref()
const msnor3Ref: any = ref()
const msnor4Ref: any = ref()
const report3Ref: any = ref()
const report4Ref: any = ref()

const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $startWith: any = inject('$startWith')

const pageCtl = reactive({
  report1Data: {
    data: [],
    mtdTitle: 'MTD SO NOR',
    ytdTitle: 'YTD SO NOR'
  },
  report2Data: {
    data: [],
    mtdTitle: 'MTD PO NOR',
    ytdTitle: 'YTD PO NOR'
  },
  report3Data: { yAxis: [], series: [], total: [] },
  report4Data: { yAxis: [], xAxis: [], legend: [] },
  filterOpts: [],
  filterPoOpts: [],
  displayOpts: ['YTD_SO_NOR', 'MTD_SO_NOR', 'YTD_SO_NORO', 'YTD_SO_NOR_2', 'YTD_SO_NOR_5', 'YTD_SO_NOR_7'],
  display2Opts: ['YTD_PO_NORO', 'MTD_PO_NORO'],
  weekOpts: [],
  report3StackOpts: ['PURCH_ORDER_NUMBER'],
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false,
    report4: false
  },
  conditions: {
    $scpFilter: {},
    filterList: [],
    filterPoList: [],
    report1Week: '',
    report2Week: '',
    report1Display: ['YTD_SO_NOR', 'MTD_SO_NOR', 'YTD_SO_NORO', 'YTD_SO_NOR_2', 'YTD_SO_NOR_5', 'YTD_SO_NOR_7'],
    report2Display: ['YTD_PO_NORO', 'MTD_PO_NORO'],
    report1WarningValue: 2,
    report1RocWarningValue: 0,
    report2WarningValue: 5,
    report1Categories: ['ENTITY', 'PRODUCT_LINE'],
    report2Categories: ['ENTITY', 'PRODUCT_LINE'],
    report1SelectedValues: [],
    report2SelectedValues: [],
    report3Category: 'NORO_FLAG',
    report3StackBy: '',
    report4Category: '[TOTAL]',
    report4DateRange: [],
    tooltips: [],
    report4DateType: 'Week'
  },
  pivotOpts: [],
  poOpts: [],
  visible: {
    win: false
  }
})

const initPage = () => {
  initReport4Filter()
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.filterPoOpts = body.filterPoOpts
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.weekOpts = body.weekOpts
    pageCtl.report3StackOpts = body.report3StackOpts
    pageCtl.poOpts = body.poOpts
    if (pageCtl.conditions.report1Week === '') {
      pageCtl.conditions.report1Week = body.currentWeek
    }
    if (pageCtl.conditions.report2Week === '') {
      pageCtl.conditions.report2Week = body.currentWeek
    }
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const initReport4Filter = () => {
  const end = new Date()
  const start = new Date()
  start.setMonth(end.getMonth() - 2)
  let startMonth: any = start.getMonth()
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  let endMonth: any = end.getMonth() + 1
  if (endMonth < 10) {
    endMonth = '0' + endMonth
  }
  pageCtl.conditions.report4DateRange = [start.getFullYear() + startMonth, end.getFullYear() + endMonth]
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  pageCtl.conditions.report2SelectedValues = []
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report2SortChange = (e) => {
  report2SortCache[e.prop] = e.order
}
const report1RowDblick = (row) => {
  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
  searchReport4()
}

const report2RowDblick = (row) => {
  if (row.category !== 'Total') {
    pageCtl.conditions.report2SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report2SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report2SelectedValues = []
  }
  searchReport1()
  searchReport3()
  searchReport4()
}

const report1SortCache = {}
const report2SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}
const report2Sort = (a, b, category) => {
  const beforeOrder = report2SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const _report3Opts = computed(() => {
  const series = []
  const legend = []
  for (const key in pageCtl.report3Data.series) {
    legend.push(key)
    series.push({
      name: key,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: pageCtl.report3Data.series[key]
    })
  }
  series.push({
    name: 'Total',
    type: 'line',
    symbolSize: 0,
    lineStyle: {
      width: 0
    },
    label: {
      show: true,
      position: 'right',
      valueAnimation: true
    },
    data: pageCtl.report3Data.total
  })
  return {
    title: {
      text: 'OPEN PO' +
          ' ' + pageCtl.conditions.report3Category +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          show: true,
          title: {
            zoom: 'Zoom',
            back: 'Back'
          }
        },
        dataView: {
          lang: ['Data Viewer', 'Close', 'Refresh'],
          show: true,
          readOnly: false,
          optionToContent: function (option) {
            // 构建表格HTML字符串
            let tableContent = '<table border="1"><tr><th>Category</th>'
            pageCtl.report3Data.yAxis.forEach((day, index) => {
              tableContent += `<th>${day}</th>`
            })
            tableContent += '</tr>'

            // 添加每个系列的数据行
            option.series.forEach((serie, i) => {
              tableContent += `<tr><td>${serie.name}</td>`
              serie.data.forEach((value, j) => {
                tableContent += `<td>${value}</td>`
              })
              tableContent += '</tr>'
            })
            return tableContent
          }
        },
        saveAsImage: {}
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report3Data.yAxis,
      axisLabel: {
        interval: 0
      }
    },
    series
  }
})
const poOpts = computed(() => {
  const result = ['[TOTAL]']
  for (let i = 0; i < pageCtl.poOpts.length; i++) {
    if (pageCtl.poOpts[i].indexOf('WEEK') === -1) {
      result.push(pageCtl.poOpts[i])
    }
  }
  return result
})

const report3StackOpts = computed(() => {
  const result = ['PURCH_ORDER_NUMBER']
  for (let i = 0; i < pageCtl.report3StackOpts.length; i++) {
    if (pageCtl.report3StackOpts[i].indexOf('WEEK') === -1) {
      result.push(pageCtl.report3StackOpts[i])
    }
  }
  return result
})

const _report4Opts = computed(() => {
  const series = []

  for (const y in pageCtl.report4Data.yAxis) {
    const s = {
      name: y,
      data: pageCtl.report4Data.yAxis[y],
      type: 'line',
      label: {},
      markLine: {
        symbol: 'none',
        silent: true,
        data: [{
          type: 'line',
          yAxis: 5,
          lineStyle: {
            color: 'grey',
            width: 2,
            type: 'dashed'
          }
        }]
      }
    }
    if (Object.keys(pageCtl.report4Data.yAxis).length < 3) {
      s.label = {
        show: true,
        position: 'top',
        formatter: (param) => {
          return param.data + '%'
        }
      }
    }
    series.push(s)
  }
  series.sort((e1, e2) => e1.name.localeCompare(e2.name))
  const opts = {
    title: {
      text: 'PO NORO TREND'
    },
    grid: $grid(),
    legend: $legend(),
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (params.hasOwnProperty(i)) {
            const value = params[i].value || 0
            tip.push('<div style="width:9.5rem;">')
            const marker = params[i].marker

            tip.push(marker)
            tip.push(params[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push(value + '%')
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      axisLabel: {},
      type: 'category',
      data: pageCtl.report4Data.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      }
    },
    series
  }
  // @ts-ignore
  opts.visualMap = {
    show: false,
    dimension: Object.keys(pageCtl.report4Data.yAxis).length === 1 ? 1 : 0,
    seriesIndex: 0,
    pieces: [{
      gt: -1,
      lte: pageCtl.conditions.report2WarningValue,
      color: '#3dcd58'

    }],
    outOfRange: {
      color: '#c12e34'
    }
  }
  return opts
})

interface NORTable {
  id: string,
  category: string,
  ytdSonor: string,
  mtdSonor: string,
  ytdPonoro: string,
  mtdPonoro: string,
  openPonoro: string,
  ytdSonoro: string,
  ytdSonor2: string,
  ytdSonor5: string,
  ytdSonor7: string,
  parent: Array<string>,
  hasChildren?: boolean
  children?: NORTable[]
}

interface PONORTable {
  id: string,
  category: string,
  ytdPonoro: string,
  mtdPonoro: string,
  parent: Array<string>,
  hasChildren?: boolean
  children?: PONORTable[]
}

const load = (
  row: NORTable,
  treeNode: any,
  resolve: (date: NORTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}
const load2 = (
  row: PONORTable,
  treeNode: any,
  resolve: (date: PONORTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_nor/query_report2_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'NOR'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report3Ref.value.resize()
  report4Ref.value.resize()
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryNor'
}
</script>

<style lang="scss">
.my-story-nor {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
