<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / MPT
  </div>

  <el-row class="my-story-mpt">
    <el-col :span="24">
      <div class="subscript-container tree-table-box" @contextmenu.prevent="" v-loading="pageCtl.loading.report1 || pageCtl.loading.init"
        :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSMPT1" ref="msMpt1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              class="tree-table">
            <el-table-column prop="category" label="Category">
              <template #header>
                <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                           collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </template>
              <template v-slot="scope">
                <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                  {{ scope.row.category }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="escalation" label="Escalation %" sortable :sort-method="(a, b) => report1Sort(a, b, 'escalation')"
                             v-if="pageCtl.conditions.report1Display.indexOf('ESCALATION') !== -1">
              <template v-slot="scope">
                    <span :style="{color: scope.row.escalation > pageCtl.conditions.report1WarningValue ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.escalation }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column prop="escalationLine" label="Escalation Line" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'escalationLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('ESCALATION_LINE') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.escalationLine) }}
              </template>
            </el-table-column>
            <el-table-column prop="toBeEliminatedLine" label="To Be Eliminated Line" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'toBeEliminatedLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('TO_BE_ELIMINATED_LINE') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.toBeEliminatedLine) }}
              </template>
            </el-table-column>
            <el-table-column prop="openErrorLine" label="Open Error Line" sortable
                             :sort-method="(a, b) => report1Sort(a, b, 'openErrorLine')"
                             v-if="pageCtl.conditions.report1Display.indexOf('OPEN_ERROR_LINE') !== -1">
              <template v-slot="scope">
                {{ $thousandBitSeparator(scope.row.openErrorLine) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="4">MPT Filter</el-col>
              <el-col :span="8">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Display</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.displayOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Warning Baseline</el-col>
              <el-col :span="8">
                <el-input-number v-model="pageCtl.conditions.report1WarningValue" size="small" :precision="1" :step="0.1" :max="100" :min="0"
                                 style="width: var(--scp-input-width) !important;"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msMpt1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msMpt1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="10">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSMPT2" ref="msMpt2Ref"/>
        <div class="front">
          <chart ref="report2Ref" :height="_part2Height" :option="_report2Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in _report2CategoryOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Stack by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2StackBy" placeholder="Stack by" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.pivotOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Top</el-col>
              <el-col :span="16">
                <el-input-number v-model="pageCtl.conditions.report2TopStart" style="width: 45%" @change="resetReport2TopStartEnd"/>
                ~
                <el-input-number v-model="pageCtl.conditions.report2TopEnd" style="width: 45%" @change="resetReport2TopStartEnd"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msMpt2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msMpt2Ref.toggleView();searchReport2()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="14">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSMPT3" ref="msMpt3Ref"/>
        <div class="front">
          <chart ref="report3Ref" :height="_part2Height" :option="_report3Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in _report3CategoryOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Value Type</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3ValueType" placeholder="Value Type" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['ESCALATION_RATIO', 'ESCALATION_LINE', 'TO_BE_ELIMINATED_LINE']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Date Range</el-col>
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.report3DateRange"
                    type="daterange"
                    unlink-panels
                    range-separator="~"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :clearable="false"
                    prefix-icon=null
                    style="width: calc(100% - 1.333rem) !important;">
                </el-date-picker>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msMpt3Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msMpt3Ref.toggleView();searchReport3()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')

const msMpt1Ref: any = ref()
const msMpt2Ref: any = ref()
const msMpt3Ref: any = ref()
const report2Ref: any = ref()
const report3Ref: any = ref()
const $deepClone: any = inject('$deepClone')

const pageCtl = reactive({
  report1Data: [],
  report2Data: { yAxis: [], series: [], total: [] },
  report3Data: { yAxis: [], xAxis: [], legend: [] },
  filterOpts: [],
  displayOpts: ['ESCALATION', 'ESCALATION_LINE', 'TO_BE_ELIMINATED_LINE', 'OPEN_ERROR_LINE'],
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {},
    filterList: [],
    report1Display: ['ESCALATION', 'ESCALATION_LINE', 'TO_BE_ELIMINATED_LINE', 'OPEN_ERROR_LINE'],
    report1WarningValue: 5,
    report1Categories: ['TOTAL_PENDING_DAYS_CD_RNAGE', 'SUBJECT'],
    report1SelectedValues: [] as any,
    report2TopStart: 0 as number,
    report2TopEnd: 15 as number,
    report2Category: 'MATERIAL_OWNER_NAME',
    report2StackBy: 'MATERIAL_OWNER_ORGANIZATION',
    report3Category: '[TOTAL]',
    report3DateRange: [] as any,
    report3ValueType: 'ESCALATION_RATIO',
    tooltips: []
  },
  pivotOpts: [] as any,
  visible: {
    win: false
  }
})

const resetReport2TopStartEnd = () => {
  if (pageCtl.conditions.report2TopStart >= pageCtl.conditions.report2TopEnd) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 15
  } else if (pageCtl.conditions.report2TopEnd > pageCtl.conditions.report2TopStart + 50) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 50
  }
}

const initPage = () => {
  initReport3Filter()
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mpt/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.pivotOpts = body.pivotOpts
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const initReport3Filter = () => {
  const end = new Date()
  const start = new Date()
  start.setMonth(end.getMonth() - 1)
  start.setDate(start.getDate() - start.getDay())
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mpt/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mpt/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mpt/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1RowDblick = (row) => {
  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
}

const report1SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const _report2Opts = computed(() => {
  const series = [] as any
  const legend = [] as any
  for (const key in pageCtl.report2Data.series) {
    legend.push(key)
    series.push({
      name: key,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: pageCtl.report2Data.series[key]
    })
  }
  series.push({
    name: 'Total',
    type: 'line',
    symbolSize: 0,
    lineStyle: {
      width: 0
    },
    label: {
      show: true,
      position: 'right',
      valueAnimation: true
    },
    data: pageCtl.report2Data.total
  })
  return {
    title: {
      text: 'ESCALATION LINE TOP ' +
          (pageCtl.conditions.report2TopStart === 0 ? pageCtl.conditions.report2TopEnd : pageCtl.conditions.report2TopStart + ' ~ ' + pageCtl.conditions.report2TopEnd) +
          ' ' + pageCtl.conditions.report2Category +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report2Data.yAxis,
      axisLabel: {
        interval: 0
      }
    },
    series
  }
})

const _report2CategoryOpts = computed(() => {
  const result:any = []
  for (let i = 0; i < pageCtl.pivotOpts.length; i++) {
    if (pageCtl.pivotOpts[i].indexOf('CALENDAR_DATE') === -1) {
      result.push(pageCtl.pivotOpts[i])
    }
  }
  return result
})

const _report3CategoryOpts = computed(() => {
  const result = ['[TOTAL]']
  for (let i = 0; i < pageCtl.pivotOpts.length; i++) {
    if (pageCtl.pivotOpts[i].indexOf('CALENDAR_DATE') === -1) {
      result.push(pageCtl.pivotOpts[i])
    }
  }
  return result
})

const report3ValueRender = (val) => {
  if (pageCtl.conditions.report3ValueType === 'ESCALATION_RATIO') {
    return val + '%'
  } else {
    return $thousandBitSeparator(val)
  }
}

const calculateAverage = (arr) => {
  if (arr.length === 0) {
    return null
  }
  const avg = (arr.reduce((accumulator, currentValue) => accumulator + currentValue, 0) / arr.length).toFixed(1)
  const result = []
  arr.forEach(item => result.push(avg))
  return result
}

const _report3Opts = computed(() => {
  const series = [] as any

  for (const y in pageCtl.report3Data.yAxis) {
    const s = {
      name: y,
      data: pageCtl.report3Data.yAxis[y],
      type: 'line',
      label: {}
    }
    if (Object.keys(pageCtl.report3Data.yAxis).length < 3) {
      s.label = {
        show: true,
        position: 'top',
        formatter: (param) => {
          return report3ValueRender(param.data)
        }
      }
    }
    series.push(s)
  }
  series.sort((e1, e2) => e1.name.localeCompare(e2.name))
  const yAxis = pageCtl.report3Data.yAxis
  if (Object.keys(yAxis).length === 1 && Object.keys(yAxis)[0] === '[TOTAL]') {
    series.push({
      name: 'Avg Ratio',
      type: 'line',
      symbol: 'none',
      data: calculateAverage(yAxis['[TOTAL]']),
      itemStyle: {
        color: '#73c0de'
      }
    })
  }

  const opts = {
    title: {
      text: 'ESCALATION TRENDS' +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    grid: $grid(),
    legend: $legend(),
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (params.hasOwnProperty(i)) {
            const value = params[i].value || 0
            tip.push('<div style="width:9.5rem;">')
            const marker = params[i].marker

            tip.push(marker)
            tip.push(params[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push(report3ValueRender(value))
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      axisLabel: {},
      type: 'category',
      data: pageCtl.report3Data.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      }
    },
    series
  }
  if (pageCtl.conditions.report3ValueType === 'ESCALATION_RATIO') {
    // @ts-ignore
    opts.visualMap = {
      show: false,
      dimension: Object.keys(pageCtl.report3Data.yAxis).length === 1 ? 1 : 0,
      seriesIndex: 0,
      pieces: [{
        gt: pageCtl.conditions.report1WarningValue + 5,
        lte: 100,
        color: '#c12e34'
      }, {
        gt: pageCtl.conditions.report1WarningValue,
        lte: pageCtl.conditions.report1WarningValue + 5,
        color: '#e79d5e'
      }
      ],
      outOfRange: {
        color: '#3dcd58'
      }
    }
  }
  return opts
})

interface MPTTable {
  id: string,
  category: string
  escalation: string
  escalationLine: string
  toBeEliminatedLine: string,
  openErrorLine: string,
  parent: Array<string>,
  hasChildren?: boolean
  children?: MPTTable[]
}

const load = (
  row: MPTTable,
  treeNode: any,
  resolve: (date: MPTTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_mpt/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'MPT'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report2Ref.value.resize()
  report3Ref.value.resize()
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryMPT'
}
</script>

<style lang="scss">
.my-story-mpt {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              margin-left: 10px;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__tags {
                max-width: calc(100% - 20px) !important;

                span {
                  .el-tag:nth-child(2) {
                    font-size: .4rem;
                    color: var(--scp-text-color-secondary);
                  }

                  .el-tag {
                    max-width: calc(100% - 18px) !important;
                    padding: 0 !important;
                    margin-left: 0 !important;
                    font-size: 0.45rem;
                    color: var(--scp-text-color-primary);

                    .el-tag__close {
                      display: none !important;
                    }
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
