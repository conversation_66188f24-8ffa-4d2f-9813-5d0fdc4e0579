<template>
  <div class="my-story-header">
    <font-awesome-icon icon="star"/>&nbsp;&nbsp;Mini-Dashboard / MyCP
  </div>

  <el-row class="my-story-mycp">
    <el-col :span="24">
      <div class="subscript-container tree-table-box" v-loading="pageCtl.loading.report1 || pageCtl.loading.init"
        :style="{height: _part1Height + 'px'}">
        <scp-subscript id="MSMCP1" ref="msMcp1Ref"/>
        <div class="front">
          <el-table
              :data="pageCtl.report1Data.data"
              style="width: 100%;"
              :height="_part1Height"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @sort-change="report1SortChange"
              @row-dblclick="report1RowDblick"
              @row-contextmenu="getContextmenuRow"
              v-contextmenu:contextMenu
              class="tree-table">
            <el-table-column>
              <el-table-column prop="category" label="Category">
                <template #header>
                  <el-select v-model="pageCtl.conditions.report1Categories" size="small" style="border:0 !important" placeholder="Pivot Columns"
                             collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="5" filterable>
                    <el-option
                        v-for="item in pageCtl.pivotOpts"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </template>
                <template v-slot="scope">
                  <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                    {{ scope.row.category }}
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="MyCP Commitment" v-if="_containsCommitColumns">
              <el-table-column prop="commitYtd" label="YTD %" sortable :sort-method="(a, b) => report1Sort(a, b, 'commitYtd')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_YTD') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.commitYtd < pageCtl.conditions.report1WarningValue && scope.row.commitYtd !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.commitYtd }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="commitMtd" label="MTD %" sortable :sort-method="(a, b) => report1Sort(a, b, 'commitMtd')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_MTD') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.commitMtd < pageCtl.conditions.report1WarningValue && scope.row.commitMtd !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.commitMtd }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="commitLw" :label="pageCtl.report1Data.wk1Title + ' %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'commitLw')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_LAST_WEEK') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.commitLw < pageCtl.conditions.report1WarningValue && scope.row.commitLw !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.commitLw }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="commitLwOntimeLine" :label="pageCtl.report1Data.wk1Title + ' OnTime'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'commitLwOntimeLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_LAST_WEEK_ONTIME_LINE') !== -1">
                <template v-slot="scope">
                  {{ scope.row.commitLwOntimeLine }}
                </template>
              </el-table-column>
              <el-table-column prop="commitLwDelayLine" :label="pageCtl.report1Data.wk1Title + ' Fail'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'commitLwDelayLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_LAST_WEEK_DELAY_LINE') !== -1">
                <template v-slot="scope">
                  {{ scope.row.commitLwDelayLine }}
                </template>
              </el-table-column>
              <el-table-column prop="commitLwVip" :label="pageCtl.report1Data.wk1Title + '(VIP) %'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'commitLwVip')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_LAST_WEEK_VIP') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.commitLwVip < pageCtl.conditions.report1WarningValue && scope.row.commitLwVip !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.commitLwVip }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="commitLwVipOntimeLine" :label="pageCtl.report1Data.wk1Title + '(VIP) OnTime'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'commitLwVipOntimeLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_LAST_WEEK_VIP_ONTIME_LINE') !== -1">
                <template v-slot="scope">
                  {{ scope.row.commitLwVipOntimeLine }}
                </template>
              </el-table-column>
              <el-table-column prop="commitLwVipDelayLine" :label="pageCtl.report1Data.wk1Title + '(VIP) Fail'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'commitLwVipDelayLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('COMMIT_LAST_WEEK_VIP_DELAY_LINE') !== -1">
                <template v-slot="scope">
                  {{ scope.row.commitLwVipDelayLine }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="MyCP Response" v-if="_containsResponseColumns">
              <el-table-column prop="responseYtd" label="YTD %" sortable :sort-method="(a, b) => report1Sort(a, b, 'responseYtd')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_YTD') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.responseYtd < pageCtl.conditions.report1WarningValue && scope.row.responseYtd !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.responseYtd }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="responseMtd" label="MTD %" sortable :sort-method="(a, b) => report1Sort(a, b, 'responseMtd')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_MTD') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.mtdMyCP < pageCtl.conditions.report1WarningValue && scope.row.mtdMyCP !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.responseMtd }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="responseLw" :label="pageCtl.report1Data.wk1Title + ' %'" sortable :sort-method="(a, b) => report1Sort(a, b, 'responseLw')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_LAST_WEEK') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.responseLw < pageCtl.conditions.report1WarningValue && scope.row.responseLw !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.responseLw }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="responseLwOntimeLine" :label="pageCtl.report1Data.wkTitle + ' OnTime'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseLwOntimeLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_LAST_WEEK_ONTIME_LINE') !== -1">
                <template v-slot="scope">
                  {{ $thousandBitSeparator(scope.row.responseLwOntimeLine) }}
                </template>
              </el-table-column>
              <el-table-column prop="responseLwDelayLine" :label="pageCtl.report1Data.wkTitle + ' Fail'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseLwDelayLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_LAST_WEEK_DELAY_LINE') !== -1">
                <template v-slot="scope">
                  {{ $thousandBitSeparator(scope.row.responseLwDelayLine) }}
                </template>
              </el-table-column>
              <el-table-column prop="responseLwVip" :label="pageCtl.report1Data.wk1Title + '(VIP) %'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseLwVip')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_LAST_WEEK_VIP') !== -1">
                <template v-slot="scope">
                    <span
                        :style="{color: scope.row.responseLwVip < pageCtl.conditions.report1WarningValue && scope.row.responseLwVip !== 0 ? 'var(--scp-text-color-error)' : 'inherit'}">
                      {{ scope.row.responseLwVip }}
                    </span>
                </template>
              </el-table-column>
              <el-table-column prop="responseLwVipOntimeLine" :label="pageCtl.report1Data.wkTitle + '(VIP) OnTime'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseLwVipOntimeLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_LAST_WEEK_VIP_ONTIME_LINE') !== -1">
                <template v-slot="scope">
                  {{ $thousandBitSeparator(scope.row.responseLwVipOntimeLine) }}
                </template>
              </el-table-column>
              <el-table-column prop="responseLwVipDelayLine" :label="pageCtl.report1Data.wkTitle + '(VIP) Fail'" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseLwVipDelayLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_LAST_WEEK_VIP_DELAY_LINE') !== -1">
                <template v-slot="scope">
                  {{ $thousandBitSeparator(scope.row.responseLwVipDelayLine) }}
                </template>
              </el-table-column>
              <el-table-column prop="responseNonVipIncompleteLine" label="Incomplete(Non VIP)" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseNonVipIncompleteLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_NON_VIP_INCOMPLETE_LINE') !== -1">
                <template v-slot="scope">
                  {{ $thousandBitSeparator(scope.row.responseNonVipIncompleteLine) }}
                </template>
              </el-table-column>
              <el-table-column prop="responseVipIncompleteLine" label="Incomplete(VIP)" sortable
                               :sort-method="(a, b) => report1Sort(a, b, 'responseVipIncompleteLine')"
                               v-if="pageCtl.conditions.report1Display.indexOf('RESPONSE_VIP_INCOMPLETE_LINE') !== -1">
                <template v-slot="scope">
                  {{ $thousandBitSeparator(scope.row.responseVipIncompleteLine) }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="4">MyCP Filter</el-col>
              <el-col :span="8">
                <scp-cascader
                    style="width: var(--scp-input-width) !important;"
                    v-model="pageCtl.conditions.filterList"
                    :placeholder="pageCtl.loading.init ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Display</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Display" placeholder="Display"
                           multiple filterable clearable collapse-tags collapse-tags-tooltip
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.displayOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">MyCP Week</el-col>
              <el-col :span="8">
                <el-select v-model="pageCtl.conditions.report1Week" placeholder="MyCP Week"
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.weekOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">Warning Baseline</el-col>
              <el-col :span="8">
                <el-input-number v-model="pageCtl.conditions.report1WarningValue" size="small" :precision="1" :step="0.1" :max="100" :min="40"
                                 style="width: var(--scp-input-width) !important;"/>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msMcp1Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msMcp1Ref.toggleView();search()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <el-row>
    <el-col :span="10">
      <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSMCP2" ref="msMcp2Ref"/>
        <div class="front">
          <chart ref="report2Ref" :height="_part2Height" :option="_report2Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in _report2CategoryOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Stack by</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report2StackBy" placeholder="Stack by" filterable clearable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in pageCtl.report2StackOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Top</el-col>
              <el-col :span="16">
                <el-input-number v-model="pageCtl.conditions.report2TopStart" style="width: 45%" @change="resetTopStartEnd"/>
                ~
                <el-input-number v-model="pageCtl.conditions.report2TopEnd" style="width: 45%" @change="resetTopStartEnd"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Date Range</el-col>
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.report2DateRange"
                    type="daterange"
                    unlink-panels
                    range-separator="~"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :clearable="false"
                    prefix-icon=null
                    style="width: calc(100% - 1.333rem) !important;">
                </el-date-picker>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msMcp2Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msMcp2Ref.toggleView();searchReport2()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="14">
      <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3 || pageCtl.loading.init"
        :style="{height: _part2Height + 'px'}">
        <scp-subscript id="MSMCP3" ref="msMcp3Ref"/>
        <div class="front">
          <chart ref="report3Ref" :height="_part2Height" :option="_report3Opts"/>
        </div>
        <div class="back"><!-- 高度已调整 -->
          <div class="box" style="padding: 5px;">
            <div class="box-title">Report Settings</div>
            <el-row>
              <el-col :span="6">Category</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3Category" placeholder="Category" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in _report3CategoryOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Value Type</el-col>
              <el-col :span="16">
                <el-select v-model="pageCtl.conditions.report3ValueType" placeholder="Value Type" filterable
                           :loading="pageCtl.loading.init" style="width: var(--scp-input-width) !important;">
                  <el-option
                      v-for="item in ['ONTIME_RATE']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">Date Range</el-col>
              <el-col :span="16">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.report3DateRange"
                    type="daterange"
                    unlink-panels
                    range-separator="~"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    start-placeholder="Start date"
                    end-placeholder="End date"
                    :clearable="false"
                    prefix-icon=null
                    style="width: calc(100% - 1.333rem) !important;">
                </el-date-picker>
              </el-col>
            </el-row>
            <div class="box-footer">
              <el-button @click="msMcp3Ref.toggleView()">Back</el-button>
              <el-button type="primary" @click="msMcp3Ref.toggleView();searchReport3()">Search</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>

  <v-contextmenu ref="contextMenu">
    <v-contextmenu-item @click="jumpToMyCP">
      View in MyCP
    </v-contextmenu-item>
  </v-contextmenu>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

const $axios: any = inject('$axios')
const $grid: any = inject('$echarts.grid')
const $toolbox: any = inject('$echarts.toolbox')
const $legend: any = inject('$echarts.legend')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')
const $router = useRouter()

const msMcp1Ref: any = ref()
const msMcp2Ref: any = ref()
const msMcp3Ref: any = ref()
const report2Ref: any = ref()
const report3Ref: any = ref()
const $deepClone: any = inject('$deepClone')

const pageCtl = reactive({
  report1Data: {
    data: [],
    wkTitle: 'MyCP',
    wk1Title: 'W-1 MyCP'
  },
  report2Data: { yAxis: [], series: [], total: [] },
  report3Data: { yAxis: [], xAxis: [], legend: [] },
  filterOpts: [],
  displayOpts: [
    'COMMIT_YTD',
    'COMMIT_MTD',
    'COMMIT_LAST_WEEK',
    'COMMIT_LAST_WEEK_VIP',
    'COMMIT_LAST_WEEK_ONTIME_LINE',
    'COMMIT_LAST_WEEK_DELAY_LINE',
    'COMMIT_LAST_WEEK_VIP_ONTIME_LINE',
    'COMMIT_LAST_WEEK_VIP_DELAY_LINE',
    'RESPONSE_YTD',
    'RESPONSE_MTD',
    'RESPONSE_LAST_WEEK',
    'RESPONSE_LAST_WEEK_VIP',
    'RESPONSE_LAST_WEEK_ONTIME_LINE',
    'RESPONSE_LAST_WEEK_DELAY_LINE',
    'RESPONSE_LAST_WEEK_VIP_ONTIME_LINE',
    'RESPONSE_LAST_WEEK_VIP_DELAY_LINE',
    'RESPONSE_NON_VIP_INCOMPLETE_LINE',
    'RESPONSE_VIP_INCOMPLETE_LINE'
  ],
  weekOpts: [],
  report2StackOpts: [],
  loading: {
    init: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {},
    filterList: [],
    report1Week: '',
    report1Display: [
      'COMMIT_YTD',
      'COMMIT_MTD',
      'COMMIT_LAST_WEEK',
      'COMMIT_LAST_WEEK_VIP',
      'RESPONSE_YTD',
      'RESPONSE_MTD',
      'RESPONSE_LAST_WEEK',
      'RESPONSE_LAST_WEEK_VIP',
      'RESPONSE_NON_VIP_INCOMPLETE_LINE',
      'RESPONSE_VIP_INCOMPLETE_LINE'
    ],
    report1WarningValue: 95,
    report1Categories: ['BU', 'PRODUCT_LINE', 'ENTITY'],
    report1SelectedType: 'MyCP Commitment',
    report1SelectedValues: [] as any,
    report2TopStart: 0 as number,
    report2TopEnd: 15 as number,
    report2Category: 'MATERIAL',
    report2StackBy: '',
    report3Category: '[TOTAL]',
    report2DateRange: [] as any,
    report3DateRange: [] as any,
    report3ValueType: 'ONTIME_RATE',
    tooltips: [],
    report1Filters: [] as any
  },
  pivotOpts: [] as any,
  visible: {
    win: false
  }
})

const getContextmenuRow = (row) => {
  let selectValues = [] as any
  selectValues = selectValues.concat(row.parent)
  selectValues.push(row.category)
  pageCtl.conditions.report1Filters = []
  for (const index in selectValues) {
    pageCtl.conditions.report1Filters.push([pageCtl.conditions.report1Categories[index], selectValues[index]])
  }
}

const jumpToMyCP = () => {
  const url = '/customer/mycp?' + JSON.stringify(pageCtl.conditions.report1Filters)
  nextTick(() => {
    window.open($router.resolve(url).href, '_blank')
  })
}

const _containsCommitColumns = computed(() => {
  return pageCtl.conditions.report1Display.some(column =>
    [
      'COMMIT_YTD',
      'COMMIT_MTD',
      'COMMIT_LAST_WEEK',
      'COMMIT_LAST_WEEK_VIP',
      'COMMIT_LAST_WEEK_ONTIME_LINE',
      'COMMIT_LAST_WEEK_DELAY_LINE',
      'COMMIT_LAST_WEEK_VIP_ONTIME_LINE',
      'COMMIT_LAST_WEEK_VIP_DELAY_LINE'
    ].indexOf(column) !== -1)
})

const _containsResponseColumns = computed(() => {
  return pageCtl.conditions.report1Display.some(column =>
    [
      'RESPONSE_YTD',
      'RESPONSE_MTD',
      'RESPONSE_LAST_WEEK',
      'RESPONSE_LAST_WEEK_VIP',
      'RESPONSE_LAST_WEEK_ONTIME_LINE',
      'RESPONSE_LAST_WEEK_DELAY_LINE',
      'RESPONSE_LAST_WEEK_VIP_ONTIME_LINE',
      'RESPONSE_LAST_WEEK_VIP_DELAY_LINE',
      'RESPONSE_NON_VIP_INCOMPLETE_LINE',
      'RESPONSE_VIP_INCOMPLETE_LINE'
    ].indexOf(column) !== -1)
})

const resetTopStartEnd = () => {
  if (pageCtl.conditions.report2TopStart >= pageCtl.conditions.report2TopEnd) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 15
  } else if (pageCtl.conditions.report2TopEnd > pageCtl.conditions.report2TopStart + 50) {
    pageCtl.conditions.report2TopEnd = pageCtl.conditions.report2TopStart + 50
  }
}

const initPage = () => {
  initReportFilter()
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mycp/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.weekOpts = body.weekOpts
    pageCtl.report2StackOpts = body.report2StackOpts
    if (pageCtl.conditions.report1Week === '') {
      pageCtl.conditions.report1Week = body.currentWeek
    }
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const initReportFilter = () => {
  const end = new Date()
  const start = new Date()
  start.setMonth(end.getMonth() - 3)
  start.setDate(start.getDate() - start.getDay())
  end.setDate(end.getDate() - 1)
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report2DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mycp/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mycp/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_mycp/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1RowDblick = (row, column) => {
  const commitCols = [
    'responseYtd',
    'responseMtd',
    'responseLw',
    'responseLwVip',
    'responseLwOntimeLine',
    'responseLwDelayLine',
    'responseLwVipOntimeLine',
    'responseLwVipDelayLine',
    'responseNonVipIncompleteLine',
    'responseVipIncompleteLine'
  ]
  if (commitCols.includes(column.property)) {
    pageCtl.conditions.report1SelectedType = 'MyCP Response'
  } else {
    pageCtl.conditions.report1SelectedType = 'MyCP Commitment'
  }

  if (row.category !== 'Total') {
    pageCtl.conditions.report1SelectedValues = $deepClone(row.parent) || []
    pageCtl.conditions.report1SelectedValues.push(row.category)
  } else {
    pageCtl.conditions.report1SelectedValues = []
  }
  searchReport2()
  searchReport3()
}

const report1SortCache = {}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] - b[category]
  }
}

const _report2Opts = computed(() => {
  const series = [] as any
  const legend = [] as any
  for (const key in pageCtl.report2Data.series) {
    legend.push(key)
    series.push({
      name: key,
      type: 'bar',
      stack: 'total',
      emphasis: {
        focus: 'series'
      },
      data: pageCtl.report2Data.series[key]
    })
  }
  series.push({
    name: 'Total',
    type: 'line',
    symbolSize: 0,
    lineStyle: {
      width: 0
    },
    label: {
      show: true,
      position: 'right',
      valueAnimation: true
    },
    data: pageCtl.report2Data.total
  })
  return {
    title: {
      text: pageCtl.conditions.report1SelectedType + ' - DELAY LINE TOP ' +
          (pageCtl.conditions.report2TopStart === 0 ? pageCtl.conditions.report2TopEnd : pageCtl.conditions.report2TopStart + ' ~ ' + pageCtl.conditions.report2TopEnd) +
          ' ' + pageCtl.conditions.report2Category +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report2Data.yAxis,
      axisLabel: {
        interval: 0
      }
    },
    series
  }
})

const _report2CategoryOpts = computed(() => {
  const result: any = []
  for (let i = 0; i < pageCtl.pivotOpts.length; i++) {
    if (pageCtl.pivotOpts[i].indexOf('CALENDAR_WEEK') === -1) {
      result.push(pageCtl.pivotOpts[i])
    }
  }
  return result
})

const _report3CategoryOpts = computed(() => {
  const result = ['[TOTAL]']
  for (let i = 0; i < pageCtl.pivotOpts.length; i++) {
    if (pageCtl.pivotOpts[i].indexOf('CALENDAR_WEEK') === -1) {
      result.push(pageCtl.pivotOpts[i])
    }
  }
  return result
})

const report3ValueRender = (val) => {
  if (pageCtl.conditions.report3ValueType === 'ONTIME_RATE') {
    return val + '%'
  } else {
    return $thousandBitSeparator(val)
  }
}

const _report3Opts = computed(() => {
  const series = [] as any
  for (const y in pageCtl.report3Data.yAxis) {
    let s = '' as any
    if (y === 'ONTIME_LINE' || y === 'DELAY_LINE') {
      s = {
        name: y,
        data: pageCtl.report3Data.yAxis[y],
        type: 'bar',
        yAxisIndex: 0,
        stack: true,
        label: {
          show: true,
          position: 'inside', // outside 外部显示  inside 内部显示
          color: '#ffffff', // 颜色
          fontSize: 10 // 字体大小
        }
      }
    } else {
      s = {
        name: y,
        data: pageCtl.report3Data.yAxis[y],
        type: 'line',
        yAxisIndex: 1,
        label: {}
      }
      if (Object.keys(pageCtl.report3Data.yAxis).length < 4) {
        s.label = {
          show: true,
          position: 'top',
          formatter: (param) => {
            return report3ValueRender(param.data)
          }
        }
      }
    }
    series.push(s)
  }
  series.sort((e1, e2) => e1.name.localeCompare(e2.name))
  const opts = {
    title: {
      text: pageCtl.conditions.report1SelectedType + ' - ONTIME TRENDS' +
          (pageCtl.conditions.report1SelectedValues.length > 0 ? ' [' + pageCtl.conditions.report1SelectedValues.join(', ') + ']' : '')
    },
    grid: $grid(),
    legend: $legend(),
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (params.hasOwnProperty(i)) {
            const value = params[i].value || 0
            tip.push('<div style="width:9.5rem;">')
            const marker = params[i].marker

            tip.push(marker)
            tip.push(params[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push(report3ValueRender(value))
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      axisLabel: {},
      type: 'category',
      data: pageCtl.report3Data.xAxis
    },
    yAxis: [{
      type: 'value',
      boundaryGap: [0, '10%'],
      splitLine: {
        show: true
      }
    }, {
      type: 'value',
      splitLine: {
        show: true
      }
    }
    ],
    series
  }
  if (pageCtl.conditions.report3ValueType === 'ONTIME_RATE') {
    // @ts-ignore
    opts.visualMap = {
      show: false,
      dimension: Object.keys(pageCtl.report3Data.yAxis).length === 1 ? 1 : 0,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.conditions.report1WarningValue,
        color: '#c12e34'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    }
  }
  return opts
})

interface MyCPTable {
  id: string,
  category: string,
  responseYtd: string,
  responseMtd: string,
  responseLw: string,
  responseLwVip: string,
  responseLwOntimeLine: string,
  responseLwDelayLine: string,
  responseLwVipOntimeLine: string,
  responseLwVipDelayLine: string,
  responseNonVipIncompleteLine: string,
  responseVipIncompleteLine: string,
  commitYtd: string,
  commitMtd: string,
  commitLw: string,
  commitLwVip: string,
  commitLwOntimeLine: string,
  commitLwDelayLine: string,
  commitLwVipOntimeLine: string,
  commitLwVipDelayLine: string,
  parent: Array<string>,
  hasChildren?: boolean,
  children?: MyCPTable[]
}

const load = (
  row: MyCPTable,
  treeNode: any,
  resolve: (date: MyCPTable[]) => void
) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story_mycp/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'MyCP'
const $store = useStore()
const commentRef: any = ref()
const part1Ratio = 0.46

const _part1Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * part1Ratio, 200)
})

const _part2Height = computed(() => {
  return Math.max(($store.state.pageHeight - 300) * (1 - part1Ratio), 200)
})

const resize = () => {
  report2Ref.value.resize()
  report3Ref.value.resize()
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryMyCP'
}
</script>

<style lang="scss">
.my-story-mycp {
  .tree-table-box {
    overflow: auto;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill) !important;

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header-wrapper thead tr:first-child th {
      border-bottom: solid 5px var(--scp-bg-color) !important;
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
