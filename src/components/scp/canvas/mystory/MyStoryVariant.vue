<template>
  <div id="myStoryVariant">
    <scp-draggable-resizable v-model="pageCtl.visible.win" w="60vw" h="600px" title="Story Management">
      <template v-slot="{height}">
        <el-row :style="{height:(height - 85) + 'px'}">
          <el-col :span="8">
            <scp-tree-menu
                ref="treeRef"
                style="margin: 5px;"
                url="/canvas/my_story/query_variant_list"
                node-key="label"
                :default-expanded-keys="['我创建的']"
                :pop-show-after="3000"
                :new-click="newVariant"
                :node-click="showVariantDetails"
                :node-dblclick="applyVariant"
            ></scp-tree-menu>
          </el-col>
          <el-col :span="16" style="border-left: 1px solid var(--scp-border-color-lighter)">
            <el-empty description="Please select a story" v-show="!pageCtl.modifyVariant.key"/>
            <div class="new-variant" v-show="!!pageCtl.modifyVariant.key" v-loading="pageCtl.loading.show">
              <el-row>
                <el-col :span="3" class="title">Story Name</el-col>
                <el-col :span="10" class="content">
                  <el-input v-model="pageCtl.modifyVariant.name" placeholder="Story Name"></el-input>
                </el-col>
                <el-col :span="2" class="title" style="text-align: center">Group</el-col>
                <el-col :span="7" class="content" style="padding-right: 0 !important;">
                  <el-autocomplete
                      class="inline-input"
                      v-model="pageCtl.modifyVariant.groups"
                      :maxlength="30"
                      :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                      placeholder="Group"
                      show-word-limit
                      style="width: calc(100% - 5px) !important;"
                  ></el-autocomplete>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="3" class="title">Share To</el-col>
                <el-col :span="19" class="content">
                  <el-select placeholder="Share to" v-model="pageCtl.modifyVariant.shareTo" style="width: 100% !important;" multiple collapse-tags filterable
                             clearable>
                    <el-option
                        v-for="item in pageCtl.shareToOpts"
                        :key="item['VALUE']"
                        :label="item['NAME']"
                        :value="item['VALUE']">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="22" style="text-align: right;padding-right: 6px;">
                  <el-button-group>
                    <el-popconfirm title="确定删除当前故事内容?" v-if="pageCtl.modifyVariant.isMyStory"
                                   iconColor="var(--scp-text-color-error)"
                                   @confirm="()=>deleteVariant({key: pageCtl.modifyVariant.key})"

                                   confirmButtonType="danger"
                                   cancelButtonType="text"
                                   confirmButtonText='确定'
                                   cancelButtonText='取消'>
                      <template #reference>
                        <el-button>
                          <font-awesome-icon icon="times"/>
                        </el-button>
                      </template>
                    </el-popconfirm>
                    <el-popconfirm title="如果您希望新建故事, 请点击加号创建, 点击此按钮会覆盖原有的故事内容, 确定覆盖?" v-if="pageCtl.modifyVariant.isMyStory"
                                   iconColor="var(--scp-text-color-error)"
                                   @confirm="()=>modifyVariant({key: pageCtl.modifyVariant.key})"

                                   confirmButtonType="danger"
                                   cancelButtonType="text"
                                   confirmButtonText='确定'
                                   cancelButtonText='取消'>
                      <template #reference>
                        <el-button>
                          <font-awesome-icon icon="check"/>
                        </el-button>
                      </template>
                    </el-popconfirm>
                    <el-tooltip :show-after="1000" effect="light" placement="bottom" :content="pageCtl.modifyVariant.isDefault?'取消默认页':'设为默认页'">
                      <el-button @click="setDefaultVariant({key: pageCtl.modifyVariant.key})">
                        <font-awesome-icon :icon="pageCtl.modifyVariant.isDefault? 'fa-solid fa-star':'fa-regular fa-star'"/>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip :show-after="1000" effect="light" placement="bottom" content="复制故事链接">
                      <el-button @click="copyShareLink({key: pageCtl.modifyVariant.key})">
                        <font-awesome-icon icon="fa-solid fa-link"/>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip :show-after="1000" effect="light" placement="bottom" content="分享故事">
                      <el-button @click="shareVariant({key: pageCtl.modifyVariant.key, label: pageCtl.modifyVariant.name})">
                        <font-awesome-icon icon="fa-solid fa-share-nodes"/>
                      </el-button>
                    </el-tooltip>
                  </el-button-group>
                </el-col>
              </el-row>
              <el-row v-show="pageCtl.isAdmin">
                <el-col :span="23">
                  <el-divider style="margin-bottom: 8px;margin-top: 8px"/>
                </el-col>
                <el-col :span="22" style="margin-bottom: 8px;color: var(--scp-text-color-secondary); font-style: italic">
                  以下信息仅管理员可见, 当点击保存时, 会用下面的参数覆盖原有参数
                </el-col>
                <el-col :span="22">
                  <scp-ace-editor v-model="_bindToValue" :readonly="true" :wrap="true" lang="json"
                                  :style="{height:(height - 280) + 'px'}"/>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>

    <!-- new variant -->
    <scp-draggable-resizable id="newVariant" w="800" h="240" v-model="pageCtl.visible.newVariant" title="New Story" :save="saveVariant"
                             :save-loading="pageCtl.loading.save">
      <div class="new-variant">
        <el-row>
          <el-col :span="3" class="title">Story Name</el-col>
          <el-col :span="10" class="content">
            <el-input v-model="pageCtl.newVariant.name" placeholder="Story Name"></el-input>
          </el-col>
          <el-col :span="2" class="title" style="text-align: center">Group</el-col>
          <el-col :span="7" class="content" style="padding-right: 0 !important;">
            <el-autocomplete
                class="inline-input"
                v-model="pageCtl.newVariant.groups"
                :maxlength="30"
                :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                placeholder="Group"
                show-word-limit
                style="width: calc(100% - 5px) !important;"
            ></el-autocomplete>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="3" class="title">Share To</el-col>
          <el-col :span="19" class="content">
            <el-select placeholder="Share to" v-model="pageCtl.newVariant.shareTo" style="width: 100% !important;" multiple collapse-tags filterable clearable>
              <el-option
                  v-for="item in pageCtl.shareToOpts"
                  :key="item['VALUE']"
                  :label="item['NAME']"
                  :value="item['VALUE']">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>

    <scp-draggable-resizable v-model="pageCtl.visible.share" :save="shareConditionAction" :save-loading="pageCtl.loading.share" save-text="Share"
                             w="50vw" h="500px" :title="'Share [ ' + pageCtl.share.name + ' ]'">
      <template v-slot="{height}">
        <div style="padding: 5px 5px 0 5px;">
          <el-row>
            <el-col :span="24" style="margin-bottom: var(--scp-widget-margin)">
              <el-select v-model="pageCtl.share.sharedUsers" :placeholder="pageCtl.loading.share ? 'Loading...' : 'Share to'" style="width: 100% !important;"
                         collapse-tags clearable multiple filterable>
                <el-option
                    v-for="user in pageCtl.share.allUsers" :key="user['VAL']" :label="user['LABEL']" :value="user['VAL']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="24">
              <scp-ck-editor v-model="pageCtl.share.remarks" :style="{height: (height - 180) + 'px'}"/>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref, inject, watch } from 'vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import { useRoute } from 'vue-router'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $route = useRoute()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const treeRef = ref()

// @ts-ignore
const props = withDefaults(defineProps<{
  bindTo?: any,
  clickNative?: Function
}>(), {
  bindTo: () => {
  },
  clickNative: null
})

const pageCtl = reactive({
  isAdmin: false,
  existsGroup: [],
  shareToOpts: [],
  visible: {
    win: false,
    newVariant: false,
    share: false
  },
  loading: {
    show: false,
    save: false,
    apply: false,
    modify: false,
    share: false
  },
  newVariant: {
    name: '',
    groups: '',
    shareTo: []
  },
  modifyVariant: {
    key: '',
    name: '',
    groups: '',
    isDefault: false,
    isMyStory: false,
    shareTo: []
  },
  share: {
    cid: '',
    name: '',
    allUsers: [],
    sharedUsers: [],
    remarks: ''
  }
})

const initVariantPage = () => {
  $axios({
    method: 'post',
    url: '/canvas/my_story/init_variant_page'
  }).then((body) => {
    pageCtl.shareToOpts = body.shareTo
    pageCtl.existsGroup = body.existsGroup
    pageCtl.isAdmin = body.isAdmin
  }).catch((error) => {
    console.log(error)
  })
}

const newVariant = () => {
  pageCtl.visible.newVariant = true
}

const deleteVariant = (data) => {
  treeRef.value.setLoading(true)
  pageCtl.loading.show = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/delete_variant',
    data: {
      key: data.key
    }
  }).then((body) => {
    if (body) {
      $message.error('Variant包含评论信息, 请先删除评论, 再删除Variant')
      treeRef.value.setLoading(false)
    } else {
      $message.success('Variant deleted.')
      pageCtl.modifyVariant.key = ''
      treeRef.value.search()
      if (props.clickNative) {
        props.clickNative({})
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.show = false
  })
}

const showVariantDetails = (e) => {
  if (!e.key) {
    return
  }
  pageCtl.loading.show = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/query_variant_config_by_key',
    data: {
      key: e.key
    }
  }).then((body) => {
    pageCtl.modifyVariant.key = e.key
    pageCtl.modifyVariant.name = body.VARIANT_NAME
    pageCtl.modifyVariant.groups = body.GROUP_NAME
    pageCtl.modifyVariant.shareTo = body.shareTo
    pageCtl.modifyVariant.isDefault = (body.IS_DEFAULT === 'IS_DEFAULT')
    pageCtl.modifyVariant.isMyStory = (body.IS_MY_STORY === 'Y')

    pageCtl.newVariant.groups = body.GROUP_NAME
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.show = false
  })
}

const modifyVariant = (e) => {
  if (pageCtl.modifyVariant.name === '') {
    $message.error('Please input variant name')
    return
  }
  if (pageCtl.modifyVariant.groups === '') {
    $message.error('Please input variant group')
    return
  }
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/modify_variant',
    data: {
      key: pageCtl.modifyVariant.key,
      name: pageCtl.modifyVariant.name,
      groups: pageCtl.modifyVariant.groups,
      shareTo: pageCtl.modifyVariant.shareTo,
      conditions: _bindToValue.value,
      componentList: props.bindTo.componentList || []
    }
  }).then(() => {
    $message.success('Story modified.')
    treeRef.value.search()
    if (e) {
      showVariantDetails(e)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

const saveVariant = () => {
  if (pageCtl.newVariant.name === '') {
    $message.error('Please input variant name')
    return
  }
  if (pageCtl.newVariant.groups === '') {
    $message.error('Please input variant group')
    return
  }
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/save_variant',
    data: {
      name: pageCtl.newVariant.name,
      groups: pageCtl.newVariant.groups,
      shareTo: pageCtl.newVariant.shareTo,
      conditions: _bindToValue.value,
      componentList: props.bindTo.componentList || []
    }
  }).then(() => {
    pageCtl.visible.newVariant = false
    $message.success('Variant saved.')
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const showVariant = () => {
  pageCtl.visible.win = !pageCtl.visible.win
}

const applyVariant = (e) => {
  if (pageCtl.loading.apply) {
    return
  }
  pageCtl.loading.apply = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/query_variant_by_key',
    data: {
      key: e.key
    }
  }).then((body) => {
    if (body) {
      for (const key in body) {
        if (body.hasOwnProperty(key)) {
          props.bindTo[key] = body[key]
        }
      }
      nextTick(() => {
        if (props.clickNative) {
          props.clickNative(e)
        }
      })
      showVariant()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.apply = false
  })
}

const setDefaultVariant = (e) => {
  $axios({
    method: 'post',
    url: '/canvas/my_story/update_default_variant',
    data: {
      key: e.key
    }
  }).then(() => {
    treeRef.value.search()
    showVariantDetails(e)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.share = false
  })
}

const copyShareLink = (e) => {
  let url = window.location.href
  url = url.split('?')[0]
  url = url + '?cid=' + e.key
  $copyText(url)
  $message.success('Share link has been copied to the clipboard.')
}

const shareVariant = (e) => {
  pageCtl.visible.share = true
  pageCtl.share.cid = e.key
  pageCtl.share.name = e.label

  if (pageCtl.share.allUsers.length === 0) {
    pageCtl.loading.share = true
    $axios({
      method: 'post',
      url: '/search/query_all_users'
    }).then((body) => {
      pageCtl.share.allUsers = body
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.share = false
    })
  }
}

const shareConditionAction = () => {
  if (pageCtl.share.sharedUsers.length === 0) {
    $message.error('Please select at least one user to share')
    return
  }
  pageCtl.loading.share = true
  $axios({
    method: 'post',
    url: '/canvas/my_story/share_condition',
    data: {
      cid: pageCtl.share.cid,
      name: pageCtl.share.name,
      url: $route.path,
      users: pageCtl.share.sharedUsers,
      remarks: pageCtl.share.remarks
    }
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success(pageCtl.share.name + ' shared!')
      pageCtl.visible.share = false
      pageCtl.share.name = ''
      pageCtl.share.sharedUsers = []
      pageCtl.share.remarks = ''
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.share = false
  })
}

const _bindToValue = computed(() => {
  return JSON.stringify(props.bindTo, null, 2)
})

onMounted(() => {
  initVariantPage()
  loadPage()
})

const loadPage = () => {
  if (props.clickNative) {
    const cid = $route.query.cid
    if (cid) {
      $axios({
        method: 'post',
        url: '/canvas/my_story/query_story_name_by_id',
        data: {
          cid
        }
      }).then((body) => {
        if (body) {
          props.clickNative({
            key: cid,
            label: body
          })
        } else {
          props.clickNative(null)
        }
      }).catch((error) => {
        console.log(error)
      })
    } else {
      $axios({
        method: 'post',
        url: '/canvas/my_story/query_default_condition'
      }).then((body) => {
        if (body) {
          props.clickNative({
            key: body.VARIANT_ID,
            label: body.VARIANT_NAME
          })
        } else {
          props.clickNative(null)
        }
      }).catch((error) => {
        console.log(error)
      })
    }
  }
}

watch(() => $route.query.cid, () => {
  loadPage()
})

defineExpose({
  showVariant
})
</script>

<script lang="ts">
export default {
  name: 'MyStoryVariant'
}
</script>

<style lang="scss">
#myStoryVariant {
  .open-btn-icon {
    display: none;
  }

  .open-btn {
    position: fixed;
    top: 20%;
    width: 5px;
    height: 60px;
    color: #fff;
    background-color: var(--scp-text-color-lighter);
    font-size: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .open-btn:hover {
    width: 30px !important;

    .open-btn-icon {
      display: inline;
    }
  }

  .variant-open-btn {
    left: 0;
    display: flex;
    border-radius: 0 5px 5px 0;

    .variant-open-btn-more {
      text-align: center;
      width: 100%;
      height: 30px;
      line-height: 30px;

      svg:hover {
        color: var(--scp-text-color-hightlight-lighter);
      }
    }
  }

  .variant-open {
    width: 10px !important;
  }

  .variant-open-btn:hover {
    color: #fff;

    .first-btn {
      border-bottom: 1px solid rgb(255, 255, 255);
    }
  }

  .new-variant {
    padding-top: 15px;
    padding-left: var(--scp-widget-margin);

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 5px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }

  .input-tips {
    padding-left: 10px;
    font-style: italic;
    color: var(--scp-text-color-secondary);
    height: 24px;
    line-height: 24px;
  }
}
</style>
