<template>
  <div class="my-story-header">
    <font-awesome-icon icon="chart-line"/>&nbsp;&nbsp;Business Metrics
  </div>

  <el-row class="my-story-business-metrics" :style="{height: _height + 'px'}" style="overflow: auto;margin-bottom: var(--scp-widget-margin)">
    <!-- Business Metrics Cards -->
    <el-col :span="24" class="subscript-container column-container">
      <scp-subscript id="MSBM1" ref="msbm1Ref"/>
      <div class="front">
        <div class="sub-title">Business Metrics</div>
        <div style="height: calc(100% - 1rem);overflow: auto">
          <el-row>
            <!-- Left Column: Business Metrics and KPI Metrics -->
            <el-col :span="24" style="padding-right: 0.125rem;">
              <!-- Business Metrics Card -->
              <my-story-business-metrics-merged-card
                  v-loading="pageCtl.loading.report1"
                  :month-name="pageCtl.conditions.monthName"
                  :tree-data="pageCtl.data.businessMetricsTree"
                  :conditions="pageCtl.conditions"
                  />

              <!-- KPI Metrics Card -->
              <my-story-kpi-merged-card
                  v-loading="pageCtl.loading.report1"
                  :month-name="pageCtl.conditions.monthName"
                  :otds-data="pageCtl.data.otds"
                  :otdm-data="pageCtl.data.otdm"
                  :clo-data="pageCtl.data.clo"
                  :otc-data="pageCtl.data.otc"/>
            </el-col>

            <!-- Right Column: Other Performance Cards -->
            <el-col :span="8" style="display: flex; flex-direction: column; padding-left: 0.125rem;">
              <my-story-business-metrics-card
                  v-loading="pageCtl.loading.report1"
                  card-type="PI"
                  title="Accuracy of Commit. in MyCP"
                  :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                  :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                  :sub-title4="'WEIGHT'"
                  :data="pageCtl.data.mycp"/>

              <my-story-business-metrics-card
                  v-loading="pageCtl.loading.report1"
                  card-type="PI"
                  title="SO NOR(OPM)"
                  :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                  :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                  :sub-title4="'WEIGHT'"
                  :data="pageCtl.data.sonor"/>

              <my-story-business-metrics-card
                  v-loading="pageCtl.loading.report1"
                  card-type="PI"
                  title="Effective PO NORO%"
                  :sub-title2="pageCtl.conditions.monthName +  ' MTD'"
                  :sub-title3="pageCtl.conditions.monthName +  ' YTD'"
                  :sub-title4="'WEIGHT'"
                  :data="pageCtl.data.ponoro"/>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="back">
        <div class="box" style="padding: 5px;width: 70%">
          <div class="box-title">Report Settings</div>
          <el-row>
            <el-col :span="4">Month</el-col>
            <el-col :span="16">
              <el-date-picker
                  style="width: var(--scp-input-width)"
                  v-model="pageCtl.conditions.month"
                  format="YYYYMM"
                  value-format="YYYYMM"
                  type="month"
                  :clearable="false"
                  placeholder="Month">
              </el-date-picker>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">Display</el-col>
            <el-col :span="16">
              <el-select v-model="pageCtl.conditions.display" placeholder="Display" size="small" style="width: var(--scp-input-width) !important;"
                         @click.stop="(e)=>e.preventDefault()"
                         collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="7" filterable
                  >
                  <el-option
                      v-for="item in ['ENTITY', 'CLUSTER_NAME', 'BU']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
            </el-col>
          </el-row>
          <el-divider/>
          <el-row>
            <el-col :span="8">Show <b>OTDS</b> as</el-col>
            <el-col :span="16">
              <el-radio-group v-model="pageCtl.conditions.otdsType">
                <el-radio value="ONTIME_PERCENTAGE">OnTime%</el-radio>
                <el-radio value="FAIL_LINES">Delay Lines%</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">Show <b>OTDM</b> as</el-col>
            <el-col :span="16">
              <el-radio-group v-model="pageCtl.conditions.otdmType">
                <el-radio value="ONTIME_PERCENTAGE">OnTime%</el-radio>
                <el-radio value="FAIL_LINES">Delay Lines%</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>

          <div class="box-footer">
            <el-button @click="msbm1Ref.toggleView()">Back</el-button>
            <el-button type="primary" @click="msbm1Ref.toggleView();search()">Search</el-button>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>

  <el-row>
    <el-col :span="24">
      <my-story-comment ref="commentRef"/>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MyStoryComment from '@/components/scp/canvas/mystory/MyStoryComment.vue'
import MyStoryBusinessMetricsCard from '@/components/scp/canvas/mystory/MyStoryBusinessMetricsCard.vue'
import MyStoryBusinessMetricsMergedCard from '@/components/scp/canvas/mystory/MyStoryBusinessMetricsMergedCard.vue'
import MyStoryKpiMergedCard from '@/components/scp/canvas/mystory/MyStoryKpiMergedCard.vue'
import { useStore } from 'vuex'

const $axios: any = inject('$axios')
const $deepClone: any = inject('$deepClone')
const $convertDateStr: any = inject('$convertDateStr')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')

const msbm1Ref = ref()

const pageCtl = reactive({
  conditions: {
    $scpFilter: {},
    month: '',
    monthName: '',
    display: ['CLUSTER_NAME', 'ENTITY'],
    otdsType: 'ONTIME_PERCENTAGE', // FAIL_LINES OR ONTIME_PERCENTAGE
    otdmType: 'ONTIME_PERCENTAGE' // FAIL_LINES OR ONTIME_PERCENTAGE
  },
  loading: {
    report1: false
  },
  data: {
    // Original performance result data
    otds: [],
    otdm: [],
    clo: [],
    otc: [],
    mycp: [],
    sonor: [],
    ponoro: [],
    din12: [],
    nydin: [],
    // New business metrics data
    orderIntake: [],
    sales: [],
    fcst: [],
    backlog: [],
    // Merged business metrics tree data
    businessMetricsTree: []
  }
})

const renderValue = (value, unit?) => {
  if (isNaN(value)) {
    return '--'
  } if (value === 0) {
    return '0'
  } else {
    return value + (unit || '')
  }
}

const initPage = () => {
  const start = new Date()
  let startMonth: any = start.getMonth() + 1
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  if (!pageCtl.conditions.month) {
    pageCtl.conditions.month = start.getFullYear() + '' + startMonth
  }
  search()
}

const search = () => {
  pageCtl.conditions.monthName = $firstCharUpperCase($convertDateStr(pageCtl.conditions.month).split('-')[0])
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/canvas/my_story_business_metrics/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    // Process OTDS data
    if (pageCtl.conditions.otdsType === 'FAIL_LINES') {
      pageCtl.data.otds = body.otds.map(e => {
        e.value2Color = 'none'
        e.value3Color = 'none'
        e.value2 = renderValue(e.value2, '')
        e.value3 = renderValue(e.value3, '')

        e.children.map(e2 => {
          e2.value2Color = 'none'
          e2.value3Color = 'none'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    } else {
      pageCtl.data.otds = body.otds.map(e => {
        e.value2Color = e.value2 >= 97.5 ? 'green' : 'red'
        e.value3Color = e.value3 >= 97.5 ? 'green' : 'red'
        e.value2 = renderValue(e.value2, '%')
        e.value3 = renderValue(e.value3, '%')

        e.children.map(e2 => {
          e2.value2Color = e2.value2 >= 97.5 ? 'green' : 'red'
          e2.value3Color = e2.value3 >= 97.5 ? 'green' : 'red'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    }

    // Process OTDM data
    if (pageCtl.conditions.otdmType === 'FAIL_LINES') {
      pageCtl.data.otdm = body.otdm.map(e => {
        e.value2Color = 'none'
        e.value3Color = 'none'
        e.value2 = renderValue(e.value2, '')
        e.value3 = renderValue(e.value3, '')

        e.children.map(e2 => {
          e2.value2Color = 'none'
          e2.value3Color = 'none'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    } else {
      pageCtl.data.otdm = body.otdm.map(e => {
        e.value2Color = e.value2 >= 90 ? 'green' : 'red'
        e.value3Color = e.value3 >= 90 ? 'green' : 'red'
        e.value2 = renderValue(e.value2, '%')
        e.value3 = renderValue(e.value3, '%')

        e.children.map(e2 => {
          e2.value2Color = e2.value2 >= 90 ? 'green' : 'red'
          e2.value3Color = e2.value3 >= 90 ? 'green' : 'red'
          e2.value2 = renderValue(e2.value2, '%')
          e2.value3 = renderValue(e2.value3, '%')
          return e2
        })
        return e
      })
    }

    // Process CLO data
    pageCtl.data.clo = body.clo.map(e => {
      e.value2Color = e.value2 >= 95 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 95 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        return e2
      })
      return e
    })

    // Process MyCP data
    pageCtl.data.mycp = body.mycp.map(e => {
      e.value2Color = e.value2 >= 95 ? 'green' : 'red'
      e.value3Color = e.value3 >= 95 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 95 ? 'green' : 'red'
        e2.value3Color = e2.value3 >= 95 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    // Process PO NORO data
    pageCtl.data.ponoro = body.ponoro.map(e => {
      e.value2Color = e.value2 < 5 ? 'green' : 'red'
      e.value3Color = e.value3 < 5 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 < 5 ? 'green' : 'red'
        e2.value3Color = e2.value3 < 5 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    // Process SO NOR data
    pageCtl.data.sonor = body.sonor.map(e => {
      e.value2Color = e.value2 < 2 ? 'green' : 'red'
      e.value3Color = e.value3 < 2 ? 'green' : 'red'
      e.value2 = renderValue(e.value2)
      e.value3 = renderValue(e.value3)

      e.children.map(e2 => {
        e2.value2Color = e2.value2 < 2 ? 'green' : 'red'
        e2.value3Color = e2.value3 < 2 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2)
        e2.value3 = renderValue(e2.value3)
        return e2
      })
      return e
    })

    // Process OTC data
    pageCtl.data.otc = body.otc.map(e => {
      e.value2Color = e.value2 >= 97.7 ? 'green' : 'red'
      e.value3Color = e.value3 >= 97.7 ? 'green' : 'red'
      e.value2 = renderValue(e.value2, '%')
      e.value3 = renderValue(e.value3, '%')

      e.children.map(e2 => {
        e2.value2Color = e2.value2 >= 97.7 ? 'green' : 'red'
        e2.value3Color = e2.value3 >= 97.7 ? 'green' : 'red'
        e2.value2 = renderValue(e2.value2, '%')
        e2.value3 = renderValue(e2.value3, '%')
        return e2
      })
      return e
    })

    // Process orderIntake data
    pageCtl.data.orderIntake = body.orderIntake.map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')
      // Keep arrowValue as number for formatArrowValue function
      // e.arrowValue = renderValue(e.arrowValue, '%')

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        // Keep arrowValue as number for formatArrowValue function
        // e2.arrowValue = renderValue(e2.arrowValue, '%')
        return e2
      })
      return e
    })

    // Process business metrics data
    pageCtl.data.sales = (body.sales || []).map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')
      // Keep arrowValue as number for formatArrowValue function

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        return e2
      })
      return e
    })

    pageCtl.data.fcst = (body.fcst || []).map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        return e2
      })
      return e
    })

    pageCtl.data.backlog = (body.backlog || []).map(e => {
      e.value2Color = e.arrowColor
      e.value2 = renderValue(e.value2, 'M')
      e.value3 = renderValue(e.value3, 'M')
      e.value4 = renderValue(e.value4, '%')
      // Keep arrowValue as number for formatArrowValue function

      e.children && e.children.map(e2 => {
        e2.value2Color = e2.arrowColor
        e2.value2 = renderValue(e2.value2, 'M')
        e2.value3 = renderValue(e2.value3, 'M')
        e2.value4 = renderValue(e2.value4, '%')
        return e2
      })
      return e
    })

    // Process merged business metrics tree data
    pageCtl.data.businessMetricsTree = processMergedBusinessMetrics(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

// 处理合并的业务指标数据
const processMergedBusinessMetrics = (body) => {
  const entityMap = new Map()

  // 处理四种业务指标数据
  const processData = (data, type) => {
    if (!data || !Array.isArray(data)) return

    data.forEach(item => {
      const entityKey = item.value1
      if (!entityMap.has(entityKey)) {
        entityMap.set(entityKey, {
          id: entityKey,
          entity: entityKey,
          rowType: item.rowType,
          hasChildren: true,
          parentName: [],
          level: 0
        })
      }

      const mergedItem = entityMap.get(entityKey)
      const cardData = {
        mtd: renderValue(item.value2, type === 'orderIntake' || type === 'sales' || type === 'fcst' || type === 'backlog' ? 'M' : ''),
        mtdColor: item.value2Color || item.arrowColor,
        yoy: renderValue(item.value3, type === 'orderIntake' || type === 'sales' || type === 'fcst' || type === 'backlog' ? 'M' : ''),
        yoyColor: item.value3Color,
        weight: renderValue(item.value4, '%'),
        weightColor: item.value4Color,
        arrowValue: item.arrowValue,
        arrowColor: item.arrowColor
      }

      mergedItem[type] = cardData
    })
  }

  processData(body.orderIntake, 'orderIntake')
  processData(body.sales, 'sales')
  processData(body.fcst, 'fcst')
  processData(body.backlog, 'backlog')

  return Array.from(entityMap.values())
}

const mergedCardExpandChange = (row) => {
  if (pageCtl.report1LastExpandRow && row.id === pageCtl.report1LastExpandRow.id) {
    return
  }
  // 展开一个子菜单的时候, 不会关闭其他子菜单
  // if (that.report1LastExpandRow !== null) {
  //   report1TableRef.value.toggleRowExpansion(that.report1LastExpandRow, false)
  // }
  pageCtl.report1LastExpandRow = row
}

// region 每个子组件必须要有的代码
// 以下代码如果应用到其他子模块, 需要修改pageKey为对应的值
const pageKey = 'BUSINESS_METRICS'
const $store = useStore()
const commentRef: any = ref()

const _height = computed(() => {
  return Math.max($store.state.pageHeight - 272, 200)
})

const resize = () => {
}

// 下面代码非必要, 不要动
// @ts-ignore
const props = withDefaults(defineProps<{
  conditions?: any
}>(), {
  conditions: () => {
  }
})

// 将父组件的参数应用到子组件, 父页面会主动的call这个函数, 所以这个函数也是子组件的入口函数
const apply = (e) => {
  pageCtl.conditions = $deepClone(props.conditions[pageKey])
  pageCtl.conditions.$scpFilter = $deepClone(props.conditions.$scpFilter)
  initPage()
  if (e) {
    commentRef.value.setBindTo(e.key, pageKey)
  } else {
    commentRef.value.setBindTo('', '')
  }
}

// 子组件有任何更新, 回写父组件
watch(() => pageCtl.conditions, (newVal) => {
  props.conditions[pageKey] = newVal
}, { deep: true })

// 每个组件的默认条件由组件自己控制, 所以需要在加载完成时将默认参数传递至父页面
onMounted(() => {
  props.conditions[pageKey] = pageCtl.conditions
})

// 必须暴露的两个方法, 一个用来传递参数, 一个用来缩放报表
defineExpose({
  apply, resize
})
// endregion
</script>

<script lang="ts">
export default {
  name: 'MyStoryBusinessMetrics'
}
</script>

<style lang="scss">
.my-story-business-metrics {
  margin-top: calc(var(--scp-widget-margin) + var(--scp-widget-margin));

  .column-container {
    display: flex;
    justify-items: center;
    flex-direction: column;
    padding-bottom: calc(var(--scp-widget-margin) + var(--scp-widget-margin));
    height: 100%;
    margin-bottom: 0 !important;

    .sub-title {
      text-align: center;
      font-size: 0.55rem;
      padding: 0;
      font-weight: bold;
      margin-bottom: var(--scp-widget-margin);
      color: var(--scp-text-color-secondary);
    }
  }

  .kpi-tips {
    text-align: center;
    color: #fff;
    font-weight: bold;
  }
}
</style>
