<template>
  <div class="left-sidebar" id="BOL">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :loading="pageCtl.loading.filter"
                        :filter-base="['DEMAND_BACK_ORDER_V']" :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                range-separator="~"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                start-placeholder="Start date"
                end-placeholder="End date"
                :clearable="false">
            </el-date-picker>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.orderType" size="small">
              <el-option
                  v-for="item in ['OPEN_SO_W_O_GI', 'OPEN_SO_W_O_DEL']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.valueType" size="small">
              <el-option
                  v-for="item in ['Net Net Price', 'Net Price', 'Net Net Price HKD', 'Quantity', 'Line', 'Weight']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.scope" size="small">
              <el-option
                  v-for="item in ['SECI', 'PLANT']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.subScope" size="small" collapse-tags multiple>
              <el-option
                  v-for="item in _subScopeOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" class="part1">
            <div class="subscript-container" v-loading="pageCtl.loading.report1 || pageCtl.loading.filter">
              <scp-subscript id="COBS"/>
              <div class="tree-table-box pull-left" @contextmenu.prevent="">
                <el-table ref="report1TableRef" style="border:0;" :data="pageCtl.report1Data" row-key="id" border lazy
                          :load="load"
                          :tree-props="{children: 'children', hasChildren: 'hasChildren'}" class="tree-table" @sort-change="report1Sort">
                  <el-table-column prop="category1" :label="pageCtl.conditions.category1Value" min-width="130px">
                    <template #header>
                      <el-tooltip effect="light" :show-after="500" :content="pageCtl.conditions.category1Value"
                                  placement="top-end">
                        <el-select v-model="pageCtl.conditions.category1Value" size="small" style="border:0 !important;width:100%"
                                   class="hide-input-border" filterable>
                          <el-option
                              v-for="item in _pivotColumns"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-tooltip>
                    </template>
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" :content="scope.row.category1" placement="top-end">
                        <div class="category1-div" v-if="scope.row.category1 !== 'Total'"
                             :data-value="pageCtl.conditions.category1Value + '=' + scope.row.category1">
                          {{ scope.row.category1 }}
                        </div>
                        <div v-else>Total</div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="category2" :label="pageCtl.conditions.category2Value">
                    <template #header>
                      <el-tooltip effect="light" :show-after="500" :content="pageCtl.conditions.category2Value"
                                  placement="top-end">
                        <el-select v-model="pageCtl.conditions.category2Value" size="small" style="border:0 !important;width:100%"
                                   class="hide-input-border" filterable>
                          <el-option
                              v-for="item in _pivotColumns"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-tooltip>
                    </template>
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" :content="scope.row.category2" placement="top-end">
                        <div class="category2-div" :data-value="pageCtl.conditions.category2Value + '=' + scope.row.category2">
                          {{ scope.row.category2 }}
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="d1Gap" label="D-1 Gap" align="right" sortable="custom">
                    <template v-slot="scope">
                      <div>
                        {{ $thousandBitSeparator(scope.row.d1Gap, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="vsTarget" label="VS Target" align="right" sortable="custom">
                    <template v-slot="scope">
                      <div>
                        {{ $thousandBitSeparator(scope.row.vsTarget, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column v-for="(item, index) in pageCtl.report1Headers" :key="item" :prop="item" :label="item" align="right"
                                   sortable="custom">
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenuRef @contextmenu.prevent="rightClick"
                           :data-value="pageCtl.conditions.report1HeadersRaw[index] + ',' + (scope.row.category1 || scope.row.parent) + ',' + (scope.row.category2 || '')">
                        {{ $thousandBitSeparator(scope.row[item], 0) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="target" label="Base Line" align="right" sortable="custom">
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenuRef @contextmenu.prevent="rightClick"
                           :data-value="'Target,' + (scope.row.category1 || scope.row.parent) + ',' + (scope.row.category2 || '')">
                        {{ $thousandBitSeparator(scope.row.target, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="clearfix"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2 || pageCtl.loading.filter">
              <scp-subscript id="COBT"/>
              <chart ref="report2Ref" :height="350" :option="pageCtl.report2Opt"/>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="subscript-container subscript-container-right"
                 v-loading="pageCtl.loading.report2 || pageCtl.loading.report3  || pageCtl.loading.filter">
              <scp-subscript id="CODB"/>
              <chart ref="report3Ref" :height="350" :option="pageCtl.report3Opt"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.report6 || pageCtl.loading.filter">
              <scp-subscript id="COBR"/>
              <chart ref="report6Ref" :height="400" :option="pageCtl.report6Opt"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="contextmenuRef" class="menu-item-box">
      <v-contextmenu-item @click="viewReport1Details">
        View Details
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')
const $randomString: any = inject('$randomString')
const $isEmpty: any = inject('$isEmpty')
const $shortenNumber: any = inject('$shortenNumber')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1TableRef = ref()
const report2Ref = ref()
const report3Ref = ref()
const report6Ref = ref()
const contextmenuRef = ref()

const pageCtl = reactive({
  filterOpts: [],
  report1Data: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    scope: 'SECI',
    subScope: ['SECI_OG', 'SPC_OG'] as any,
    dateRange: [] as any,
    viewDate: '',
    viewCategory1: '',
    viewCategory2: '',
    expandCategory1: '',
    category1Value: 'BU',
    category2Value: 'ENTITY',
    valueType: 'Net Net Price',
    orderType: 'OPEN_SO_W_O_GI',
    report1HeadersRaw: [] as any,
    report3Date: '',
    report3Type: ''
  },
  report1SortColumn: '',
  report1SortOrder: null,
  report1Headers: [],
  report2Opt: {},
  report3Opt: {},
  report6Opt: {},
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false,
    report6: false
  }
})

onMounted(() => {
  initPage()
  report2Ref.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report3Date = obj.name
    searchReport3()
  })

  report3Ref.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report3Type = obj.name

    $viewDetails({
      title: '[' + pageCtl.conditions.report3Type + '] ' + pageCtl.conditions.report3Date,
      url: '/customer/bol/query_report3_details',
      durl: '/customer/bol/download_report3_details',
      params: pageCtl.conditions
    })
  })
})

watch(() => pageCtl.conditions.scope, () => {
  pageCtl.conditions.subScope = []
})

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7)
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/bol/init_page',
    data: pageCtl.conditions
  }).then((body) => {
    const headers = body.report1Headers.map(e => e.substr(5)).reverse()
    pageCtl.conditions.report1HeadersRaw = body.report1Headers.reverse()
    pageCtl.report1Headers = headers
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  searchReport1()
  searchReport2()
  searchReport6()
}
const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/bol/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    const headers = body.headers.map(e => e.substr(5)).reverse()
    pageCtl.conditions.report1HeadersRaw = body.headers.reverse()
    pageCtl.report1Headers = headers
    pageCtl.report1Data = parseReport1Data(body.data, 1)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/bol/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Opt = parseReport2Data(body)

    const xAxis = body.xAxis
    pageCtl.conditions.report3Date = xAxis[xAxis.length - 1]
    searchReport3()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/bol/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Opt = parseReport3Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport6 = () => {
  pageCtl.loading.report6 = true
  $axios({
    method: 'post',
    url: '/customer/bol/query_report6',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report6Opt = parseReport6Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report6 = false
  })
}
const load = (tree, treeNode, resolve) => {
  pageCtl.conditions.expandCategory1 = tree.category1

  $axios({
    method: 'post',
    url: '/customer/bol/query_report1_sub',
    data: pageCtl.conditions
  }).then((body) => {
    resolve(parseReport1Data(body, 2))
  }).catch((error) => {
    console.log(error)
  })
}

const parseReport1Data = (raw, level) => {
  const result = [] as any
  const total = {} as any
  total.id = 'total_' + $randomString(8)
  if (level === 1) {
    total.category1 = 'Total'
  }

  for (let i = 0; i < raw.length; i++) {
    const c: any = { id: 'id_' + $randomString(8) }
    if (level === 2) {
      c.parent = pageCtl.conditions.expandCategory1
    }

    const data = raw[i]
    c.hasChildren = level === 1
    c.category1 = data.CATEGORY1
    c.category2 = data.CATEGORY2
    c.target = data['\'Target\'_VAL']
    total.target = $isEmpty(c.target) ? total.target : (total.target || 0) + c.target

    const len = pageCtl.conditions.report1HeadersRaw.length
    const keyToday = '\'' + pageCtl.conditions.report1HeadersRaw[len - 1] + '\'_VAL'
    const keyYesterday = '\'' + pageCtl.conditions.report1HeadersRaw[len - 2] + '\'_VAL'

    c.d1Gap = $isEmpty(data[keyYesterday]) ? data[keyToday] : (data[keyToday] || 0) - data[keyYesterday]

    c.vsTarget = $isEmpty(c.target) ? data[keyToday] : (data[keyToday] || 0) - c.target

    total.d1Gap = $isEmpty(c.d1Gap) ? total.d1Gap : (total.d1Gap || 0) + c.d1Gap
    total.vsTarget = $isEmpty(c.vsTarget) ? total.vsTarget : (total.vsTarget || 0) + c.vsTarget
    for (let j = 0; j < len; j++) {
      const key = pageCtl.conditions.report1HeadersRaw[j].substr(5)
      const value = data['\'' + pageCtl.conditions.report1HeadersRaw[j] + '\'_VAL']
      c[key] = value
      total[key] = $isEmpty(value) ? total[key] : (total[key] || 0) + value
    }

    result.push(c)
  }

  // 排序
  if (pageCtl.report1SortOrder === 'ascending') {
    result.sort((e1, e2) => {
      const value1 = $isEmpty(e1[pageCtl.report1SortColumn]) ? -1 : e1[pageCtl.report1SortColumn]
      const value2 = $isEmpty(e2[pageCtl.report1SortColumn]) ? -1 : e2[pageCtl.report1SortColumn]

      if (value1 > value2) {
        return 1
      } else {
        return -1
      }
    })
  } else if (pageCtl.report1SortOrder === 'descending') {
    result.sort((e1, e2) => {
      const value1 = $isEmpty(e1[pageCtl.report1SortColumn]) ? -1 : e1[pageCtl.report1SortColumn]
      const value2 = $isEmpty(e2[pageCtl.report1SortColumn]) ? -1 : e2[pageCtl.report1SortColumn]

      if (value1 > value2) {
        return -1
      } else {
        return 1
      }
    })
  }

  if (level === 1) {
    result.push(total)
  }
  return result
}
const parseReport2Data = (raw) => {
  const name = pageCtl.conditions.orderType
  return {
    color: ['#c12e34'],
    legend: $legend({
      data: [name, '0-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M', '3-6M', '>6M'],
      selected: {
        '0-3D': false,
        '3-7D': false,
        '7-14D': false,
        '14-30D': false,
        '1-2M': false,
        '2-3M': false,
        '3-6M': false,
        '>6M': false
      }
    }),
    grid: $grid(),
    title: {
      text: name + ' Trend',
      left: 'left'
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        const x = params[0].name
        tip.push('<div>')
        tip.push(x)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const seriesName = params[i].seriesName

          const value = params[i].value || 0

          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push(' : ')
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      type: 'category',
      data: raw.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series: [{
      name,
      data: raw.yAxis,
      smooth: false,
      type: 'line'
    }, {
      name: '0-3D',
      data: raw.yAxis1,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#4f8f15'
      }
    }, {
      name: '3-7D',
      data: raw.yAxis2,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#ccb601'
      }
    }, {
      name: '7-14D',
      data: raw.yAxis3,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#e2a705'
      }
    }, {
      name: '14-30D',
      data: raw.yAxis4,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#dc7318'
      }
    }, {
      name: '1-2M',
      data: raw.yAxis5,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#d45e21'
      }
    }, {
      name: '2-3M',
      data: raw.yAxis6,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#cb4a2a'
      }
    }, {
      name: '3-6M',
      data: raw.yAxis7,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#c63930'
      }
    }, {
      name: '>6M',
      data: raw.yAxis8,
      smooth: false,
      type: 'line',
      stack: 'sum',
      areaStyle: {},
      itemStyle: {
        color: '#c12e34'
      }
    }]
  }
}
const parseReport3Data = (raw) => {
  const colorSettings = {
    '0-3D': '#4f8f15',
    '3-7D': '#ccb601',
    '7-14D': '#e2a705',
    '14-30D': '#dc7318',
    '1-2M': '#d45e21',
    '2-3M': '#cb4a2a',
    '3-6M': '#c63930',
    '>6M': '#c12e34'
  }
  const colors = [] as any
  for (let i = 0; i < raw.length; i++) {
    const name = raw[i].name
    colors.push(colorSettings[name])
    raw[i].label = { color: colorSettings[name] }
  }
  return {
    color: colors,
    title: {
      text: 'Delay Depth [' + pageCtl.conditions.report3Date + ']',
      left: 'left'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const tip = [] as any
        const percent = ' (' + params.percent + '%)'

        tip.push('<div style="width:9rem;">')
        tip.push(params.data.name)
        tip.push(': ')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.data.value))
        tip.push(percent)
        tip.push('</span>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ type: 'pie' }),
    series: [
      {
        name: 'Delay Depth',
        type: 'pie',
        radius: '60%',
        // startAngle: 240,
        center: ['30%', '55%'],
        data: raw,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

const parseReport6Data = (raw) => {
  const series = [] as any
  const legend = [] as any
  let colorMap = {}
  const name = pageCtl.conditions.orderType
  for (const key in raw) {
    colorMap = {}
  }
  for (const key in raw) {
    if (raw.hasOwnProperty(key) && key !== 'xAxis' && key !== 'yAxis' && key !== 'Others') {
      legend.push(key)
      series.push({
        name: key,
        type: 'line',
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: raw[key] || []
      })
    }
  }
  for (const key in raw) {
    if (key === 'Others') {
      legend.push(key)
      series.push({
        name: key,
        type: 'line',
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: raw[key] || []
      })
    }
  }
  return {
    color: ['#c12e34'],
    legend: $legend({ data: legend }),
    grid: $grid(),
    title: {
      text: 'RCA - ' + name,
      left: 'left'
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        const x = params[0].name
        tip.push('<div>')
        tip.push(x)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }
          const seriesName = params[i].seriesName
          const value = params[i].value || 0
          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push(' : ')
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      type: 'category',
      data: raw.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series
  }
}
const rightClick = (e) => {
  const cs = e.target.dataset.value.split(',')
  pageCtl.conditions.viewDate = cs[0]
  if (cs[1] !== 'Total') {
    pageCtl.conditions.viewCategory1 = cs[1]
    pageCtl.conditions.viewCategory2 = cs[2]
  } else {
    pageCtl.conditions.viewCategory1 = ''
    pageCtl.conditions.viewCategory2 = ''
  }
}
const viewReport1Details = () => {
  $viewDetails({
    title: '[' + pageCtl.conditions.viewDate + '] ' + pageCtl.conditions.viewCategory1 + (pageCtl.conditions.viewCategory2 ? ' - ' + pageCtl.conditions.viewCategory2 : ''),
    url: '/customer/bol/query_report1_details',
    durl: '/customer/bol/download_report1_details',
    params: pageCtl.conditions
  })
}

const report1Sort = (e) => {
  pageCtl.report1SortColumn = e.prop
  pageCtl.report1SortOrder = e.order
  searchReport1()
}

const _subScopeOpts = computed(() => {
  if (pageCtl.conditions.scope === 'SECI') {
    return ['SECI_IG', 'SECI_OG', 'SEHK_IG', 'SEHK_OG', 'SPC_IG', 'SPC_OG']
  } else if (pageCtl.conditions.scope === 'PLANT') {
    return ['PLANT_IG_DOMESTIC', 'PLANT_IG_EXPORT', 'PLANT_OG_DOMESTIC', 'PLANT_OG_EXPORT']
  } else {
    return []
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

watch(() => pageCtl.conditions.category1Value, () => {
  searchReport1()
})

watch(() => pageCtl.conditions.category2Value, () => {
  searchReport1()
})
</script>

<style lang="scss">
#BOL {
  .el-table {
    --el-table-border-color: transparent !important;
  }

  .part1 {
    .el-select__wrapper {
      background-color: initial;
      box-shadow: 0 0 0 0;
    }
  }

  .tree-table-box {
    height: 100%;
    width: calc(100% - 5px);
    overflow: auto;
  }

  .hide-input-border {
    input {
      border: 0 !important;
    }

    .el-input__inner {
      text-align: center;
    }
  }

  .el-table__placeholder {
    display: none;
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-border-color-success) !important;
    color: #fff !important;
  }

  .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border-right: none;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }

  .hide-input-border input {
    border: 0 !important;
  }

  .category1-div {
    width: calc(100% - 23px);
    height: 100%;
    float: right;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .category2-div {
    width: 100%;
    height: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;

        td:first-child {
          padding-left: 22px !important
        }
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 4px !important;
          border-bottom: 0 !important;
          border-right: solid 10px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
    --el-table-border-color: var(--scp-border-color);
  }
}
</style>
