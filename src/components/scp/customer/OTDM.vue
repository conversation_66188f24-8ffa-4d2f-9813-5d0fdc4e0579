<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <!--filter field-->
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.fields" size="small" placeholder="Filter Field" filterable multiple collapse-tags clearable>
              <el-option
                v-for="item in ['AC2_RANGE', 'ACTIVENESS', 'AVAILABILITY_CHECK', 'BU', 'CLUSTER_NAME', 'CUSTOMER_CODE', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT', 'GRA_STATUS', 'GRA_TYPE', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'LT_RANGE', 'MATERIAL_CATEGORY', 'MRP_CONTROLLER', 'PLANT_CODE', 'PLANT_NAME', 'PLANT_TYPE', 'PRODUCT_LINE', 'PRODUCTION_LINE', 'SALES_ORGANIZATION', 'SCOPE', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_REGION', 'SHORTAGE_STATUS', 'SOLD_TO_REGION', 'SOURCE_TYPE', 'STOCKING_POLICY', 'VENDOR_NAME', 'DELAY_RANGE', 'MATERIAL', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!--category-->
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="OTDM_FILTER_V" :filter-base="['OTDM_V']"
                                    :after-apply="search"/>
          </el-col>
          <el-col :span="4">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="monthrange"
                unlink-panels
                range-separator="~"
                format="YYYY/MM"
                value-format="YYYY/MM"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{
                  disabledDate(e) {
                    let time = e.getTime() - new Date().getTime()
                    return time > 5356800000 // 最多只能往前选62天
                  }
                }"
                :clearable="false">
              </el-date-picker>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.otdmType" size="small" placeholder="OTDM Type">
              <el-option
                v-for="item in ['OTDM', 'OTDM Commit', 'OTDM 5WD']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-input-number v-model="pageCtl.conditions.warningValue" size="small" :precision="1" :step="0.1" :max="100" :min="0" style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="3">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange']"/>
            &nbsp;
            <el-tooltip :show-after="1000" effect="light" placement="bottom" content="View RCA/Tips Description">
              <el-button @click="viewTipsDescript">
                <font-awesome-icon icon="question"/>
              </el-button>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="subscript-container subscript-container-left" style="height:516px">
              <h3 style="text-align: left;">OTDM Official</h3>
              <scp-subscript id="COMO"/>
              <scp-table
                :contextMenuItems="pageCtl.report1MenuItems"
                ref="report1TableRef"
                :columns="_report1Columns['columns']"
                :nested-headers="_report1Columns['headers']"
                :lazy="true"
                :fixed-columns-left="pageCtl.report1SelectedYears.length + _fields.length"
                :params="pageCtl.conditions"
                :tableStriped="false"
                :dropdownMenu="false"
                :row-headers="false"
                :pagging="false"
                :max-height="452"
                :after-select="afterReport1Select"
                :pagging-setting-enable="false"
                url="/customer/otdm/query_report1"/>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report2">
              <scp-subscript id="COMC"/>
              <el-row>
                <el-col :span="6" class="report-sub-title-right" :style="{ transform: 'translateX(-25px) translateY(2px)' }">
                  <el-select v-model="pageCtl.conditions.report2DateType" size="small" placeholder="Date Type">
                    <el-option
                        v-for="item in ['By Week', 'By Month']"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <chart ref="report2Ref" :height="516" :option="_report2Opt"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.report1DetailsVisible" :title="pageCtl.report1DetailsTitle">
      <template v-slot="{ height }">
        <scp-table style="margin-bottom:10px;"
                   ref="report1DetailsTableRef"
                   url="/customer/otdm/query_report1_details"
                   download-url="/customer/otdm/download_report1_details"
                   :lazy="true"
                   :max-height="height - 150"
                   :page-sizes="[20, 50, 100, 200, 500]"
                   :params="pageCtl.conditions"
                   :columns="_report1DetailsColumns"
                   :editable="false"
                   :fixed-columns-left="2"
                   :after-change="afterReport1DetailsChange"
                   :context-menu-items="pageCtl.report1DetailsContextMenuItems"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter:any = inject('$dateFormatter')
const $px2Rem:any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $deepClone:any = inject('$deepClone')
const $thousandBitSeparator:any = inject('$thousandBitSeparator')
const $join:any = inject('$join')
const $shortenNumber:any = inject('$shortenNumber')
const $firstCharUpperCase:any = inject('$firstCharUpperCase')
const $convertDateStr:any = inject('$convertDateStr')
const $viewDetails:any = inject('$viewDetails')
const $message: any = inject('$message')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1TableRef = ref()
const report1DetailsTableRef = ref()
const report2Ref = ref()

const viewReport1Details = () => {
  const title = [] as any
  for (let i = 0; i < pageCtl.conditions.report1SelectedValues.length; i++) {
    if (_fields.value.indexOf(pageCtl.conditions.report1SelectedValues[i]) === -1) {
      title.push(pageCtl.conditions.report1SelectedValues[i])
    }
  }
  pageCtl.report1DetailsTitle = 'View Details [' + $join(...title) + ']'

  pageCtl.report1DetailsVisible = true
  pageCtl.report1DetailsUpdate = {}
  report1DetailsTableRef.value.clearAndSearch()
}

const viewReport1Chart = () => {
  searchReport2()
}

const saveReport1Details = () => {
  if (report1DetailsTableRef.value.getLoading() === true) {
    return
  }
  if (JSON.stringify(pageCtl.report1DetailsUpdate) === '{}') {
    $message.error('No changes detected')
    return
  }
  report1DetailsTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/otdm/save_otdm_report1_details',
    data: {
      report1DetailsUpdate: pageCtl.report1DetailsUpdate
    }
  }).then(() => {
    $message.success('RCA code(s) saved')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report1DetailsUpdate = {}
    report1DetailsTableRef.value.setLoading(false)
  })
}

const pageCtl = reactive({
  report1DetailsContextMenuItems: {
    save_rca: {
      name: '<b>Save RCA</b>',
      callback: saveReport1Details
    },
    view_split0: { name: '---------' }
  },
  report1DetailsVisible: false,
  report1DetailsUpdate: {},
  report1DetailsTitle: '',
  loading: {
    report2: false
  },
  conditions: {
    report1SelectedValues: [] as any,
    $scpFilter: {
      cascader: [],
      filter: [{
        joiner: 'AND',
        fields: [
          'PLANT_CODE'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'J001\nXA01\nAX01\nAX02\nLH01\nK001\nK003\nB002\nR001'
      }, {
        joiner: 'AND',
        fields: [
          'SHIP_TO_REGION'
        ],
        types: [
          'VARCHAR2'
        ],
        operator: 'NOT IN',
        value: [],
        text: 'China\nCHINA'
      }]
    },
    fields: ['ENTITY'],
    selectedType: '',
    selectedValue: '',
    dateRange: [] as any,
    warningValue: 97,
    otdmType: 'OTDM',
    report2DateType: 'By Week'
  },
  report1SelectedYears: [],
  report1SelectedMonths: [],
  report1SelectedWeeks: [],
  report1MenuItems: {
    view_details: {
      name: 'View details',
      disabled: () => {
        return pageCtl.conditions.selectedType === 'ONTIME' || !pageCtl.conditions.selectedValue
      },
      callback: viewReport1Details
    },
    view_charts: {
      name: 'View chart',
      callback: viewReport1Chart
    },
    view_split0: { name: '---------' }
  },
  report2Data: {
    xAxis: [],
    yAxis1: [], // ratio
    yAxis2: [], // ontime
    yAxis3: [], // <7
    yAxis4: [], // 7-15
    yAxis5: [], // 15-30
    yAxis6: [] // > 30
  },
  report2SubTitle: '',
  rcaTips: {},
  rcaCode: []
})

watch(() => pageCtl.conditions.dateRange, (newVal, oldVal) => {
  if (oldVal.length > 0) {
    search()
  }
})

watch(() => pageCtl.conditions.report2DateType, () => {
  searchReport2()
})

onMounted(() => {
  initPage()
  report2Ref.value.chart().on('dblclick', (params) => {
    const values = [params.seriesName]
    if (pageCtl.conditions.report2DateType === 'By Week') {
      values.push('W' + params.name)
    } else {
      values.push('M' + params.name)
    }
    if (pageCtl.conditions.report1SelectedValues.length > 2) {
      for (let i = 2; i < pageCtl.conditions.report1SelectedValues.length; i++) {
        values.push(pageCtl.conditions.report1SelectedValues[i])
      }
    }
    pageCtl.conditions.report1SelectedValues = values

    const title = [] as any
    for (let i = 0; i < pageCtl.conditions.report1SelectedValues.length; i++) {
      if (_fields.value.indexOf(pageCtl.conditions.report1SelectedValues[i]) === -1) {
        title.push(pageCtl.conditions.report1SelectedValues[i])
      }
    }
    pageCtl.report1DetailsTitle = 'View Details [' + $join(...title) + ']'
    pageCtl.report1DetailsVisible = true
    pageCtl.report1DetailsUpdate = {}
    report1DetailsTableRef.value.clearAndSearch()
  })
})

const viewTipsDescript = () => {
  $viewDetails({
    title: 'View RCA / Tips Description',
    url: '/customer/otdm/query_otdm_rca_tips_list',
    columns: [
      { data: 'CODE', title: 'RCA_TIPS_CODE', width: 100 },
      { data: 'DESCRIPTION', width: 200 },
      { data: 'COMPUTING_LOGIC', width: 300 }
    ]
  })
}

const initPage = () => {
  let plus = new Date().getTime()
  if (new Date().getDate() < 7) {
    plus -= 86400000 * 7
  }
  const start = new Date(plus)
  pageCtl.conditions.dateRange = [$dateFormatter(start, 'yyyy/MM'), $dateFormatter(start, 'yyyy/MM')]
  searchRef.value.loadAndClick()
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = []
  searchReport1()
  searchReport2()
}

const searchReport1 = () => {
  report1TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/otdm/query_report1_columns',
    data: _conditions.value
  }).then((body) => {
    pageCtl.report1SelectedYears = body.years
    pageCtl.report1SelectedMonths = body.months
    pageCtl.report1SelectedWeeks = body.weeks
    pageCtl.rcaCode = body.rcaCode
    pageCtl.rcaTips = body.rcaTips
    report1TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/otdm/query_report2',
    data: _conditions.value
  }).then((body) => {
    if (pageCtl.conditions.report1SelectedValues.length > 2) {
      const title = [] as any
      for (let i = 2; i < pageCtl.conditions.report1SelectedValues.length; i++) {
        title.push(pageCtl.conditions.report1SelectedValues[i])
      }
      pageCtl.report2SubTitle = '[' + $join(...title) + ']'
    } else {
      pageCtl.report2SubTitle = ''
    }
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const afterReport1Select = (r, rc, c, column) => {
  const values = [r.TYPE, c]
  for (let i = 0; i < _fields.value.length; i++) {
    values.push(r[_fields.value[i]])
  }
  pageCtl.conditions.selectedValue = rc
  pageCtl.conditions.selectedType = r.TYPE
  pageCtl.conditions.report1SelectedValues = values
}

const renderReport1Ratio = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r.TYPE === 'RATIO') {
    td.style.background = 'var(--scp-bg-color-fill-lighter)'
  } else {
    td.style.background = 'var(--scp-bg-color)'
  }

  if (_fields.value.indexOf(prop) !== -1) {
    if (prop !== _fields.value[_fields.value.length - 1]) {
      if (r.TYPE !== 'RATIO') {
        td.innerHTML = ''
      } else {
        td.innerHTML = value
      }
    } else {
      if (r.TYPE === 'RATIO') {
        td.innerHTML = value
      } else if (r.TYPE === 'DELAY') {
        td.innerHTML = 'Delay'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-error)'
      } else if (r.TYPE === 'ONTIME') {
        td.innerHTML = 'On Time'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-success)'
        td.style.fontWeight = '700'
      }
    }
  } else {
    td.style.textAlign = 'left'
    if (r.TYPE === 'RATIO') {
      if (value && !isNaN(value)) {
        td.innerHTML = (value * 100).toFixed(1) + '%'
        if (value * 100 < pageCtl.conditions.warningValue) {
          td.style.color = 'var(--scp-text-color-error)'
        }
      } else {
        td.innerHTML = value
        if (value === 0) {
          td.style.color = 'var(--scp-text-color-error)'
        }
      }
    } else {
      td.innerHTML = $thousandBitSeparator(value, 1)
      const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
      if (r && r.TYPE === 'DELAY' && prop.indexOf('W') !== -1) {
        if (value === 0) {
          const style = 'background-image: linear-gradient(to right, #2c821d 100%, white 100%) !important;color: #fff !important;'
          td.title = '100%'
          td.style = style
        } else if (value) {
          const finish = r[prop + '_CONFIRMED'] || 0
          const percent = finish / value * 100
          const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
          const t = Math.min(percent + 2, 100)

          let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

          td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

          const tdWidth = td.offsetWidth // 单元格宽度
          const textWidth = $px2Rem((td.innerHTML + '').length * 8 + 4) // 文字宽度 = 文字个数 * 每个字符宽度8px + 左边距4px
          if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
            style += 'color: #fff !important;'
          }
          td.style = style
        }
      }
    }
  }
}

const _conditions = computed(() => {
  const cond = $deepClone(pageCtl.conditions)
  cond.fields = _fields.value
  cond.report1SelectedValues = pageCtl.conditions.report1SelectedValues
  return cond
})

const _fields = computed(() => {
  let fields = pageCtl.conditions.fields
  if (fields.length === 0) {
    fields = ['ENTITY']
  }
  return fields
})

const _report1Columns = computed(() => {
  const columns = [] as any
  // 为了可以让warningValue的修改触发表格更新
  if (pageCtl.conditions.warningValue < 0) {
    return [{}]
  }

  for (let i = 0; i < _fields.value.length; i++) {
    columns.push({
      data: _fields.value[i],
      render: renderReport1Ratio
    })
  }

  for (let i = 0; i < pageCtl.report1SelectedYears.length; i++) {
    columns.push({
      data: 'Y' + pageCtl.report1SelectedYears[i],
      title: pageCtl.report1SelectedYears[i],
      render: renderReport1Ratio
    })
  }

  for (let i = 0; i < pageCtl.report1SelectedMonths.length; i++) {
    const name = pageCtl.report1SelectedMonths[i]
    let title = $convertDateStr(name)
    title = $firstCharUpperCase(title.split('-')[0].toLowerCase())
    columns.push({
      data: 'M' + name,
      title,
      render: renderReport1Ratio
    })
  }

  for (let i = 0; i < pageCtl.report1SelectedWeeks.length; i++) {
    const name:any = pageCtl.report1SelectedWeeks[i]
    const title = 'W' + name.substr(4, 2)
    columns.push({
      data: 'W' + pageCtl.report1SelectedWeeks[i],
      title,
      render: renderReport1Ratio
    })
  }

  return {
    headers: [
      [{
        label: '',
        colspan: _fields.value.length
      }, {
        label: 'YTD',
        colspan: pageCtl.report1SelectedYears.length
      }, {
        label: 'Monthly',
        colspan: pageCtl.report1SelectedMonths.length
      }, {
        label: 'Weekly',
        colspan: pageCtl.report1SelectedWeeks.length
      }
      ]
    ],
    columns
  }
})

const _report2Opt = computed(() => {
  const marklineColor = '#fc8452'
  const option:any = {
    title: {
      text: 'OTDM Charts ' + pageCtl.report2SubTitle
    },
    color: [
      '#2c821d',
      '#c13033',
      '#d25924',
      '#e1990a',
      '#87a40c',
      '#448a1a'
    ],
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'axis'
    },
    grid: $grid(),
    legend: $legend({ data: ['>30', '16~30', '8~15', '<=7', 'On Time', 'On Time(%)'] }),
    xAxis: {
      type: 'category',
      data: pageCtl.report2Data.xAxis
    },
    yAxis: [{
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      max: 100,
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0) + '%'
        }
      }
    }],
    visualMap: {
      show: false,
      dimension: 1,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.conditions.warningValue,
        color: marklineColor
      }],
      outOfRange: {
        color: '#2c821d'
      }
    },
    series: [
      {
        name: 'On Time(%)',
        type: 'line',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report2Data.yAxis1,
        label: {
          show: true,
          fontSize: 10
        },
        markLine: {
          symbol: 'none',
          label: {
            position: 'end', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
            formatter: '{c}%',
            fontSize: 10,
            color: marklineColor
          },
          data: [{
            silent: false,
            lineStyle: {
              type: 'dotted',
              color: marklineColor
            },
            yAxis: pageCtl.conditions.warningValue
          }]
        }
      },
      {
        name: '>30',
        type: 'bar',
        stack: 'details',
        data: pageCtl.report2Data.yAxis6
      },
      {
        name: '16~30',
        type: 'bar',
        stack: 'details',
        data: pageCtl.report2Data.yAxis5
      },
      {
        name: '8~15',
        type: 'bar',
        stack: 'details',
        data: pageCtl.report2Data.yAxis4
      },
      {
        name: '<=7',
        type: 'bar',
        stack: 'details',
        data: pageCtl.report2Data.yAxis3
      },
      {
        name: 'On Time',
        type: 'bar',
        stack: 'details',
        data: pageCtl.report2Data.yAxis2
      }
    ]
  }
  return option
})

const encodeHTML = (text) => {
  return text.replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/\u2028/g, '&#8232;')
    .replace(/\u2029/g, '&#8233;')
}

const _report1DetailsColumns = computed(() => {
  return [
    {
      title: 'RCA Result',
      data: 'RCA_RESULT',
      editor: 'select',
      selectOptions: pageCtl.rcaCode,
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value && Object.keys(pageCtl.rcaTips).includes(value)) {
          tips = '- 【' + value + '】 ' + encodeHTML(pageCtl.rcaTips[value])
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    }, {
      title: 'Comments',
      data: 'RCA_COMMENTS'
    }, {
      title: 'Recom.RCA Code',
      data: 'RECOM_RCA_CODE',
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value) {
          tips = '- 【' + value + '】 ' + encodeHTML(pageCtl.rcaTips[value])
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    },
    { title: 'RCA Tips', data: 'RCA_TIPS' },
    { data: 'CALENDAR_DATE' },
    { data: 'CALENDAR_WEEK' },
    { data: 'CALENDAR_MONTH' },
    { data: 'CALENDAR_YEAR' },
    { data: 'CALENDAR_NEXT_5WD_DATE' },
    { data: 'CALENDAR_NEXT_5WD_WEEK' },
    { data: 'CALENDAR_NEXT_5WD_MONTH' },
    { data: 'CALENDAR_NEXT_5WD_YEAR' },
    { data: 'SALES_ORDER_NUMBER' },
    { data: 'SALES_ORDER_ITEM' },
    { data: 'PURCHASE_ORDER_NUMBER' },
    { data: 'PURCHASE_ORDER_ITEM' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'PLANT_CODE' },
    { data: 'MATERIAL' },
    { data: 'ORDER_QUANTITY' },
    { data: 'ACTUAL_GI_DATE' },
    { data: 'FIRST_DLV_DATE' },
    { data: 'ORI_PLANNED_FIRST_GI_DATE' },
    { data: 'FIRST_CRD_DATE' },
    { data: 'SO_CREATED_DATE' },
    { data: 'PURCHASE_ORD_DATE' },
    { data: 'INIT_CRD_MISSING' },
    { data: 'ACTUAL_COMPLETE_GI_QTY' },
    { data: 'CUSTOMER_CODE' },
    { data: 'SHIP_TO' },
    { data: 'SHIP_TO_COUNTRY' },
    { data: 'SOLD_TO_COUNTRY' },
    { data: 'CUSTOMER_NAME' },
    { data: 'SHIP_TO_SHORT_NAME' },
    { data: 'SHIP_TO_FULL_NAME' },
    { data: 'SHIP_TO_PARENT_CODE' },
    { data: 'SHIP_TO_PARENT_NAME' },
    { data: 'SOLD_TO_REGION' },
    { data: 'SOLD_TO_SHORT_NAME' },
    { data: 'SOLD_TO_FULL_NAME' },
    { data: 'SOLD_TO_PARENT_NAME' },
    { data: 'SOLD_TO_PARENT_CODE' },
    { data: 'SHIP_TO_REGION' },
    { data: 'DELIVERY' },
    { data: 'DELIVERY_CREATE_DATE' },
    { data: 'END_CUSTOMER_PO' },
    { data: 'CUSTOMER_MATERIAL' },
    { data: 'SCOPE' },
    { data: 'DELAY_DAYS' },
    { data: 'DELAY_RANGE' },
    { data: 'DELAY_DAYS_COMMIT' },
    { data: 'DELAY_RANGE_COMMIT' },
    { data: 'MATERIAL_CATEGORY' },
    { data: 'LT_RANGE' },
    { data: 'MATERIAL_ST_PLANT' },
    { data: 'ABC' },
    { data: 'CALCULATED_ABC' },
    { data: 'CALCULATED_FMR' },
    { data: 'PROCUREMENT_TYPE' },
    { data: 'ACTIVENESS' },
    { data: 'STOCKING_POLICY' },
    { data: 'COUNTRY' },
    { data: 'MRP_CONTROLLER' },
    { data: 'MRP_CONTROLLER_DESCRIPTION' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'PRODUCT_LINE' },
    { data: 'ENTITY' },
    { data: 'CLUSTER_NAME' },
    { data: 'BU' },
    { data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'PRODUCTION_LINE' },
    { data: 'PLANT_TYPE' },
    { data: 'REPL_STRATEGY' },
    { data: 'SOURCE_TYPE' },
    { data: 'AC2_RANGE' },
    { data: 'AVAILABILITY_CHECK' },
    { data: 'FULFILL_OR_NOT_NONBLOCK' },
    { data: 'FULFILL_OR_NOT_UNRESTRICT' },
    { data: 'GRA_STATUS' },
    { data: 'GRA_TYPE' },
    { data: 'SHIP_TO_CITY' },
    { data: 'SHORTAGE_STATUS' },
    { data: 'VENDOR_NAME' },
    { data: 'PLANT_NAME' },
    { data: 'AVG_SELLING_PRICE_RMB' },
    { data: 'ORDER_VALUE' },
    { data: 'OTDM' },
    { data: 'ORI_PLANNED_FIRST_GI_DATE_NEXT_5WD' },
    { data: 'OTDM_DELAY_WITHIN_5WD' },
    { data: 'VIP_SO_INDICATOR' },
    { data: 'PURCH_ORDER_NUMBER' },
    { data: 'PURCH_ORDER_ITEM' },
    { data: 'DN_CREATED_DELIVERY_QUANTITY' }
  ]
})

const afterReport1DetailsChange = (changes) => {
  if (changes) {
    const ht = report1DetailsTableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'RCA_RESULT' || prop === 'RCA_COMMENTS') {
          const key = r.SALES_ORDER_NUMBER + '#' + r.SALES_ORDER_ITEM
          pageCtl.report1DetailsUpdate[key] = { RCA_RESULT: r.RCA_RESULT, RCA_COMMENTS: r.RCA_COMMENTS }
        }
      }
    })
  }
}
</script>
