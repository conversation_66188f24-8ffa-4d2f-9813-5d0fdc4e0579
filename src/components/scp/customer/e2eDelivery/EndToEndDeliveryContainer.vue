<template>
  <div id="e2e">
    <el-container>
      <el-aside>
        <el-menu
            :default-active="pageCtl.activeMenu"
            collapse
            popper-effect="light"
            @select="handleMenuClick"
        >
          <el-menu-item index="E2E Dashboard">
            <el-icon>
              <icon-menu/>
            </el-icon>
            <template #title>E2E Dashboard</template>
          </el-menu-item>
          <el-menu-item index="E2E Manual D0">
            <el-icon>
              <money/>
            </el-icon>
            <template #title>E2E Manual D+0</template>
          </el-menu-item>
          <el-menu-item index="E2E Delivery Calendar">
            <el-icon>
              <calendar/>
            </el-icon>
            <template #title>E2E Delivery Calendar</template>
          </el-menu-item>
          <el-menu-item index="E2E Delivery Configuration">
            <el-icon>
              <tools/>
            </el-icon>
            <template #title>E2E Delivery Configuration</template>
          </el-menu-item>
          <el-menu-item index="E2E RCA">
            <el-icon>
              <document/>
            </el-icon>
            <template #title>E2E RCA</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-main class="right-main">
        <keep-alive>
          <component :is="pageCtl.currentComponent"></component>
        </keep-alive>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>

import { markRaw, reactive } from 'vue'
import {
  Document,
  Menu as IconMenu, Calendar, Tools, Money
} from '@element-plus/icons-vue'

import DCCalendar from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryCalendar.vue'
import DCConfiguration from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryConfiguration.vue'
import E2EDashboard from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryTracking.vue'
import E2ERCA from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryRCA.vue'
import E2EManualD0 from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryManualD0.vue'

const pageCtl = reactive({
  activeMenu: 'E2E Dashboard' as string,
  currentComponent: markRaw(E2EDashboard) as any
})

const handleMenuClick = (index: string) => {
  pageCtl.activeMenu = index
  switch (index) {
    case 'E2E Dashboard':
      pageCtl.currentComponent = markRaw(E2EDashboard)
      break
    case 'E2E Delivery Configuration':
      pageCtl.currentComponent = markRaw(DCConfiguration)
      break
    case 'E2E Delivery Calendar':
      pageCtl.currentComponent = markRaw(DCCalendar)
      break
    case 'E2E RCA':
      pageCtl.currentComponent = markRaw(E2ERCA)
      break
    case 'E2E Manual D0':
      pageCtl.currentComponent = markRaw(E2EManualD0)
      break
  }
}
</script>

<style lang="scss">
#e2e {
  .widget-body {
    padding-left: 0;
  }

  .el-menu {
    border: 0;
  }

  .el-aside {
    width: 35px;
    overflow: hidden;

    display: flex;
    position: fixed;
    top: 45px;
  }

  .el-menu-item .el-menu-tooltip__trigger {
    width: 35px;
    padding: 0;
    display: flow;
    text-align: center;
  }

  .el-menu--collapse {
    width: 35px;
  }

  .el-main {
    --el-main-padding: 0px;
    padding-left: 35px;
  }

}

</style>
