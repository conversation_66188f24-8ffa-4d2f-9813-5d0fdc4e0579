<template>
  <div class="left-sidebar" id="e2eRca">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.field" size="small" placeholder="Filter Field" clearable filterable collapse-tags multiple>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['E2E_D0_DELIVERY_HIST_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <el-input-number v-model="pageCtl.conditions.warningValue" size="small" :precision="1" :step="0.1" :max="100" :min="40"
                             style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.reportOrderBy" size="small" style="width: var(--scp-input-width) !important;" placeholder="Order by">
              <el-option label="ONTIME" value="ONTIME"/>
              <el-option label="DELAY" value="FAIL"/>
              <el-option label="PERCENTAGE" value="PERCENTAGE"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['rcaSelectedWeekRange']"/>
            &nbsp;
            <el-tooltip :show-after="1000" effect="light" placement="bottom" content="View RCA/Tips Description">
              <el-button @click="viewTipsDescript">
                <font-awesome-icon icon="question"/>
              </el-button>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container subscript-container-left">
              <h3 style="text-align: left;">E2E Delivery RCA</h3>
              <div class="weekRange" style="width: 330px !important;border: 0; position:absolute; top:10px;right: 10px; border-bottom: solid 1px var(--scp-border-color);">
                <el-select v-model="pageCtl.conditions.rcaSelectedWeekRange[0]" placeholder="Start week" style="width: 150px;" filterable>
                  <el-option
                      v-for="(week, index) in pageCtl.weekOpts"
                      :key="index"
                      :label="formatRCAWeek(week)"
                      :value="week">
                  </el-option>
                </el-select>
                <span style="width: 30px; display: inline-block; text-align: center;">~</span>
                <el-select v-model="pageCtl.conditions.rcaSelectedWeekRange[1]" placeholder="End week" style="width: 150px;" filterable>
                  <el-option
                      v-for="(week, index) in pageCtl.weekOpts"
                      :key="index"
                      :label="formatRCAWeek(week)"
                      :value="week">
                  </el-option>
                </el-select>
              </div>
              <scp-subscript id="COSO"/>
              <scp-table
                  :contextMenuItems="pageCtl.contextWeeklyMenuItems"
                  ref="e2eRcaWeeklyTableRef"
                  :columns="_e2eRcaWeeklyColumns"
                  :fixedColumnsLeft="_field.length"
                  :nested-headers="_weeklyNestedHeaders"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :tableStriped="false"
                  :dropdownMenu="false"
                  :row-headers="false"
                  :pagging="false"
                  :max-height="winHeight"
                  :afterSelect="afterWeeklySelect"
                  :pagging-setting-enable="false"
                  url="/customer/end_to_end_delivery_tracking/query_rca_weekly"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- weekly details-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.detailsWeeklyVisible" :title="pageCtl.weeklyDetailsTitle">
      <template v-slot="{ height }">
        <scp-table style="margin-bottom:10px;"
                   ref="weeklyDetailsTableRef"
                   url="/customer/end_to_end_delivery_tracking/query_rca_weekly_details"
                   download-url="/customer/end_to_end_delivery_tracking/download_rca_weekly_details"
                   :lazy="true"
                   :max-height="height - 150"
                   :page-sizes="[20, 50, 100, 200, 500]"
                   :params="pageCtl.conditions"
                   :columns="_weeklyDetailsColumns"
                   :editable="false"
                   :fixed-columns-left="2"
                   :after-change="afterWeeklyDetailsChange"
                   :context-menu-items="pageCtl.contextWeeklyDetailsMenuItems"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, onUpdated, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $join: any = inject('$join')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')
const $startWith: any = inject('$startWith')
const $message: any = inject('$message')
const $viewDetails: any = inject('$viewDetails')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const searchRef = ref()
const e2eRcaWeeklyTableRef = ref()
const weeklyDetailsTableRef = ref()
const winHeight = ref(350)

onUpdated(() => {
  winHeight.value = document.body.clientHeight - 225
})

const saveWeeklyDetails = () => {
  if (weeklyDetailsTableRef.value.getLoading() === true) {
    return
  }
  if (JSON.stringify(pageCtl.weeklyDetailsUpdate) === '{}') {
    $message.error('No changes detected')
    return
  }
  weeklyDetailsTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/save_rca_weekly_details',
    data: {
      weeklyDetailsUpdate: pageCtl.weeklyDetailsUpdate
    }
  }).then(() => {
    $message.success('RCA code(s) saved')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.weeklyDetailsUpdate = {}
    weeklyDetailsTableRef.value.setLoading(false)
  })
}

const viewWeeklyDetails = () => {
  pageCtl.detailsWeeklyVisible = true

  pageCtl.weeklyDetailsUpdate = {}
  weeklyDetailsTableRef.value.clearAndSearch()
}

const viewTipsDescript = () => {
  $viewDetails({
    title: 'View RCA / Tips Description',
    url: '/customer/end_to_end_delivery_tracking/query_rca_tips_list',
    columns: [
      { data: 'RCA_TIPS_CODE', width: 100 },
      { data: 'DESCRIPTION', width: 200 },
      { data: 'COMPUTING_LOGIC', width: 300 }
    ]
  })
}

const pageCtl = reactive({
  pageWidth: document.documentElement.clientWidth * 0.6 - 10,
  weeklyDetailsTitle: '',
  totalType: 'Total',
  filterOpts: [] as any,
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    reportOrderBy: 'PERCENTAGE',
    field: ['PLANT_CODE', 'OPERATION_SCENARIO'],
    selectedField: [],
    selectedDate: [] as any,
    selectedType: '',
    selectedValue: '',
    warningValue: 100,
    rcaSelectedWeekRange: ['', '']
  },
  monthNames: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OTC', 'NOV', 'DEC'],
  selectedYears: [],
  selectedMonths: [],
  selectedWeeks: [],
  selectedWeeksAsc: [],
  stripName: '',
  stripedClass: '',
  timer: null as any,
  contextWeeklyMenuItems: {
    view_derails: {
      name: 'View details',
      disabled: () => {
        return pageCtl.conditions.selectedType === 'On Time' || !pageCtl.conditions.selectedValue
      },
      callback: viewWeeklyDetails
    },
    view_split0: { name: '---------' }
  },
  contextWeeklyDetailsMenuItems: {
    save_rca: {
      name: '<b>Save RCA</b>',
      callback: saveWeeklyDetails
    },
    view_split0: { name: '---------' }
  },
  detailsWeeklyVisible: false,
  detailsColumns: [{}],
  monthlyData: [],
  e2eRcaTipsColumns: [
    {
      data: 'RCA_TIPS_CODE'
    }, {
      data: 'RECOM_RCA_CODE'
    }, {
      data: 'PRIORITY'
    }, {
      data: 'DESCRIPTION'
    }, {
      data: 'COMPUTING_LOGIC'
    }
  ],
  weeklyDetailsUpdate: {},
  weeklyDetailsUpdate2: {},
  rcaTips: {},
  rcaCode: [],
  weekOpts: []
})

watch(() => pageCtl.conditions.warningValue, () => {
  delayer(() => {
    e2eRcaWeeklyTableRef.value.redrawTable()
  })
})

watch(() => pageCtl.conditions.rcaSelectedWeekRange, (newVal, oldVal) => {
  if (oldVal.length > 0) {
    if (newVal[0] && newVal[1]) {
      // 比较并排序
      if (newVal[0] > newVal[1]) {
        const temp = pageCtl.conditions.rcaSelectedWeekRange[0]
        pageCtl.conditions.rcaSelectedWeekRange[0] = pageCtl.conditions.rcaSelectedWeekRange[1]
        pageCtl.conditions.rcaSelectedWeekRange[1] = temp
      }
    }
    search()
  }
}, { deep: true })

const _field = computed(() => {
  if (pageCtl.conditions.field.length > 0) {
    return pageCtl.conditions.field
  } else {
    return ['PLANT_CODE']
  }
})

const _e2eRcaWeeklyColumns = computed(() => {
  const columns = [] as any

  for (let i = 0; i < _field.value.length; i++) {
    const name = _field.value[i]
    columns.push({
      title: name,
      data: name,
      render: renderTableStriped
    })
  }

  for (let i = 0; i < pageCtl.selectedYears.length; i++) {
    columns.push({
      title: pageCtl.selectedYears[i],
      data: pageCtl.selectedYears[i],
      render: renderRatioWeekly
    })
  }

  for (let i = 0; i < pageCtl.selectedMonths.length; i++) {
    columns.push({
      title: $firstCharUpperCase(pageCtl.selectedMonths[i]),
      data: pageCtl.selectedMonths[i],
      render: renderRatioWeekly
    })
  }

  for (let i = 0; i < pageCtl.selectedWeeks.length; i++) {
    columns.push({
      title: pageCtl.selectedWeeks[i],
      data: pageCtl.selectedWeeks[i],
      render: renderRatioWeekly
    })
  }
  return columns
})

const _weeklyNestedHeaders = computed(() => {
  return [
    [
      {
        label: '',
        colspan: _field.value.length
      },
      {
        label: 'Monthly',
        colspan: pageCtl.selectedMonths.length + pageCtl.selectedYears.length
      }, {
        label: 'Weekly',
        colspan: pageCtl.selectedWeeks.length
      }
    ]
  ]
})

const _weeklyDetailsColumns = computed(() => {
  return [
    {
      title: 'RCA Result',
      data: 'RCA_RESULT',
      type: 'autocomplete',
      source: (query, process) => {
        if (query) {
          process(pageCtl.rcaCode.filter((e: any) => e.toLowerCase().indexOf(query.toLowerCase()) !== -1))
        } else {
          process(pageCtl.rcaCode)
        }
      },
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value) {
          tips = '- 【' + value + '】 ' + pageCtl.rcaTips[value]
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    }, {
      title: 'Comments',
      data: 'RCA_COMMENTS'
    }, {
      title: 'Recom.RCA Code',
      data: 'RECOM_RCA_CODE',
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value) {
          tips = '- 【' + value + '】 ' + pageCtl.rcaTips[value]
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    }, {
      title: 'RCA Tips',
      data: 'RCA_TIPS',
      render: (hotInstance, td, row, column, prop, value) => {
        const r = hotInstance.getSourceDataAtRow(row)
        const tips = [] as any
        if (value) {
          const vs = value.split(',')
          for (let i = 0; i < vs.length; i++) {
            const v = vs[i]
            if (pageCtl.rcaTips[v]) {
              tips.push('- 【' + v + '】 ' + pageCtl.rcaTips[v])
            }
          }
        }

        if (r.RCA_REMARK) {
          tips.push('- 【Remark】 ' + r.RCA_REMARK)
        }

        if (tips.length > 0) {
          let html = '<div title="#content#">'
          html += value
          html += '</div>'
          td.innerHTML = html.replace('#content#', tips.join('&#13;'))
        } else {
          td.innerHTML = value
        }
      }
    },
    { data: 'DATA_SOUCRE' },
    { data: 'DELIVERY_NUMBER' },
    { data: 'DELIVERY_ITEM' },
    { data: 'SALES_ORDER_NUMBER' },
    { data: 'SALES_ORDER_ITEM' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'ENTITY' },
    { data: 'SO_PRIORITY' },
    { data: 'OPERATION_SCENARIO' },
    { data: 'GOODS_MOVEMENT' },
    { data: 'STORAGE_TYPE' },
    { data: 'STORAGE_STATUS' },
    { data: 'SOLD_TO' },
    { data: 'SHIP_TO_CITY' },
    { data: 'INCOTERM_1' },
    { data: 'COT' },
    { data: 'GI_DATE' },
    { data: 'CREDIT_STATUS' },
    { data: 'TO_EMAIL' },
    { data: 'URGENT_LINES' },
    { data: 'DN_COUNT' },
    { data: 'NOT_URGENT_LINES' },
    { data: 'COMPLETE_STATUS' },
    { data: 'CREATE_TIME' },
    { data: 'SOLD_TO_SHORT_NAME' },
    { data: 'CC_EMAIL' },
    { data: 'PICKING_STATUS' },
    { data: 'PACKING_STATUS' },
    { data: 'SHIPMENT_ITEM' },
    { data: 'SHIPMENT_NUMBER' },
    { data: 'D0_EXECUTION_STATUS' },
    { data: 'FIRST_ENTRY_TIME' },
    { data: 'RN' },
    { data: 'D0_EXECUTION' },
    { data: 'ONTIME_STATUS' }
  ]
})

onMounted(() => {
  initPage()
  winHeight.value = document.documentElement.clientHeight - 200
})

const formatRCAWeek = (week) => {
  const year = week.substring(0, 4)
  const weekNumber = week.substring(4, 6)
  return `${year.slice(-2)}W${weekNumber}`
}

const afterWeeklySelect = (r, rc, c, column) => {
  // disable select event when details windows shown
  if (pageCtl.detailsWeeklyVisible === true) {
    return
  }
  pageCtl.conditions.selectedValue = rc
  const selected = [] as any
  for (let i = 0; i < _field.value.length; i++) {
    selected.push(r[_field.value[i]])
  }
  pageCtl.conditions.selectedField = selected
  if (column >= _field.value.length) {
    pageCtl.conditions.selectedDate = c
  } else {
    pageCtl.conditions.selectedDate = '-1'
  }
  pageCtl.conditions.selectedType = r.TYPE
  const title = pageCtl.conditions.selectedDate === '-1' ? pageCtl.conditions.rcaSelectedWeekRange.join(' - ') : pageCtl.conditions.selectedDate
  pageCtl.weeklyDetailsTitle = 'View Weekly Details - ' + $join(...selected) + '  ' + title + '  ' + (r.TYPE === 'RATIO' ? '(On Time,Delay)' : r.TYPE)
}

const afterWeeklyDetailsChange = (changes) => {
  if (changes) {
    const ht = weeklyDetailsTableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'RCA_RESULT' || prop === 'RCA_COMMENTS') {
          const key = r.DELIVERY_NUMBER + '#' + r.DELIVERY_ITEM
          pageCtl.weeklyDetailsUpdate[key] = { RCA_RESULT: r.RCA_RESULT, RCA_COMMENTS: r.RCA_COMMENTS }
        }
      }
    })
  }
}

const initPage = () => {
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/init_rca_page'
  }).then((body) => {
    pageCtl.weekOpts = body.weekOpts
    pageCtl.conditions.rcaSelectedWeekRange = [body.weekOpts[9], body.weekOpts[1]]
    pageCtl.rcaCode = body.rcaCode
    pageCtl.rcaTips = body.rcaTips
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}

const search = () => {
  pageCtl.stripedClass = ''
  e2eRcaWeeklyTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/query_rca_columns_by_daterange',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.selectedYears = body.years
    pageCtl.selectedMonths = body.months
    pageCtl.selectedWeeks = body.weeks

    if (pageCtl.selectedWeeks) {
      pageCtl.selectedWeeksAsc = []
      for (let i = pageCtl.selectedWeeks.length - 1; i >= 0; i--) {
        pageCtl.selectedWeeksAsc.push(pageCtl.selectedWeeks[i])
      }
    }
    e2eRcaWeeklyTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

// 为ratio添加百分号
const renderRatioWeekly = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      if (value * 100 < pageCtl.conditions.warningValue) {
        td.style = 'color: var(--scp-text-color-error) !important'
      }
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }

    const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
    if (r && r.TYPE === 'Delay' && prop.indexOf('W') !== -1) {
      if (value === 0) {
        const style = 'background-image: linear-gradient(to right, #2c821d 100%, white 100%) !important;color: #fff !important;'
        td.title = '100%'
        td.style = style
      } else if (value) {
        const finish = r[prop + '_CONFIRMED'] || 0
        const percent = finish / value * 100
        const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
        const t = Math.min(percent + 2, 100)

        let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

        td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

        const tdWidth = td.offsetWidth // 单元格宽度
        const textWidth = $px2Rem((td.innerHTML + '').length * 8 + 4) // 文字宽度 = 文字个数 * 每个字符宽度8px + 左边距4px
        if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
          style += 'color: #fff !important;'
        }
        td.style = style
      }
    }
  }
}

// 表格添加斑马纹
const renderTableStriped = (hotInstance, td, row, column, prop, value) => {
  if (row % 3 === 0) {
    pageCtl.stripedClass = 'striped-even3-tr'
  } else {
    pageCtl.stripedClass = 'striped-odd3-tr'
  }

  if (td.parentNode) {
    td.parentNode.className = pageCtl.stripedClass
  }
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    td.innerHTML = value || ''
  } else {
    if (column === _field.value.length - 1) {
      td.innerHTML = r.TYPE || ''
    } else {
      td.innerHTML = ''
    }

    td.style.textAlign = 'right'
    if (r.TYPE === 'Delay') {
      td.style.color = 'var(--scp-text-color-error)'
    } else if (r.TYPE === 'On Time') {
      td.style.color = 'var(--scp-text-color-success)'
      td.style.fontWeight = '700'
    }
  }
}
const delayer = (action, delay = 600) => {
  if (pageCtl.timer) {
    clearTimeout(pageCtl.timer)
  }
  pageCtl.timer = setTimeout(() => {
    action.call()
  }, delay)
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
</script>

<style lang="scss">
#e2eRca {
  .el-date-editor.el-input__wrapper {
    box-shadow: 1 0 0 1px var(--scp-border-color, var(--scp-border-color)) inset;
  }
  .weekRange {
    .el-select--small .el-select__wrapper {
      box-shadow: none;
    }
    .el-select--small .el-select__suffix {
      display: none;
    }
    .el-select__placeholder {
      text-align: center;
    }
  }
}
</style>
