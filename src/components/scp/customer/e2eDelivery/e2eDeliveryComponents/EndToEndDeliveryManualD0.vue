<template>
  <div class="left-sidebar" id="manualD0">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['E2E_D0_MANUAL_DN_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                style="width: calc(100% - 35px)"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
            ></el-date-picker>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button plain @click="openGuidePage" style="white-space: nowrap; width: 20%">
              <el-icon>
                <guide/>
              </el-icon>
              上传指南
            </el-button>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <p style="font-size: 14px; padding: 10px 10px 10px 0; font-weight: 600">End to End D+0 Manual DN
            Maintenance</p>
          <scp-table
              url="/customer/end_to_end_delivery_tracking/query_manual_d0"
              download-url="/customer/end_to_end_delivery_tracking/download_manual_d0"
              save-url="/customer/end_to_end_delivery_tracking/save_manual_d0"
              ref="report1TableRef"
              :params="pageCtl.conditions"
              :contextMenuItemsReverse="true"
              :lazy="true"
              :columns="pageCtl.report1Columns"
              :primary-key-id="['PRIMARY_KEY']"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { ElMessageBox } from 'element-plus'
import {
  Guide
} from '@element-plus/icons-vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const report1TableRef = ref()

onMounted(() => {
  report1TableRef.value.search()
})

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const pageCtl = reactive({
  filterOpts: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    dateRange: [] as any
  },
  loading: {
    report1: false
  },
  report1Columns: [
    { data: 'DELIVERY_NUMBER', title: camelCaseStartPlaceholder('DELIVERY_NUMBER') },
    { data: 'UPLOAD_OWNER', title: camelCaseStartPlaceholder('UPLOAD_OWNER') },
    { data: 'UPLOAD_OWNER_NAME', title: camelCaseStartPlaceholder('UPLOAD_OWNER_NAME') },
    { data: 'OPERATION_SCENARIO', title: camelCaseStartPlaceholder('OPERATION_SCENARIO') },
    { data: 'CREATE_DATE', title: camelCaseStartPlaceholder('CREATE_DATE') },
    { data: 'CREATE_TIME', title: camelCaseStartPlaceholder('CREATE_TIME') },
    { data: 'SALES_ORDER_NUMBER', title: camelCaseStartPlaceholder('SALES_ORDER_NUMBER') },
    { data: 'SALES_ORDER_ITEM', title: camelCaseStartPlaceholder('SALES_ORDER_ITEM') },
    { data: 'MATERIAL', title: camelCaseStartPlaceholder('MATERIAL') },
    { data: 'PLANT_CODE', title: camelCaseStartPlaceholder('PLANT_CODE') },
    { data: 'ENTITY', title: camelCaseStartPlaceholder('ENTITY') },
    { data: 'SO_PRIORITY', title: camelCaseStartPlaceholder('SO_PRIORITY') },
    { data: 'GOODS_MOVEMENT', title: camelCaseStartPlaceholder('GOODS_MOVEMENT') },
    { data: 'STORAGE_TYPE', title: camelCaseStartPlaceholder('STORAGE_TYPE') },
    { data: 'STORAGE_STATUS', title: camelCaseStartPlaceholder('STORAGE_STATUS') },
    { data: 'SOLD_TO', title: camelCaseStartPlaceholder('SOLD_TO') },
    { data: 'SOLD_TO_SHORT_NAME', title: camelCaseStartPlaceholder('SOLD_TO_SHORT_NAME') },
    { data: 'SHIP_TO', title: camelCaseStartPlaceholder('SHIP_TO') },
    { data: 'SHIP_TO_CITY', title: camelCaseStartPlaceholder('SHIP_TO_CITY') },
    { data: 'INCOTERM_1', title: camelCaseStartPlaceholder('INCOTERM_1') },
    { data: 'COT', title: camelCaseStartPlaceholder('COT') },
    { data: 'GI_DATE', title: camelCaseStartPlaceholder('GI_DATE') },
    { data: 'CREDIT_STATUS', title: camelCaseStartPlaceholder('CREDIT_STATUS') },
    { data: 'TO_EMAIL', title: camelCaseStartPlaceholder('TO_EMAIL') },
    { data: 'CC_EMAIL', title: camelCaseStartPlaceholder('CC_EMAIL') },
    { data: 'HOURLY_TO_EMAIL', title: camelCaseStartPlaceholder('HOURLY_TO_EMAIL') },
    { data: 'HOURLY_CC_EMAIL', title: camelCaseStartPlaceholder('HOURLY_CC_EMAIL') },
    { data: 'DAILY_TO_EMAIL', title: camelCaseStartPlaceholder('DAILY_TO_EMAIL') },
    { data: 'DAILY_CC_EMAIL', title: camelCaseStartPlaceholder('DAILY_CC_EMAIL') },
    { data: 'MONTHLY_TO_EMAIL', title: camelCaseStartPlaceholder('MONTHLY_TO_EMAIL') },
    { data: 'MONTHLY_CC_EMAIL', title: camelCaseStartPlaceholder('MONTHLY_CC_EMAIL') },
    { data: 'URGENT_LINES', title: camelCaseStartPlaceholder('URGENT_LINES') },
    { data: 'DN_COUNT', title: camelCaseStartPlaceholder('DN_COUNT') },
    { data: 'NOT_URGENT_LINES', title: camelCaseStartPlaceholder('NOT_URGENT_LINES') },
    { data: 'COMPLETE_STATUS', title: camelCaseStartPlaceholder('COMPLETE_STATUS') },
    { data: 'LAST_OPERATION_EXPRESS_TIME', title: camelCaseStartPlaceholder('LAST_OPERATION_EXPRESS_TIME') },
    { data: 'COT_LAST_OPERATION_TIME', title: camelCaseStartPlaceholder('COT_LAST_OPERATION_TIME') },
    { data: 'NEW_LABEL', title: camelCaseStartPlaceholder('NEW_LABEL') },
    { data: 'PICKING_STATUS', title: camelCaseStartPlaceholder('PICKING_STATUS') },
    { data: 'PACKING_STATUS', title: camelCaseStartPlaceholder('PACKING_STATUS') },
    { data: 'SHIPMENT_ITEM', title: camelCaseStartPlaceholder('SHIPMENT_ITEM') },
    { data: 'SHIPMENT_NUMBER', title: camelCaseStartPlaceholder('SHIPMENT_NUMBER') },
    { data: 'FIRST_ENTRY_TIME', title: camelCaseStartPlaceholder('FIRST_ENTRY_TIME') },
    { data: 'CRD_DATE', title: camelCaseStartPlaceholder('CRD_DATE') },
    { data: 'D0_EXECUTION_STATUS', title: camelCaseStartPlaceholder('D0_EXECUTION_STATUS') },
    { data: 'CREATED_BY', title: camelCaseStartPlaceholder('CREATED_BY') },
    { data: 'D0_EXECUTION', title: camelCaseStartPlaceholder('D0_EXECUTION') },
    { data: 'SHIPPING_POINT', title: camelCaseStartPlaceholder('SHIPPING_POINT') },
    { data: 'SO_CREATED_DATE', title: camelCaseStartPlaceholder('SO_CREATED_DATE') },
    { data: 'ONTIME_STATUS', title: camelCaseStartPlaceholder('ONTIME_STATUS') },
    { data: 'CALENDAR_TYPE', title: camelCaseStartPlaceholder('CALENDAR_TYPE') }
  ] as Array<any>
})

const search = () => {
  searchReport1()
}

const searchReport1 = () => {
  report1TableRef.value.search()
}

const initPage = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/manual_d0_init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
  }).catch((error) => {
    console.log(error)
  }).finally(
    pageCtl.loading.report1 = false
  )
}

const openGuidePage = () => {
  ElMessageBox.alert(
      `
    <div style="font-size: 14px; color: red; line-height: 1.5;">
      请参考下方图片操作步骤完成上传操作。
    </div>
    <div style="text-align: center; margin: 10px 0;">
      <img
        src="http://scp-dss.cn.schneider-electric.com/chevereto/images/2025/07/25/123.png"
        alt="操作指南"
        style="max-width: 100%; height: auto; display: block; margin: 0 auto;"
      />
    </div>
    `,
      'Manual D0 上传指南',
      {
        dangerouslyUseHTMLString: true,
        customClass: 'end-to-end-manual-d0-message-box'
      }
  )
}

onMounted(() => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.dateRange = [
    $dateFormatter(end, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  initPage()
})

</script>

<style lang="scss">
.end-to-end-manual-d0-message-box {
  --el-messagebox-width: 60vw !important;
  height: 80vh !important;
  margin: auto !important;
  position: absolute !important;
  top: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;

  .el-message-box__btns {
    padding-top: 0;
  }
  .el-message-box__title {
    font-weight: bold;
  }
}
</style>
