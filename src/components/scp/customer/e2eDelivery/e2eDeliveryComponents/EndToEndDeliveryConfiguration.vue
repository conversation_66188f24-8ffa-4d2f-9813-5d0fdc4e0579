<template>
  <div class="left-sidebar" id="CsfValidation">
    <div class="widget">
      <div class="widget-body">
        <div class="subscript-container">
          <p style="font-size: 14px; padding: 10px 10px 10px 0; font-weight: 600">End to End D+0 Delivery Configuration</p>
          <scp-table
              url="/customer/end_to_end_delivery_tracking/query_configuration"
              download-url="/customer/end_to_end_delivery_tracking/download_configuration"
              save-url="/customer/end_to_end_delivery_tracking/save_configuration"
              ref="report1TableRef"
              :contextMenuItemsReverse="true"
              :lazy="true"
              :primary-key-id="['PLANT_CODE']"
              :columns="_report1TableColumn"/>
        </div>
        <div class="subscript-container">
          <p style="font-size: 14px; padding: 10px 10px 10px 0; font-weight: 600">End to End D+0 Delivery COT</p>
          <scp-table
              url="/customer/end_to_end_delivery_tracking/query_cot_configuration"
              download-url="/customer/end_to_end_delivery_tracking/download_cot_configuration"
              save-url="/customer/end_to_end_delivery_tracking/save_cot_configuration"
              ref="report2TableRef"
              :contextMenuItemsReverse="true"
              :lazy="true"
              :primary-key-id="['ROW_ID']"
              :columns="_report2TableColumn"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { computed, onMounted, ref } from 'vue'

const report1TableRef = ref()
const report2TableRef = ref()

onMounted(() => {
  report1TableRef.value.search()
  report2TableRef.value.search()
})

const _report1TableColumn = computed(() => {
  return [
    { data: 'PLANT_CODE' },
    { data: 'COT_OPERATION_BUFFER_MINUTES' },
    { data: 'COT_LAST_OPERATION_TIME' },
    { data: 'LAST_OPERATION_EXPRESS_TIME' },
    { data: 'ENABLE_DN_CREATION' },
    { data: 'HOURLY_TO_EMAIL' },
    { data: 'HOURLY_CC_EMAIL' },
    { data: 'DAILY_TO_EMAIL' },
    { data: 'DAILY_CC_EMAIL' },
    { data: 'MONTHLY_TO_EMAIL' },
    { data: 'MONTHLY_CC_EMAIL' }
  ]
})

const _report2TableColumn = computed(() => {
  return [
    { data: 'PLANT_CODE' },
    { data: 'SHIP_TO_CITY' },
    { data: 'COT' },
    { data: 'WAREHOUSE_NUMBER' }
  ]
})

</script>
