<template>
  <div class="left-sidebar" id="e2e">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['E2E_D0_DELIVERY_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <div class="subscript-container subscript-container-left">
              <el-row>
                <el-col :span="24">
                  <div style="margin: 3px 5px 6px; font-weight: 600; font-size: 14px; ">
                    E2E Execution Performance - Yesterday {{ pageCtl.conditions.selectedTreePath === '' ? '' : '[' + pageCtl.conditions.selectedTreePath + ']' }}
                  </div>
                </el-col>
              </el-row>
              <div class="card-container e2e-widget">
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.YESTERDAY_TOTAL_D0_LINES, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('YESTERDAY_TOTAL_D0_LINES')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.YESTERDAY_TOTAL_D0_LINES) ? '--' : $shortenNumber(pageCtl.report5Data.YESTERDAY_TOTAL_D0_LINES)
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('TOTAL_D0_LINES') }}</h6>
                  </el-card>
                </el-tooltip>
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.YESTERDAY_COMPLETED_D0_LINES, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('YESTERDAY_COMPLETED_D0_LINES')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.YESTERDAY_COMPLETED_D0_LINES) ? '--' : $shortenNumber(pageCtl.report5Data.YESTERDAY_COMPLETED_D0_LINES)
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('COMPLETED_D0_LINES') }}</h6>
                  </el-card>
                </el-tooltip>
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.YESTERDAY_D0_EXECUTION_RATIO, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('YESTERDAY_D0_EXECUTION_RATIO')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.YESTERDAY_D0_EXECUTION_RATIO) ? '--' : $shortenNumber(pageCtl.report5Data.YESTERDAY_D0_EXECUTION_RATIO * 100, 2) + '%'
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('D0_EXECUTION_RATIO') }}</h6>
                  </el-card>
                </el-tooltip>
              </div>
            </div>
          </el-col>
          <el-col :span="15">
            <div class="subscript-container subscript-container-right">
              <el-row>
                <el-col :span="24">
                  <div style="margin: 3px 5px 6px; font-weight: 600; font-size: 14px; ">
                    E2E Execution Performance - Today {{ pageCtl.conditions.selectedTreePath === '' ? '' : '[' + pageCtl.conditions.selectedTreePath + ']' }}
                  </div>
                </el-col>
              </el-row>
              <div class="card-container e2e-widget">
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.TODAY_TOTAL_D0_LINES, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card2" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('TODAY_TOTAL_D0_LINES')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.TODAY_TOTAL_D0_LINES) ? '--' : $shortenNumber(pageCtl.report5Data.TODAY_TOTAL_D0_LINES)
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('TOTAL_D0_LINES') }}</h6>
                  </el-card>
                </el-tooltip>
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.TODAY_TARGET_D0_LINES, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card2" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('TODAY_TARGET_D0_LINES')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.TODAY_TARGET_D0_LINES) ? '--' : $shortenNumber(pageCtl.report5Data.TODAY_TARGET_D0_LINES)
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('TARGET_D0_LINES') }}</h6>
                  </el-card>
                </el-tooltip>
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.TODAY_TARGET_D0_RATIO, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card2" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('TODAY_TARGET_D0_RATIO')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.TODAY_TARGET_D0_RATIO) ? '--' : $shortenNumber(pageCtl.report5Data.TODAY_TARGET_D0_RATIO * 100, 2) + '%'
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('TARGET_D0_RATIO') }}</h6>
                  </el-card>
                </el-tooltip>
                <el-tooltip
                    :content="($thousandBitSeparator(pageCtl.report5Data.TODAY_COMPLETED_D0_LINES, 1) || '--') + ''"
                    effect="light" placement="bottom">
                  <el-card class="box-card2" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('TODAY_COMPLETED_D0_LINES')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.TODAY_COMPLETED_D0_LINES) ? '--' : $shortenNumber(pageCtl.report5Data.TODAY_COMPLETED_D0_LINES)
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('COMPLETED_D0_LINES') }}</h6>
                  </el-card>
                </el-tooltip>
                <el-tooltip :content="($thousandBitSeparator(pageCtl.report5Data.TODAY_D0_EXECUTION_RATIO, 1) || '--') + ''"
                            effect="light" placement="bottom">
                  <el-card class="box-card2" shadow="hover" v-loading="pageCtl.loading.report5"
                           @dblclick="viewReport5Details('TODAY_D0_EXECUTION_RATIO')">
                    <h4>{{
                        $isEmpty(pageCtl.report5Data.TODAY_D0_EXECUTION_RATIO) ? '--' : $shortenNumber(pageCtl.report5Data.TODAY_D0_EXECUTION_RATIO * 100, 2) + '%'
                      }}</h4>
                    <h6>{{ camelCaseStartPlaceholder('D0_EXECUTION_RATIO') }}</h6>
                  </el-card>
                </el-tooltip>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="IESC" ref="report1SubRef"/>
              <div class="front">
                <chart ref="report1Ref" v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..."
                                 filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :key="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..."
                                 filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..."
                                 filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :value="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..."
                                 filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :label="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..."
                                 filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :label="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option v-for="item in [1, 2]"
                                   :label="item"
                                   :key="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox v-for="item in pageCtl.report1TooltipsOpts"
                                 :value="item"
                                 :label="item"
                                 :key="item"/>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button
                        @click="report1SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report1SubRef.toggleView();searchReport1()">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="15" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <el-row>
                <el-col :span="10" style="text-align: left">
                  <div style="margin: 3px 5px 6px; font-weight: 600; font-size: 14px; ">
                    E2E Delivery Execution Status  {{ pageCtl.conditions.selectedTreePath === '' ? '' : '[' + pageCtl.conditions.selectedTreePath + ']' }}
                  </div>
                </el-col>
                <el-col :span="13" style="text-align: right">
                  <el-input class="e2eInput" v-model="pageCtl.report2Input" style="width: 300px;"
                            placeholder="Input SO Num/Item, DN Num/Item, Material, or Plant Code to filter" clearable/>
                </el-col>
                <el-col :span="1" style="text-align: right">
                  <el-button @click="convertReport2Data" style="margin: 0 10px">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <div class="report2"
                   style="height: calc(100% - 60px); overflow-y: auto; font-size: small; padding: 0 10px 15px">
                <el-collapse v-model="pageCtl.conditions.report2ActiveNames" accordion>
                  <el-collapse-item v-for="item in pageCtl.report2DataConverted"
                                    :title="item.DN_NUMBER"
                                    :key="item.DN_NUMBER">
                    <template #title>
                      <div style="width: 100%; height: 100%">
                        <el-row>
                          <el-col :span="4" style="text-align: left">
                            {{ item.DN_NUMBER }}
                          </el-col>
                          <el-col :span="4" style="text-align: left">
                            {{ item.SO_NUMBER }}
                          </el-col>
                          <el-col :span="4" style="text-align: left">
                            {{ item.OPERATION_SCENARIO }}
                          </el-col>
                          <el-col :span="4" style="text-align: left">
                            {{ item.MATERIAL }}
                          </el-col>
                          <el-col :span="4" style="text-align: left">
                            {{ item.PLANT_CODE }}
                          </el-col>
                          <el-col :span="4" style="text-align: right">
                            <el-tag :type="report2ExecuteLabelColor(item.D0_EXECUTION_STATUS)" round effect="dark">
                              {{ item.D0_EXECUTION_STATUS }}
                            </el-tag>
                          </el-col>
                        </el-row>
                      </div>
                    </template>
                    <el-steps direction="horizontal"  :active="getActiveStepIndex(item)">
                      <el-step
                          v-for="(col, index) in report2Steps"
                          :key="`${item.DN_NUMBER}-${index}`"
                          :title="col.title"
                          :icon="col.icon"
                          :description="item[col.col]"
                          :status="getStepStatus(item, index)"
                      ></el-step>
                    </el-steps>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="E2E3" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report3ShowType"
                             size="small">
                    <el-option v-for="item in ['VIEW_BY_VALUE', 'VIEW_BY_PERCENT']"
                               :label="item"
                               :value="item"
                               :key="item"/>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report3DateType">
                    <el-option label="View By Day" value="VIEW_BY_DAY"/>
                    <el-option label="View By Week" value="VIEW_BY_WEEK"/>
                    <el-option label="View By Month" value="VIEW_BY_MONTH"/>
                    <el-option label="View By Quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View By Year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select
                      v-model="pageCtl.conditions.report3ViewType" size="small" filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select
                      v-model="pageCtl.conditions.report3ViewCalendar" size="small" filterable>
                    <el-option
                        v-for="item in ['SE Working Day', 'DC Working Day']"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-date-picker
                      style="width: calc(100% - 35px)"
                      v-model="pageCtl.conditions.report3DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                  ></el-date-picker>
                </el-col>
                <el-col :span="1">
                  <scp-search :click-native="searchReport3" :data="pageCtl.conditions"/>
                </el-col>
              </el-row>
              <chart ref="report3ChartRef" :height="350" :option="_report3Opt"/>
            </div>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <el-row class="search-box">
            <el-col :span="5">
              <el-date-picker
                  size="small"
                  v-model="pageCtl.conditions.report4DateRange"
                  type="daterange"
                  unlink-panels
                  range-separator="~"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  :clearable="false">
              </el-date-picker>
            </el-col>
            <el-col :span="3">
              <el-select v-model="pageCtl.conditions.reportViewType" size="small">
                <el-option label="View By Day" value="CALENDAR_DATE"/>
                <el-option label="View By Week" value="CALENDAR_WEEK"/>
                <el-option label="View By Month" value="CALENDAR_MONTH"/>
                <el-option label="View By Quarter" value="CALENDAR_QUARTER"/>
                <el-option label="View By Year" value="CALENDAR_YEAR"/>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="pageCtl.conditions.category" placeholder="Category" filterable clearable multiple
                         collapse-tags>
                <el-option
                    v-for="item in _pivotColumns"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="2">
              <scp-search ref="search4Ref" :click-native="searchReport4" :data="pageCtl.conditions" :expand="true"/>
            </el-col>
          </el-row>
          <scp-subscript id="ETED"/>
          <scp-table
              ref="tableRef"
              :columns="pageCtl.columns"
              :show-total="true"
              :params="pageCtl.conditions"
              :after-select="afterSelect"
              :fixedColumnsLeft="1"
              :context-menu-items="pageCtl.contextItems"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              :lazy="true"
              :showTotalIgnoreCols="pageCtl.conditions.category"
              url="/customer/end_to_end_delivery_tracking/query_report4"
              download-url="/customer/end_to_end_delivery_tracking/download_report4"
              :editable="false"/>
        </div>
      </div>
    </div>

    <!--  report1 contextmenu  -->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onActivated, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { SwitchFilled, Box, Sell, Timer, Printer, ShoppingCartFull } from '@element-plus/icons-vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $isEmpty: any = inject('$isEmpty')
const $toFixed: any = inject('$toFixed')
const $deepClone: any = inject('$deepClone')

const tableRef = ref()
const searchRef = ref()
const report1Ref = ref()
const report1SubRef = ref()
const report1ContextmenuRef = ref()
const report3ChartRef = ref()

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  search()
}
const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  search()
}

const viewDataDetails = () => {
  $viewDetails({
    url: '/customer/end_to_end_delivery_tracking/query_report4_details',
    durl: '/customer/end_to_end_delivery_tracking/download_report4_details',
    params: pageCtl.conditions,
    title: 'View Summary Details ' + (pageCtl.conditions.selectedValue.length ? ('[' + pageCtl.conditions.selectedValue.join(', ') + ']') : '')
  })
}

interface report2DataType {
  DN_NUMBER: string
  SO_NUMBER: string
  MATERIAL: string
  SO_CREATED_TIME: string
  DN_CREATE_TIME: string
  DN_SEND_TIME: string
  PLANT_CODE: string
  PICKING_STATUS: string
  PACKING_STATUS: string
  DN_STATUS: string
  D0_EXECUTION_STATUS: string,
  OPERATION_SCENARIO: string
}

const pageCtl = reactive({
  filterOpts: [],
  columns: [],
  loading: {
    report1: false,
    report2: false,
    report3: false,
    report5: false,
    filter: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    level1: 'PLANT_CODE',
    level2: 'ENTITY',
    level3: 'ENTITY',
    level4: 'ENTITY',
    level5: 'ENTITY',
    leafDepth: 1,
    report1Tooltips: [],
    category: ['DELIVERY_NUMBER', 'DELIVERY_ITEM', 'SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM', 'ENTITY', 'PLANT_CODE', 'OPERATION_SCENARIO', 'SO_PRIORITY', 'SHIP_TO_CITY'],
    selectedValue: [],
    report4DateRange: [],
    reportViewType: 'CALENDAR_DATE',
    selectedTreePath: '',
    totalColumns: [],
    report2ActiveNames: '1',
    report3ShowType: 'VIEW_BY_VALUE',
    report3DateType: 'VIEW_BY_DAY',
    report3ViewType: 'ONTIME_STATUS',
    report3ViewCalendar: 'SE Working Day',
    report3DateRange: [] as any,
    report3SeriesType: 'bar',
    report3SelectedXAxis: '',
    report3SelectedValue: ''
  },
  report1TooltipsOpts: ['Count'],
  report1Data: [],
  report2Input: '',
  report2Data: [] as Array<report2DataType>,
  report2DataConverted: [] as Array<report2DataType>,
  report3Data: [] as any,
  report5Data: {
    YESTERDAY_TOTAL_D0_LINES: 0,
    YESTERDAY_COMPLETED_D0_LINES: 0,
    YESTERDAY_D0_EXECUTION_RATIO: 0,
    TODAY_TOTAL_D0_LINES: 0,
    TODAY_TARGET_D0_LINES: 0,
    TODAY_TARGET_D0_RATIO: 0,
    TODAY_COMPLETED_D0_LINES: 0,
    TODAY_D0_EXECUTION_RATIO: 0
  },
  report5DetailsType: '',
  selectedCurrentLevel: '',
  selectedParentLevel: '',
  contextItems: {
    view_details: {
      name: 'View Details',
      callback: viewDataDetails
    },
    view_split0: { name: '---------' }
  }
})

const report2Steps = [
  {
    col: 'SO_CREATED_TIME',
    icon: ShoppingCartFull,
    title: 'SO Create'
  }, {
    col: 'DN_CREATE_TIME',
    icon: Printer,
    title: 'DN Create'
  }, {
    col: 'DN_SEND_TIME',
    icon: Timer,
    title: 'DN Send'
  }, {
    col: 'PICKING_STATUS',
    icon: Sell,
    title: 'DN Pick'
  }, {
    col: 'PACKING_STATUS',
    icon: Box,
    title: 'DN Pack'
  }, {
    col: 'DN_STATUS',
    icon: SwitchFilled,
    title: 'DN D+0 Finish'
  }
]

onMounted(() => {
  initPage()
  report3ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report3SeriesType = obj.currentType
    }
  })

  report3ChartRef.value.chart().on('dblclick', (obj) => {
    if (obj.componentType === 'series') {
      pageCtl.conditions.report3SelectedXAxis = obj.name
      pageCtl.conditions.report3SelectedValue = obj.seriesName
    } else {
      pageCtl.conditions.report3SelectedXAxis = obj.value
      pageCtl.conditions.report3SelectedValue = ''
    }

    $viewDetails({
      title: 'View Details [' + [pageCtl.conditions.report3SelectedXAxis, pageCtl.conditions.report3SelectedValue].join(', ') + ']',
      url: '/customer/end_to_end_delivery_tracking/query_report3_details',
      durl: '/customer/end_to_end_delivery_tracking/download_report3_details',
      params: pageCtl.conditions
    })
  })
})

onActivated(() => {
  if (report1Ref.value || report3ChartRef.value) {
    setTimeout(() => {
      report1Ref.value?.resize()
      report3ChartRef.value?.resize()
    }, 0)
  }
})

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 30)
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report4DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const report2ExecuteLabelColor = (status) => {
  switch (status) {
    case 'Not Started':
      return 'info'
    case 'PGI Completed':
      return 'success'
    case 'Miss COT':
      return 'warning'
    case 'Out of Service':
      return 'warning'
    default:
      return 'primary'
  }
}

const getActiveStepIndex = (item) => {
  const executionStatusToStepIndex = {
    'Picking Started': 3,
    'Picking Completed': 4,
    'Packing Started': 4,
    'Packing Completed': 5,
    'PGI Completed': 7,
    'Not Started': 2,
    'Out of Service': 2,
    'Miss COT': 2,
    'Task Actived': 2
  }
  return executionStatusToStepIndex[item.D0_EXECUTION_STATUS] || 0
}

const getStepStatus = (item, index) => {
  const activeIndex = getActiveStepIndex(item)
  if (index < activeIndex) {
    return 'success'
  } else if (index === activeIndex) {
    return 'finish'
  } else {
    return 'wait'
  }
}

const search = () => {
  if (pageCtl.conditions.category.length === 0) {
    pageCtl.conditions.category = ['ENTITY']
  }
  parseTableColumn()
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
  searchReport5()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
    pageCtl.report2DataConverted = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  parseTableColumn()
  tableRef.value.search()
}

const searchReport5 = () => {
  pageCtl.loading.report5 = true
  $axios({
    method: 'post',
    url: '/customer/end_to_end_delivery_tracking/query_report5',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report5Data = body || {} as any
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report5 = false
  })
}

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const _conditions = computed(() => {
  const cond = $deepClone(pageCtl.conditions)
  cond.report5DetailsType = pageCtl.report5DetailsType
  return cond
})

const viewReport5Details = (type) => {
  let title = 'View Details ['
  title += camelCaseStartPlaceholder(type)
  title += ']'
  pageCtl.report5DetailsType = type
  $viewDetails({
    url: '/customer/end_to_end_delivery_tracking/query_report5_details',
    durl: '/customer/end_to_end_delivery_tracking/download_report5_details',
    params: _conditions.value,
    title
  })
}

const afterSelect = (row) => {
  pageCtl.conditions.totalColumns = _selectedCategory.value.concat([pageCtl.conditions.reportViewType])
  if (row[pageCtl.conditions.totalColumns[0]] === 'Total') {
    pageCtl.conditions.selectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < pageCtl.conditions.totalColumns.length; i++) {
      selectedValue.push(row[pageCtl.conditions.totalColumns[i]])
    }
    pageCtl.conditions.selectedValue = selectedValue
  }
}

const parseTableColumn = () => {
  const columns = [] as any
  columns.push(
    { data: pageCtl.conditions.reportViewType }
  )
  for (let i = 0; i < _selectedCategory.value.length; i++) {
    columns.push({
      data: _selectedCategory.value[i],
      title: _selectedCategory.value[i]
    })
  }
  columns.push(
    { data: 'LINE_COUNT', title: 'DN Lines', type: 'numeric' }
  )
  pageCtl.columns = columns
}

const _selectedCategory = computed(() => {
  if (pageCtl.conditions.category.length > 0) {
    return pageCtl.conditions.category
  } else {
    return ['ENTITY', 'PLANT_CODE']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'ENTITY').concat(['DELIVERY_NUMBER', 'DELIVERY_ITEM', 'SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM']).sort()
})

const _report1Opt = computed(() => {
  const rootName = 'E2E Delivery'
  return {
    title: {
      text: 'E2E Delivery by Category',
      textStyle: {
        fontWeight: 600,
        fontSize: 14,
        fontFamily: 'Arial'
      }
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width: 11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)
          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)
            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const convertReport2Data = () => {
  pageCtl.loading.report2 = true
  setTimeout(() => {
    const t = pageCtl.report2Data
    if (pageCtl.report2Input.trim()) {
      pageCtl.report2DataConverted = t.filter(item =>
        item.DN_NUMBER.toLowerCase().includes(pageCtl.report2Input.toLowerCase()) ||
          item.SO_NUMBER.toLowerCase().includes(pageCtl.report2Input.toLowerCase()) ||
          item.MATERIAL.toLowerCase().includes(pageCtl.report2Input.toLowerCase()) ||
          item.PLANT_CODE.toLowerCase().includes(pageCtl.report2Input.toLowerCase()) ||
          item.OPERATION_SCENARIO.toLowerCase().includes(pageCtl.report2Input.toLowerCase())
      )
    } else {
      pageCtl.report2DataConverted = pageCtl.report2Data
    }
    pageCtl.loading.report2 = false
  }, 100)
}

const _report3Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  let yAxisData = pageCtl.report3Data
  const result = {}
  if (yAxisData.hasOwnProperty('xAxis')) {
    for (let i = 0; i < yAxisData.xAxis.length; i++) {
      let total = 0
      for (const key in yAxisData) {
        if (yAxisData.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
          total = yAxisData[key][i] + total
        }
      }
      result[yAxisData.xAxis[i]] = total
    }
  }

  legend.push('D0 Finished(%)')
  series.push({
    name: 'D0 Finished(%)',
    type: 'line',
    smooth: false,
    yAxisIndex: 1,
    data: pageCtl.report3Data.lineData,
    color: '#3dcd58',
    label: {
      show: true,
      fontSize: 10,
      formatter: (data) => {
        return data.data + '%'
      }
    }
  })

  // 转换数字
  // 转换数字为百分比
  if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report3Data.xAxis

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report3Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report3Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }

  const colorMap = {
    Fail: '#dc2e34',
    OnTime: '#2b8c1d',
    Ongoing: '#e6b600',
    Others: '#5470c6'
  }

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis' && key !== 'lineData') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report3SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || [],
        itemStyle: {
          color: colorMap[key]
        }
      })
    }
  }
  return {
    title: {
      text: 'E2E Delivery Tracking by ' + pageCtl.conditions.report3DateType.split('VIEW_BY_')[1] +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : ''),
      textStyle: {
        fontWeight: 600,
        fontSize: 14,
        fontFamily: 'Arial'
      }
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          if (params[i].seriesName !== 'D0 Finished(%)') {
            total += value
          }
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: legend,
      selected: {
        Others: false
      }
    }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report3SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: [{
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      max: 100,
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0) + '%'
        }
      }
    }],
    toolbox: $toolbox({
      opts: ['line', 'stack']
    }),
    series,
    visualMap: {
      seriesIndex: 0,
      show: false,
      pieces: [
        {
          gt: 0,
          lte: 95,
          color: '#f62707'
        },
        {
          gt: 95,
          color: '#3dcd58'
        }
      ]
    }
  }
})

</script>

<style lang="scss">
#e2e {
  .card-container {
    display: flex;
    gap: 5px; /* 设置每个卡片之间的间距为10px */
  }

  .box-card {
    width: calc(calc(100% - 15px - 1px) / 3);
    flex-shrink: 0;
    box-sizing: content-box;
  }

  .box-card2 {
    width: calc(calc(100% - 30px - 1px) / 5);
    flex-shrink: 0;
    box-sizing: content-box;
  }

  .report2 {
    overflow-y: auto;
  }

  .e2e-widget {
    .el-card {
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      border-radius: 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary) !important;
          margin: 0 !important;
        }
      }
    }
  }

  .el-collapse {
    .el-collapse-item__wrap {
      border-top: 1px solid var(--el-collapse-border-color);
    }

    .el-collapse-item__content {
      line-height: 15px;
      margin: 20px 40px;
      padding-bottom: 0px;
    }

    .el-step__title {
      font-size: 12px;
      line-height: 28px;
    }

    .el-step__description {
      font-size: 10px;
    }
  }
}

</style>
