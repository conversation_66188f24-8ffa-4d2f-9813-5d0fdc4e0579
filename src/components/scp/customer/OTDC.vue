<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <!--filter field-->
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.selectedField" size="small" placeholder="Filter Field" multiple collapse-tags filterable clearable>
              <el-option v-for="item in pageCtl.fieldOpts" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-col>
          <!--category-->
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="OTDC_DC_SOURCE_FILTER_V" :filter-base="['OTDC_DC_SOURCE_V']"
                                    :after-apply="search"/>
          </el-col>
          <!-- Date Range -->
          <el-col :span="4">
            <el-date-picker
              size="small"
              v-model="pageCtl.conditions.dateRange"
              type="monthrange"
              unlink-panels
              range-separator="~"
              format="YYYYMM"
              value-format="YYYYMM"
              start-placeholder="Start date"
              end-placeholder="End date"
              :picker-options="{
                  disabledDate(e) {
                    let time = e.getTime() - new Date().getTime()
                    return time > 63244800000 // 最多只能往前选2年，3600 * 24 * 1000 * (366 * 2) = 63244800000
                  }
                }"
              :clearable="false">
            </el-date-picker>
          </el-col>
          <el-col :span="2">
            <el-input-number
              v-model="pageCtl.conditions.warningValue"
              size="small"
              :precision="1"
              :step="0.1"
              :max="100"
              :min="40"
              style="width: var(--scp-input-width) !important;">
            </el-input-number>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: var(--scp-widget-margin)">
          <el-col :span="12">
            <div class="subscript-container subscript-container-left" style="min-height:456px" v-loading="pageCtl.loading.report1">
              <h3 style="text-align: left;">OTDC Official</h3>
              <scp-subscript id="COSR"/>
              <scp-table
                :contextMenuItems="pageCtl.report1ContextMenuItems"
                ref="report1Ref"
                :columns="_report1Columns"
                :enable-loading="false"
                :before-search="() => pageCtl.loading.report1 = true"
                :fixedColumnsLeft="_selectedField.length"
                :nested-headers="_report1NestedHeaders"
                :lazy="true"
                :params="pageCtl.conditions"
                :after-search="afterSearchReport1"
                :max-height="350"
                :afterSelect="afterReport1Select"
                :pagging-setting-enable="false"
                url="/customer/otdc/query_report1"/>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report1 || pageCtl.loading.report2">
              <scp-subscript id="COSB"/>
              <chart
                ref="monthlyChartRef"
                style="width:100%;height:456px"
                :autoresize="true"
                :option="_report2Opt"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="padding-left:2px">
            <div class="subscript-container" v-loading="pageCtl.loading.report3">
              <h3 style="text-align: left">OTDC Weekly (Simulated)</h3>
              <scp-subscript id="COWK"/>
              <scp-table
                :contextMenuItems="pageCtl.report3ContextMenuItems"
                ref="report3Ref"
                :enable-loading="false"
                :columns="_report3Columns"
                :fixedColumnsLeft="_selectedField.length"
                :lazy="true"
                :before-search="()=>{pageCtl.loading.report3 = true}"
                :after-search="()=>{pageCtl.loading.report3 = false}"
                :params="pageCtl.conditions"
                :tableStriped="false"
                :dropdownMenu="false"
                :row-headers="false"
                :pagging="false"
                :max-height="400"
                :afterSelect="afterReport3Select"
                :pagging-setting-enable="false"
                url="/customer/otdc/query_report3"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter:any = inject('$dateFormatter')
const $px2Rem:any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator:any = inject('$thousandBitSeparator')
const $join:any = inject('$join')
const $shortenNumber:any = inject('$shortenNumber')
const $distinctArray:any = inject('$distinctArray')
const $isEmpty:any = inject('$isEmpty')
const $startWith:any = inject('$startWith')
const $viewDetails:any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1Ref = ref()
const monthlyChartRef = ref()
const report3Ref = ref()

const searchReport1Details = () => {
  setSelectedValue() // 用户每次点击的数据记录在temp中, 只有真正触发查询的时候才会放入conditions
  let selectedTitle = pageCtl.conditions.report1SelectedFieldValue.join(', ')
  if (pageCtl.conditions.report1SelectedFieldValue.length > 0 && pageCtl.conditions.report1SelectedFieldValue[0] === 'Total') {
    selectedTitle = 'Total'
  }
  const title = [
    'View Monthly Details - ',
    selectedTitle,
    '  ',
    pageCtl.conditions.report1SelectedColumn === '-1' ? pageCtl.conditions.dateRange.join(' - ') : pageCtl.conditions.report1SelectedColumn,
    '  ',
    pageCtl.conditions.report1SelectedType === 'RATIO' ? '(On Time,Delay)' : pageCtl.conditions.report1SelectedType
  ]

  $viewDetails({
    url: '/customer/otdc/query_report1_details',
    durl: '/customer/otdc/download_report1_details',
    title: title.join(''),
    params: pageCtl.conditions
  })

  searchReport2()
}

const searchReport3Details = () => {
  $viewDetails({
    url: '/customer/otdc/query_report3_details',
    durl: '/customer/otdc/download_report3_details',
    title: pageCtl.report3DetailsTitle,
    params: pageCtl.conditions
  })
}

const searchReport2 = () => {
  setSelectedValue()
  pageCtl.loading.report2 = true
  if (pageCtl.conditions.report1SelectedFieldValue.length > 0 || pageCtl.conditions.report1SelectedFieldValue[0] === 'Total') {
    pageCtl.report2SubTitle = 'Total'
  } else {
    pageCtl.report2SubTitle = $join(...pageCtl.conditions.report1SelectedFieldValue)
  }

  pageCtl.report2DataRatio = getSelectedReport1Ratio()
  $axios({
    method: 'post',
    url: '/customer/otdc/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const pageCtl = reactive({
  timer: null as any,
  fieldOpts: [
    'BU',
    'CLUSTER_NAME',
    'ENTITY',
    'FORWARDER_NAME',
    'LOCAL_PRODUCT_FAMILY',
    'LOCAL_PRODUCT_SUBFAMILY',
    'LOCAL_PRODUCT_LINE',
    'MATERIAL',
    'MATERIAL_OWNER_NAME',
    'MATERIAL_OWNER_SESA',
    'MRP_CONTROLLER',
    'PLANT_CODE',
    'PLANT_NAME',
    'PRODUCT_LINE',
    'SALES_ORGANIZATION',
    'SHIPPING_POINT',
    'STOCK_INDENT'
  ],
  temp: {
    report1SelectedColumn: '',
    report1SelectedFieldValue: [],
    report1SelectedType: '',
    report2SelectedType: ''
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    dateRange: [] as any,
    selectedField: ['ENTITY'],
    selectedWeeks: [],
    selectedMonths: [],
    selectedYears: [],
    report1SelectedColumn: '',
    report1SelectedFieldValue: [],
    report1SelectedType: '',
    report2SelectedType: '',
    warningValue: 96.5
  },
  loading: {
    report1: false,
    report2: false,
    report3: false
  },
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: searchReport1Details
    },
    view_otdc_vs_otds: {
      name: 'Refresh chart',
      callback: searchReport2
    },
    view_split0: { name: '---------' }
  },
  report2Data: [],
  report2SubTitle: '',
  report2DataRatio: [],
  report3DetailsTitle: '',
  report3ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: searchReport3Details
    },
    view_split0: { name: '---------' }
  },
  // old var
  common: {
    stripedClass: '' /* 斑马纹 */
  }
})

watch(() => pageCtl.conditions.warningValue, () => {
  delayer(() => {
    report1Ref.value.redrawTable()
    report3Ref.value.redrawTable()
  })
})

onMounted(() => {
  initPage()

  monthlyChartRef.value.chart().on('dblclick', (params) => {
    if (params.seriesName.indexOf(',') === -1) {
      return
    }

    // 双击表格查询详细的时候并不需要清除已有的查询条件, 只需要对其中部分查询条件进行修改即可
    pageCtl.conditions.report1SelectedColumn = params.name
    pageCtl.conditions.report2SelectedType = params.seriesName

    $viewDetails({
      url: '/customer/otdc/query_report1_details',
      durl: '/customer/otdc/download_report1_details',
      title: 'View Details [' + params.name + ', ' + params.seriesName + ']',
      params: pageCtl.conditions
    })
  })
})

const initPage = () => {
  const start = new Date()
  const end = new Date()
  start.setMonth(start.getMonth() - 3)
  pageCtl.conditions.dateRange = [$dateFormatter(start, 'yyyyMM'), $dateFormatter(end, 'yyyyMM')]
  searchRef.value.loadAndClick()
}

const search = () => {
  clearTempValue()
  clearSelectedValue()
  pageCtl.loading.report1 = true
  pageCtl.loading.report2 = true
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/otdc/query_date_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.selectedYears = body.years || [$dateFormatter(new Date(), 'yyyy')]
    pageCtl.conditions.selectedMonths = body.months || [$dateFormatter(new Date(), 'yyyyMM')]
    pageCtl.conditions.selectedWeeks = body.weeks

    searchReport1()
    searchReport2()
    searchReport3()
  })
}

const searchReport1 = () => {
  report1Ref.value.search()
}

const afterSearchReport1 = () => {
  pageCtl.loading.report1 = false
  pageCtl.report2DataRatio = getSelectedReport1Ratio()
}

const afterReport1Select = (r, rc, c, column) => {
  clearTempValue() // 清空临时区, 准备放入查询数据
  if (column >= _selectedField.value.length) {
    pageCtl.temp.report1SelectedColumn = c
  } else {
    pageCtl.temp.report1SelectedColumn = '-1'
  }

  const selected = [] as any
  for (let i = 0; i < _selectedField.value.length; i++) {
    selected.push(r[_selectedField.value[i]])
  }

  pageCtl.temp.report1SelectedFieldValue = selected
  pageCtl.temp.report1SelectedType = r.TYPE
}

const searchReport3 = () => {
  report3Ref.value.search()
}

// 表格添加斑马纹
const renderTableStriped = (hotInstance, td, row, column, prop, value) => {
  let stripedClass
  if (row % 3 === 0) {
    stripedClass = 'striped-even3-tr'
  } else {
    stripedClass = 'striped-odd3-tr'
  }

  if (td.parentNode) {
    td.parentNode.className = stripedClass
  }
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 'Total' && column !== _selectedField.value.length - 1) {
      td.innerHTML = ''
    } else {
      td.innerHTML = value || ''
    }
  } else {
    if (column === _selectedField.value.length - 1) {
      td.innerHTML = r.TYPE || ''
    } else {
      td.innerHTML = ''
    }

    td.style.textAlign = 'right'
    if (r.TYPE === 'Delay') {
      td.style.color = 'var(--scp-text-color-error)'
    } else if (r.TYPE === 'On Time') {
      td.style.color = 'var(--scp-text-color-success)'
      td.style.fontWeight = '700'
    }
  }
}

const getSelectedReport1Ratio = () => {
  let data = []
  if (report1Ref.value) {
    data = report1Ref.value.getData()
  }
  const es = data.filter(
    (e:any) => {
      if (pageCtl.conditions.report1SelectedFieldValue.length === 0) {
        return e[_selectedField.value[_selectedField.value.length - 1]] === 'Total' && e.TYPE === 'RATIO'
      } else {
        let match = true
        for (let i = 0; i < pageCtl.conditions.report1SelectedFieldValue.length; i++) {
          if (e[_selectedField.value[i]] !== pageCtl.conditions.report1SelectedFieldValue[i] || e.TYPE !== 'RATIO') {
            match = false
          }
        }
        return match
      }
    }
  )
  let e = {}
  if (es && es.length > 0) {
    e = es[0]
  }
  const yAxis = [] as any
  const months = pageCtl.conditions.selectedMonths
  for (let i = 0; i < months.length; ++i) {
    let v = e[months[i]] as any
    if ($isEmpty(v) === false) {
      v = (v * 100).toFixed(1)
      yAxis.push(v)
    }
  }
  return yAxis
}

// 为ratio添加百分号
const renderRatio = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      if (value * 100 < pageCtl.conditions.warningValue) {
        td.style = 'color: var(--scp-text-color-error) !important'
      }
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }

    const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
    if (r && r.TYPE === 'Delay' && $startWith(prop, 'W')) {
      if (value === 0) {
        const style = 'background-image: linear-gradient(to right, #2c821d 100%, white 100%) !important;color: #fff !important;'
        td.title = '100%'
        td.style = style
      } else if (value) {
        const finish = r[prop + '_CONFIRMED'] || 0
        const percent = finish / value * 100
        const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
        const t = Math.min(percent + 2, 100)

        let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, white ' + t + '%) !important;'

        td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

        const tdWidth = td.offsetWidth // 单元格宽度
        const textWidth = $px2Rem((td.innerHTML + '').length * 8 + 4) // 文字宽度 = 文字个数 * 每个字符宽度8px + 左边距4px
        if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
          style += 'color: #fff !important;'
        }
        td.style = style
      }
    }
  }
}

const clearTempValue = () => {
  pageCtl.temp.report1SelectedType = ''
  pageCtl.temp.report1SelectedFieldValue = []
  pageCtl.temp.report1SelectedColumn = ''
  pageCtl.temp.report2SelectedType = ''
}

const clearSelectedValue = () => {
  pageCtl.conditions.report1SelectedType = ''
  pageCtl.conditions.report1SelectedFieldValue = []
  pageCtl.conditions.report1SelectedColumn = ''
  pageCtl.conditions.report2SelectedType = ''
}

const setSelectedValue = () => {
  pageCtl.conditions.report1SelectedType = pageCtl.temp.report1SelectedType
  pageCtl.conditions.report1SelectedFieldValue = pageCtl.temp.report1SelectedFieldValue
  pageCtl.conditions.report1SelectedColumn = pageCtl.temp.report1SelectedColumn
  pageCtl.conditions.report2SelectedType = pageCtl.temp.report2SelectedType
}

const delayer = (action, delay = 600) => {
  if (pageCtl.timer) {
    clearTimeout(pageCtl.timer)
  }
  pageCtl.timer = setTimeout(() => {
    action.call()
  }, delay)
}

const afterReport3Select = (r, rc, c, column) => {
  setSelectedValue()
  if (column > 0) {
    pageCtl.conditions.report1SelectedColumn = c
  } else {
    pageCtl.conditions.report1SelectedColumn = '-1'
  }

  const selected = [] as any
  for (let i = 0; i < _selectedField.value.length; i++) {
    selected.push(r[_selectedField.value[i]])
  }

  pageCtl.conditions.report1SelectedFieldValue = selected
  pageCtl.conditions.report1SelectedType = r.TYPE
  const selectedTile = selected.join(', ')
  const title = [
    'View Weekly Details - ',
    selectedTile,
    '  ',
    pageCtl.conditions.report1SelectedColumn === '-1' ? pageCtl.conditions.dateRange.join(' - ') : pageCtl.conditions.report1SelectedColumn,
    '  ',
    r.TYPE === 'RATIO' ? '(On Time,Delay)' : r.TYPE
  ]
  pageCtl.report3DetailsTitle = title.join('')
}

const _selectedField = computed(() => {
  if (pageCtl.conditions.selectedField.length > 0) {
    return pageCtl.conditions.selectedField
  } else {
    return ['ENTITY']
  }
})

const _report1Columns = computed(() => {
  const columns = [] as any
  for (let i = 0; i < _selectedField.value.length; ++i) {
    const name = _selectedField.value[i]
    columns.push({
      title: name,
      data: name,
      render: renderTableStriped
    })
  }
  for (let i = 0; i < pageCtl.conditions.selectedYears.length; ++i) {
    columns.push({
      title: pageCtl.conditions.selectedYears[i],
      data: pageCtl.conditions.selectedYears[i],
      render: renderRatio
    })
  }
  for (let i = 0; i < pageCtl.conditions.selectedMonths.length; ++i) {
    columns.push({
      title: pageCtl.conditions.selectedMonths[i],
      data: pageCtl.conditions.selectedMonths[i],
      render: renderRatio
    })
  }
  return columns
})

const _report1NestedHeaders = computed(() => {
  return [
    [
      {
        label: '',
        colspan: _selectedField.value.length
      },
      {
        label: 'YTD',
        colspan: pageCtl.conditions.selectedYears.length
      },
      {
        label: 'Monthly',
        colspan: pageCtl.conditions.selectedMonths.length
      }
    ]
  ]
})

const _report2Opt = computed(() => {
  const xAxis = $distinctArray(pageCtl.report2Data.map((e:any) => {
    return e.OTDC_MONTH
  }))
  return {
    title: {
      text: 'OTDC vs OTDS' + (pageCtl.report2SubTitle ? ' - ' + pageCtl.report2SubTitle : ''),
      top: 10
    },
    toolbox: $toolbox(),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params.seriesName)
        tip.push('<br>')
        tip.push(params.marker)
        tip.push($shortenNumber(params.data))
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: [
        'OTDC Failed, OTDS Failed',
        'OTDC Failed, OTDS Ontime',
        'OTDC Ontime, OTDS Failed',
        'OTDC Ontime, OTDS Ontime',
        'On-time Ratio'
      ]
    }),
    grid: $grid(),
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      },
      {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        axisTick: {
          show: false
        }
      }
    ],
    xAxis: {
      type: 'category',
      data: xAxis
    },
    series: [
      {
        name: 'OTDC Failed, OTDS Failed',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: pageCtl.report2Data.filter((e:any) => e.ONTIME_CATEGORY === 'OTDC Failed, OTDS Failed').map((e:any) => e.LINES)
      },
      {
        name: 'OTDC Failed, OTDS Ontime',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: pageCtl.report2Data.filter((e:any) => e.ONTIME_CATEGORY === 'OTDC Failed, OTDS Ontime').map((e:any) => e.LINES)
      },
      {
        name: 'OTDC Ontime, OTDS Failed',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: pageCtl.report2Data.filter((e:any) => e.ONTIME_CATEGORY === 'OTDC Ontime, OTDS Failed').map((e:any) => e.LINES)
      },
      {
        name: 'OTDC Ontime, OTDS Ontime',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: pageCtl.report2Data.filter((e:any) => e.ONTIME_CATEGORY === 'OTDC Ontime, OTDS Ontime').map((e:any) => e.LINES)
      },
      {
        name: 'On-time Ratio',
        type: 'line',
        label: {
          show: true,
          formatter: '{c}%'
        },
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report2DataRatio
      }
    ]
  }
})

const _report3Columns = computed(() => {
  const columns = [] as any
  for (let i = 0; i < _selectedField.value.length; i++) {
    const name = _selectedField.value[i]
    columns.push({
      title: name,
      data: name,
      render: renderTableStriped
    })
  }

  for (let i = 0; i < pageCtl.conditions.selectedWeeks.length; ++i) {
    columns.push({
      title: pageCtl.conditions.selectedWeeks[i],
      data: pageCtl.conditions.selectedWeeks[i],
      render: renderRatio
    })
  }
  return columns
})

</script>
