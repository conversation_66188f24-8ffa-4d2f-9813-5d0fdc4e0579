<template>
  <div class="left-sidebar" id="openPONOR">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['OPEN_PO_NOR_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.reportCalType" size="small" filterable collapse-tags>
              <el-option v-for="item in pageCtl.reportCalTypeOpts"
                         :key="item.label"
                         :label="item.label"
                         :value="item.value"/>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option
                  v-for="item in ['Line', 'Quantity', 'Value']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['report2DeliveryDateRange', 'report2DetailsDate', 'report3DateRange', 'report4DateRange', 'report3DetailsDate']"/>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="OPN1" ref="report1SubscriptRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." clearable
                                 filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." clearable
                                 filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report1TooltipsOpts"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></el-checkbox>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button @click="report1SubscriptRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="report1SubscriptRef.toggleView();searchReport1()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="14" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="OPN2" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Filter For Open PO {{ _norLabel }}</div>
                  <el-row>
                    <el-col :span="6">
                      <div class="selectedLeft">XAixs</div>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="pageCtl.conditions.report2ShowType">
                        <el-option label="View By Value" value="VIEW_BY_VALUE"/>
                        <el-option label="View By Percentage" value="VIEW_BY_PERCENT"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">
                      <div class="selectedLeft">YAixs</div>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="pageCtl.conditions.report2SelectedType">
                        <el-option label="View By Day" value="VIEW_BY_DAY"/>
                        <el-option label="View By Week" value="VIEW_BY_WEEK"/>
                        <el-option label="View By Month" value="VIEW_BY_MONTH"/>
                        <el-option label="View By Quarter" value="VIEW_BY_QUARTER"/>
                        <el-option label="View By Year" value="VIEW_BY_YEAR"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">
                      <div class="selectedLeft">Stack By</div>
                    </el-col>
                    <el-col :span="12">
                      <el-select
                          v-model="pageCtl.conditions.report2ViewType" filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">
                      <div class="selectedLeft">Delivery Date Range</div>
                    </el-col>
                    <el-col :span="12">
                      <el-date-picker
                          size="small"
                          v-model="pageCtl.conditions.report2DeliveryDateRange"
                          type="daterange"
                          unlink-panels
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{}">
                      </el-date-picker>
                    </el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button @click="report2SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button type="primary" @click="report2SubRef.toggleView();searchReport2()">
                      Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="OPN3"/>
              <el-row class="search-box" style="margin-bottom: var(--scp-widget-margin)">
                <el-col :span="4">
                  <el-select
                      v-model="pageCtl.conditions.report3ViewType" size="small"
                      style="width: var(--scp-input-width) !important;" filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select
                      v-model="pageCtl.conditions.report3SelectedType"
                      style="width: var(--scp-input-width) !important;"
                      size="small">
                    <el-option label="View by day" value="VIEW_BY_DAY"/>
                    <el-option label="View by week" value="VIEW_BY_WEEK"/>
                    <el-option label="View by month" value="VIEW_BY_MONTH"/>
                    <el-option label="View by quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View by year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.report3DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}">
                  </el-date-picker>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ShowType" size="small">
                    <el-option label="View by value" value="VIEW_BY_VALUE"/>
                    <el-option label="View by percentage" value="VIEW_BY_PERCENT"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report3Ref" :height="360" :option="_report3Opt" v-loading="pageCtl.loading.report3"/>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.report4">
              <el-row class="search-box">
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report4ViewColumns" size="small" placeholder="category"
                             filterable clearable multiple collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.report4DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}">
                  </el-date-picker>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport4">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-subscript id="OPN4"/>
              <scp-table
                  ref="report4Ref"
                  url="/customer/open_po_nor/query_report4"
                  download-url="/customer/open_po_nor/download_report4"
                  :params="pageCtl.conditions"
                  :max-height="375"
                  :after-select="afterReport4Selected"
                  :lazy="true"
                  :show-total="true"
                  :pagging="false"
                  :filters="false"
                  :editable="false"
                  :context-menu-items="pageCtl.contextItems.report4"
                  :columns="pageCtl.report4Columns"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $join: any = inject('$join')

const searchRef = ref()
const report1ContextmenuRef = ref()
const report1SubscriptRef = ref()
const report2ChartRef = ref()
const report2SubRef = ref()
const report3Ref = ref()
const report4Ref = ref()

const viewReport4Details = () => {
  $viewDetails({
    title: pageCtl.report4DetailsTitle,
    url: '/customer/open_po_nor/query_report4_details',
    durl: '/customer/open_po_nor/download_report4_details',
    params: pageCtl.conditions
  })
}
const viewReport4MoDetails = () => {
  $viewDetails({
    title: pageCtl.report4DetailsMoTitle,
    url: '/customer/open_po_nor/query_report4_mo_details',
    durl: '/customer/open_po_nor/download_report4_mo_details',
    params: pageCtl.conditions
  })
}

const pageCtl = reactive({
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  filterOpts: [],
  report1TooltipsOpts: ['AVG_SELLING_PRICE_RMB'],
  conditions: {
    $scpFilter: {
      cascader: [] as any,
      filter: []
    },
    resultType: 'Value',
    leafDepth: 1,
    level1: 'NORO_FLAG',
    level2: 'PLANT_CODE',
    level3: 'PLANT_CODE',
    level4: 'PLANT_CODE',
    level5: 'PLANT_CODE',
    report1Tooltips: [],
    selectedTreePath: '',
    selectedLegend: {},
    reportCalType: 'NORO_FLAG',
    report2ShowType: 'VIEW_BY_VALUE',
    report2DeliveryDateRange: [] as any,
    report2SelectedType: 'VIEW_BY_WEEK',
    report2ViewType: 'NORO_FLAG',
    report2DetailsDate: '',
    report2DetailsValue: '',
    report2SeriesType: 'line',
    report3SeriesType: 'line',
    report3DateRange: [] as any,
    report4DateRange: [] as any,
    report3SelectedType: 'VIEW_BY_DAY',
    report3ShowType: 'VIEW_BY_VALUE',
    report3ViewType: 'ENTITY',
    report3DetailsDate: '',
    report3DetailsValue: '',
    report4ViewColumns: ['ENTITY'],
    report4SelectedValue: [],
    report4SelectedColumn: '',
    report4FixedColumn: {
      RC_GE_11: 'ROC>=11',
      RC_GE_6_LE_10: 'ROC=6~10',
      RC_GE_4_LE_5: 'ROC=4~5',
      RC_EQ_3: 'ROC=3',
      RC_EQ_2: 'ROC=2',
      RC_EQ_1: 'ROC=1',
      RC_EQ_0: 'ROC=0'
    }
  },
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false,
    report4: false
  },
  reportCalTypeOpts: [
    { label: 'NOR', value: 'NOR_FLAG' },
    { label: 'NORO', value: 'NORO_FLAG' }
  ],
  report1Data: [],
  report2Data: [] as any,
  report3Data: {} as any,
  report2DetailsType: '' as any,
  contextItems: {
    report4: {
      view_details: {
        name: 'View Details',
        callback: viewReport4Details
      },
      view_production: {
        name: 'View Production(MO/Vendor)',
        callback: viewReport4MoDetails
      },
      view_split0: { name: '---------' }
    }
  },
  report4DetailsTitle: '',
  report4DetailsMoTitle: '',
  report4Columns: []
})

onMounted(() => {
  initPage()

  report2ChartRef.value.chart().on('dblclick', (params) => {
    if (params.componentType === 'series') {
      pageCtl.conditions.report2DetailsDate = params.name
      pageCtl.conditions.report2DetailsValue = params.seriesName
    } else {
      pageCtl.conditions.report2DetailsDate = params.value
      pageCtl.conditions.report2DetailsValue = ''
    }
    $viewDetails({
      url: '/customer/open_po_nor/query_report2_details',
      durl: '/customer/open_po_nor/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + pageCtl.conditions.report2DetailsDate + (pageCtl.conditions.report2DetailsValue === '' ? '' : ' - '.concat(pageCtl.conditions.report2DetailsValue)) + ']'
    })
  })
  report2ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report2SeriesType = obj.currentType
    }
  })

  report3Ref.value.chart().on('dblclick', (params) => {
    if (params.componentType === 'series') {
      pageCtl.conditions.report3DetailsDate = params.name
      pageCtl.conditions.report3DetailsValue = params.seriesName
    } else {
      pageCtl.conditions.report3DetailsDate = params.value
      pageCtl.conditions.report3DetailsValue = ''
    }
    $viewDetails({
      url: '/customer/open_po_nor/query_report3_details',
      durl: '/customer/open_po_nor/download_report3_details',
      params: pageCtl.conditions,
      title: 'View Details [' + pageCtl.conditions.report3DetailsDate + (pageCtl.conditions.report3DetailsValue === '' ? '' : ' - '.concat(pageCtl.conditions.report3DetailsValue)) + ']'
    })
  })
  report3Ref.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report3SeriesType = obj.currentType
    }
  })
})

watch(() => pageCtl.conditions.reportCalType, () => {
  pageCtl.conditions.level1 = pageCtl.conditions.reportCalType
  pageCtl.conditions.report2ViewType = pageCtl.conditions.reportCalType
  pageCtl.conditions.report3ViewType = pageCtl.conditions.reportCalType
  search()
})

const initPage = () => {
  const now = new Date()

  // index（0代表1月，1代表2月，依此类推）
  const threeMonthBefore = new Date()
  const currentMonthIndex = now.getMonth()
  let threeMonthBeforeIndex = currentMonthIndex - 3

  // 如果结果小于0，需要回退到前一年，并调整月份
  if (threeMonthBeforeIndex < 0) {
    threeMonthBeforeIndex += 12
    threeMonthBefore.setFullYear(now.getFullYear() - 1)
  }

  threeMonthBefore.setMonth(threeMonthBeforeIndex)
  threeMonthBefore.setDate(now.getDate())

  pageCtl.conditions.report3DateRange = [
    $dateFormatter(threeMonthBefore, 'yyyy/MM/dd'),
    $dateFormatter(now, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report4DateRange = [
    $dateFormatter(threeMonthBefore, 'yyyy/MM/dd'),
    $dateFormatter(now, 'yyyy/MM/dd')
  ]

  pageCtl.conditions.report2DeliveryDateRange = [
    $dateFormatter(threeMonthBefore, 'yyyy/MM/dd'),
    $dateFormatter(now, 'yyyy/MM/dd')
  ]
  $axios({
    method: 'post',
    url: '/customer/open_po_nor/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.CASCADER
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/open_po_nor/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/open_po_nor/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/open_po_nor/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  pageCtl.conditions.report4SelectedValue = []
  report4Column()
  report4Ref.value.search()
}

const report4Column = () => {
  const columns = [] as any
  for (let i = 0; i < _report4SelectedColumns.value.length; i++) {
    columns.push({
      data: _report4SelectedColumns.value[i],
      title: _report4SelectedColumns.value[i]
    })
  }

  if (pageCtl.conditions.reportCalType === 'NOR_FLAG') {
    columns.push(
      { data: 'RC_EQ_0', title: 'RC=0', type: 'numeric' },
      { data: 'RC_EQ_1', title: 'RC=1', type: 'numeric' },
      { data: 'RC_EQ_2', title: 'RC=2', type: 'numeric' },
      { data: 'RC_EQ_3', title: 'RC=3', type: 'numeric' },
      { data: 'RC_GE_4_LE_5', title: 'RC=4~5', type: 'numeric' },
      { data: 'RC_GE_6_LE_10', title: 'RC=6~10', type: 'numeric' },
      { data: 'RC_GE_11', title: 'RC>=11', type: 'numeric' },
      { data: 'RC_AVERAGE', title: 'Avg of RC', type: 'numeric' },
      { data: 'RC_TOTAL', title: 'Sum of RC', type: 'numeric' },
      { data: 'RC_LINES', title: 'Total Lines of RC', type: 'numeric' }
    )
  } else {
    columns.push(
      { data: 'RC_EQ_0', title: 'ROC=0', type: 'numeric' },
      { data: 'RC_EQ_1', title: 'ROC=1', type: 'numeric' },
      { data: 'RC_EQ_2', title: 'ROC=2', type: 'numeric' },
      { data: 'RC_EQ_3', title: 'ROC=3', type: 'numeric' },
      { data: 'RC_GE_4_LE_5', title: 'ROC=4~5', type: 'numeric' },
      { data: 'RC_GE_6_LE_10', title: 'ROC=6~10', type: 'numeric' },
      { data: 'RC_GE_11', title: 'ROC>=11', type: 'numeric' },
      { data: 'RC_AVERAGE', title: 'Avg of ROC', type: 'numeric' },
      { data: 'RC_TOTAL', title: 'Sum of ROC', type: 'numeric' },
      { data: 'RC_LINES', title: 'Total Lines of ROC', type: 'numeric' }
    )
  }

  pageCtl.report4Columns = columns
}

const afterReport4Selected = (r, c, a) => {
  pageCtl.conditions.report4SelectedColumn = a
  if (r[_report4SelectedColumns.value[0]] === 'Total') {
    pageCtl.conditions.report4SelectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _report4SelectedColumns.value.length; i++) {
      selectedValue.push(r[_report4SelectedColumns.value[i]])
    }
    pageCtl.conditions.report4SelectedValue = selectedValue
  }

  let selectColumnName = ''
  for (let i = 0; i < pageCtl.report4Columns.length; i++) {
    const obj: any = pageCtl.report4Columns[i]
    if (a === obj.data && (a.indexOf('RC_EQ') === 0 || a.indexOf('RC_GE') === 0)) {
      selectColumnName = obj.title
    }
  }
  const title = $join(...pageCtl.conditions.report4SelectedValue, selectColumnName)
  if (title) {
    pageCtl.report4DetailsTitle = 'View Details [' + title + ']'
  } else {
    if (pageCtl.conditions.report4SelectedValue.length > 0) {
      pageCtl.report4DetailsTitle = 'View Details [' + $join(...pageCtl.conditions.report4SelectedValue) + ']'
    } else {
      pageCtl.report4DetailsTitle = 'View Details [Total]'
    }
  }
  const titlemo = $join(...pageCtl.conditions.report4SelectedValue, selectColumnName)
  if (titlemo) {
    pageCtl.report4DetailsMoTitle = 'View MO Details [' + title + ']'
  } else {
    if (pageCtl.conditions.report4SelectedValue.length > 0) {
      pageCtl.report4DetailsMoTitle = 'View MO Details [' + $join(...pageCtl.conditions.report4SelectedValue) + ']'
    } else {
      pageCtl.report4DetailsMoTitle = 'View MO Details [Total]'
    }
  }
}

const _norLabel = computed(() => {
  if (pageCtl.conditions.reportCalType === 'NOR_FLAG') {
    return 'NOR'
  } else {
    return 'NORO'
  }
})

const _report1Opt = computed(() => {
  return {
    title: {
      text: 'Open PO ' + _norLabel.value + ' by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)

          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)

            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === 'SO' ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: _norLabel.value,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  const series: any = []
  const legend: any = []
  let yAxisData = pageCtl.report2Data
  if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {} as any
    const stackTotal = {}
    const xAxis = pageCtl.report2Data.xAxis
    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report2Data[key][i]
        }
      }
    }
    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report2Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report2SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  const titleMap = {
    VIEW_BY_DAY: 'Date',
    VIEW_BY_WEEK: 'Week',
    VIEW_BY_MONTH: 'Month',
    VIEW_BY_QUARTER: 'Quarter',
    VIEW_BY_YEAR: 'Year'
  }
  return {
    title: {
      text: 'Evolution of Open PO ' + _norLabel.value + ' by ' + titleMap[pageCtl.conditions.report2SelectedType] || 'Date' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report2SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report2Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    legend: $legend({
      data: legend
    }),
    toolbox: $toolbox({
      opts: ['line', 'stack', 'no-details']
    }),
    series
  }
})

const _report3Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const colorMap = {}

  let yAxisData = pageCtl.report3Data

  // 转换数字为百分比
  if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report3Data.xAxis

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report3Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report3Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report3SeriesType,
        smooth: false,
        stack: 'sum',
        itemStyle: {
          color: colorMap[key]
        },
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }

  let title = 'Evolution of Open PO ' + _norLabel.value + ' by Date'
  title += (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')

  return {
    title: {
      text: title
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report3SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const _report4SelectedColumns = computed(() => {
  if (pageCtl.conditions.report4ViewColumns.length > 0) {
    return pageCtl.conditions.report4ViewColumns
  } else {
    return ['ENTITY']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
</script>

<style lang="scss">
#openPONOR {
  .selectedLeft {
    display: flex;
    justify-content: left;
    align-items: center;
    margin: 0 0 10px 10px;
    height: 100%;
  }
}
</style>
