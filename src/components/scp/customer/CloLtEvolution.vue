<template>
  <div class="left-sidebar" style="width: 100%">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.filterOpts"/>
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="pageCtl.conditions.specialType"
                size="small"
                style="width: 100% !important;"
                class="search-group-left">
              <el-option v-for="item in ['MATERIAL', 'MATERIAL_DESCRIPTION']"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input
                :placeholder="pageCtl.conditions.specialType"
                v-model="pageCtl.conditions.specialContent"
                size="small"
                style="width: var(--scp-input-width) !important;"
                type="textarea"
                class="search-group-right"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option label="Avg Lines" value="AVG_LINES"/>
              <el-option label="Avg Value" value="AVG_VALUE"/>
              <el-option label="Material" value="MATERIAL"/>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="COCT" ref="report1SubRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :key="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :value="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :label="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :label="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option v-for="item in [1, 2]"
                                   :label="item"
                                   :key="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Date</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          size="small"
                          v-model="pageCtl.conditions.report1PickerDate"
                          type="date"
                          unlink-panels
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox v-for="item in pageCtl.report1TooltipsOpts"
                                 :value="item"
                                 :label="item"
                                 :key="item"/>
                  </el-checkbox-group>

                  <div class="box-footer">
                    <el-button
                        @click="report1SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report1SubRef.toggleView();searchReport1()">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="14" v-loading="pageCtl.loading.report4 || pageCtl.loading.downloadReport4">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="COCD" ref="report4SubRef"/>
              <div class="front">
                <chart ref="report4ChartRef" :height="350" :option="_report4Opt"/>
                <el-dialog
                    align-center
                    v-model="pageCtl.report4DownloadDialogVisible"
                    width="40%"
                    :show-close="false">
                  <el-row class="selected">
                    <el-col :span="8">
                      <div>Select Scope</div>
                    </el-col>
                    <el-col :span="16">
                      <el-select v-model="pageCtl.conditions.report4DownloadType">
                        <el-option value="ALL_RESULT" label="All Result"/>
                        <el-option value="CURRENT_VIEW_RESULT" label="Current Page"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <template #title>
                    <div style="text-align: center; font-size: 0.583rem; font-weight: bold;">Download Scope</div>
                  </template>
                  <template #footer>
                    <span style="right: 0;">
                      <el-button @click="pageCtl.report4DownloadDialogVisible = false">Cancel</el-button>
                      <el-button type="primary"
                                 @click="downloadReport4(); pageCtl.report4DownloadDialogVisible = false">
                        Download
                      </el-button>
                    </span>
                  </template>
                </el-dialog>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Filter For {{ pageCtl.conditions.report4SortColumn }}</div>
                  <el-row>
                    <el-col :span="6">
                      <div>Sorting</div>
                    </el-col>
                    <el-col :span="16">
                      <el-select v-model="pageCtl.conditions.report4SortColumn">
                        <el-option value="AVG_LINES"/>
                        <el-option value="AVG_VALUE"/>
                        <el-option value="CLO_LT"/>
                        <el-option value="PLANNED_DELIV_TIME"/>
                        <el-option value="PICK_PACK_TIME"/>
                        <el-option value="GR_PROCESSING_TIME"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">
                      <div>Dimension</div>
                    </el-col>
                    <el-col :span="16">
                      <el-select v-model="pageCtl.conditions.report4SelectedLegends" multiple collapse-tags>
                        <el-option v-for="item in pageCtl.conditions.report4LegendsOpts"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">
                      <div>TOP Range</div>
                    </el-col>
                    <el-col :span="16">
                      <el-select v-model="pageCtl.conditions.report4TopQuantities" filterable>
                        <el-option v-for="item in 500"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">
                      <div>Date</div>
                    </el-col>
                    <el-col :span="18">
                      <el-date-picker
                          size="small"
                          v-model="pageCtl.conditions.report4PickerDate"
                          type="date"
                          unlink-panels
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date">
                      </el-date-picker>
                    </el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button
                        @click="report4SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report4SubRef.toggleView();searchReport4()">
                      Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report2">
            <div class="subscript-container">
              <scp-subscript id="COCM" ref="report2SubRef"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report2ShowType"
                             size="small">
                    <el-option v-for="item in ['VIEW_BY_VALUE', 'VIEW_BY_PERCENT']"
                               :label="item"
                               :value="item"
                               :key="item"/>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report2DateType">
                    <el-option label="View By Day" value="VIEW_BY_DAY"/>
                    <el-option label="View By Week" value="VIEW_BY_WEEK"/>
                    <el-option label="View By Month" value="VIEW_BY_MONTH"/>
                    <el-option label="View By Quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View By Year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select
                      v-model="pageCtl.conditions.report2ViewType" size="small" filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-date-picker
                      style="width: calc(100% - 35px)"
                      v-model="pageCtl.conditions.report2DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                  ></el-date-picker>
                </el-col>
                <el-col :span="3" v-show="pageCtl.report2PercentageBar">
                  <el-input-number v-model="pageCtl.conditions.report2CalRange" style="width: var(--scp-input-width)"
                                   size="small"
                                   :precision="1" :step="0.1" :max="100" :min="0"/>
                </el-col>
                <el-col :span="1">
                  <scp-search :click-native="searchReport2" :data="pageCtl.conditions"
                              :data-exclude="['report1PickerDate', 'report2DateRange','report3DateRange','report4PickerDate' ]"/>
                </el-col>
              </el-row>
              <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="COCS" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3SelectedColumns" size="small" placeholder="Columns"
                             multiple collapse-tags
                             clearable filterable>
                    <el-option v-for="item in _pivotColumns.concat(['MATERIAL']).sort()"
                               :label="item"
                               :key="item"
                               :value="item"/>
                  </el-select>
                </el-col>
                <el-col :span="3" v-show="pageCtl.conditions.report3SelectedColumns.includes('MATERIAL')">
                  <el-tooltip effect="light" placement="top">
                    <el-select v-model="pageCtl.conditions.report3TopQuantities">
                      <el-option v-for="item in 500"
                                 :label="item"
                                 :value="item"
                                 :key="item"
                      />
                    </el-select>
                    <template #content>
                      Display Quantities of material
                    </template>
                  </el-tooltip>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3DateType">
                    <el-option label="View By Day" value="VIEW_BY_DAY"/>
                    <el-option label="View By Week" value="VIEW_BY_WEEK"/>
                    <el-option label="View By Month" value="VIEW_BY_MONTH"/>
                    <el-option label="View By Quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View By Year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ResultType" size="small">
                    <el-option
                        v-for="item in ['AVG_LINES', 'AVG_VALUE', 'CLO_LT', 'MATERIAL', 'PLANNED_DELIV_TIME', 'PICK_PACK_TIME', 'GR_PROCESSING_TIME']"
                        :value="item"
                        :label="item"
                        :key="item"/>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-date-picker
                      style="width: calc(100% - 35px)"
                      v-model="pageCtl.conditions.report3DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                  ></el-date-picker>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  url="/customer/clo_lt_evolution/query_report3"
                  download-url="/customer/clo_lt_evolution/download_report3"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :show-total="true"
                  :pagging="false"
                  :fixed-columns-left="_report3SelectedColumns.length"
                  :max-height="375"
                  :context-menu-items="pageCtl.report3ContextItems"
                  :after-select="afterReport3Select"
                  ref="report3TableRef"
                  :columns="pageCtl.report3Columns"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--  report1 contextmenu  -->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $downloadFile: any = inject('$downloadFile')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1SubRef = ref()
const report1ContextmenuRef = ref()
const report2SubRef = ref()
const report2ChartRef = ref()
const report3SubRef = ref()
const report3TableRef = ref()
const report4SubRef = ref()
const report4ChartRef = ref()

const viewReport3Details = () => {
  let title = $join(
    pageCtl.conditions.report3SelectedDate,
    ...pageCtl.conditions.report3SelectedValues
  )
  if (title) {
    title = ' [' + title + ']'
  }

  $viewDetails({
    title: 'View Details' + title,
    url: '/customer/clo_lt_evolution/query_report3_details',
    durl: '/customer/clo_lt_evolution/download_report3_details',
    params: pageCtl.conditions
  })
}

const pageCtl = reactive({
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    downloadReport4: false
  },
  conditions: {
    filterList: [],
    level1: 'CLO_RANGE',
    level2: 'ENTITY',
    level3: 'STORAGE_LOCATION',
    level4: 'PRODUCT_LINE',
    level5: 'LT_RANGE',
    leafDepth: 1,
    selectedTreePath: '',
    specialType: 'MATERIAL',
    specialContent: '',
    resultType: 'AVG_LINES',
    report1Tooltips: [],
    report1PickerDate: '',
    report2CalRange: 25,
    report2ShowType: 'VIEW_BY_VALUE',
    report2ViewType: 'CLO_RANGE',
    report2DateRange: [] as any,
    report2SeriesType: 'bar',
    report2SelectedXAxis: '',
    report2SelectedValue: '',
    report2DateType: 'VIEW_BY_MONTH',
    report3DateType: 'VIEW_BY_MONTH',
    report3SelectedColumns: ['TRIGGER_TYPE', 'CLO_RANGE'],
    report3SelectedValues: [],
    report3SelectedDate: '',
    report3ValueType: 'Value',
    report3DateRange: [] as any,
    report3ColumnNames: [],
    report3ResultType: 'Avg Value',
    report4SelectedMaterial: '',
    report4ValueType: 'Value',
    report4ColumnNames: [],
    report4Data: [] as any,
    report4ValueColumn: 'CLO_LT',
    report4SelectedValue: '',
    report4SortColumn: 'CLO_LT',
    report4PickerDate: '',
    report3TopQuantities: 50,
    report4TopQuantities: 50,
    report4DownloadType: 'CURRENT_VIEW_RESULT',
    report4LegendsOpts: [],
    report4SelectedLegends: ['PICK_PACK_TIME', 'GR_PROCESSING_TIME', 'PLANNED_DELIV_TIME'],
    report4Level: 1,
    report4LevelTitle: '',
    report4Exclude: 0,
    report4IconDisplay: true
  },
  filterOpts: [],
  selectedCurrentLevel: '',
  selectedParentLevel: '',
  report1Data: [],
  report1TooltipsOpts: ['AVG_VALUE'],
  report2Data: [] as any,
  report2PercentageBar: false,
  report3ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3Columns: [],
  report4DownloadDialogVisible: false
})

onMounted(() => {
  initFilter()
  report2ChartRef.value.chart().on('dblclick', (obj) => {
    if (obj.componentType === 'series') {
      pageCtl.conditions.report2SelectedXAxis = obj.name
      pageCtl.conditions.report2SelectedValue = obj.seriesName
    } else {
      pageCtl.conditions.report2SelectedXAxis = obj.value
      pageCtl.conditions.report2SelectedValue = ''
    }

    $viewDetails({
      title: 'View Details [' + [pageCtl.conditions.report2SelectedXAxis, pageCtl.conditions.report2SelectedValue].join(', ') + ']',
      url: '/customer/clo_lt_evolution/query_report2_details',
      durl: '/customer/clo_lt_evolution/download_report2_details',
      params: pageCtl.conditions
    })
  })
  report2ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report2SeriesType = obj.currentType
    }
  })
  report4ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report4SelectedValue = obj.name === undefined ? obj.value : obj.name

    $viewDetails({
      title: pageCtl.conditions.report4SelectedValue === undefined ? 'View Details' : 'View Details [' + pageCtl.conditions.report4SelectedValue.replaceAll('\\t', '\t') + ']',
      url: '/customer/clo_lt_evolution/query_report4_details',
      durl: '/customer/clo_lt_evolution/download_report4_details',
      params: pageCtl.conditions
    })
  })
  report4ChartRef.value.chart().on('click', (obj) => {
    if (obj.seriesName === 'PLANNED_DELIV_TIME') {
      pageCtl.conditions.report4IconDisplay = true
      pageCtl.conditions.report4LevelTitle = ' > Planned Deliv Time'
      pageCtl.conditions.report4Level = 2
      pageCtl.conditions.report4SelectedLegends = ['TRANSPORTATION_ZMGC', 'T_REPLENISHMENT_LT']
    } else if (obj.seriesName === 'T_REPLENISHMENT_LT') {
      pageCtl.conditions.report4IconDisplay = true
      pageCtl.conditions.report4Level = 3
      pageCtl.conditions.report4LevelTitle = ' > Planned Deliv Time > T Replenishment LT'
      pageCtl.conditions.report4SelectedLegends = ['PICK_PACK_TIME', 'PLANT_GR_PROCESSING_TIME', 'PLANT_IN_HOUSE_PRODN_LT']
    }
  })
})

watch(() => pageCtl.conditions.resultType, () => {
  pageCtl.conditions.report3ResultType = pageCtl.conditions.resultType
})

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const initFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), 1)
  const start = new Date(now.getFullYear(), now.getMonth() - 11, 1)
  pageCtl.conditions.report1PickerDate = $dateFormatter(now, 'yyyy/MM/dd')
  pageCtl.conditions.report4PickerDate = $dateFormatter(now, 'yyyy/MM/dd')
  pageCtl.conditions.report2DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/clo_lt_evolution/query_filters'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/clo_lt_evolution/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/clo_lt_evolution/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/clo_lt_evolution/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.clearAndSearch()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}
const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/customer/clo_lt_evolution/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report4Data = body
    pageCtl.conditions.report4LegendsOpts = body.legend.sort()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }
  const selected = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    let v = r[_report3SelectedColumns.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report3SelectedValues = selected
}
const parseReport3Columns = () => {
  const result = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    result.push({
      data: _report3SelectedColumns.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (pageCtl.conditions.report3ValueType === 'Value') {
          td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
        } else {
          td.innerHTML = value ? (value * 100).toFixed(2) + '%' : '0'
        }
      }
    })
  }
  return result
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
}
const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
}

const _report1Opt = computed(() => {
  const rootName = 'CLO LT'
  return {
    title: {
      text: 'CLO LT Evolution by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width: 11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)
          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)
            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  let yAxisData = pageCtl.report2Data
  const result = {}
  if (yAxisData.hasOwnProperty('xAxis')) {
    for (let i = 0; i < yAxisData.xAxis.length; i++) {
      let total = 0
      for (const key in yAxisData) {
        if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
          total = yAxisData[key][i] + total
        }
      }
      result[yAxisData.xAxis[i]] = total
    }
  }
  const colorMap = {
    '2.8_MTO_>1Y': '#ff0000',
    '2.7_MTO_6-12M': '#ff1e1e',
    '2.6_MTO_3-6M': '#ff3c3c',
    '2.5_MTO_2-3M': '#ff5a5a',
    '2.4_MTO_1-2M': '#ff7878',
    '2.3_MTO_2-4W': '#ff9696',
    '2.2_MTO_1-2W': '#ffb4b4',
    '2.1_MTO_<1W': '#ffd2d2',
    '1.8_MTS_>6M': '#ccebcc',
    '1.7_MTS_3-6M': '#cde0cd',
    '1.6_MTS_2-3M': '#d2e3d2',
    '1.5_MTS_1-2M': '#bce2bc',
    '1.4_MTS_2-4W': '#a7dca7',
    '1.3_MTS_1-2W': '#92d792',
    '1.2_MTS_<1W': '#7ccf7c',
    '1.1_MTS_D+1': '#009900',
    Others: '#006400'
  }

  // 转换数字
  // 转换数字为百分比
  if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report2Data.xAxis

    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report2Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report2Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report2SeriesType,
        smooth: false,
        stack: 'sum',
        itemStyle: {
          color: colorMap[key]
        },
        areaStyle: {},
        data: yAxisData[key] || [],
        label: {
          show: pageCtl.report2PercentageBar,
          position: 'inside', // outside 外部显示  inside 内部显示
          formatter: (item) => {
            if (pageCtl.conditions.report2ShowType === 'VIEW_BY_VALUE' && (item.data / result[item.name] * 100) > pageCtl.conditions.report2CalRange) {
              return (item.data / result[item.name] * 100).toFixed(0) + '%'
            } else if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT' && item.data > pageCtl.conditions.report2CalRange) {
              return item.data.toFixed(0) + '%'
            } else {
              return ''
            }
          },
          color: '#fff', // 颜色
          fontSize: 12 // 字体大小
        }
      })
    }
  }
  return {
    title: {
      text: 'Evolution of CLO LT by ' + pageCtl.conditions.report2DateType.split('VIEW_BY_')[1] +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report2SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report2Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({
      opts: ['line', 'stack'],
      custom: {
        myTool1: {
          show: true,
          title: 'Show Percentage',
          // 阿里图标库直接使用symbol引入自动生成路径 ，看path路径
          icon: 'path://M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667zM490.666667 490.666667h-149.482667A21.269333 21.269333 0 0 0 320 512c0 11.861333 9.493333 21.333333 21.184 21.333333H490.666667v149.482667c0 11.690667 9.557333 21.184 21.333333 21.184 11.861333 0 21.333333-9.493333 21.333333-21.184V533.333333h149.482667A21.269333 21.269333 0 0 0 704 512c0-11.861333-9.493333-21.333333-21.184-21.333333H533.333333v-149.482667A21.269333 21.269333 0 0 0 512 320c-11.861333 0-21.333333 9.493333-21.333333 21.184V490.666667z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            pageCtl.report2PercentageBar = pageCtl.report2PercentageBar !== true
          }
        }
      }
    }),
    series
  }
})

const _report3SelectedColumns = computed(() => {
  if (pageCtl.conditions.report3SelectedColumns.length > 0) {
    return pageCtl.conditions.report3SelectedColumns
  } else {
    return ['BU']
  }
})

const reloadData = (dataList, errorLabel) => {
  const resultData: any = {}
  for (const key in dataList) {
    resultData[key] = []
  }
  for (let i = 0; i < dataList.xAxis.length; i++) {
    if ([errorLabel, 3].includes(dataList.ERROR_LABEL[i])) {
      for (const key in dataList) {
        if (key !== 'legend') {
          resultData[key].push(dataList[key][i])
        }
      }
    }
  }
  resultData.legend = dataList.legend
  return resultData
}

const _report4Opt = computed(() => {
  const series = [] as any
  let yAxisData = pageCtl.conditions.report4Data
  let dataLength = 0
  if (pageCtl.conditions.report4Data.hasOwnProperty('xAxis')) {
    dataLength = pageCtl.conditions.report4Data.xAxis.length
  }
  if (JSON.stringify(yAxisData) !== '[]' && pageCtl.conditions.report4Exclude !== 0) {
    yAxisData = reloadData(yAxisData, pageCtl.conditions.report4Exclude)
  }

  // Level1 && Level2判断其子项是否相等，不相等标为红色
  const colorLabel: any = []
  if (JSON.stringify(yAxisData) !== '[]') {
    for (let i = 0; i < yAxisData.xAxis.length; i++) {
      if ([1, 2].includes(pageCtl.conditions.report4Level) && [pageCtl.conditions.report4Level, 3].includes(yAxisData.ERROR_LABEL[i])) {
        colorLabel.push(1)
      } else {
        colorLabel.push(0)
      }
    }
  }

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis' && key !== 'errorLabel' && pageCtl.conditions.report4SelectedLegends.indexOf(key) !== -1) {
      series.push({
        name: key,
        type: 'bar',
        itemStyle: {
          normal: {
            color: function (params) {
              if ((pageCtl.conditions.report4Level === 1 && key === 'PLANNED_DELIV_TIME' && colorLabel[params.dataIndex] === 1) || ((pageCtl.conditions.report4Level === 2 && key === 'T_REPLENISHMENT_LT') && colorLabel[params.dataIndex] === 1)) {
                return '#ee6666'
              } else {
                return params.color
              }
            }
          }
        },
        stack: 'total',
        smooth: false,
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  let report4Title: any = ''
  if (pageCtl.conditions.report4Exclude === 1) {
    report4Title = 'CLO Drill Down' + pageCtl.conditions.report4LevelTitle + ' - Exclude<L1>'
  } else if (pageCtl.conditions.report4Exclude === 2) {
    report4Title = 'CLO Drill Down' + pageCtl.conditions.report4LevelTitle + ' - Exclude<L2>'
  } else if (pageCtl.conditions.report4Exclude === 3) {
    report4Title = 'CLO Drill Down' + pageCtl.conditions.report4LevelTitle + ' - Exclude<L1, L2>'
  } else {
    report4Title = 'CLO Drill Down' + pageCtl.conditions.report4LevelTitle
  }
  return {
    title: {
      text: report4Title,
      triggerEvent: true
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        const a = params[0].name.split('\\t')
        let str = a[0] + '\t' + a[1] + '\t' + a[2]
        if (pageCtl.conditions.report4Level === 2) {
          str = a[0] + '\t' + a[1] + '\t' + a[2] + '\t' + a[3]
        }
        tip.push(str)
        tip.push('<br>')
        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }
          const value = p[i].value || 0
          if ($shortenNumber(value) !== 0) {
            total += value
            tip.push('<div style="width:9.5rem;">')
            const marker = p[i].marker
            tip.push(marker)
            tip.push(p[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(value))
            tip.push('</span></div>')
          }
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: pageCtl.conditions.report4SelectedLegends }),
    grid: $grid(),
    yAxis: [{
      position: 'left',
      type: 'category',
      data: yAxisData.xAxis || [],
      axisLabel: {
        width: 50,
        overflow: 'truncate',
        formatter: (value) => {
          return value.split('\\t')[0]
        },
        // interval: 0, // 控制x坐标轴是否显示全
        textStyle: {
          fontSize: 10
        }
      },
      align: 'left',
      triggerEvent: true
    }, {
      position: 'inside',
      type: 'category',
      data: yAxisData.xAxis || [],
      axisLabel: {
        inside: true,
        width: 50,
        formatter: (value) => {
          const num = pageCtl.conditions.report4TopQuantities > dataLength ? dataLength : pageCtl.conditions.report4TopQuantities
          return num - pageCtl.conditions.report4Data.xAxis.indexOf(value)
        },
        textStyle: {
          fontSize: 10
        }
      },
      axisTick: {
        inside: true
      },
      align: 'left',
      triggerEvent: true
    }],
    xAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      },
      triggerEvent: true
    },
    toolbox: $toolbox({
      opts: ['line', 'stack'],
      custom: {
        myTool4: {
          show: pageCtl.conditions.report4Level > 1,
          title: 'Return',
          // 阿里图标库直接使用symbol引入自动生成路径 ，看path路径
          icon: 'path://M588.468659 257.265591H123.316451L371.227243 58.55359a31.947267 31.947267 0 1 0-39.614611-49.837737l-319.472671 255.578137v11.501016a30.669376 30.669376 0 0 0 0 4.472617v3.194727a30.669376 30.669376 0 0 0 0 4.472617v11.501016l319.472671 255.578137a31.947267 31.947267 0 1 0 40.253556-49.837737L123.316451 321.160125h465.152208C792.292223 321.160125 958.418011 464.283881 958.418011 640.632795s-166.125789 319.47267-369.949352 319.472671H95.841801a31.947267 31.947267 0 0 0 0 63.894534h492.626858C830.628943 1024 1022.312545 852.123703 1022.312545 640.632795s-191.683602-383.367205-433.843886-383.367204z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            pageCtl.conditions.report4Level = pageCtl.conditions.report4Level - 1
            if (pageCtl.conditions.report4Level === 2) {
              pageCtl.conditions.report4LevelTitle = pageCtl.conditions.report4LevelTitle.split(' > T Replenishment LT')[0]
              pageCtl.conditions.report4SelectedLegends = ['TRANSPORTATION_ZMGC', 'T_REPLENISHMENT_LT']
              if (pageCtl.conditions.report4Exclude === 2 || pageCtl.conditions.report4Exclude === 3) {
                pageCtl.conditions.report4IconDisplay = false
              }
            } else if (pageCtl.conditions.report4Level === 1) {
              pageCtl.conditions.report4LevelTitle = ''
              pageCtl.conditions.report4SelectedLegends = ['PICK_PACK_TIME', 'GR_PROCESSING_TIME', 'PLANNED_DELIV_TIME']
              if (pageCtl.conditions.report4Exclude === 1 || pageCtl.conditions.report4Exclude === 3) {
                pageCtl.conditions.report4IconDisplay = false
              }
            }
          }
        },
        myTool3: {
          show: true,
          title: 'Exclude Normal',
          // 阿里图标库直接使用symbol引入自动生成路径 ，看path路径
          icon: pageCtl.conditions.report4IconDisplay ? 'path://M909.888 843.584H114.112A50.112 50.112 0 0 1 64 793.472V178.112C64 150.464 86.4 128 114.112 128h795.776c27.648 0 50.112 22.4 50.112 50.112v615.36c0 27.712-22.4 50.112-50.112 50.112z m-728.64 56.32h661.504a30.08 30.08 0 1 1 0 60.096H181.248a30.08 30.08 0 1 1 0-60.16zM611.2 584.96l155.904-155.904a30.08 30.08 0 1 0-42.496-42.56L589.952 521.216 455.296 386.56a30.08 30.08 0 0 0-42.496 0L256.896 542.528a30.08 30.08 0 0 0 42.496 42.496l134.656-134.656L568.704 584.96a30.08 30.08 0 0 0 42.496 0z' : 'M909.888 843.584H114.112A50.112 50.112 0 0 1 64 793.472V178.112C64 150.464 86.4 128 114.112 128h795.776c27.648 0 50.112 22.4 50.112 50.112v615.36c0 27.712-22.4 50.112-50.112 50.112z m-10.048-635.392a20.032 20.032 0 0 0-20.032-20.032H144.192a20.032 20.032 0 0 0-20.032 20.032v555.2c0 11.072 8.96 20.096 20.032 20.096h735.616c11.072 0 20.032-8.96 20.032-20.096V208.192zM611.2 584.96a30.08 30.08 0 0 1-42.496 0L434.048 450.368 299.392 584.96a30.08 30.08 0 0 1-42.496-42.496L412.8 386.56a30.08 30.08 0 0 1 42.496 0l134.656 134.656 134.656-134.656a30.08 30.08 0 1 1 42.496 42.56L611.2 584.96z m-429.952 314.88h661.504a30.08 30.08 0 1 1 0 60.096H181.248a30.08 30.08 0 1 1 0-60.16z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.2,
            borderType: 'solid'
          },
          color: 'red',
          onclick: () => {
            switch (pageCtl.conditions.report4Level) {
              case 1:
                if (pageCtl.conditions.report4Exclude === 0) {
                  pageCtl.conditions.report4Exclude = 1
                  pageCtl.conditions.report4IconDisplay = false
                } else {
                  pageCtl.conditions.report4Exclude = 0
                  pageCtl.conditions.report4IconDisplay = true
                }
                break
              case 2:
                if (pageCtl.conditions.report4Exclude === 2) {
                  pageCtl.conditions.report4Exclude = 0
                  pageCtl.conditions.report4IconDisplay = true
                } else if (pageCtl.conditions.report4Exclude === 3) {
                  pageCtl.conditions.report4Exclude = 1
                  pageCtl.conditions.report4IconDisplay = true
                } else if (pageCtl.conditions.report4Exclude === 0) {
                  pageCtl.conditions.report4Exclude = 2
                  pageCtl.conditions.report4IconDisplay = false
                } else if (pageCtl.conditions.report4Exclude === 1) {
                  pageCtl.conditions.report4Exclude = 3
                  pageCtl.conditions.report4IconDisplay = false
                }
                break
            }
          }
        }
      }
    }),
    dataZoom: [
      {
        type: 'slider',
        show: true,
        yAxisIndex: [0, 1],
        showDetail: false,
        startValue: dataLength,
        endValue: dataLength - 10,
        handleSize: '10%',
        width: 20,
        scrollStep: 10
      },
      {
        type: 'inside',
        yAxisIndex: [0, 1],
        start: 29,
        end: 36,
        zoomOnMouseWheel: false,
        moveOnMouseWheel: true
      }
    ],
    series
  }
}
)

const downloadReport4 = () => {
  pageCtl.loading.downloadReport4 = true
  $downloadFile('/customer/clo_lt_evolution/download_report4', pageCtl.conditions, () => {
    pageCtl.loading.downloadReport4 = false
  })
}
const showReport4DownloadDialog = () => {
  pageCtl.report4DownloadDialogVisible = true
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>

<style scoped>
:deep(.el-dialog) {
  border-radius: 0 !important;
}

:deep(.el-dialog__footer) {
  padding-top: 0 !important;
}
</style>
