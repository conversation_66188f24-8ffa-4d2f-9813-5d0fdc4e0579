<template>
  <div class="left-sidebar" id="mycp" style="width: 100%">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.pageType"
                       class="search-group-left">
              <el-option value="MyCP by Response"></el-option>
              <el-option value="MyCP by Commitment"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['MYCP_RESPONSE_V', 'MYCP_COMMITMENT_V']" :after-apply="search"/>
          </el-col>
          <el-col :span="3">
            <el-select
                style="width: 100% !important;"
                class="search-group-left"
                v-model="pageCtl.conditions.filterDateColumn"
                placeholder="Select Date Column"
                size="small">
              <el-option
                  v-for="item in pageCtl.filterDateColumns"
                  :key="item"
                  :value="item"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                range-separator="~"
                format="YYYY/MM/DD"
                :disabled-date="disabledDate"
                value-format="YYYY/MM/DD"
                start-placeholder="Start date"
                end-placeholder="End date"
                :clearable="false">
            </el-date-picker>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.reportViewType" size="small">
              <el-option
                  v-for="item in ['VIEW_BY_DAY', 'VIEW_BY_WEEK', 'VIEW_BY_MONTH', 'VIEW_BY_QUARTER', 'VIEW_BY_YEAR']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['dateRange', 'report2SelectedDate']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report1">
              <scp-subscript id="MCR1" ref="report1Ref"/>
              <el-table
                  :data="pageCtl.report1Data"
                  style="width: 100%"
                  height="350"
                  row-key="id"
                  lazy
                  :load="loadReport1"
                  :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                  @sort-change="report1SortChange"
                  class="tree-table" @contextmenu.prevent="">
                <el-table-column prop="category" label="Category" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'category')">
                  <template #header>
                    <el-select v-model="pageCtl.conditions.report1Categories" size="small"
                               style="border:0 !important;width:calc(100% - 40px)"
                               @click.stop="(e)=>e.preventDefault()"
                               placeholder="Pivot Columns"
                               collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="7" filterable
                               :loading="pageCtl.loading.filter">
                      <el-option
                          v-for="item in pageCtl.pivotOpts"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </template>
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="1000" :content="scope.row.category"
                                placement="bottom-end">
                        <span v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                              @dblclick="report1ViewTrends"
                              @contextmenu.prevent="report1RightClick2">
                          {{ scope.row.category }}
                        </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="failRatio" label="Fail(%)" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'failRatio')" width="100"
                                 class-name="onTime-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="500" placement="top-end">
                      <template #content>
                        fail Ratio: {{ $thousandBitSeparator(scope.row.failRatio, 1) }}{{
                          scope.row.failRatio ? '%' : ''
                        }}
                      </template>
                      <span v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                            @dblclick="report1ViewTrends"
                            @contextmenu.prevent="report1RightClick2">
                          {{ $thousandBitSeparator(scope.row.failRatio, 1) }}{{ scope.row.failRatio ? '%' : '' }}
                        </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="fail" label="Fail Line" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'fail')"
                                 width="200" class-name="fail-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="1500" placement="top-end">
                      <template #content>
                        MyCP Failed: {{ $thousandBitSeparator(scope.row.fail, 1) }}
                        ({{ $toFixed(scope.row.fail * 100 / (scope.row.onTime + scope.row.fail), 1) }}%)
                      </template>
                      <div v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                           @dblclick="report1ViewTrends"
                           @contextmenu.prevent="report1RightClick2">
                        <div style="width: 200px;display: flex;justify-content: right;align-items:center;"
                             v-if="scope.row.category !== 'Total'">
                          <!-- Fail显示文本 -->
                          <div style="font-size: 80%;cursor: pointer;" :style="{color: pageCtl.color.fail}"
                               @click="switchReport1DisplayMode">
                            {{ report1DislayValue(scope.row, 'fail') }}
                          </div>

                          <div style="height:19px;margin-left: 2px"
                               :style="{width: scope.row.failWidth + '%', backgroundColor: pageCtl.color.fail}">
                            &nbsp;
                          </div>
                        </div>
                        <div v-else style="text-align: right;cursor: pointer" :style="{color: pageCtl.color.fail}"
                             @click="switchReport1DisplayMode">
                          {{ report1DislayValue(scope.row, 'fail') }}
                        </div>
                      </div>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="onTime" label="OnTime Line" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'onTime')"
                                 width="200" class-name="onTime-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="1500" placement="top-end">
                      <template #content>
                        MyCP Ontime: {{ $thousandBitSeparator(scope.row.onTime, 1) }}
                        ({{ $toFixed(scope.row.onTime * 100 / (scope.row.onTime + scope.row.fail), 1) }}%)
                      </template>
                      <div v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                           @dblclick="report1ViewTrends"
                           @contextmenu.prevent="report1RightClick2">
                        <div style="width: 200px;display: flex;justify-content: left;align-items:center;"
                             v-if="scope.row.category !== 'Total'">
                          <div style="height:19px;;margin-right: 2px"
                               :style="{width: scope.row.onTimeWidth + '%', backgroundColor: pageCtl.color.onTime}">
                            &nbsp;
                          </div>
                          <div style="font-size: 80%;cursor: pointer" :style="{color: pageCtl.color.onTime}"
                               @click="switchReport1DisplayMode">
                            {{ report1DislayValue(scope.row, 'onTime') }}
                          </div>
                        </div>
                        <div v-else style="text-align: left;cursor: pointer" :style="{color: pageCtl.color.onTime}"
                             @click="switchReport1DisplayMode">
                          {{ report1DislayValue(scope.row, 'onTime') }}
                        </div>
                      </div>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column prop="onTimeRatio" label="OnTime(%)" sortable
                                 :sort-method="(a, b) => report1Sort(a, b, 'onTimeRatio')" width="100"
                                 class-name="onTime-header">
                  <template v-slot="scope">
                    <el-tooltip effect="light" :show-after="500" placement="top-end">
                      <template #content>
                        OnTime Ratio: {{ $thousandBitSeparator(scope.row.onTimeRatio, 1) }}{{
                          scope.row.onTimeRatio ? '%' : ''
                        }}
                      </template>
                      <span v-contextmenu:contextmenu1Ref :data-value="JSON.stringify(scope.row)"
                            @dblclick="report1ViewTrends"
                            @contextmenu.prevent="report1RightClick2">
                          {{ $thousandBitSeparator(scope.row.onTimeRatio, 1) }}{{ scope.row.onTimeRatio ? '%' : '' }}
                        </span>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col :span="12" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="MYR2" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Filter Settings</div>
                  <el-row>
                    <el-col :span="6">Stack By</el-col>
                    <el-col :span="18">
                      <el-select
                          v-model="pageCtl.conditions.report2ViewType" size="small" filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                  <div class="box-footer">
                    <el-button
                        @click="report2SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report2SubRef.toggleView();searchReport2()">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.fields" size="small" placeholder="Filter Field" filterable
                             multiple collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-subscript id="MCR3"/>
              <scp-table
                  :contextMenuItems="pageCtl.report3MenuItems"
                  ref="report3TableRef"
                  :columns="_report3Columns['columns']"
                  :pagging="false"
                  :pagging-setting-enable="false"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :max-height="456"
                  :after-select="afterReport3Select"
                  url="/customer/mycp/query_report3"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="contextmenu1Ref">
      <v-contextmenu-item @click="report1ViewDetails">
        View Details
      </v-contextmenu-item>
    </v-contextmenu>

    <!-- report3 view details-->
    <scp-draggable-resizable w="60vw" h="420px" v-model="pageCtl.report3DetailsVisible"
                             :title="pageCtl.report3DetailsTitle">
      <template v-slot="{ height }">
        <scp-table ref="report3DetailsTableRef"
                   url="/customer/mycp/query_report3_details"
                   download-url="/customer/mycp/download_report3_details"
                   :lazy="true"
                   :editable="false"
                   :columns="_report3DetailsColumn"
                   :params="_conditions"
                   :maxHeight="height - 150">
        </scp-table>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { useRouter } from 'vue-router'

const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $toolbox: any = inject('$echarts.toolbox')
const $toFixed: any = inject('$toFixed')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $axios: any = inject('$axios')
const $join: any = inject('$join')

const searchRef = ref()
const contextmenu1Ref = ref()

const report1Ref = ref()
const report2SubRef = ref()
const report2ChartRef = ref()
const report3TableRef = ref()
const report3DetailsTableRef = ref()

const viewReport3Details = () => {
  const title = [] as any
  for (let i = 0; i < pageCtl.report3SelectedValues.length; i++) {
    if (_fields.value.indexOf(pageCtl.report3SelectedValues[i]) === -1) {
      title.push(pageCtl.report3SelectedValues[i])
    }
  }
  pageCtl.report3DetailsTitle = 'View Details [' + $join(...title) + ']'
  pageCtl.report3DetailsVisible = true
  nextTick(() => {
    report3DetailsTableRef.value.search()
  })
}

const pageCtl = reactive({
  color: {
    onTime: '#61a3f1',
    fail: '#fc6464'
  },
  pivotOpts: [],
  filterOpts: [],
  rcaDesc: {},
  loading: {
    filter: false,
    report1: false,
    report2: false
  },
  filterDateColumns: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    filterDateColumn: 'APPLICATION_DATE',
    reportViewType: 'VIEW_BY_WEEK',
    dateColumn: 'CALENDAR_WEEK',
    pageType: 'MyCP by Response',
    report1DisplayMode: 'value',
    report1Categories: ['ENTITY', 'MATERIAL_OWNER_NAME', 'BU', 'VENDOR_NAME', 'STOCKING_POLICY'],
    report1SelectedValues: '',
    warningValue: {
      Fail: 95
    },
    fields: ['ENTITY'],
    report2ViewType: 'MYCP_ONTIME_STATUS',
    report3DateColumns: [],
    dateRange: [] as any
  },
  report1Data: [],
  report1RightClickData: '',
  report1ColumnStartName: '',
  report2Data: {
    xAxis: [],
    yAxis: {},
    lineData: []
  },
  report2yAxisMax: 100,
  report2SelectedDate: '',
  report3DetailsVisible: false,
  report3SelectedValues: [] as any,
  report3MenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3DetailsTitle: ''
})

onMounted(() => {
  const router = useRouter()
  const url = router.currentRoute.value.fullPath
  const parts = url.match(/mycp\?(?!cid)[^?&]*/)
  if (parts && parts[1]) {
    // 对URL进行解码
    const decodedArray = decodeURIComponent(parts[1])
    try {
      pageCtl.conditions.$scpFilter.cascader = JSON.parse(decodedArray)
    } catch (error) {
      console.error('Error parsing JSON:', error)
    }
  }

  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getFullYear(), 0, 1)
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  initPage()
  searchRef.value.loadAndClick()

  report2ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.report2SelectedDate = params.name

    $viewDetails({
      title: 'View Details [' + $join(params.seriesName, params.name) + ']',
      url: '/customer/mycp/query_report2_details',
      durl: '/customer/mycp/download_report2_details',
      params: _conditions.value
    })
  })
})

watch(() => pageCtl.conditions.pageType, () => {
  initPage()
  if (pageCtl.conditions.pageType === 'MyCP by Response') {
    pageCtl.conditions.filterDateColumn = 'APPLICATION_DATE'
  } else {
    pageCtl.conditions.filterDateColumn = 'PURCHASE_EXPECTED_DATE'
  }
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/customer/mycp/init_page',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.rcaDesc = body.rcaDesc
    pageCtl.filterDateColumns = body.filterDateColumns
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
  })
}

const disabledDate = (time: Date) => {
  return (time <= new Date('2023-05-31'))
}

const search = () => {
  if (pageCtl.conditions.pageType === 'MyCP by Response') {
    pageCtl.report1ColumnStartName = 'Response'
  } else {
    pageCtl.report1ColumnStartName = 'Commitment'
  }

  pageCtl.conditions.report1SelectedValues = ''
  pageCtl.report1RightClickData = ''
  initPage()
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/mycp/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const switchReport1DisplayMode = () => {
  if (pageCtl.conditions.report1DisplayMode === 'value') {
    pageCtl.conditions.report1DisplayMode = 'percent'
  } else if (pageCtl.conditions.report1DisplayMode === 'percent') {
    pageCtl.conditions.report1DisplayMode = 'value'
  }
}

const report1DislayValue = (row, type) => {
  if (pageCtl.conditions.report1DisplayMode === 'value') {
    return $shortenNumber(row[type])
  } else if (pageCtl.conditions.report1DisplayMode === 'percent') {
    const result = $toFixed(row[type] * 100 / (row.fail + row.onTime), 1)
    if (result) {
      return result + '%'
    } else {
      return '-'
    }
  }
}

const loadReport1 = (
  row: any,
  treeNode: any,
  resolve: (date: any[]) => void
) => {
  $axios({
    method: 'post',
    url: '/customer/mycp/query_report1_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category,
      parentMaxWidth: row.parentMaxWidth
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

const report1SortCache = {}

const report1SortChange = (e) => {
  report1SortCache[e.prop] = e.order
}

const report1Sort = (a, b, category) => {
  const beforeOrder = report1SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] > b[category] ? 1 : (a[category] === b[category] ? 0 : -1)
  }
}

const report1RightClick2 = (e) => {
  let target = e.target
  let maxLoop = 5
  while (maxLoop-- > 0) {
    if (target && target.dataset.value) {
      break
    }
    target = target.parentElement
  }
  // 用户点击右键时, 不一定继续点击右键弹出菜单, 所以找个临时变量来存一下
  pageCtl.report1RightClickData = target.dataset.value
}

const report1ViewDetails = () => {
  pageCtl.conditions.report1SelectedValues = pageCtl.report1RightClickData
  const title = JSON.parse(pageCtl.conditions.report1SelectedValues)
  $viewDetails({
    url: '/customer/mycp/query_report1_details',
    durl: '/customer/mycp/download_report1_details',
    params: pageCtl.conditions,
    title: 'View Details [' + [...title.parent, title.category].join(', ') + ']',
    editable: false
  })
}

const report1ViewTrends = (e) => {
  report1RightClick2(e)
  pageCtl.conditions.report1SelectedValues = pageCtl.report1RightClickData
  searchReport2()
  searchReport3()
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/mycp/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
    pageCtl.report2yAxisMax = 100
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  report3TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/mycp/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3DateColumns = body
    report3TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const afterReport3Select = (r, rc, c) => {
  const values = [r.TYPE]
  if (_fields.value.includes(c) || c === 'Total') {
    values.push('')
  } else {
    values.push(c)
  }
  for (let i = 0; i < _fields.value.length; i++) {
    values.push(r[_fields.value[i]])
  }
  pageCtl.report3SelectedValues = values
}

const renderReport3Ratio = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r.TYPE === 'RATIO') {
    td.style.background = 'var(--scp-bg-color-fill)'
  } else {
    td.style.background = 'var(--scp-bg-color)'
  }

  if (_fields.value.indexOf(prop) !== -1) {
    if (prop !== _fields.value[_fields.value.length - 1]) {
      if (r.TYPE !== 'RATIO') {
        td.innerHTML = ''
      } else {
        td.innerHTML = value
      }
    } else {
      if (r.TYPE === 'RATIO') {
        td.innerHTML = value
      } else if (r.TYPE === 'ONTIME') {
        td.innerHTML = 'On Time'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-success)'
        // td.style.fontWeight = '700'
      } else if (r.TYPE === 'FAIL') {
        td.innerHTML = 'Fail'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-error)'
      } else if (r.TYPE === 'OTHERS') {
        td.innerHTML = 'Others'
        td.style.textAlign = 'right'
      }
    }
  } else {
    td.style.textAlign = 'left'
    if (r.TYPE === 'RATIO') {
      if (value && !isNaN(value)) {
        td.innerHTML = (value * 100).toFixed(1) + '%'
        if (value * 100 < pageCtl.conditions.warningValue.Fail) {
          td.style.color = 'var(--scp-text-color-error)'
        }
      } else {
        td.innerHTML = value
        if (value === 0) {
          td.style.color = 'var(--scp-text-color-error)'
        }
      }
    } else {
      td.innerHTML = $thousandBitSeparator(value, 1)
    }
  }
}

const _fields = computed(() => {
  let fields = pageCtl.conditions.fields
  if (fields.length === 0) {
    fields = ['ENTITY']
  }
  return fields
})

const _conditions = computed(() => {
  const cond = $deepClone(pageCtl.conditions)
  cond.report2SelectedDate = pageCtl.report2SelectedDate
  cond.report3SelectedValues = pageCtl.report3SelectedValues
  return cond
})

const _report2Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const colorMap = {
    'Planner Irrelevant': '#f0f0f0',
    Fail: '#fc6464',
    Ongoing: '#3dcd58',
    Ontime: '#61a3f1'
  }

  const yAxisData = pageCtl.report2Data.yAxis
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
    }
  }
  legend.push('OnTime (%)')
  series.push({
    name: 'OnTime (%)',
    type: 'line',
    smooth: false,
    yAxisIndex: 1,
    data: pageCtl.report2Data.lineData,
    color: '#3dcd58',
    label: {
      show: true,
      fontSize: 10,
      formatter: (data) => {
        return data.data + '%'
      }
    }
  })

  for (let i = 0; i < legend.length; i++) {
    const key = legend[i]
    const data = pageCtl.report2Data.yAxis[key]
    series.push({
      name: key,
      stack: 'total',
      data,
      type: 'bar',
      itemStyle: {
        color: colorMap[key]
      }
    })
  }

  let subTitle = 'Week'

  if (pageCtl.conditions.reportViewType === 'VIEW_BY_WEEK') {
    subTitle = 'Week'
  } else if (pageCtl.conditions.reportViewType === 'VIEW_BY_MONTH') {
    subTitle = 'Month'
  } else if (pageCtl.conditions.reportViewType === 'VIEW_BY_QUARTER') {
    subTitle = 'Quarter'
  } else if (pageCtl.conditions.reportViewType === 'VIEW_BY_YEAR') {
    subTitle = 'Year'
  } else if (pageCtl.conditions.reportViewType === 'VIEW_BY_DATE') {
    subTitle = 'Date'
  }

  let title = 'MyCP Trends by ' + subTitle
  if (pageCtl.conditions.report1SelectedValues !== '') {
    const selected = JSON.parse(pageCtl.conditions.report1SelectedValues)
    const value = [...selected.parent, selected.category]
    title = title + ' [' + value.join(', ') + ']'
  }

  return {
    title: {
      text: title
    },
    grid: $grid(),
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        let total = 0
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName !== 'OnTime (%)') {
            total += (params[i].value || 0)
          }
        }
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName !== 'OnTime (%)') {
            tip.push('<div style="width:9rem;">')
            tip.push(params[i].marker)
            tip.push(params[i].seriesName)
            tip.push('<div style="width: 2rem;font-size: 90%;float: right;text-align: right">')
            tip.push(' (')
            tip.push(total === 0 ? '0' : ((params[i].value || 0) * 100 / total).toFixed(1) + '%')
            tip.push(')')
            tip.push('</div>')
            tip.push('<div style="width: 2rem;font-size: 90%;float: right;text-align: right">')
            tip.push($shortenNumber(params[i].value, 1))
            tip.push('</div></div>')
          }
        }
        tip.push('<div style="width:9rem;">')
        tip.push('Total')
        tip.push('<div style="width: 2rem; font-size: 90%; float: right; text-align: right">')
        tip.push(' (')
        tip.push('100%')
        tip.push(')')
        tip.push('</div>')
        tip.push('<div style="width: 2rem; font-size: 90%; float: right; text-align: right">')
        tip.push($shortenNumber(total))
        tip.push('</div>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    xAxis: {
      type: 'category',
      data: pageCtl.report2Data.xAxis
    },
    yAxis: [{
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      max: pageCtl.report2yAxisMax,
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0) + '%'
        }
      }
    }],
    series,
    visualMap: {
      seriesIndex: 0,
      show: false,
      pieces: [
        {
          gt: 0,
          lte: pageCtl.conditions.warningValue.Fail,
          color: '#f62707'
        },
        {
          gt: pageCtl.conditions.warningValue.Fail,
          color: '#3dcd58'
        }
      ]
    }
  }
})

const _report3Columns = computed(() => {
  const columns = [] as any
  // 为了可以让warningValue的修改触发表格更新
  if (pageCtl.conditions.warningValue.Fail < 0) {
    return [{}]
  }

  for (let i = 0; i < _fields.value.length; i++) {
    columns.push({
      data: _fields.value[i],
      render: renderReport3Ratio
    })
  }

  for (let i = 0; i < pageCtl.conditions.report3DateColumns.length; i++) {
    const name: any = pageCtl.conditions.report3DateColumns[i]
    const title = name
    columns.push({
      data: pageCtl.conditions.report3DateColumns[i],
      title,
      render: renderReport3Ratio
    })
  }

  return {
    columns
  }
})

const _report3DetailsColumn = computed(() => {
  const columns = [] as any

  if (pageCtl.report3SelectedValues[0] === 'FAIL') {
    columns.push({
      title: 'RCA Code',
      data: 'RCA_CODE',
      render: (hotInstance, td, row, column, prop, value) => {
        if (value) {
          let html = '<div title="' + pageCtl.rcaDesc[value] + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    })
  }

  columns.push(...[
    { data: 'SALES_ORDER_NUMBER' },
    { data: 'SALES_ORDER_ITEM' },
    { data: 'IMPORTANCE' },
    { data: 'APPLICATION_NUMBER' },
    { data: 'EXPEDITE_LEVEL' },
    { data: 'APPLICATION_STATUS' },
    { data: 'APPLICATION_SORT' },
    { data: 'REMIND_COUNT' },
    { data: 'APPLICANT_USERNAME' },
    { data: 'APPLICATION_DATE' },
    { data: 'PURCHASE_PROCESSOR_USERNAME' },
    { data: 'PURCHASE_DEAL_DATE' },
    { data: 'ORDER_PROCESSOR_USERNAME' },
    { data: 'ORDER_DEAL_DATE' },
    { data: 'UPGRADE_PROCESSOR_USERNAME' },
    { data: 'UPGRADE_DEAL_DATE' },
    { data: 'UPGRADE_RESULT_CONFIRMER_USERNAME' },
    { data: 'UPGRADE_RESULT_CONFIRM_DATE' },
    { data: 'EXPEDITE_ORDER_TYPE' },
    { data: 'EXPEDITE_QUANTITY' },
    { data: 'CUSTOMER_EXPECTED_DATE' },
    { data: 'ESTIMATED_DELIVERY_DATE' },
    { data: 'PURCHASE_EXPECTED_DATE' },
    { data: 'TRANSFER_TO_ORDERER' },
    { data: 'UPGRADE_CASE_NUMBER' },
    { data: 'UPGRADE_EXPEDITE_RESULT' },
    { data: 'COMPLAINED' },
    { data: 'CURRENT_HANDLER_USERNAME' },
    { data: 'TARGET_RESPONSE_DATE' },
    { data: 'CALENDAR_DATE' },
    { data: 'CALENDAR_WEEK' },
    { data: 'CALENDAR_MONTH' },
    { data: 'CALENDAR_QUARTER' },
    { data: 'CALENDAR_YEAR' },
    { data: 'MYCP_ONTIME_STATUS' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'BU' },
    { data: 'PRODUCT_LINE' },
    { data: 'ENTITY' },
    { data: 'VENDOR_NAME' },
    { data: 'STOCKING_POLICY' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'APPLICATION_SOUCRE' },
    { data: 'DELIVERY_PRIORITY' },
    { data: 'REPEATED_BLOCK_INDICATOR' },
    { data: 'VIP_VIP_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'SHIPPING_POINT' },
    { data: 'LT_RANGE' },
    { data: 'PRODUCTION_LINE' },
    { data: 'VIP_CUSTOMER_TYPE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'ACTUAL_LT_RANGE' },
    { data: 'MATERIAL_TYPE' },
    { data: 'MOQ_COV_RANGE' },
    { data: 'ITEM_CATEGORY' },
    { data: 'LATEST_GI_DATE' },
    { data: 'GI_STATUS' },
    { data: 'VIP_PROJECT_NAME' },
    { data: 'SE_SCOPE' },
    { data: 'CLUSTER_NAME' },
    { data: 'SALES_GROUP' },
    { data: 'MATERIAL_ST_PLANT' },
    { data: 'RESCH_GROUP' },
    { data: 'VIP_BU' },
    { data: 'VIP_SEGMENT_L2' },
    { data: 'VIP_VIP_TYPE' },
    { data: 'VIP_VCP_OEM' },
    { data: 'SO_STOCKING_POLICY' },
    { data: 'TRIGGER_CATEGORY' },
    { data: 'ACTIVENESS' },
    { data: 'PROCUREMENT_TYPE' },
    { data: 'SOLD_TO_REGION' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'VIP_SO_INDICATOR' },
    { data: 'SO_ONTIME_STATUS' },
    { data: 'VIP_DIRECT_INDIRECT' },
    { data: 'VIP_EU' },
    { data: 'PLANT_SCOPE' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'CALCULATED_ABC' },
    { data: 'PLANT_TYPE' },
    { data: 'VIP_REGION' },
    { data: 'LOCAL_BU' },
    { data: 'CALCULATED_FMR' },
    { data: 'MAT_PRICING_GROUP' },
    { data: 'COV_RANGE' },
    { data: 'WEEKLY_ADI_RANGE' },
    { data: 'MYCP_VIP_INDICATOR' },
    { data: 'SO_CREATED_DATE' },
    { data: 'CONSTR_GI_DATE' },
    { data: 'SOLD_TO' },
    { data: 'SOLD_TO_SHORT_NAME' },
    { data: 'SOLD_TO_FULL_NAME' },
    { data: 'SOLD_TO_PARENT_NAME' },
    { data: 'SOLD_TO_PARENT_CODE' },
    { data: 'SOLD_TO_REGION_NAME' },
    { data: 'SOLD_TO_COUNTRY' },
    { data: 'SHIP_TO_REGION' },
    { data: 'SHIP_TO_SUB_REGION' },
    { data: 'SHIP_TO_COUNTRY' },
    { data: 'SHIP_TO_REGION_NAME' },
    { data: 'SHIP_TO_SHORT_NAME' },
    { data: 'SHIP_TO_FULL_NAME' },
    { data: 'SHIP_TO_PARENT_NAME' },
    { data: 'SHIP_TO_PARENT_CODE' },
    { data: 'CUSTOMER_PARENT_CODE' },
    { data: 'CUSTOMER_PARENT_NAME' },
    { data: 'SO_BLOCK_STATUS' }
  ])
  return columns
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>

<style lang="scss">
#mycp {
  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th.fail-header {
          border-right: solid 2.5px var(--scp-bg-color) !important;
        }

        th.onTime-header {
          border-left: solid 2.5px var(--scp-bg-color) !important;
        }

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill) !important;

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              margin-left: 10px;
              width: calc(100% - 40px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }

                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);
                  background-color: transparent !important;

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table tr:hover {
    td {
      * {
        color: #fff !important;
      }
    }
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
