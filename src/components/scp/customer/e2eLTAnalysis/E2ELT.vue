<template>
  <div class="left-sidebar" id="e2eLT">
    <div class="widget">
      <div class="widget-body" style="padding-top: 0">
        <el-tabs v-model="pageCtl.activeMenu">
          <el-tab-pane
              label="E2E LT Overview"
              name="E2E LT Overview"
              :lazy="true"
          >
            <keep-alive>
              <component
                  :is="pageCtl.components['E2E LT Overview']"
                  :key="pageCtl.activeMenu + '-E2E LT Overview'"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane
              label="E2E LT Analysis"
              name="E2E LT Analysis"
              :lazy="true"
          >
            <keep-alive>
              <component
                  :is="pageCtl.components['E2E LT Analysis']"
                  :key="pageCtl.activeMenu + '-E2E LT Analysis'"
              />
            </keep-alive>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

import E2EltOverview from '@/components/scp/customer/e2eLTAnalysis/e2eLTAnalysisComponents/E2ELTOverview.vue'
import E2ELTAnalysis from '@/components/scp/customer/e2eLTAnalysis/e2eLTAnalysisComponents/E2ELTAnalysis.vue'

const pageCtl = reactive({
  activeMenu: 'E2E LT Overview',
  components: {
    'E2E LT Overview': E2EltOverview,
    'E2E LT Analysis': E2ELTAnalysis
  }
})

</script>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin: 0 0 10px 0;
}
</style>
