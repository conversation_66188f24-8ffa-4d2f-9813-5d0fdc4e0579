<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-select v-model="pageCtl.nor.conditions.category" size="small" placeholder="category" filterable clearable multiple collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.nor.conditions.$scpFilter" :cascader-base-opts="pageCtl.nor.filterOpts" :loading="pageCtl.nor.loading.filter"
                        :filter-base="['NOR_V']" :after-apply="searchNor"/>
          </el-col>
          <el-col :span="6">
            <el-date-picker
                size="small"
                v-model="pageCtl.nor.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="searchNor" :data="pageCtl.nor.conditions" :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.nor.loading.filter" style="height: 450px;">
              <scp-subscript id="CONR" ref="report1SubRef"/>
              <div class="front">
                <scp-table
                    :lazy="true"
                    :params="pageCtl.nor.conditions"
                    :max-height="450"
                    :after-select="afterNorReport1Selected"
                    :pagging="false"
                    :pagging-setting-enable="false"
                    url="/customer/nor/query_report1"
                    download-url="/customer/nor/download_report1"
                    ref="norReport1Ref"
                    :context-menu-items="pageCtl.nor.contextItems.report1"
                    :columns="pageCtl.norColumns"
                />
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">
                    Filter For NOR
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="8">Select RC Threshold (Total Lines of RC ≥ ? Ratio)</el-col>
                    <el-col :span="16">
                      <el-input-number v-model="pageCtl.nor.conditions.report1SelectedRatio"/>
                    </el-col>
                  </el-row>
                </div>
                <div class="box-footer">
                  <el-button
                      @click="report1SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report1SubRef.toggleView();searchNorReport1()">
                    Search
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.nor.loading.report2 || pageCtl.nor.loading.filter">
              <scp-subscript id="CONP"/>
              <el-row>
                <el-col :span="4" class="report-sub-title-right" :style="{ transform: 'translateX(-75px) translateY(3px)' }">
                  <el-button size="small" title="back to today" @click="norReport2Back">
                    <font-awesome-icon icon="fa-arrows-rotate"/>
                  </el-button>
                </el-col>
              </el-row>
              <div class="front">
                <chart
                    ref="norReport2Ref"
                    :autoresize="true"
                    :height="420"
                    :option="_norReport2Opt"/>
              </div>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.nor.loading.report3 || pageCtl.nor.loading.filter">
              <scp-subscript id="CONG" ref="norReport3SubRef"/>
              <el-row>
                <el-col :span="4" class="report-sub-title-right" :style="{ transform: 'translateX(-175px) translateY(3px)' }">
                  <el-select v-model="pageCtl.nor.conditions.report3DateType" size="small" @change="searchNorReport3">
                    <el-option label="By Day" value="BY_DAY"/>
                    <el-option label="By Week" value="BY_WEEK"/>
                    <el-option label="By Month" value="BY_MONTH"/>
                  </el-select>
                </el-col>
              </el-row>
              <chart
                  ref="norReport3Ref"
                  style="margin-bottom: var(--scp-widget-margin)"
                  :autoresize="true"
                  :height="292"
                  :option="_norReport3Opt"/>
              <scp-table
                  :lazy="true"
                  :params="pageCtl.nor.conditions"
                  :max-height="120"
                  :pagging="false"
                  :pagging-setting-enable="false"
                  url="/customer/nor/query_report3_ratios"
                  ref="norReport3RatiosRef"
                  :columns="pageCtl.nor.report3RatiosColumns"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.nor.loading.report4 || pageCtl.nor.loading.filter">
            <div class="subscript-container">
              <scp-subscript id="COND" ref="norReport4SubRef"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.nor.conditions.report4Type" size="small">
                    <el-option label="Sales Order" value="so"/>
                    <el-option label="Material" value="material"/>
                  </el-select>
                </el-col>
                <el-col :span="5" v-show="pageCtl.nor.conditions.report4Type === 'so'">
                  <el-select v-model="pageCtl.norReport4SOSelectedColumns" size="small" filterable clearable multiple collapse-tags>
                    <el-option
                        v-for="item in pageCtl.report4SOAvailableColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5" v-show="pageCtl.nor.conditions.report4Type === 'material'">
                  <el-select v-model="pageCtl.norReport4MaterialSelectedColumns" size="small" filterable clearable multiple collapse-tags>
                    <el-option
                        v-for="item in pageCtl.report4MaterialAvailableColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <scp-table
                  :lazy="true"
                  :params="pageCtl.nor.conditions"
                  :max-height="450"
                  url="/customer/nor/query_report4"
                  download-url="/customer/nor/download_report4"
                  ref="norReport4Ref"
                  :context-menu-items="pageCtl.nor.contextItems.report4"
                  :after-select="afterNorReport4Selected"
                  :columns="_norReport4Column"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $axios: any = inject('$axios')
const $join: any = inject('$join')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1SubRef = ref()
const norReport1Ref = ref()
const norReport2Ref = ref()
const norReport3SubRef = ref()
const norReport3Ref = ref()
const norReport3RatiosRef = ref()
const norReport4SubRef = ref()
const norReport4Ref = ref()

const viewReport1Details = () => {
  $viewDetails({
    title: pageCtl.nor.report1DetailsTitle,
    url: '/customer/nor/query_report1_details',
    durl: '/customer/nor/download_report1_details',
    params: pageCtl.nor.conditions
  })
}

const viewReport4Details = () => {
  $viewDetails({
    title: 'View Details',
    url: '/customer/nor/query_report4_details',
    durl: '/customer/nor/download_report4_details',
    params: pageCtl.nor.conditions
  })
}

const pageCtl = reactive({
  nor: {
    filterOpts: [],
    conditions: {
      $scpFilter: {
        cascader: [],
        filter: []
      },
      category: ['CLUSTER_NAME', 'ENTITY'],
      dateRange: [] as any,
      report1SelectedValue: [] as any,
      report1SelectedRatio: 2,
      report1SelectedColumn: '',
      report3DateType: 'BY_DAY',
      report3SelectedValue: '',
      report4SelectedValue: [null, null],
      report4Type: 'so'
    },
    loading: {
      filter: false,
      report1: false,
      report2: false,
      report3: false,
      report4: false
    },
    current: {
      dateRange: [],
      category: 'ENTITY'
    } as any,
    contextItems: {
      report1: {
        view_details: {
          name: 'View details',
          callback: viewReport1Details
        },
        view_split0: { name: '---------' }
      },
      report4: {
        view_details: {
          name: 'View details',
          callback: viewReport4Details
        },
        view_split0: { name: '---------' }
      }
    },
    colorSettings: {
      RC_GE_11: '#c13033',
      RC_GE_6_LE_10: '#c8412d',
      RC_GE_4_LE_5: '#d25924',
      RC_EQ_3: '#e1a506',
      RC_EQ_2: '#87a40c',
      RC_EQ_1: '#629512'
    } as any,
    nameSettings: {
      RC_GE_11: 'RC>=11',
      RC_GE_6_LE_10: 'RC=6~10',
      RC_GE_4_LE_5: 'RC=4~5',
      RC_EQ_3: 'RC=3',
      RC_EQ_2: 'RC=2',
      RC_EQ_1: 'RC=1'
    },
    report1DetailsTitle: '',
    report2Data: [] as any,
    report3Data: [] as any,
    report3RatiosColumns: [] as any
  },
  norColumns: [],
  report4SOAvailableColumns: ['AC2_RANGE', 'ACTIVENESS', 'AVAILABILITY_CHECK', 'BLOCK_STATUS', 'BU', 'CLUSTER_NAME', 'CUSTOMER_CODE', 'DELIVERY_PRIORITY', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT', 'GRA_STATUS', 'GRA_TYPE', 'IMPORT_VENDOR', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'LT_RANGE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'ORDER_TYPE', 'PLANT_CODE', 'PLANT_NAME', 'PLANT_TYPE', 'PRODUCTION_LINE', 'PRODUCT_LINE', 'RESCH_GROUP', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_REGION', 'SHORTAGE_STATUS', 'SOLD_TO_REGION', 'SOURCE_CATEGORY', 'STOCKING_POLICY', 'UD_STATUS', 'VENDOR_NAME', 'GI_DATE', 'GI_WEEK', 'GI_MONTH', 'SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM', 'MATERIAL', 'RESCH_COUNTER', 'VENDOR_CODE', 'CREATED_DATE', 'CRD_DATE', 'PURCH_ORDER_NUMBER', 'PURCH_ORDER_ITEM'],
  report4MaterialAvailableColumns: ['MATERIAL', 'RESCH_COUNTER', 'VENDOR_CODE', 'AC2_RANGE', 'ACTIVENESS', 'AVAILABILITY_CHECK', 'BLOCK_STATUS', 'BU', 'CLUSTER_NAME', 'CUSTOMER_CODE', 'DELIVERY_PRIORITY', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT', 'GRA_STATUS', 'GRA_TYPE', 'IMPORT_VENDOR', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'LT_RANGE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'ORDER_TYPE', 'PLANT_CODE', 'PLANT_NAME', 'PLANT_TYPE', 'PRODUCTION_LINE', 'PRODUCT_LINE', 'RESCH_GROUP', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_REGION', 'SHORTAGE_STATUS', 'SOLD_TO_REGION', 'SOURCE_CATEGORY', 'STOCKING_POLICY', 'UD_STATUS', 'VENDOR_NAME'],
  norReport4SOSelectedColumns: ['GI_DATE', 'GI_WEEK', 'GI_MONTH', 'SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM',
    'MATERIAL', 'PLANT_CODE', 'RESCH_COUNTER', 'RESCH_GROUP', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'PRODUCT_LINE', 'ENTITY',
    'CLUSTER_NAME', 'BU', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY',
    'STOCKING_POLICY', 'VENDOR_CODE', 'VENDOR_NAME', 'ACTIVENESS', 'SOURCE_CATEGORY', 'CUSTOMER_CODE', 'SHIP_TO',
    'CREATED_DATE', 'CRD_DATE', 'AVAILABILITY_CHECK', 'PURCH_ORDER_NUMBER', 'PURCH_ORDER_ITEM'
  ],
  norReport4MaterialSelectedColumns: ['MATERIAL', 'PLANT_CODE', 'RESCH_COUNTER', 'RESCH_GROUP', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER',
    'PRODUCT_LINE', 'ENTITY', 'CLUSTER_NAME', 'BU', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE',
    'LOCAL_PRODUCT_SUBFAMILY', 'STOCKING_POLICY', 'VENDOR_CODE', 'VENDOR_NAME', 'ACTIVENESS', 'SOURCE_CATEGORY']
})

onMounted(() => {
  pageCtl.report4SOAvailableColumns.sort()
  pageCtl.report4MaterialAvailableColumns.sort()
  pageCtl.norReport4SOSelectedColumns.sort()
  pageCtl.norReport4MaterialSelectedColumns.sort()
  initNorFilter()
  norReport3Ref.value.chart().on('dblclick', 'series.line', (params) => {
    if (params.name != null && params.name.length > 5) {
      pageCtl.nor.conditions.report3SelectedValue = params.name
      searchNorReport2()
    } else {
      pageCtl.nor.conditions.report3SelectedValue = ''
    }
  })
})

watch(() => pageCtl.nor.conditions.report4Type, () => {
  norReport4Ref.value.search()
})

const initNorFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), now.getMonth() - 3, 1)
  pageCtl.nor.conditions.dateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]

  pageCtl.nor.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/nor/query_cascader'
  }).then((body) => {
    pageCtl.nor.filterOpts = body
    pageCtl.nor.conditions.$scpFilter.cascader = [
      ['RESCH_GROUP', 'RC 4~5'],
      ['RESCH_GROUP', 'RC 6~10'],
      ['RESCH_GROUP', 'RC >=11'],
      ['RESCH_GROUP', 'RC=1'],
      ['RESCH_GROUP', 'RC=2'],
      ['RESCH_GROUP', 'RC=3']
    ]
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.nor.loading.filter = false
  })
}

const searchNor = () => {
  pageCtl.nor.conditions.report1SelectedValue = ''
  pageCtl.nor.conditions.report3SelectedValue = ''
  pageCtl.nor.conditions.report4SelectedValue = []
  pageCtl.nor.current.category = pageCtl.nor.conditions.category
  pageCtl.nor.current.dateRange = pageCtl.nor.conditions.dateRange

  norReport1Column()
  norReport1Ref.value.search()
  norReport4Ref.value.search()
  searchNorReport2()
  searchNorReport3()
  searchNorReport3Ratios()
}

const searchNorReport1 = () => {
  pageCtl.nor.conditions.report1SelectedValue = ''
  norReport1Column()
  norReport1Ref.value.search()
}

const searchNorReport2 = () => {
  pageCtl.nor.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/nor/query_report2',
    data: pageCtl.nor.conditions
  }).then((body) => {
    pageCtl.nor.report2Data = [
      { name: 'RC=1', value: body.RC_EQ_1, field: 'RC_EQ_1' },
      { name: 'RC=2', value: body.RC_EQ_2, field: 'RC_EQ_2' },
      { name: 'RC=3', value: body.RC_EQ_3, field: 'RC_EQ_3' },
      { name: 'RC=4~5', value: body.RC_GE_4_LE_5, field: 'RC_GE_4_LE_5' },
      { name: 'RC=6~10', value: body.RC_GE_6_LE_10, field: 'RC_GE_6_LE_10' },
      { name: 'RC≥11', value: body.RC_GE_11, field: 'RC_GE_11' }
    ]
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.nor.loading.report2 = false
  })
}

const searchNorReport3 = () => {
  pageCtl.nor.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/nor/query_report3',
    data: pageCtl.nor.conditions
  }).then((body) => {
    pageCtl.nor.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.nor.loading.report3 = false
  })
}

const norReport1Column = () => {
  const columns = [] as any
  for (let i = 0; i < _norSelectedcategory.value.length; i++) {
    columns.push({
      data: _norSelectedcategory.value[i],
      title: _norSelectedcategory.value[i],
      render: renderRowTitle
    })
  }

  columns.push(
    { data: 'RC_EQ_1', title: 'RC=1', type: 'numeric' },
    { data: 'RC_EQ_2', title: 'RC=2', type: 'numeric' },
    { data: 'RC_EQ_3', title: 'RC=3', type: 'numeric' },
    { data: 'RC_GE_4_LE_5', title: 'RC=4~5', type: 'numeric' },
    { data: 'RC_GE_6_LE_10', title: 'RC=6~10', type: 'numeric' },
    { data: 'RC_GE_11', title: 'RC>=11', type: 'numeric' },
    { data: 'RC_TOTAL', title: 'Total No. of RC', type: 'numeric' },
    { data: 'RC_LINES', title: 'Total Lines of RC', type: 'numeric' },
    { data: 'RC_LINES_RATIO', title: 'Total Lines of RC >= ' + pageCtl.nor.conditions.report1SelectedRatio + ' Ratio', type: 'numeric' },
    { data: 'RC_AVERAGE', title: 'AVG RC', type: 'numeric', render: renderRow }
  )
  pageCtl.norColumns = columns
}

const norReport3RatiosColumns = (data) => {
  const result: any = [{ data: 'STATISTICS' }]
  for (let i = 0; i < data.length; ++i) {
    result.push({ data: data[i], title: data[i], type: 'numeric' })
  }
  return result
}

const searchNorReport3Ratios = () => {
  norReport3RatiosRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/nor/query_report3_ratios_columns',
    data: pageCtl.nor.conditions
  }).then((body) => {
    pageCtl.nor.report3RatiosColumns = norReport3RatiosColumns(body)
    norReport3RatiosRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const afterNorReport1Selected = (r, c, a) => {
  pageCtl.nor.conditions.report1SelectedColumn = a
  if (!_norSelectedcategory.value.every(key => key in r)) {
    pageCtl.nor.conditions.report1SelectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _norSelectedcategory.value.length; i++) {
      selectedValue.push(r[_norSelectedcategory.value[i]])
    }
    pageCtl.nor.conditions.report1SelectedValue = selectedValue
  }

  let selectColumnName = ''
  for (let i = 0; i < pageCtl.norColumns.length; i++) {
    const obj: any = pageCtl.norColumns[i]
    if (a === obj.data && (a.indexOf('RC_EQ') === 0 || a.indexOf('RC_GE') === 0)) {
      selectColumnName = obj.title
    }
  }
  const title = $join(...pageCtl.nor.conditions.report1SelectedValue, selectColumnName)
  if (title) {
    pageCtl.nor.report1DetailsTitle = 'View Details [' + title + ']'
  } else {
    if (pageCtl.nor.conditions.report1SelectedValue.length > 0) {
      pageCtl.nor.report1DetailsTitle = 'View Details [' + $join(...pageCtl.nor.conditions.report1SelectedValue) + ']'
    } else {
      pageCtl.nor.report1DetailsTitle = 'View Details [Total]'
    }
  }
}

const afterNorReport4Selected = (r) => {
  if (pageCtl.nor.conditions.report4Type === 'so') {
    pageCtl.nor.conditions.report4SelectedValue = [r.MATERIAL, r.SALES_ORDER_NUMBER, r.SALES_ORDER_ITEM]
  } else {
    pageCtl.nor.conditions.report4SelectedValue = [r.MATERIAL, r.STOCKING_POLICY, r.ENTITY, null]
  }
}

const renderRow = (hotInstance, td, row, column, prop, value) => {
  if (value) {
    let html: any
    if (prop.indexOf('RC_AVERAGE') !== -1) {
      const r = hotInstance.getSourceDataAtRow(row)

      td.style.textAlign = 'right'
      html = value.toFixed(2)
    } else if (prop.indexOf('MATERIAL') !== -1) {
      html = value
    } else {
      html = value
    }
    td.innerHTML = html
  } else {
    td.style.textAlign = 'right'
    td.innerHTML = value
  }
}

const renderRowTitle = (hotInstance, td, row, column, prop, value) => {
  const c = hotInstance.getSourceData().length
  if (column === 0 && row !== 0 && row === c - 1) {
    td.innerHTML = 'Total'
    if (td.parentNode) {
      td.parentNode.style.fontWeight = 'bold'
    }
  } else {
    td.innerHTML = value
  }
}

const norReport2Back = () => {
  pageCtl.nor.conditions.report3SelectedValue = ''
  searchNorReport2()
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.nor.filterOpts)
})
const _norReport4Column = computed(() => {
  if (pageCtl.nor.conditions.report4Type === 'so') {
    const allColumns = [
      { data: 'GI_DATE' },
      { data: 'GI_WEEK' },
      { data: 'GI_MONTH' },
      { data: 'SALES_ORDER_NUMBER' },
      { data: 'SALES_ORDER_ITEM' },
      { data: 'MATERIAL' },
      { data: 'RESCH_COUNTER', title: 'Total No. of Rescheduling Counters', type: 'numeric' },
      { data: 'VENDOR_CODE' },
      { data: 'CREATED_DATE' },
      { data: 'CRD_DATE' },
      { data: 'PURCH_ORDER_NUMBER' },
      { data: 'PURCH_ORDER_ITEM' },
      { data: 'AC2_RANGE' },
      { data: 'ACTIVENESS' },
      { data: 'AVAILABILITY_CHECK' },
      { data: 'BLOCK_STATUS' },
      { data: 'BU' },
      { data: 'CLUSTER_NAME' },
      { data: 'CUSTOMER_CODE' },
      { data: 'DELIVERY_PRIORITY' },
      { data: 'ENTITY' },
      { data: 'FULFILL_OR_NOT_NONBLOCK' },
      { data: 'FULFILL_OR_NOT_UNRESTRICT' },
      { data: 'GRA_STATUS' },
      { data: 'GRA_TYPE' },
      { data: 'IMPORT_VENDOR' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ORDER_TYPE' },
      { data: 'PLANT_CODE' },
      { data: 'PLANT_NAME' },
      { data: 'PLANT_TYPE' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PRODUCT_LINE' },
      { data: 'RESCH_GROUP' },
      { data: 'SHIP_TO' },
      { data: 'SHIP_TO_CITY' },
      { data: 'SHIP_TO_COUNTRY' },
      { data: 'SHIP_TO_REGION' },
      { data: 'SHORTAGE_STATUS' },
      { data: 'SOLD_TO_REGION' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'STOCKING_POLICY' },
      { data: 'UD_STATUS' },
      { data: 'VENDOR_NAME' }
    ]
    const columns = [] as any
    for (let i = 0; i < allColumns.length; i++) {
      const c = allColumns[i]
      if (pageCtl.norReport4SOSelectedColumns.indexOf(c.data) !== -1) {
        columns.push(allColumns[i])
      }
    }
    return columns
  } else {
    const allColumns = [
      { data: 'MATERIAL', render: renderRow },
      { data: 'RESCH_COUNTER', title: 'Total No. of Rescheduling Counters', type: 'numeric' },
      { data: 'BU' },
      { data: 'VENDOR_CODE' },
      { data: 'AC2_RANGE' },
      { data: 'ACTIVENESS' },
      { data: 'AVAILABILITY_CHECK' },
      { data: 'BLOCK_STATUS' },
      { data: 'BU' },
      { data: 'CLUSTER_NAME' },
      { data: 'CUSTOMER_CODE' },
      { data: 'DELIVERY_PRIORITY' },
      { data: 'ENTITY' },
      { data: 'FULFILL_OR_NOT_NONBLOCK' },
      { data: 'FULFILL_OR_NOT_UNRESTRICT' },
      { data: 'GRA_STATUS' },
      { data: 'GRA_TYPE' },
      { data: 'IMPORT_VENDOR' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ORDER_TYPE' },
      { data: 'PLANT_CODE' },
      { data: 'PLANT_NAME' },
      { data: 'PLANT_TYPE' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PRODUCT_LINE' },
      { data: 'RESCH_GROUP' },
      { data: 'SHIP_TO' },
      { data: 'SHIP_TO_CITY' },
      { data: 'SHIP_TO_COUNTRY' },
      { data: 'SHIP_TO_REGION' },
      { data: 'SHORTAGE_STATUS' },
      { data: 'SOLD_TO_REGION' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'STOCKING_POLICY' },
      { data: 'UD_STATUS' },
      { data: 'VENDOR_NAME' }
    ]
    const columns = [] as any
    for (let i = 0; i < allColumns.length; i++) {
      const c = allColumns[i]
      if (pageCtl.norReport4MaterialSelectedColumns.indexOf(c.data) !== -1) {
        columns.push(allColumns[i])
      }
    }
    return columns
  }
})
const _norReport2Opt = computed(() => {
  const colors = [] as any
  const data = $deepClone(pageCtl.nor.report2Data)
  for (let i = 0; i < data.length; i++) {
    const name = data[i].field
    colors.push(pageCtl.nor.colorSettings[name])
    data[i].label = { color: pageCtl.nor.colorSettings[name] }
  }
  return {
    color: colors,
    title: {
      text: 'NOR [' + (
        pageCtl.nor.conditions.report3SelectedValue === ''
          ? (pageCtl.nor.conditions.dateRange[0] + ' - ' + pageCtl.nor.conditions.dateRange[1])
          : pageCtl.nor.conditions.report3SelectedValue) + ']'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:9.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')

        return tip.join('')
      }
    },
    legend: $legend({ type: 'pie' }),
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data
      }
    ]
  }
})
const _norReport3Opt = computed(() => {
  const legend = [] as any
  for (const k in pageCtl.nor.nameSettings) {
    legend.push(pageCtl.nor.nameSettings[k])
  }

  const series = [] as any
  for (const k in pageCtl.nor.colorSettings) {
    series.push({
      name: pageCtl.nor.nameSettings[k],
      type: 'line',
      smooth: false,
      stack: 'sum',
      itemStyle: {
        color: pageCtl.nor.colorSettings[k]
      },
      areaStyle: {},
      data: pageCtl.nor.report3Data[k] || []
    })
  }

  return {
    title: {
      text: 'Evolution of NOR by GI Date'
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params.reverse()
        let marker = ''
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push(marker)
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: pageCtl.nor.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    legend: $legend({ data: legend }),
    series
  }
})
const _norSelectedcategory = computed(() => {
  if (pageCtl.nor.conditions.category.length > 0) {
    return pageCtl.nor.conditions.category
  } else {
    return ['ENTITY']
  }
})

</script>
