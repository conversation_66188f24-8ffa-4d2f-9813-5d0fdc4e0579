<template>
  <div class="left-sidebar" id="otds">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <!--filter field-->
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.field" size="small" placeholder="Filter Field" clearable filterable collapse-tags multiple>
              <el-option
                  v-for="item in ['AC2_RANGE', 'ACTIVENESS', 'AVAILABILITY_CHECK', 'BUSINESS_UNIT', 'CLUSTER_NAME', 'CUSTOMER_CODE', 'CUSTOMER_GROUP', 'DATA_SOURCE', 'DELIVERY_PRIORITY', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT',
                'GRA_STATUS', 'GRA_TYPE', 'IMPORT_VENDOR', 'LOCAL_BUSINESS_UNIT', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'LT_RANGE', 'MATERIAL', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'ORDER_TYPE',
                 'PLANT_CODE', 'PLANT_NAME', 'PLANT_TYPE', 'PRODUCT_LINE', 'RCA_RESULT', 'REGION', 'SALES_DISTRICT', 'SALES_ORGANIZATION', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_REGION', 'SHIP_TO_SHORT_NAME', 'SHIP_TO_FULL_NAME', 'SHIP_TO_PARENT_NAME', 'SHIP_TO_PARENT_CODE', 'SHORTAGE_STATUS', 'SOLD_TO', 'SOLD_TO_REGION', 'SOLD_TO_SHORT_NAME', 'SOLD_TO_FULL_NAME', 'SOLD_TO_PARENT_NAME', 'SOLD_TO_PARENT_CODE', 'SOURCE_CATEGORY',
                'STOCKING_POLICY', 'STOCK_INDENT', 'SUB_TITLE1', 'VENDOR_NAME', 'VIP_REGION', 'VIP_SO_INDICATOR', 'VIP_VIP_NAME','MRP_CONTROLLER_DESCRIPTION']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!--category-->
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="OTDS_FILTER_V" :filter-base="['OTDS_SOURCE_WEEKLY_V', 'OTDS_SOURCE_DAILY_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <el-input-number v-model="pageCtl.conditions.warningValue" size="small" :precision="1" :step="0.1" :max="100" :min="40"
                             style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.reportOrderBy" size="small" style="width: var(--scp-input-width) !important;" placeholder="Order by">
              <el-option label="ONTIME" value="ONTIME"/>
              <el-option label="DELAY" value="FAIL"/>
              <el-option label="PERCENTAGE" value="PERCENTAGE"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange']"/>
            &nbsp;
            <el-tooltip :show-after="1000" effect="light" placement="bottom" content="View RCA/Tips Description">
              <el-button @click="viewTipsDescript">
                <font-awesome-icon icon="question"/>
              </el-button>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="subscript-container subscript-container-left">
              <h3 style="text-align: left;">OTDS Official</h3>
              <el-date-picker
                  size="small"
                  v-model="pageCtl.conditions.dateRange"
                  type="monthrange"
                  unlink-panels
                  range-separator="~"
                  format="YYYY/MM"
                  value-format="YYYY/MM"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  :picker-options="{
                  disabledDate(e) {
                    let time = e.getTime() - new Date().getTime()
                    return time > 5356800000 // 最多只能往前选62天
                  }
                }"
                  :clearable="false"
                  prefix-icon=null
                  style="width: 200px !important;border: 0;border-bottom: solid 1px var(--scp-border-color); position:absolute; top:10px;right: 10px">
              </el-date-picker>
              <scp-subscript id="COSO"/>
              <scp-table
                  :contextMenuItems="pageCtl.contextWeeklyMenuItems"
                  ref="otdsWeeklyTableRef"
                  :columns="_otdsWeeklyColumns"
                  :fixedColumnsLeft="_field.length"
                  :nested-headers="_weeklyNestedHeaders"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :tableStriped="false"
                  :dropdownMenu="false"
                  :row-headers="false"
                  :pagging="false"
                  :max-height="winHeight"
                  :afterSelect="afterWeeklySelect"
                  :pagging-setting-enable="false"
                  url="/customer/otds/query_otds_weekly"/>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="subscript-container subscript-container-right">
              <h3 style="text-align: left">OTDS Projection</h3>
              <el-select v-model="pageCtl.dailyRange" size="small" placeholder="" style="width: 120px; position: absolute; top:12px; right: 140px">
                <el-option
                    v-for="item in ['Last Week', 'This Week', 'Next Week']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
              <el-select v-model="pageCtl.totalType" size="small" placeholder="" style="width: 120px; position: absolute; top:12px;right: 10px;">
                <el-option
                    v-for="item in ['Total', 'WTD']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
              <scp-subscript id="COSD"/>
              <scp-table
                  :contextMenuItems="pageCtl.contextDailyMenuItems"
                  ref="otdsDailyTableRef"
                  :columns="_otdsDailyColumns"
                  :fixedColumnsLeft="_field.length"
                  :nested-headers="_dailyNestedHeaders"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :tableStriped="false"
                  :row-headers="false"
                  :dropdownMenu="false"
                  :pagging="false"
                  :max-height="winHeight"
                  :afterSelect="afterDailySelect"
                  :pagging-setting-enable="false"
                  url="/customer/otds/query_otds_daily"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- daily details-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.detailsDailyVisible" :title="pageCtl.dailyDetailsTitle">
      <template v-slot="{ height }">
        <scp-table style="margin-bottom:10px;"
                   ref="dailyDetailsTableRef"
                   url="/customer/otds/query_otds_daily_details"
                   download-url="/customer/otds/download_otds_daily_details"
                   :max-height="height - 150"
                   :lazy="true"
                   :params="pageCtl.conditions"
                   :page-sizes="[20, 50, 100, 200, 500]"
                   :columns="_dailyDetailsColumn"
                   :fixed-columns-left="2"
                   :context-menu-items="pageCtl.contextDailyDetailsMenuItems"
                   :after-change="afterDailyDetailsChange"
                   :context-menu-items-reverse="true"/>
      </template>
    </scp-draggable-resizable>

    <!-- weekly details-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.detailsWeeklyVisible" :title="pageCtl.weeklyDetailsTitle">
      <template v-slot="{ height }">
        <scp-table style="margin-bottom:10px;"
                   ref="weeklyDetailsTableRef"
                   url="/customer/otds/query_otds_weekly_details"
                   download-url="/customer/otds/download_otds_weekly_details"
                   :lazy="true"
                   :max-height="height - 150"
                   :page-sizes="[20, 50, 100, 200, 500]"
                   :params="pageCtl.conditions"
                   :columns="_weeklyDetailsColumns"
                   :editable="false"
                   :fixed-columns-left="2"
                   :after-change="afterWeeklyDetailsChange"
                   :context-menu-items="pageCtl.contextWeeklyDetailsMenuItems"/>
      </template>
    </scp-draggable-resizable>

    <!-- view daily chart-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.chartsDailyVisible" title="View Daily Chart">
      <template v-slot="{ height }">
        <chart ref="dailyChartRef" :style="{ height : height - 150 + 'px', width : pageCtl.pageWidth + 'px'}" :option="_dailyOpt"/>
      </template>
    </scp-draggable-resizable>

    <!-- view weekly chart-->
    <scp-draggable-resizable w="60vw" h="450px" :title="pageCtl.weeklyChartTitle" v-model="pageCtl.chartsWeeklyVisible">
      <template v-slot="{ height }">
        <el-row style="margin-top:5px;margin-left:5px" class="search-box">
          <el-col :span="11">
            <el-radio-group v-model="pageCtl.weeklyChartDisplayType" size="small">
              <el-radio-button value="Line">OnTime Trends</el-radio-button>
              <el-radio-button value="Pie">RCA Layout</el-radio-button>
              <el-radio-button value="Pie2">Tips Layout</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
        <chart ref="weeklyLineChartRef" :style="{ height : height - 150 + 'px'}" :option="_weeklyLineOpt"
               v-if="!pageCtl.loading.weeklyChart && (pageCtl.weeklyChartDisplayType === 'Line')"/>
        <chart ref="weeklyPieChartRef" :style="{ height : height - 150 + 'px'}" :option="_weeklyPieOpt"
               v-if="!pageCtl.loading.weeklyChart && (pageCtl.weeklyChartDisplayType === 'Pie')"/>
        <chart ref="weeklyPieChartRef" :style="{ height : height - 150 + 'px'}" :option="_weeklyPie2Opt"
               v-if="!pageCtl.loading.weeklyChart && (pageCtl.weeklyChartDisplayType === 'Pie2')"/>
      </template>
    </scp-draggable-resizable>

    <!-- view weekly summary-->
    <scp-draggable-resizable w="60vw" h="450px" title="View Official Summary" v-model="pageCtl.summaryWeeklyVisible">
      <template v-slot="{ height }">
        <el-row style="margin-top:5px;margin-left:5px" class="search-box">
          <el-col :span="6">
            <el-select v-model="pageCtl.summarySelectedWeeks" size="small" placeholder="Weeks" multiple collapse-tags style="min-width: 150px">
              <el-option
                  v-for="item in pageCtl.selectedWeeks"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="11">
            <el-radio-group v-model="pageCtl.summaryDisplayType" size="small">
              <el-radio-button value="Lite">Lite</el-radio-button>
              <el-radio-button value="Full">Full</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
        <scp-table
            ref="weeklySummaryTableRef"
            :columns="_otdsWeeklySummaryColumns"
            :fixedColumnsLeft="1"
            :max-height="height - 200"
            :nested-headers="_otdsWeeklySummaryNestedHeaders"
            :lazy="true"
            :params="pageCtl.conditions"
            :dropdown-menu="false"
            :column-sorting="true"
            :row-headers="true"
            :pagging="false"
            :pagging-setting-enable="false"
            url="/customer/otds/query_otds_weekly_summary"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, onUpdated, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $join: any = inject('$join')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')
const $startWith: any = inject('$startWith')
const $endWith: any = inject('$endWith')
const $message: any = inject('$message')
const $viewDetails: any = inject('$viewDetails')

const searchRef = ref()
const otdsWeeklyTableRef = ref()
const otdsDailyTableRef = ref()
const dailyDetailsTableRef = ref()
const weeklyDetailsTableRef = ref()
const dailyChartRef = ref()
const weeklyLineChartRef = ref()
const weeklyPieChartRef = ref()
const weeklySummaryTableRef = ref()
const winHeight = ref(400)

onUpdated(() => {
  winHeight.value = document.body.clientHeight - 225
})

const viewDailyDetails = () => {
  pageCtl.detailsDailyVisible = true
  pageCtl.dailyDetailsUpdate = {}
  dailyDetailsTableRef.value.clearAndSearch()
}

const saveWeeklyDetails = () => {
  if (weeklyDetailsTableRef.value.getLoading() === true) {
    return
  }
  if (JSON.stringify(pageCtl.weeklyDetailsUpdate) === '{}') {
    $message.error('No changes detected')
    return
  }
  weeklyDetailsTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/otds/save_otds_weekly_details',
    data: {
      weeklyDetailsUpdate: pageCtl.weeklyDetailsUpdate
    }
  }).then(() => {
    $message.success('RCA code(s) saved')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.weeklyDetailsUpdate = {}
    weeklyDetailsTableRef.value.setLoading(false)
  })
}

const viewDailyChart = () => {
  pageCtl.loading.dailyChart = true
  pageCtl.chartsDailyVisible = true
  $axios({
    method: 'post',
    url: '/customer/otds/query_otds_daily_chart',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.dailyChart = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.dailyChart = false
  })
}

const viewWeeklySummary = () => {
  pageCtl.summaryWeeklyVisible = true
  weeklySummaryTableRef.value.clearAndSearch()
}

const viewWeeklyDetails = () => {
  pageCtl.detailsWeeklyVisible = true

  pageCtl.weeklyDetailsUpdate = {}
  weeklyDetailsTableRef.value.clearAndSearch()
}

const viewWeeklyChart = () => {
  pageCtl.loading.weeklyChart = true
  pageCtl.chartsWeeklyVisible = true
  $axios({
    method: 'post',
    url: '/customer/otds/query_otds_weekly_chart',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.weeklyChart = body.chart1
    pageCtl.weeklyChart2 = body.chart2
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.weeklyChart = false
  })
}

const viewTipsDescript = () => {
  $viewDetails({
    title: 'View RCA / Tips Description',
    url: '/customer/otds/query_otds_rca_tips_list',
    columns: [
      { data: 'RCA_TIPS_CODE', width: 100 },
      { data: 'DESCRIPTION', width: 200 },
      { data: 'COMPUTING_LOGIC', width: 300 }
    ]
  })
}

const saveDailyDetails = () => {
  if (dailyDetailsTableRef.value.getLoading() === true) {
    return
  }
  if (JSON.stringify(pageCtl.dailyDetailsUpdate) === '{}') {
    $message.error('No changes detected')
    return
  }
  dailyDetailsTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/otds/save_otds_daily_details',
    data: {
      dailyDetailsUpdate: pageCtl.dailyDetailsUpdate
    }
  }).then(() => {
    $message.success('RCA code(s) saved')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.dailyDetailsUpdate = {}
    dailyDetailsTableRef.value.setLoading(false)
  })
}

const pageCtl = reactive({
  pageWidth: document.documentElement.clientWidth * 0.6 - 10,
  dailyRange: 'This Week',
  dailyDetailsTitle: '',
  weeklyDetailsTitle: '',
  weeklyChartTitle: '',
  totalType: 'Total',
  loading: {
    weeklyChart: false,
    dailyChart: false
  },
  conditions: {
    $scpFilter: {
      cascader: [
        ['ORDER_TYPE', 'KA'],
        ['ORDER_TYPE', 'KL'],
        ['ORDER_TYPE', 'TA'],
        ['ORDER_TYPE', 'KB'],
        ['ORDER_TYPE', 'FD'],
        ['ORDER_TYPE', 'OR'],
        ['ORDER_TYPE', 'YDP'],
        ['ORDER_TYPE', 'ZDP'],
        ['ORDER_TYPE', 'ZFD'],
        ['ORDER_TYPE', 'ZN'],
        ['ORDER_TYPE', 'ZNDP'],
        ['ORDER_TYPE', 'ZOR'],
        ['ORDER_TYPE', 'ZORN'],
        ['ORDER_TYPE', 'ZPOR'],
        ['CUSTOMER_GROUP', 'BA'],
        ['CUSTOMER_GROUP', 'BK'],
        ['CUSTOMER_GROUP', 'C1'],
        ['CUSTOMER_GROUP', 'C2'],
        ['CUSTOMER_GROUP', 'C3'],
        ['CUSTOMER_GROUP', 'C4'],
        ['CUSTOMER_GROUP', 'C5'],
        ['CUSTOMER_GROUP', 'C6'],
        ['CUSTOMER_GROUP', 'CS'],
        ['CUSTOMER_GROUP', 'D1'],
        ['CUSTOMER_GROUP', 'D2'],
        ['CUSTOMER_GROUP', 'D3'],
        ['CUSTOMER_GROUP', 'D4'],
        ['CUSTOMER_GROUP', 'D5'],
        ['CUSTOMER_GROUP', 'D6'],
        ['CUSTOMER_GROUP', 'D7'],
        ['CUSTOMER_GROUP', 'D8'],
        ['CUSTOMER_GROUP', 'D9'],
        ['CUSTOMER_GROUP', 'DS'],
        ['CUSTOMER_GROUP', 'E1'],
        ['CUSTOMER_GROUP', 'EC'],
        ['CUSTOMER_GROUP', 'EM'],
        ['CUSTOMER_GROUP', 'EP'],
        ['CUSTOMER_GROUP', 'EU'],
        ['CUSTOMER_GROUP', 'G1'],
        ['CUSTOMER_GROUP', 'G2'],
        ['CUSTOMER_GROUP', 'G3'],
        ['CUSTOMER_GROUP', 'G4'],
        ['CUSTOMER_GROUP', 'G5'],
        ['CUSTOMER_GROUP', 'G6'],
        ['CUSTOMER_GROUP', 'G7'],
        ['CUSTOMER_GROUP', 'GE'],
        ['CUSTOMER_GROUP', 'GR'],
        ['CUSTOMER_GROUP', 'HT'],
        ['CUSTOMER_GROUP', 'I1'],
        ['CUSTOMER_GROUP', 'I2'],
        ['CUSTOMER_GROUP', 'I3'],
        ['CUSTOMER_GROUP', 'I4'],
        ['CUSTOMER_GROUP', 'I5'],
        ['CUSTOMER_GROUP', 'I6'],
        ['CUSTOMER_GROUP', 'I7'],
        ['CUSTOMER_GROUP', 'I8'],
        ['CUSTOMER_GROUP', 'IT'],
        ['CUSTOMER_GROUP', 'KA'],
        ['CUSTOMER_GROUP', 'KI'],
        ['CUSTOMER_GROUP', 'MA'],
        ['CUSTOMER_GROUP', 'MP'],
        ['CUSTOMER_GROUP', 'OG'],
        ['CUSTOMER_GROUP', 'OH'],
        ['CUSTOMER_GROUP', 'OS'],
        ['CUSTOMER_GROUP', 'OT'],
        ['CUSTOMER_GROUP', 'SU'],
        ['CUSTOMER_GROUP', 'T1'],
        ['CUSTOMER_GROUP', 'T2'],
        ['CUSTOMER_GROUP', 'T3'],
        ['CUSTOMER_GROUP', 'U1'],
        ['CUSTOMER_GROUP', 'U2'],
        ['CUSTOMER_GROUP', 'U3'],
        ['CUSTOMER_GROUP', 'U4'],
        ['CUSTOMER_GROUP', 'U5'],
        ['CUSTOMER_GROUP', 'V1'],
        ['CUSTOMER_GROUP', 'W1'],
        ['CUSTOMER_GROUP', 'W2'],
        ['CUSTOMER_GROUP', 'W3'],
        ['CUSTOMER_GROUP', 'X1'],
        ['CUSTOMER_GROUP', 'X2'],
        ['CUSTOMER_GROUP', 'X3'],
        ['CUSTOMER_GROUP', 'X4'],
        ['CUSTOMER_GROUP', 'X5'],
        ['CUSTOMER_GROUP', 'X6'],
        ['CUSTOMER_GROUP', 'X7'],
        ['CUSTOMER_GROUP', 'X8'],
        ['CUSTOMER_GROUP', 'Z1'],
        ['CUSTOMER_GROUP', 'Z2'],
        ['CUSTOMER_GROUP', 'Z3'],
        ['CUSTOMER_GROUP', 'Z4'],
        ['CUSTOMER_GROUP', 'Z5'],
        ['CUSTOMER_GROUP', 'Z6'],
        ['PLANT_CODE', 'AX02'],
        ['PLANT_CODE', 'E001'],
        ['PLANT_CODE', 'I001'],
        ['PLANT_CODE', 'I003'],
        ['PLANT_CODE', 'M001'],
        ['PLANT_CODE', 'N001'],
        ['PLANT_CODE', 'N005'],
        ['PLANT_CODE', 'N006'],
        ['PLANT_CODE', 'O001'],
        ['PLANT_CODE', 'R001'],
        ['PLANT_CODE', 'X001'],
        ['PLANT_CODE', 'XA01'],
        ['PLANT_CODE', 'SP01'],
        ['PLANT_CODE', 'SP02'],
        ['DIVISION', '01'],
        ['DIVISION', '02'],
        ['DIVISION', '03'],
        ['DIVISION', '04'],
        ['DIVISION', '05'],
        ['DIVISION', '06'],
        ['DIVISION', '07'],
        ['DIVISION', '08'],
        ['DIVISION', '09'],
        ['DIVISION', '10'],
        ['DIVISION', '11'],
        ['DIVISION', '12'],
        ['DIVISION', '15'],
        ['DIVISION', 'OT'],
        ['DIVISION', 'P1']
      ],
      filter: []
    },
    reportOrderBy: 'PERCENTAGE',
    field: ['ENTITY'],
    dateRange: [] as any,
    selectedField: [],
    selectedDate: [] as any,
    selectedType: '',
    selectedValue: '',
    warningValue: 97
  },
  monthNames: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OTC', 'NOV', 'DEC'],
  selectedYears: [],
  selectedMonths: [],
  selectedWeeks: [],
  selectedWeeksAsc: [],
  selectedDays: [],
  stripName: '',
  stripedClass: '',
  timer: null as any,
  contextDailyMenuItems: {
    view_details: {
      name: 'View details',
      disabled: () => {
        return pageCtl.conditions.selectedType === 'On Time' || !pageCtl.conditions.selectedValue
      },
      callback: viewDailyDetails
    },
    view_charts: {
      name: 'View chart',
      callback: viewDailyChart
    },
    view_split0: { name: '---------' }
  },
  contextDailyDetailsMenuItems: {
    save_rca: {
      name: '<b>Save RCA</b>',
      callback: saveDailyDetails
    },
    view_split0: { name: '---------' }
  },
  contextWeeklyMenuItems: {
    view_derails: {
      name: 'View details',
      disabled: () => {
        return pageCtl.conditions.selectedType === 'On Time' || !pageCtl.conditions.selectedValue
      },
      callback: viewWeeklyDetails
    },
    view_charts: {
      name: 'View chart',
      callback: viewWeeklyChart
    },
    view_summary: {
      name: 'View summary',
      callback: viewWeeklySummary
    },
    view_split0: { name: '---------' }
  },
  contextWeeklyDetailsMenuItems: {
    save_rca: {
      name: '<b>Save RCA</b>',
      callback: saveWeeklyDetails
    },
    view_split0: { name: '---------' }
  },
  detailsDailyVisible: false,
  detailsWeeklyVisible: false,
  chartsDailyVisible: false,
  chartsWeeklyVisible: false,
  summaryWeeklyVisible: false,
  detailsColumns: [{}],
  dailyData: [] as any,
  dailyChart: [] as any,
  weeklyChart: [] as any,
  weeklyChart2: [] as any,
  monthlyData: [],
  chartWidth: '100%',
  chartHeight: '100%',
  otdsTipsColumns: [
    {
      data: 'RCA_TIPS_CODE'
    }, {
      data: 'RECOM_RCA_CODE'
    }, {
      data: 'PRIORITY'
    }, {
      data: 'DESCRIPTION'
    }, {
      data: 'COMPUTING_LOGIC'
    }
  ],
  dailyDetailsUpdate: {},
  weeklyDetailsUpdate: {},
  weeklyDetailsUpdate2: {},
  rcaTips: {},
  rcaCode: [],
  summarySelectedWeeks: [],
  summaryDisplayType: 'Lite',
  weeklyChartDisplayType: 'Line'
})

watch(() => pageCtl.conditions.warningValue, () => {
  delayer(() => {
    otdsWeeklyTableRef.value.redrawTable()
    otdsDailyTableRef.value.redrawTable()
  })
})

watch(() => pageCtl.conditions.dateRange, (newVal, oldVal) => {
  if (oldVal.length > 0) {
    search()
  }
})

const _field = computed(() => {
  if (pageCtl.conditions.field.length > 0) {
    return pageCtl.conditions.field
  } else {
    return ['ENTITY']
  }
})

const _dailyChartSummary = computed(() => {
  const temp = {}
  for (let i = 0; i < pageCtl.dailyChart.length; i++) {
    let key = pageCtl.dailyChart[i].name
    key = key.replace(/_/g, ' ')
    key = key.split(' ')[0]
    const arr = temp[key] || []
    arr.push(pageCtl.dailyChart[i].value)
    temp[key] = arr
  }
  const result = [] as any
  for (const k in temp) {
    if (temp.hasOwnProperty(k)) {
      const arr = temp[k]
      let sum = 0
      for (let i = 0; i < arr.length; i++) {
        sum += arr[i]
      }
      result.push({ name: k, value: sum })
    }
  }
  return result
})

const _weeklyChartSummary = computed(() => {
  const temp = {}
  for (let i = 0; i < pageCtl.weeklyChart.length; i++) {
    let key = pageCtl.weeklyChart[i].name
    key = key.replace(/_/g, ' ')
    key = key.split(' ')[0]
    const arr = temp[key] || []
    arr.push(pageCtl.weeklyChart[i].value)
    temp[key] = arr
  }
  const result = [] as any
  for (const k in temp) {
    if (temp.hasOwnProperty(k)) {
      const arr = temp[k]
      let sum = 0
      for (let i = 0; i < arr.length; i++) {
        sum += arr[i]
      }
      result.push({ name: k, value: sum })
    }
  }
  return result
})

const _weeklyChartSummary2 = computed(() => {
  const temp = {}
  for (let i = 0; i < pageCtl.weeklyChart2.length; i++) {
    let key = pageCtl.weeklyChart2[i].name
    key = key.replace(/_/g, ' ')
    key = key.split(' ')[0]
    const arr = temp[key] || []
    arr.push(pageCtl.weeklyChart2[i].value)
    temp[key] = arr
  }
  const result = [] as any
  for (const k in temp) {
    if (temp.hasOwnProperty(k)) {
      const arr = temp[k]
      let sum = 0
      for (let i = 0; i < arr.length; i++) {
        sum += arr[i]
      }
      result.push({ name: k, value: sum })
    }
  }
  return result
})

const _dailyOpt = computed(() => {
  return {
    title: [{
      text: 'On-Time trends',
      left: '25%',
      textAlign: 'center'
    }, {
      text: 'RCA layout',
      subtext: pageCtl.conditions.selectedDate,
      left: '80%',
      textAlign: 'center'
    }],
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        if (params.componentSubType === 'line') {
          const sourceData = otdsDailyTableRef.value.getData()
          const selectData = sourceData.filter(e => {
            let r = true
            for (let i = 0; i < _field.value.length; i++) {
              if (e[_field.value[i]] !== pageCtl.conditions.selectedField[i]) {
                r = false
              }
            }
            return r
          })

          let ontime = 0
          let delay = 0

          for (const i in selectData) {
            if (selectData.hasOwnProperty(i)) {
              if (selectData[i].TYPE === 'On Time') {
                ontime = selectData[i][params.name]
              } else if (selectData[i].TYPE === 'Delay') {
                delay = selectData[i][params.name]
              }
            }
          }

          // ontime
          tip.push('<div style="width:6rem;">')
          tip.push('On Time: ' + '<span style="float:right">' + ontime + '<span>')
          tip.push('</div>')
          // delay
          tip.push('<div style="width:6rem;">')
          tip.push('Delay: ' + '<span style="float:right">' + delay + '<span>')
          tip.push('</div>')
          // ratio
          tip.push('<div style="width:6rem;">')
          tip.push('Ratio: <span style="float:right">' + params.value + '%</span>')
          tip.push('</div>')
        } else if (params.componentSubType === 'pie') {
          tip.push(': ' + params.value + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + params.percent.toFixed(1) + '%')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: [{
      top: 50,
      width: '60%',
      bottom: 0,
      left: 10,
      containLabel: true
    }],
    xAxis: [{
      type: 'category',
      data: _selectedDailyRatio.value.x
    }],
    yAxis: [{
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      }
    }],
    visualMap: {
      show: false,
      dimension: 1,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.conditions.warningValue,
        color: '#c12e34'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    },
    series: [{
      type: 'line',
      smooth: false,
      label: {
        position: 'top',
        show: true
      },
      data: _selectedDailyRatio.value.y,
      markLine: {
        symbol: 'none',
        lineStyle: {
          type: 'solid'
        },
        label: {
          position: 'start', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
          formatter: '{c}',
          fontSize: 10
        },
        data: [{
          yAxis: pageCtl.conditions.warningValue
        }]
      }
    }, {
      type: 'pie',
      selectedMode: 'single',
      radius: [0, '40%'],
      center: ['80%', '55%'],
      label: {
        position: 'inner'
      },
      data: _dailyChartSummary.value
    }, {
      type: 'pie',
      radius: ['50%', '70%'],
      center: ['80%', '55%'],
      data: pageCtl.dailyChart
    }]
  }
})

const _weeklyLineOpt = computed(() => {
  const markLine = [] as any
  markLine.push({
    name: 'Target',
    yAxis: pageCtl.conditions.warningValue
  })

  const ytd: any = _selectedWeeklyRatio.value.ytd
  for (let i = 0; i < ytd.length; i++) {
    markLine.push({
      name: ytd[i].name,
      yAxis: ytd[i].value * 100 || 0
    })
  }

  return {
    title: {
      text: 'On-Time trends'
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker

        const tip = [] as any
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        const sourceData = otdsWeeklyTableRef.value.getData()
        const selectData = sourceData.filter(e => {
          let r = true
          for (let i = 0; i < _field.value.length; i++) {
            if (e[_field.value[i]] !== pageCtl.conditions.selectedField[i]) {
              r = false
            }
          }
          return r
        })

        let ontime = 0
        let delay = 0

        for (const i in selectData) {
          if (selectData.hasOwnProperty(i)) {
            if (selectData[i].TYPE === 'On Time') {
              ontime = selectData[i][params.name]
            } else if (selectData[i].TYPE === 'Delay') {
              delay = selectData[i][params.name]
            }
          }
        }

        // ontime
        tip.push('<div style="width:6rem;">')
        tip.push('On Time: ' + '<span style="float:right">' + ontime + '<span>')
        tip.push('</div>')
        // delay
        tip.push('<div style="width:6rem;">')
        tip.push('Delay: ' + '<span style="float:right">' + delay + '<span>')
        tip.push('</div>')
        // ratio
        tip.push('<div style="width:6rem;">')
        tip.push('Ratio: <span style="float:right">' + params.value + '%</span>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: [{
      top: 50,
      bottom: 0,
      left: 20,
      right: 20,
      containLabel: true
    }],
    xAxis: [{
      type: 'category',
      data: _selectedWeeklyRatio.value.x
    }],
    yAxis: [{
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      }
    }],
    visualMap: {
      show: false,
      dimension: 1,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.conditions.warningValue,
        color: '#c12e34'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    },
    series: [{
      type: 'line',
      smooth: false,
      label: {
        position: 'top',
        show: true
      },
      data: _selectedWeeklyRatio.value.y,
      markLine: {
        symbol: 'none',
        lineStyle: {
          type: 'solid'
        },
        label: {
          position: 'insideEndTop', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
          formatter: '{b} - {c}',
          fontSize: 10
        },
        data: markLine
      }
    }]
  }
})

const _weeklyPieOpt = computed(() => {
  return {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker

        const tip = [] as any
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push(': ' + params.value + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + params.percent.toFixed(1) + '%')
        tip.push('</div>')
        if (params.name.length > 3) {
          tip.push('<div style="max-width: 15rem;word-break: break-all;word-wrap:break-word;white-space: normal">')
          tip.push('- ' + (pageCtl.rcaTips[params.name] || 'No description for [' + params.name + ']').replace('\r\n', '<br>- '))
          tip.push('</div>')
        }
        return tip.join('')
      }
    },
    legend: {
      data: ['Summay', 'Detail'],
      y: 'top',
      x: '80%'
    },
    series: [{
      type: 'pie',
      name: 'Summay',
      selectedMode: 'single',
      radius: [0, '40%'],
      center: ['50%', '55%'],
      label: {
        position: 'inner'
      },
      data: _weeklyChartSummary.value
    }, {
      type: 'pie',
      name: 'Detail',
      radius: ['50%', '70%'],
      center: ['50%', '55%'],
      data: pageCtl.weeklyChart
    }]
  }
})

const _weeklyPie2Opt = computed(() => {
  return {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker

        const tip = [] as any
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push(': ' + params.value + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + params.percent.toFixed(1) + '%')
        if (params.name === '[Blank]') {
          tip.push('<div>')
          tip.push('- (⌯꒪꒫꒪)੭ Tonny Rong would explain why [Blank] happend.')
          tip.push('</div>')
        } else if (params.name.length > 3) {
          tip.push('<div style="max-width: 15rem;word-break: break-all;word-wrap:break-word;white-space: normal">')
          tip.push('- ' + (pageCtl.rcaTips[params.name] || 'No description for [' + params.name + ']'))
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: {
      data: ['Summay', 'Detail'],
      y: 'top',
      x: '80%'
    },
    series: [{
      type: 'pie',
      name: 'Summay',
      selectedMode: 'single',
      radius: [0, '40%'],
      center: ['50%', '55%'],
      label: {
        position: 'inner'
      },
      data: _weeklyChartSummary2.value
    }, {
      type: 'pie',
      name: 'Detail',
      radius: ['50%', '70%'],
      center: ['50%', '55%'],
      data: pageCtl.weeklyChart2
    }]
  }
})

const _otdsWeeklyColumns = computed(() => {
  const columns = [] as any

  for (let i = 0; i < _field.value.length; i++) {
    const name = _field.value[i]
    columns.push({
      title: name,
      data: name,
      render: renderTableStriped
    })
  }

  for (let i = 0; i < pageCtl.selectedYears.length; i++) {
    columns.push({
      title: pageCtl.selectedYears[i],
      data: pageCtl.selectedYears[i],
      render: renderRatioWeekly
    })
  }

  for (let i = 0; i < pageCtl.selectedMonths.length; i++) {
    columns.push({
      title: $firstCharUpperCase(pageCtl.selectedMonths[i]),
      data: pageCtl.selectedMonths[i],
      render: renderRatioWeekly
    })
  }

  for (let i = 0; i < pageCtl.selectedWeeks.length; i++) {
    columns.push({
      title: pageCtl.selectedWeeks[i],
      data: pageCtl.selectedWeeks[i],
      render: renderRatioWeekly
    })
  }
  return columns
})

const _otdsDailyColumns = computed(() => {
  const columns = [] as any

  for (let i = 0; i < _field.value.length; i++) {
    const name = _field.value[i]
    columns.push({
      title: name,
      data: name,
      render: renderTableStriped
    })
  }

  if (pageCtl.selectedDays.length !== 21) {
    return columns
  }
  let start = 0
  let avgPrefix = ''
  switch (pageCtl.dailyRange) {
    case 'This Week':
      start = 7
      avgPrefix = '_THIS'
      break
    case 'Last Week':
      start = 0
      avgPrefix = '_LAST'
      break
    case 'Next Week':
      start = 14
      avgPrefix = '_NEXT'
      break
  }
  for (let i = start; i < start + 7; i++) {
    columns.push({
      title: pageCtl.selectedDays[i],
      data: pageCtl.selectedDays[i],
      render: renderRatioDaily
    })
  }
  if (pageCtl.totalType === 'Total') {
    columns.push({
      title: 'Total',
      data: 'TOTAL' + avgPrefix,
      render: renderRatioDaily
    })
  } else {
    columns.push({
      title: 'WTD',
      data: 'WTD',
      render: renderRatioDaily
    })
  }
  return columns
})

watch(() => pageCtl.conditions.selectedDate, () => {
  let start = 0
  switch (pageCtl.dailyRange) {
    case 'This Week':
      start = 7
      break
    case 'Last Week':
      start = 0
      break
    case 'Next Week':
      start = 14
      break
  }
  pageCtl.dailyData = []
  for (let i = start; i < start + 7; i++) {
    pageCtl.dailyData.push(pageCtl.selectedDays[i])
  }
})

const _weeklyNestedHeaders = computed(() => {
  return [
    [
      {
        label: '',
        colspan: _field.value.length
      },
      {
        label: 'Monthly',
        colspan: pageCtl.selectedMonths.length + pageCtl.selectedYears.length
      }, {
        label: 'Weekly',
        colspan: pageCtl.selectedWeeks.length
      }
    ]
  ]
})

const _dailyNestedHeaders = computed(() => {
  return [
    [
      {
        label: '',
        colspan: _field.value.length
      }, {
        label: 'Daily',
        colspan: 7
      }
    ]
  ]
})

const _selectedDailyRatio = computed(() => {
  const table = otdsDailyTableRef.value
  let data = []
  if (table) {
    data = table.getData()
  }
  const es = data.filter((e: any) => {
    let r = true
    for (let i = 0; i < _field.value.length; i++) {
      if (e[_field.value[i]] !== pageCtl.conditions.selectedField[i]) {
        r = false
      }
    }
    return r && e.TYPE === 'RATIO'
  })

  let e = {}
  if (es && es.length > 0) {
    e = es[0]
  }
  const x = [] as any
  const y = [] as any
  for (let i = 0; i < pageCtl.dailyData.length; i++) {
    let v = e[pageCtl.dailyData[i]]
    if (v !== undefined && v !== null) {
      v = (v * 100).toFixed(1)
      x.push(pageCtl.dailyData[i])
      y.push(v)
    }
  }
  return {
    x,
    y
  }
})

const _selectedWeeklyRatio = computed(() => {
  const table = otdsWeeklyTableRef.value
  let data = []
  if (table) {
    data = table.getData()
  }
  const es = data.filter((e: any) => {
    let r = true
    for (let i = 0; i < _field.value.length; i++) {
      if (e[_field.value[i]] !== pageCtl.conditions.selectedField[i]) {
        r = false
      }
    }
    return r && e.TYPE === 'RATIO'
  })
  let e = {}
  if (es && es.length > 0) {
    e = es[0]
  }
  const y = [] as any
  const x = [] as any
  const ytd = [] as any
  for (let i = 0; i < pageCtl.selectedWeeksAsc.length; i++) {
    let v: any = e[pageCtl.selectedWeeksAsc[i]]
    if (v !== undefined && v !== null) {
      v = (v * 100).toFixed(1)
      x.push(pageCtl.selectedWeeksAsc[i])
      y.push(v)
    }
  }

  for (let i = 0; i < pageCtl.selectedYears.length; i++) {
    ytd.push({
      name: pageCtl.selectedYears[i],
      value: e[pageCtl.selectedYears[i]]
    })
  }

  return {
    x,
    y,
    ytd
  }
})

const _otdsWeeklySummaryNestedHeaders = computed(() => {
  if (pageCtl.summaryDisplayType === 'Full') {
    const headers: any = ['']
    const weeks = pageCtl.summarySelectedWeeks.slice()
    weeks.sort((e1, e2) => e1 > e2 ? -1 : 1)
    for (let i = 0; i < weeks.length; i++) {
      headers.push({
        label: weeks[i],
        colspan: 3
      })
    }
    return [headers]
  } else {
    return []
  }
})

const _otdsWeeklySummaryColumns = computed(() => {
  const columns = [] as any

  for (let i = 0; i < _field.value.length; i++) {
    const name = _field.value[i]
    columns.push({
      title: name,
      data: name,
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  const weeks = pageCtl.summarySelectedWeeks.slice()
  weeks.sort((e1, e2) => e1 > e2 ? -1 : 1)
  for (let i = 0; i < weeks.length; i++) {
    if (pageCtl.summaryDisplayType === 'Full') {
      columns.push({
        title: '%',
        data: '\'' + weeks[i] + '\'_RATIO',
        render: renderRatioWeeklySummary
      }, {
        title: 'CFMD',
        data: '\'' + weeks[i] + '\'_CONFIRMED',
        render: renderRatioWeeklySummary
      }, {
        title: 'TOT',
        data: '\'' + weeks[i] + '\'_FAIL',
        render: renderRatioWeeklySummary
      })
    } else {
      columns.push({
        title: weeks[i],
        data: '\'' + weeks[i] + '\'_RATIO',
        render: renderRatioWeeklySummary
      })
    }
  }
  return columns
})

const _dailyDetailsColumn = computed(() => {
  return [{
    title: 'RCA Result',
    data: 'RCA_RESULT',
    type: 'autocomplete',
    source: (query, process) => {
      if (query) {
        process(pageCtl.rcaCode.filter((e: any) => e.toLowerCase().indexOf(query.toLowerCase()) !== -1))
      } else {
        process(pageCtl.rcaCode)
      }
    },
    render: (hotInstance, td, row, column, prop, value) => {
      let tips = ''
      if (value) {
        tips = '- 【' + value + '】 ' + pageCtl.rcaTips[value]
      }

      if (tips) {
        let html = '<div title="' + tips + '">'
        html += value
        html += '</div>'
        td.innerHTML = html
      } else {
        td.innerHTML = value
      }
    }
  }, {
    title: 'Comments',
    data: 'RCA_COMMENTS'
  },
  /** 暂时取消RCA_Tips
     {
     title: 'RCA Tips',
     data: 'RCA_TIPS',
     render: (hotInstance, td, row, column, prop, value) => {
     const r = hotInstance.getSourceDataAtRow(row)
     const tips = [] as any
     if (value) {
     const vs = value.split(',')
     for (let i = 0; i < vs.length; i++) {
     const v = vs[i]
     if (pageCtl.rcaTips[v]) {
     tips.push('- 【' + v + '】 ' + pageCtl.rcaTips[v])
     }
     }
     }

     if (r.RCA_REMARK) {
     tips.push('- 【Remark】 ' + r.RCA_REMARK)
     }

     if (tips.length > 0) {
     let html = '<div title="#content#">'
     html += value
     html += '</div>'
     td.innerHTML = html.replace('#content#', tips.join('&#13;'))
     } else {
     td.innerHTML = value
     }
     }
     }**/
  { data: 'ONTIME' },
  { data: 'SALES_ORDER_NUMBER' },
  { data: 'SALES_ORDER_ITEM' },
  { data: 'MATERIAL' },
  { data: 'PLANT_CODE' },
  { data: 'DELIVERY_PRIORITY' },
  { data: 'FULFILL_OR_NOT_NONBLOCK' },
  { data: 'SHIP_TO_REGION' },
  { data: 'LT_RANGE' },
  { data: 'LOCAL_PRODUCT_LINE' },
  { data: 'VENDOR_NAME' },
  { data: 'FULFILL_OR_NOT_UNRESTRICT' },
  { data: 'MATERIAL_OWNER_SESA' },
  { data: 'LOCAL_PRODUCT_FAMILY' },
  { data: 'LOCAL_PRODUCT_SUBFAMILY' },
  { data: 'GRA_TYPE' },
  { data: 'SHIP_TO_CITY' },
  { data: 'SHIP_TO_SHORT_NAME' },
  { data: 'SHIP_TO_FULL_NAME' },
  { data: 'SHIP_TO_PARENT_NAME' },
  { data: 'SHIP_TO_PARENT_CODE' },
  { data: 'STOCKING_POLICY' },
  { data: 'PRODUCT_LINE' },
  { data: 'CLUSTER_NAME' },
  { data: 'SOLD_TO_REGION' },
  { data: 'SOLD_TO_SHORT_NAME' },
  { data: 'SOLD_TO_FULL_NAME' },
  { data: 'SOLD_TO_PARENT_NAME' },
  { data: 'SOLD_TO_PARENT_CODE' },
  { data: 'SALES_ORGANIZATION' },
  { data: 'ENTITY' },
  { data: 'AVAILABILITY_CHECK' },
  { data: 'ACTIVENESS' },
  { data: 'BU' },
  { data: 'GRA_METHOD' },
  { data: 'GRA_EVENT' },
  { data: 'SHIP_TO_COUNTRY' },
  { data: 'MATERIAL_OWNER_NAME' },
  { data: 'PLANT_TYPE' },
  { data: 'SO_CREATED_DATE' },
  { data: 'SOURCE_CATEGORY' },
  { data: 'MRP_CONTROLLER' },
  { data: 'LOCAL_BU' },
  { data: 'OTDS_DAILY_CRD' },
  { data: 'ORDER_TYPE' },
  { data: 'CUSTOMER_GROUP' },
  { data: 'VIP_SO_INDICATOR' },
  { data: 'DIVISION' },
  { data: 'OPEN_SO_QTY' }]
})

const _weeklyDetailsColumns = computed(() => {
  return [
    {
      title: 'RCA Result',
      data: 'RCA_RESULT',
      type: 'autocomplete',
      source: (query, process) => {
        if (query) {
          process(pageCtl.rcaCode.filter((e: any) => e.toLowerCase().indexOf(query.toLowerCase()) !== -1))
        } else {
          process(pageCtl.rcaCode)
        }
      },
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value) {
          tips = '- 【' + value + '】 ' + pageCtl.rcaTips[value]
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    }, {
      title: 'Comments',
      data: 'RCA_COMMENTS'
    }, {
      title: 'Recom.RCA Code',
      data: 'RECOM_RCA_CODE',
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value) {
          tips = '- 【' + value + '】 ' + pageCtl.rcaTips[value]
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    }, {
      title: 'RCA Tips',
      data: 'RCA_TIPS',
      render: (hotInstance, td, row, column, prop, value) => {
        const r = hotInstance.getSourceDataAtRow(row)
        const tips = [] as any
        if (value) {
          const vs = value.split(',')
          for (let i = 0; i < vs.length; i++) {
            const v = vs[i]
            if (pageCtl.rcaTips[v]) {
              tips.push('- 【' + v + '】 ' + pageCtl.rcaTips[v])
            }
          }
        }

        if (r.RCA_REMARK) {
          tips.push('- 【Remark】 ' + r.RCA_REMARK)
        }

        if (tips.length > 0) {
          let html = '<div title="#content#">'
          html += value
          html += '</div>'
          td.innerHTML = html.replace('#content#', tips.join('&#13;'))
        } else {
          td.innerHTML = value
        }
      }
    }, {
      data: 'ONTIME'
    }, {
      data: 'AC2_RANGE'
    }, {
      data: 'ACTIVENESS'
    }, {
      data: 'AVAILABILITY_CHECK'
    }, {
      data: 'BUSINESS_UNIT',
      title: 'BU'
    }, {
      data: 'CLUSTER_NAME'
    }, {
      data: 'CUSTOMER_CODE'
    }, {
      data: 'CUSTOMER_GROUP'
    }, {
      data: 'DELIVERY_PRIORITY'
    }, {
      data: 'ENTITY'
    }, {
      data: 'FULFILL_OR_NOT_NONBLOCK'
    }, {
      data: 'FULFILL_OR_NOT_UNRESTRICT'
    }, {
      data: 'GRA_STATUS'
    }, {
      data: 'GRA_TYPE'
    }, {
      data: 'IMPORT_VENDOR'
    }, {
      data: 'LOCAL_BUSINESS_UNIT'
    }, {
      data: 'LOCAL_PRODUCT_FAMILY'
    }, {
      data: 'LOCAL_PRODUCT_LINE'
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY'
    }, {
      data: 'LT_RANGE'
    }, {
      data: 'MATERIAL'
    }, {
      data: 'MATERIAL_OWNER_NAME'
    }, {
      data: 'MATERIAL_OWNER_SESA'
    }, {
      data: 'PRODUCT_LINE',
      title: 'MATERIAL_GROUP'
    }, {
      data: 'MRP_CONTROLLER'
    }, {
      data: 'ORDER_TYPE'
    }, {
      data: 'PLANT_CODE'
    }, {
      data: 'PLANT_NAME'
    }, {
      data: 'PLANT_TYPE'
    }, {
      data: 'PRODUCT_LINE'
    }, {
      data: 'REGION'
    }, {
      data: 'SALES_DISTRICT'
    }, {
      data: 'SALES_ORGANIZATION'
    }, {
      data: 'SHIP_TO'
    }, {
      data: 'SHIP_TO_CITY'
    }, {
      data: 'SHIP_TO_COUNTRY'
    }, {
      data: 'SHIP_TO_REGION'
    }, {
      data: 'SHIP_TO_SHORT_NAME'
    }, {
      data: 'SHIP_TO_FULL_NAME'
    }, {
      data: 'SHIP_TO_PARENT_NAME'
    }, {
      data: 'SHIP_TO_PARENT_CODE'
    }, {
      data: 'SHORTAGE_STATUS'
    }, {
      data: 'SOLD_TO'
    }, {
      data: 'SOLD_TO_REGION'
    }, {
      data: 'SOLD_TO_SHORT_NAME'
    }, {
      data: 'SOLD_TO_FULL_NAME'
    }, {
      data: 'SOLD_TO_PARENT_NAME'
    }, {
      data: 'SOLD_TO_PARENT_CODE'
    }, {
      data: 'SOURCE_CATEGORY'
    }, {
      data: 'STOCKING_POLICY'
    }, {
      data: 'STOCK_INDENT'
    }, {
      data: 'VENDOR_CODE'
    }, {
      data: 'VENDOR_NAME'
    }, {
      data: 'STOCK_INDENT',
      title: 'INDENT/STOCK'
    }, {
      data: 'SALES_ORDER_NUMBER',
      title: 'SALES_DOCUMENT'
    }, {
      data: 'SALES_ORDER_ITEM',
      title: 'ITEM'
    }, {
      data: 'ORDER_QTY',
      title: 'QTY_IN_BASE_UOM'
    }, {
      data: 'HIGHER_LEVEL_ITEM'
    }, {
      data: 'ITEM_CATEGORY'
    }, {
      data: 'SO04_CRD',
      title: 'CUSTOMER_REQUEST_DATE'
    }, {
      data: 'CREATED_DATE',
      title: 'SALES_ORDER_CREATE_DATE'
    }, {
      data: 'DELAY_DAYS'
    }, {
      data: 'REA_SOBLOCK',
      title: 'DELREA_SO_BLOCK'
    }, {
      data: 'REA_PUR',
      title: 'DELREA_PURCHASE'
    }, {
      data: 'REA_VENDOR',
      title: 'DELREA_VENDOR'
    }, {
      data: 'REA_INV',
      title: 'DELREA_INVENTORY'
    }, {
      data: 'REA_GRPAGE',
      title: 'DELREA_GRPAGE'
    }, {
      data: 'REA_DEL_CREN_BLOCK',
      title: 'DELREA_DEL_BLOCK_REASON'
    }, {
      data: 'REA_WAREHOUSE',
      title: 'DELREA_WAREHOUSE'
    }, {
      data: 'REA_DEL_CREAT',
      title: 'DELREA_DEL_CREATE'
    }, {
      data: 'REA_CUST_BLOCK',
      title: 'DELREA_CUSTOMER_BLOCK'
    }, {
      data: 'REA_FINANCE',
      title: 'DELREA_FINANCE'
    }, {
      data: 'REA_OTHER',
      title: 'DELREA_OTHER'
    }, {
      data: 'REA_CUSTOVERBLK',
      title: 'DELREA_CUSTOVERBLK'
    }, {
      data: 'REA_DEL_GIBLOCK',
      title: 'DELREA_DELIVERY_BLOCK'
    }, {
      data: 'DELIVERY_RELEASE_DATE'
    }, {
      data: 'SALES_ORDER_RELEASE'
    }, {
      data: 'FIRST_DELIVERY_DATE'
    }, {
      data: 'MATERIAL_AVAILIBALE_DATE'
    }, {
      data: 'CONFIRMED_DATE'
    }, {
      data: 'PURCHASE_GR_DATE'
    }, {
      data: 'DELIVERY_CREATE_DATE'
    }, {
      data: 'DELIVERY_PACKING_DATE'
    }, {
      data: 'DELIVERY_PICKING_DATE'
    }, {
      data: 'DELIVERY_GOODS_ISSUE_DATE'
    }, {
      data: 'MAT_PRC_GRP',
      title: 'MAT_PRICING_GROUP'
    }, {
      data: 'COMPLETE_DELIV_IND',
      title: 'COMPLETE_DELIVERY'
    }, {
      data: 'DEL_GROUP',
      title: 'DELIVERY_GROUP_ITEM'
    }, {
      data: 'PR_NUMBER',
      title: 'PURCHASE_REQUISITION'
    }, {
      data: 'MO_NUMBER',
      title: 'ORDER'
    }, {
      data: 'MO_CONFRIMED_FINISH_DATE'
    }, {
      data: 'PURCHASE_CREATE_DATE'
    }, {
      data: 'MO_SCHEDULE_FINISH_DATE'
    }, {
      data: 'PO_STATISTIC_DATE'
    }, {
      data: 'BASE_UNIT'
    }, {
      data: 'NUM_DOC',
      title: 'NUMBER_OF_DOCUMENTS'
    }, {
      data: 'INVOICED_QUANTITY'
    }, {
      data: 'INVOICED_AMOUNT'
    }, {
      data: 'GRA_EVENT_NAME'
    }, {
      data: 'GRA_BTN'
    }, {
      data: 'VIP_SO_INDICATOR'
    }
  ]
})

onMounted(() => {
  initPage()
  winHeight.value = document.documentElement.clientHeight - 200
  dailyChartRef.value.chart().on('dblclick', (param) => {
    pageCtl.conditions.selectedDate = [param.name]
    dailyChartRef.value.chart().showLoading({
      text: 'Loading...',
      textStyle: { fontSize: 30, color: '#444' },
      effectOption: { backgroundColor: 'rgba(0, 0, 0, 0)' }
    })
    pageCtl.dailyChart = []
    $axios({
      method: 'post',
      url: '/customer/otds/query_otds_daily_chart',
      data: pageCtl.conditions
    }).then((body) => {
      setTimeout(() => {
        pageCtl.dailyChart = body
        dailyChartRef.value.chart().hideLoading()
      }, 500)
    })
  })
})

const afterDailySelect = (r, rc, c, column) => {
  // disable select event when details windows shown
  if (pageCtl.detailsDailyVisible === true) {
    return
  }
  pageCtl.conditions.selectedValue = rc
  const selected = [] as any
  for (let i = 0; i < _field.value.length; i++) {
    selected.push(r[_field.value[i]])
  }
  pageCtl.conditions.selectedField = selected

  if (column >= _field.value.length && $startWith(c, 'TOTAL_') === false) {
    pageCtl.conditions.selectedDate = [c]
  } else {
    pageCtl.conditions.selectedDate = _otdsDailyColumns.value.filter((e) => $startWith(e.data, 'TOTAL_') === false && _field.value.indexOf(e.data) === -1).map(e => e.data)
  }
  pageCtl.conditions.selectedType = r.TYPE
  const title = pageCtl.conditions.selectedDate.length > 1 ? '(' + pageCtl.conditions.selectedDate.slice(1) + ')' : pageCtl.conditions.selectedDate[0]
  pageCtl.dailyDetailsTitle = 'View Daily Details - ' + $join(...selected) + '  ' + title + '  ' + (r.TYPE === 'RATIO' ? '(On Time,Delay)' : r.TYPE)
}

const afterDailyDetailsChange = (changes) => {
  if (changes) {
    const ht = dailyDetailsTableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'RCA_RESULT' || prop === 'RCA_COMMENTS') {
          const key = r.SALES_ORDER_NUMBER + '#' + r.SALES_ORDER_ITEM
          pageCtl.dailyDetailsUpdate[key] = { RCA_RESULT: r.RCA_RESULT, RCA_COMMENTS: r.RCA_COMMENTS }
        }
      }
    })
  }
}

const afterWeeklySelect = (r, rc, c, column) => {
  // disable select event when details windows shown
  if (pageCtl.detailsWeeklyVisible === true) {
    return
  }
  pageCtl.conditions.selectedValue = rc
  const selected = [] as any
  for (let i = 0; i < _field.value.length; i++) {
    selected.push(r[_field.value[i]])
  }
  pageCtl.conditions.selectedField = selected
  if (column >= _field.value.length) {
    pageCtl.conditions.selectedDate = c
  } else {
    pageCtl.conditions.selectedDate = '-1'
  }
  pageCtl.conditions.selectedType = r.TYPE
  const title = pageCtl.conditions.selectedDate === '-1' ? pageCtl.conditions.dateRange.join(' - ') : pageCtl.conditions.selectedDate
  pageCtl.weeklyDetailsTitle = 'View Weekly Details - ' + $join(...selected) + '  ' + title + '  ' + (r.TYPE === 'RATIO' ? '(On Time,Delay)' : r.TYPE)
  pageCtl.weeklyChartTitle = 'View Official Chart - ' + $join(...selected)
}

const afterWeeklyDetailsChange = (changes) => {
  if (changes) {
    const ht = weeklyDetailsTableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'RCA_RESULT' || prop === 'RCA_COMMENTS') {
          const key = r.SALES_ORDER_NUMBER + '#' + r.SALES_ORDER_ITEM
          pageCtl.weeklyDetailsUpdate[key] = { RCA_RESULT: r.RCA_RESULT, RCA_COMMENTS: r.RCA_COMMENTS }
        }
      }
    })
  }
}

const initPage = () => {
  initDatePicker()
  $axios({
    method: 'post',
    url: '/customer/otds/init_page'
  }).then((body) => {
    pageCtl.rcaCode = body.rcaCode
    pageCtl.rcaTips = body.rcaTips
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}

const initDatePicker = () => {
  let plus = new Date().getTime()
  if (new Date().getDate() < 7) {
    plus -= 86400000 * 7
  }
  const end = new Date(plus)
  const start = new Date(plus)
  end.setMonth(end.getMonth())
  let startMonth: any = start.getMonth() + 1
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  let endMonth: any = end.getMonth() + 1
  if (endMonth < 10) {
    endMonth = '0' + endMonth
  }
  pageCtl.conditions.dateRange = [start.getFullYear() + '/' + startMonth, end.getFullYear() + '/' + endMonth]
}

const search = () => {
  pageCtl.stripedClass = ''
  otdsWeeklyTableRef.value.setLoading(true)
  otdsDailyTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/otds/query_columns_by_daterange',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.selectedYears = body.years
    pageCtl.selectedMonths = body.months
    pageCtl.selectedWeeks = body.weeks
    pageCtl.summarySelectedWeeks = body.weeks

    if (pageCtl.selectedWeeks) {
      pageCtl.selectedWeeksAsc = []
      for (let i = pageCtl.selectedWeeks.length - 1; i >= 0; i--) {
        pageCtl.selectedWeeksAsc.push(pageCtl.selectedWeeks[i])
      }
    }

    pageCtl.selectedDays = body.days
    otdsWeeklyTableRef.value.search()
    otdsDailyTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

// 为ratio添加百分号
const renderRatioWeekly = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      if (value * 100 < pageCtl.conditions.warningValue) {
        td.style = 'color: var(--scp-text-color-error) !important'
      }
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }

    const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
    if (r && r.TYPE === 'Delay' && prop.indexOf('W') !== -1) {
      if (value === 0) {
        const style = 'background-image: linear-gradient(to right, #2c821d 100%, white 100%) !important;color: #fff !important;'
        td.title = '100%'
        td.style = style
      } else if (value) {
        const finish = r[prop + '_CONFIRMED'] || 0
        const percent = finish / value * 100
        const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
        const t = Math.min(percent + 2, 100)

        let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

        td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

        const tdWidth = td.offsetWidth // 单元格宽度
        const textWidth = $px2Rem((td.innerHTML + '').length * 8 + 4) // 文字宽度 = 文字个数 * 每个字符宽度8px + 左边距4px
        if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
          style += 'color: #fff !important;'
        }
        td.style = style
      }
    }
  }
}
const renderRatioDaily = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      if (value * 100 < pageCtl.conditions.warningValue) {
        td.style = 'color: var(--scp-text-color-error) !important'
      }
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }
  }
}
const renderRatioWeeklySummary = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  const failed = prop.replace('_RATIO', '_FAIL')
  if ($endWith(prop, '_RATIO')) {
    if (value === 0) {
      let style = 'background-image: linear-gradient(to right, #2c821d 100%, var(--scp-bg-color) 100%) !important;color: #fff !important;'
      td.title = '100%'
      if (r[failed]) {
        style = 'background-image: linear-gradient(to right, #c12e34 1%, var(--scp-bg-color) 1%) !important;'
        td.title = '0%'
      }

      td.style = style
      td.innerHTML = '0'
    } else if (value) {
      const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
      const percent = value
      const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
      const t = Math.min(percent + 2, 100)

      let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

      td.innerHTML = value + (pageCtl.summaryDisplayType === 'Full' ? '' : '%')
      const tdWidth = td.offsetWidth // 单元格宽度
      const textWidth = $px2Rem((td.innerHTML + '').length * 7 + 4) // 文字宽度 = 文字个数 * 每个字符宽度7px + 左边距5px
      if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
        style += 'color: #fff !important;'
      }
      td.style = style
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }
  }
  // 染色
  for (let i = 0; i < _field.value.length; i++) {
    if (r[_field.value[i]] === 'Total') {
      td.style.fontWeight = 'bold'
      break
    }
  }
}

// 表格添加斑马纹
const renderTableStriped = (hotInstance, td, row, column, prop, value) => {
  if (row % 3 === 0) {
    pageCtl.stripedClass = 'striped-even3-tr'
  } else {
    pageCtl.stripedClass = 'striped-odd3-tr'
  }

  if (td.parentNode) {
    td.parentNode.className = pageCtl.stripedClass
  }
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    td.innerHTML = value || ''
  } else {
    if (column === _field.value.length - 1) {
      td.innerHTML = r.TYPE || ''
    } else {
      td.innerHTML = ''
    }

    td.style.textAlign = 'right'
    if (r.TYPE === 'Delay') {
      td.style.color = 'var(--scp-text-color-error)'
    } else if (r.TYPE === 'On Time') {
      td.style.color = 'var(--scp-text-color-success)'
      td.style.fontWeight = '700'
    }
  }
}
const delayer = (action, delay = 600) => {
  if (pageCtl.timer) {
    clearTimeout(pageCtl.timer)
  }
  pageCtl.timer = setTimeout(() => {
    action.call()
  }, delay)
}
</script>

<style lang="scss">
#otds {
  .el-date-editor.el-input__wrapper {
    box-shadow: 1 0 0 1px var(--scp-border-color, var(--scp-border-color)) inset;
  }
}
</style>
