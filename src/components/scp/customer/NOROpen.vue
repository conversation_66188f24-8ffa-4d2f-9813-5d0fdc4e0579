<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-select
                v-model="pageCtl.norOpen.conditions.category"
                size="small" filterable clearable multiple collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.norOpen.conditions.$scpFilter" :cascader-base-opts="pageCtl.norOpen.filterOpts" :loading="pageCtl.norOpen.loading.filter"
                        :filter-base="['NOR_OPEN_V']" :after-apply="searchNorOpen"/>
          </el-col>
          <el-col :span="6">
            <el-date-picker
                size="small"
                v-model="pageCtl.norOpen.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="searchNorOpen" :data="pageCtl.norOpen.conditions" :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.norOpen.loading.filter">
              <scp-subscript id="CONA"/>
              <div class="front">
                <scp-table
                    :lazy="true"
                    :params="pageCtl.norOpen.conditions"
                    :max-height="450"
                    :after-select="afterNorOpenReport1Selected"
                    :pagging="false"
                    :pagging-setting-enable="false"
                    url="/customer/nor_open/query_report1"
                    download-url="/customer/nor_open/download_report1"
                    ref="norOpenReport1Ref"
                    :context-menu-items="pageCtl.norOpen.contextItems.report1"
                    :columns="pageCtl.norOpenColums"
                />
              </div>
            </div>
          </el-col>
        </el-row>
<!--        <el-row>-->
<!--          <el-col :span="8" v-loading="pageCtl.norOpen.loading.report2 || pageCtl.norOpen.loading.filter">-->
<!--            <div class="subscript-container subscript-container-left">-->
<!--              <scp-subscript id="CONQ" ref="norOpenReport2SubRef"/>-->
<!--              <el-row>-->
<!--                <el-col :span="4" class="report-sub-title-right" :style="{ transform: 'translateX(-75px) translateY(3px)' }">-->
<!--                  <el-button size="small" title="back to today" @click="norOpenReport2Back">-->
<!--                    <font-awesome-icon icon="fa-arrows-rotate"/>-->
<!--                  </el-button>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--              <chart-->
<!--                  ref="norOpenReport2Ref"-->
<!--                  :height="420"-->
<!--                  :option="_norOpenReport2Opt"/>-->
<!--            </div>-->
<!--          </el-col>-->
<!--          <el-col :span="16" v-loading="pageCtl.norOpen.loading.report3 || pageCtl.norOpen.loading.filter">-->
<!--            <div class="subscript-container subscript-container-right">-->
<!--              <scp-subscript id="CONF"/>-->
<!--              <el-row>-->
<!--                <el-col :span="4" class="report-sub-title-right" :style="{ transform: 'translateX(-175px) translateY(3px)' }">-->
<!--                  <el-select v-model="pageCtl.norOpen.conditions.report3DateType" size="small"-->
<!--                             @change="searchNorOpenReport3">-->
<!--                    <el-option label="By Week" value="BY_WEEK"/>-->
<!--                    <el-option label="By Month" value="BY_MONTH"/>-->
<!--                  </el-select>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--              <chart-->
<!--                  ref="norOpenReport3Ref"-->
<!--                  :height="310"-->
<!--                  :option="_norOpenReport3Opt"/>-->
<!--              <scp-table-->
<!--                  :lazy="true"-->
<!--                  :params="pageCtl.norOpen.conditions"-->
<!--                  :max-height="110"-->
<!--                  :pagging="false"-->
<!--                  :pagging-setting-enable="false"-->
<!--                  url="/customer/nor_open/query_report3_ratios"-->
<!--                  ref="norOpenReport3RatiosRef"-->
<!--                  :columns="pageCtl.norOpen.report3RatiosColumns"/>-->
<!--            </div>-->
<!--          </el-col>-->
<!--        </el-row>-->
        <el-row>
          <el-col :span="24" v-loading="pageCtl.norOpen.loading.report4 || pageCtl.norOpen.loading.filter">
            <div class="subscript-container">
              <scp-subscript id="CONE" ref="norOpenReport4SubRef"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.norOpen.conditions.report4Type" size="small">
                    <el-option label="Sales Order" value="so"/>
                    <el-option label="Material" value="material"/>
                  </el-select>
                </el-col>
                <el-col :span="5" v-show="pageCtl.norOpen.conditions.report4Type === 'so'">
                  <el-select v-model="pageCtl.norOpenReport4SOSelectedColumns" size="small" filterable clearable multiple collapse-tags>
                    <el-option
                        v-for="item in pageCtl.report4SOAvailableColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5" v-show="pageCtl.norOpen.conditions.report4Type === 'material'">
                  <el-select v-model="pageCtl.norOpenReport4MaterialSelectedColumns" size="small" filterable clearable multiple collapse-tags>
                    <el-option
                        v-for="item in pageCtl.report4MaterialAvailableColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <scp-table
                  :lazy="true"
                  :params="pageCtl.norOpen.conditions"
                  :max-height="450"
                  url="/customer/nor_open/query_report4"
                  download-url="/customer/nor_open/download_report4"
                  ref="norOpenReport4Ref"
                  :context-menu-items="pageCtl.norOpen.contextItems.report4"
                  :after-select="afterNorOpenReport4Selected"
                  :columns="_norOpenReport4Column"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <el-dialog
        v-model="pageCtl.showVisitPopup"
        title="Reminder: Phase Out"
        width="35%"
        class="custom-dialog"
    >
      <template #default>
        <div class="dialog-content">
          <p>The current page will be phased out on <b>June 30, 2024</b>. Please proceed to the <a @click="goToMenu('/demand/open_so_structure')" class="link">Open SO Structure</a> to inquire for nor information.</p>
          <img src="img/nor_open_pre.png">
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import store from '@/store'
import { useRouter } from 'vue-router'

const $dateFormatter: any = inject('$dateFormatter')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $axios: any = inject('$axios')
const $join: any = inject('$join')

// const $toolbox: any = inject('$echarts.toolbox')
// const $grid: any = inject('$echarts.grid')
// const $legend: any = inject('$echarts.legend')
const searchRef = ref()
// const norOpenReport3Ref = ref()
const norOpenReport1Ref = ref()
const norOpenReport4Ref = ref()
// const norOpenReport3RatiosRef = ref()

const $router = useRouter()

const viewOpenReport1Details = () => {
  $viewDetails({
    title: pageCtl.norOpen.report1DetailsTitle,
    url: '/customer/nor_open/query_report1_details',
    durl: '/customer/nor_open/download_report1_details',
    params: pageCtl.norOpen.conditions
  })
}

const viewOpenReport4Details = () => {
  $viewDetails({
    title: 'View Details',
    url: '/customer/nor_open/query_report4_details',
    durl: '/customer/nor_open/download_report4_details',
    params: pageCtl.norOpen.conditions
  })
}

const pageCtl = reactive({
  showVisitPopup: false,
  norOpen: {
    filterOpts: [],
    conditions: {
      $scpFilter: {
        cascader: [],
        filter: []
      },
      category: ['CLUSTER_NAME', 'ENTITY'],
      dateRange: [] as any,
      report1SelectedColumn: '',
      report1SelectedValue: [] as any,
      // report3SelectedValue: '',
      // report3DateType: 'BY_WEEK',
      report4SelectedValue: [null, null],
      report4Type: 'so'
    },
    loading: {
      filter: false,
      report1: false,
      // report2: false,
      // report3: false,
      report4: false
    },
    current: {
      category: 'ENTITY'
    } as any,
    contextItems: {
      report1: {
        view_details: {
          name: 'View details',
          callback: viewOpenReport1Details
        },
        view_split0: { name: '---------' }
      },
      report4: {
        view_details: {
          name: 'View details',
          callback: viewOpenReport4Details
        },
        view_split0: { name: '---------' }
      }
    },
    colorSettings: {
      RC_GE_11: '#c13033',
      RC_GE_6_LE_10: '#c8412d',
      RC_GE_4_LE_5: '#d25924',
      RC_EQ_3: '#e1a506',
      RC_EQ_2: '#87a40c',
      RC_EQ_1: '#629512'
    },
    nameSettings: {
      RC_GE_11: 'RC>=11',
      RC_GE_6_LE_10: 'RC=6~10',
      RC_GE_4_LE_5: 'RC=4~5',
      RC_EQ_3: 'RC=3',
      RC_EQ_2: 'RC=2',
      RC_EQ_1: 'RC=1'
    },
    report1DetailsTitle: ''
    // report2Data: [] as any,
    // report3Data: [] as any,
    // report3RatiosColumns: []
  },
  norOpenColums: [],
  report4SOAvailableColumns: ['GI_DATE', 'GI_WEEK', 'GI_MONTH', 'SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM', 'MATERIAL', 'RESCH_COUNTER', 'VENDOR_CODE', 'CREATED_DATE', 'CRD_DATE', 'PURCH_ORDER_NUMBER', 'PURCH_ORDER_ITEM', 'AC2_RANGE', 'ACTIVENESS', 'AVAILABILITY_CHECK', 'BLOCK_STATUS', 'BU', 'CLUSTER_NAME', 'CUSTOMER_CODE', 'DELIVERY_PRIORITY', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT', 'GRA_STATUS', 'GRA_TYPE', 'IMPORT_VENDOR', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'LT_RANGE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'ORDER_TYPE', 'PLANT_CODE', 'PLANT_NAME', 'PLANT_TYPE', 'PRODUCTION_LINE', 'PRODUCT_LINE', 'RESCH_GROUP', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_REGION', 'SHORTAGE_STATUS', 'SOLD_TO_REGION', 'SOURCE_CATEGORY', 'STOCKING_POLICY', 'UD_STATUS', 'VENDOR_NAME'],
  report4MaterialAvailableColumns: ['MATERIAL', 'RESCH_COUNTER', 'VENDOR_CODE', 'AC2_RANGE', 'ACTIVENESS', 'AVAILABILITY_CHECK', 'BLOCK_STATUS', 'BU', 'CLUSTER_NAME', 'CUSTOMER_CODE', 'DELIVERY_PRIORITY', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT', 'GRA_STATUS', 'GRA_TYPE', 'IMPORT_VENDOR', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'LT_RANGE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'ORDER_TYPE', 'PLANT_CODE', 'PLANT_NAME', 'PLANT_TYPE', 'PRODUCTION_LINE', 'PRODUCT_LINE', 'RESCH_GROUP', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_REGION', 'SHORTAGE_STATUS', 'SOLD_TO_REGION', 'SOURCE_CATEGORY', 'STOCKING_POLICY', 'UD_STATUS', 'VENDOR_NAME'],
  norOpenReport4SOSelectedColumns: ['GI_DATE', 'GI_WEEK', 'GI_MONTH', 'SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM',
    'MATERIAL', 'PLANT_CODE', 'RESCH_COUNTER', 'RESCH_GROUP', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'PRODUCT_LINE', 'ENTITY',
    'CLUSTER_NAME', 'BU', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY',
    'STOCKING_POLICY', 'VENDOR_CODE', 'VENDOR_NAME', 'ACTIVENESS', 'SOURCE_CATEGORY', 'CUSTOMER_CODE', 'SHIP_TO',
    'CREATED_DATE', 'CRD_DATE', 'AVAILABILITY_CHECK', 'PURCH_ORDER_NUMBER', 'PURCH_ORDER_ITEM'],
  norOpenReport4MaterialSelectedColumns: ['MATERIAL', 'PLANT_CODE', 'RESCH_COUNTER', 'RESCH_GROUP', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER',
    'PRODUCT_LINE', 'ENTITY', 'CLUSTER_NAME', 'BU', 'LOCAL_BU', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE',
    'LOCAL_PRODUCT_SUBFAMILY', 'STOCKING_POLICY', 'VENDOR_CODE', 'VENDOR_NAME', 'ACTIVENESS', 'SOURCE_CATEGORY']
})

onMounted(() => {
  // 判断浏览器是否为当天初次访问
  const currentDate = new Date().toISOString().slice(0, 10)
  const lastVisitDate = localStorage.getItem('lastVisitNorOpenDate')

  if (!lastVisitDate || lastVisitDate !== currentDate) {
    pageCtl.showVisitPopup = true
    localStorage.setItem('lastVisitNorOpenDate', currentDate)
    localStorage.setItem('hasVisitedNorOpenBefore', 'true')
  }
  pageCtl.report4SOAvailableColumns.sort()
  pageCtl.report4MaterialAvailableColumns.sort()
  pageCtl.norOpenReport4SOSelectedColumns.sort()
  pageCtl.norOpenReport4MaterialSelectedColumns.sort()
  initNorOpenFilter()
  // norOpenReport3Ref.value.chart().on('dblclick', 'series.line', (params) => {
  //   if (params.name != null && params.name.length > 5) {
  //     pageCtl.norOpen.conditions.report3SelectedValue = params.name
  //     searchNorOpenReport2()
  //   } else {
  //     pageCtl.norOpen.conditions.report3SelectedValue = ''
  //   }
  // })
})

const initNorOpenFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), now.getMonth() - 3, 1)
  pageCtl.norOpen.conditions.dateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]

  pageCtl.norOpen.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/nor_open/query_cascader'
  }).then((body) => {
    pageCtl.norOpen.filterOpts = body
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.norOpen.loading.filter = false
  })
}

const goToMenu = (url) => {
  nextTick(() => {
    if ($router.currentRoute.value.path !== url) {
      $router.push(url)
    }
  })
}

const searchNorOpen = () => {
  pageCtl.norOpen.conditions.report1SelectedValue = ''
  // pageCtl.norOpen.conditions.report3SelectedValue = ''
  pageCtl.norOpen.conditions.report4SelectedValue = []
  pageCtl.norOpen.current.category = pageCtl.norOpen.conditions.category
  pageCtl.norOpen.current.category = pageCtl.norOpen.conditions.category

  norOpenReport1Column()
  norOpenReport1Ref.value.search()
  norOpenReport4Ref.value.search()
  // searchNorOpenReport2()
  // searchNorOpenReport3()
  // searchNorOpenReport3Ratios()
}

const norOpenReport1Column = () => {
  const columns = [] as any
  for (let i = 0; i < _norOpenSelectedcategory.value.length; i++) {
    columns.push({
      data: _norOpenSelectedcategory.value[i],
      title: _norOpenSelectedcategory.value[i],
      render: renderRowTitle
    })
  }
  columns.push(
    { data: 'RC_EQ_1', title: 'RC=1', type: 'numeric' },
    { data: 'RC_EQ_2', title: 'RC=2', type: 'numeric' },
    { data: 'RC_EQ_3', title: 'RC=3', type: 'numeric' },
    { data: 'RC_GE_4_LE_5', title: 'RC=4~5', type: 'numeric' },
    { data: 'RC_GE_6_LE_10', title: 'RC=6~10', type: 'numeric' },
    { data: 'RC_GE_11', title: 'RC>=11', type: 'numeric' },
    { data: 'RC_TOTAL', title: 'Total No. of RC', type: 'numeric' },
    { data: 'RC_LINES', title: 'Total Lines of RC', type: 'numeric' },
    { data: 'RC_AVERAGE', title: 'AVG RC', type: 'numeric', render: renderRow }
  )
  pageCtl.norOpenColums = columns
}

// const searchNorOpenReport2 = () => {
//   pageCtl.norOpen.loading.report2 = true
//   $axios({
//     method: 'post',
//     url: '/customer/nor_open/query_report2',
//     data: pageCtl.norOpen.conditions
//   }).then((body) => {
//     pageCtl.norOpen.report2Data = [
//       { name: 'RC=1', value: body.RC_EQ_1, field: 'RC_EQ_1' },
//       { name: 'RC=2', value: body.RC_EQ_2, field: 'RC_EQ_2' },
//       { name: 'RC=3', value: body.RC_EQ_3, field: 'RC_EQ_3' },
//       { name: 'RC=4~5', value: body.RC_GE_4_LE_5, field: 'RC_GE_4_LE_5' },
//       { name: 'RC=6~10', value: body.RC_GE_6_LE_10, field: 'RC_GE_6_LE_10' },
//       { name: 'RC≥11', value: body.RC_GE_11, field: 'RC_GE_11' }
//     ]
//   }).catch((error) => {
//     console.log(error)
//   }).finally(() => {
//     pageCtl.norOpen.loading.report2 = false
//   })
// }
//
// const searchNorOpenReport3 = () => {
//   pageCtl.norOpen.loading.report3 = true
//   $axios({
//     method: 'post',
//     url: '/customer/nor_open/query_report3',
//     data: pageCtl.norOpen.conditions
//   }).then((body) => {
//     pageCtl.norOpen.report3Data = body
//   }).catch((error) => {
//     console.log(error)
//   }).finally(() => {
//     pageCtl.norOpen.loading.report3 = false
//   })
// }
//
// const norOpenReport3RatiosColumns = (data) => {
//   const result = [{ data: 'STATISTICS' }] as any
//   for (let i = 0; i < data.length; ++i) {
//     result.push({ data: data[i], title: data[i], type: 'numeric' })
//   }
//   return result
// }
//
// const searchNorOpenReport3Ratios = () => {
//   norOpenReport3RatiosRef.value.setLoading(true)
//   $axios({
//     method: 'post',
//     url: '/customer/nor_open/query_report3_ratios_columns',
//     data: pageCtl.norOpen.conditions
//   }).then((body) => {
//     pageCtl.norOpen.report3RatiosColumns = norOpenReport3RatiosColumns(body)
//     norOpenReport3RatiosRef.value.search()
//   }).catch((error) => {
//     console.log(error)
//   })
// }

const afterNorOpenReport1Selected = (r, c, a) => {
  pageCtl.norOpen.conditions.report1SelectedColumn = a
  if (r[_norOpenSelectedcategory.value[0]] === 'Total') {
    pageCtl.norOpen.conditions.report1SelectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _norOpenSelectedcategory.value.length; i++) {
      selectedValue.push(r[_norOpenSelectedcategory.value[i]])
    }
    pageCtl.norOpen.conditions.report1SelectedValue = selectedValue
  }

  let selectColumnName = ''
  for (let i = 0; i < pageCtl.norOpenColums.length; i++) {
    const obj = pageCtl.norOpenColums[i] as any
    if (a === obj.data && (a.indexOf('RC_EQ') === 0 || a.indexOf('RC_GE') === 0)) {
      selectColumnName = obj.title
    }
  }
  const title = $join(...pageCtl.norOpen.conditions.report1SelectedValue, selectColumnName)
  if (title) {
    pageCtl.norOpen.report1DetailsTitle = 'View Details [' + title + ']'
  } else {
    if (pageCtl.norOpen.conditions.report1SelectedValue.length > 0) {
      pageCtl.norOpen.report1DetailsTitle = 'View Details [' + $join(...pageCtl.norOpen.conditions.report1SelectedValue) + ']'
    } else {
      pageCtl.norOpen.report1DetailsTitle = 'View Details [Total]'
    }
  }
}

const afterNorOpenReport4Selected = (r) => {
  if (pageCtl.norOpen.conditions.report4Type === 'so') {
    pageCtl.norOpen.conditions.report4SelectedValue = [r.MATERIAL, r.SALES_ORDER_NUMBER, r.SALES_ORDER_ITEM]
  } else {
    pageCtl.norOpen.conditions.report4SelectedValue = [r.MATERIAL, r.STOCKING_POLICY, r.ENTITY, null]
  }
}

const renderRow = (hotInstance, td, row, column, prop, value) => {
  if (value) {
    let html: any
    if (prop.indexOf('RC_AVERAGE') !== -1) {
      const r = hotInstance.getSourceDataAtRow(row)

      td.style.textAlign = 'right'
      html = value.toFixed(2)
    } else if (prop.indexOf('MATERIAL') !== -1) {
      html = value
    } else {
      html = value
    }
    td.innerHTML = html
  } else {
    td.style.textAlign = 'right'
    td.innerHTML = value
  }
}

const renderRowTitle = (hotInstance, td, row, column, prop, value) => {
  const c = hotInstance.getSourceData().length
  if (column === 0 && row !== 0 && row === c - 1) {
    td.innerHTML = 'Total'
    if (td.parentNode) {
      td.parentNode.style.fontWeight = 'bold'
    }
  } else {
    td.innerHTML = value
  }
}

// const norOpenReport2Back = () => {
//   pageCtl.norOpen.conditions.report3SelectedValue = ''
//   searchNorOpenReport2()
// }

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.norOpen.filterOpts)
})

const _norOpenReport4Column = computed(() => {
  if (pageCtl.norOpen.conditions.report4Type === 'so') {
    const allColumns = [{ data: 'GI_DATE' },
      { data: 'GI_WEEK' },
      { data: 'GI_MONTH' },
      { data: 'SALES_ORDER_NUMBER' },
      { data: 'SALES_ORDER_ITEM' },
      { data: 'MATERIAL' },
      { data: 'RESCH_COUNTER', title: 'Total No. of Rescheduling Counters', type: 'numeric' },
      { data: 'VENDOR_CODE' },
      { data: 'CREATED_DATE' },
      { data: 'CRD_DATE' },
      { data: 'PURCH_ORDER_NUMBER' },
      { data: 'PURCH_ORDER_ITEM' },
      { data: 'AC2_RANGE' },
      { data: 'ACTIVENESS' },
      { data: 'AVAILABILITY_CHECK' },
      { data: 'BLOCK_STATUS' },
      { data: 'BU' },
      { data: 'CLUSTER_NAME' },
      { data: 'CUSTOMER_CODE' },
      { data: 'DELIVERY_PRIORITY' },
      { data: 'ENTITY' },
      { data: 'FULFILL_OR_NOT_NONBLOCK' },
      { data: 'FULFILL_OR_NOT_UNRESTRICT' },
      { data: 'GRA_STATUS' },
      { data: 'GRA_TYPE' },
      { data: 'IMPORT_VENDOR' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ORDER_TYPE' },
      { data: 'PLANT_CODE' },
      { data: 'PLANT_NAME' },
      { data: 'PLANT_TYPE' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PRODUCT_LINE' },
      { data: 'RESCH_GROUP' },
      { data: 'SHIP_TO' },
      { data: 'SHIP_TO_CITY' },
      { data: 'SHIP_TO_COUNTRY' },
      { data: 'SHIP_TO_REGION' },
      { data: 'SHORTAGE_STATUS' },
      { data: 'SOLD_TO_REGION' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'STOCKING_POLICY' },
      { data: 'UD_STATUS' },
      { data: 'VENDOR_NAME' }]
    const columns = [] as any
    for (let i = 0; i < allColumns.length; i++) {
      const c = allColumns[i]
      if (pageCtl.norOpenReport4SOSelectedColumns.indexOf(c.data) !== -1) {
        columns.push(allColumns[i])
      }
    }
    return columns
  } else {
    const allColumns = [{ data: 'MATERIAL', render: renderRow },
      { data: 'RESCH_COUNTER', title: 'Total No. of Rescheduling Counters', type: 'numeric' },
      { data: 'VENDOR_CODE' },
      { data: 'AC2_RANGE' },
      { data: 'ACTIVENESS' },
      { data: 'AVAILABILITY_CHECK' },
      { data: 'BLOCK_STATUS' },
      { data: 'BU' },
      { data: 'CLUSTER_NAME' },
      { data: 'CUSTOMER_CODE' },
      { data: 'DELIVERY_PRIORITY' },
      { data: 'ENTITY' },
      { data: 'FULFILL_OR_NOT_NONBLOCK' },
      { data: 'FULFILL_OR_NOT_UNRESTRICT' },
      { data: 'GRA_STATUS' },
      { data: 'GRA_TYPE' },
      { data: 'IMPORT_VENDOR' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ORDER_TYPE' },
      { data: 'PLANT_CODE' },
      { data: 'PLANT_NAME' },
      { data: 'PLANT_TYPE' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PRODUCT_LINE' },
      { data: 'RESCH_GROUP' },
      { data: 'SHIP_TO' },
      { data: 'SHIP_TO_CITY' },
      { data: 'SHIP_TO_COUNTRY' },
      { data: 'SHIP_TO_REGION' },
      { data: 'SHORTAGE_STATUS' },
      { data: 'SOLD_TO_REGION' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'STOCKING_POLICY' },
      { data: 'UD_STATUS' },
      { data: 'VENDOR_NAME' }]
    const columns = [] as any
    for (let i = 0; i < allColumns.length; i++) {
      const c = allColumns[i]
      if (pageCtl.norOpenReport4MaterialSelectedColumns.indexOf(c.data) !== -1) {
        columns.push(allColumns[i])
      }
    }
    return columns
  }
})

// const _norOpenReport2Opt = computed(() => {
//   const colors = [] as any
//   const data = $deepClone(pageCtl.norOpen.report2Data)
//   for (let i = 0; i < data.length; i++) {
//     const name = data[i].field
//     colors.push(pageCtl.norOpen.colorSettings[name])
//     data[i].label = { color: pageCtl.norOpen.colorSettings[name] }
//   }
//   return {
//     color: colors,
//     title: {
//       text: 'NOR [' + (
//         pageCtl.norOpen.conditions.report3SelectedValue === ''
//           ? 'This Week'
//           : pageCtl.norOpen.conditions.report3SelectedValue) + ']'
//     },
//     toolbox: $toolbox({ opts: [] }),
//     tooltip: {
//       trigger: 'item',
//       triggerOn: 'mousemove',
//       formatter: (params) => {
//         const marker = params.marker
//         const tip = [] as any
//
//         tip.push('<div style="width:9.5rem;">')
//         tip.push('<div>')
//         tip.push(marker)
//         tip.push(params.name)
//         tip.push('<span style="float: right">')
//         tip.push($shortenNumber(params.value))
//         tip.push('(')
//         tip.push(params.percent)
//         tip.push('%)')
//         tip.push('</span>')
//         tip.push('</div>')
//
//         return tip.join('')
//       }
//     },
//     legend: $legend({ type: 'pie' }),
//     series: [
//       {
//         type: 'pie',
//         radius: '60%',
//         center: ['30%', '55%'],
//         data
//       }
//     ]
//   }
// })
//
// const _norOpenReport3Opt = computed(() => {
//   const legend = [] as any
//   for (const k in pageCtl.norOpen.nameSettings) {
//     legend.push(pageCtl.norOpen.nameSettings[k])
//   }
//
//   const series = [] as any
//   for (const k in pageCtl.norOpen.colorSettings) {
//     series.push({
//       name: pageCtl.norOpen.nameSettings[k],
//       type: 'line',
//       smooth: false,
//       stack: 'sum',
//       itemStyle: {
//         color: pageCtl.norOpen.colorSettings[k]
//       },
//       areaStyle: {},
//       data: pageCtl.norOpen.report3Data[k] || []
//     })
//   }
//
//   return {
//     title: {
//       text: 'Evolution of NOR by GI Date'
//     },
//     toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
//     tooltip: {
//       trigger: 'axis',
//       triggerOn: 'mousemove',
//       formatter: (params) => {
//         const tip = [] as any
//         tip.push('<div>')
//         tip.push(params[0].name)
//         tip.push('<br>')
//
//         let total = 0.0
//         const p = params.reverse()
//         let marker = ''
//         for (const i in p) {
//           if (!p.hasOwnProperty(i)) {
//             continue
//           }
//
//           const value = p[i].value || 0
//           total += value
//           tip.push('<div style="width:9.5rem;">')
//           marker = p[i].marker
//
//           tip.push(marker)
//           tip.push(p[i].seriesName)
//           tip.push('<span style="float: right">')
//           tip.push($shortenNumber(value))
//           tip.push('</span></div>')
//         }
//         tip.push('<div style="width:9.5rem;">')
//         tip.push(marker)
//         tip.push('Total')
//         tip.push('<span style="float: right">')
//         tip.push($shortenNumber(total))
//         tip.push('</span></div>')
//
//         tip.push('</div>')
//         return tip.join('')
//       }
//     },
//     grid: $grid(),
//     xAxis: {
//       type: 'category',
//       boundaryGap: false,
//       data: pageCtl.norOpen.report3Data.xAxis || [],
//       triggerEvent: true
//     },
//     yAxis: {
//       type: 'value',
//       axisLabel: {
//         formatter: (value) => {
//           return $shortenNumber(value, 0)
//         }
//       }
//     },
//     legend: $legend({ data: legend }),
//     series
//   }
// })

const _norOpenSelectedcategory = computed(() => {
  if (pageCtl.norOpen.conditions.category.length > 0) {
    return pageCtl.norOpen.conditions.category
  } else {
    return ['ENTITY']
  }
})

</script>

<style scoped>
:deep(.custom-dialog .el-dialog__title) {
  font-size: 15px !important;
}

.dialog-content {
  text-align: left;
  line-height: 1.6;
}

.dialog-content p {
  margin-bottom: 1rem;
}

.dialog-content .link {
  color: #007bff; /* 蓝色，代表链接 */
  text-decoration: none;
  font-weight: bold;
  cursor: pointer;
}

.dialog-content .link:hover {
  text-decoration: underline;
}

.dialog-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1rem auto;
}
</style>
