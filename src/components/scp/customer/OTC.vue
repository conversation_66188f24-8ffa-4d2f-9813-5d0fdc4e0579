<template>
  <div class="left-sidebar" id="otc">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <!--filter field-->
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.field" size="small" placeholder="Filter Field" clearable filterable collapse-tags multiple>
              <el-option
                  v-for="item in ['PLANT_CODE', 'SOLD_TO_NAME', 'MATERIAL', 'OPM_PRODUCT_LINE', 'MRP_CONTROLLER', 'VENDOR', 'VENDOR_NAME', 'SHIP_TO_COUNTRY', 'ENTITY', 'FULFILL_OR_NOT_NONBLOCK', 'FULFILL_OR_NOT_UNRESTRICT',
                'GRA_STATUS', 'GRA_TYPE', 'LOCAL_BUSINESS_UNIT', 'LOCAL_PRODUCT_FAMILY', 'LOCAL_PRODUCT_LINE', 'LOCAL_PRODUCT_SUBFAMILY', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'MRP_CONTROLLER', 'ORDER_TYPE',
                'PLANT_NAME', 'OPM_PLANT_TYPE', 'OTDS_PLANT_TYPE', 'DSS_PRODUCT_LINE','GSC_REGION', 'SALES_ORG', 'SHIP_TO', 'SHIP_TO_CITY', 'SHIP_TO_COUNTRY', 'SHIP_TO_SHORT_NAME', 'SHIP_TO_FULL_NAME', 'SHIP_TO_PARENT_NAME', 'SHIP_TO_PARENT_CODE', 'SOLD_TO', 'SOLD_TO_REGION', 'SOLD_TO_SHORT_NAME', 'SOLD_TO_FULL_NAME', 'SOLD_TO_PARENT_NAME', 'SOLD_TO_PARENT_CODE', 'SOURCE_CATEGORY',
                'STOCKING_POLICY', 'VIP_REGION', 'VIP_SO_INDICATOR', 'VIP_VIP_NAME','MRP_CONTROLLER_DESCRIPTION']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!--category-->
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="OTC_FILTER_V" :filter-base="['OPM_OTC_DATA_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <el-input-number v-model="pageCtl.conditions.warningValue" size="small" :precision="1" :step="0.1" :max="100" :min="40"
                             style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.reportOrderBy" size="small" style="width: var(--scp-input-width) !important;" placeholder="Order by">
              <el-option label="ONTIME" value="ONTIME"/>
              <el-option label="DELAY" value="FAIL"/>
              <el-option label="PERCENTAGE" value="PERCENTAGE"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container subscript-container-left">
              <h3 style="text-align: left;">OTC Official</h3>
              <el-date-picker
                  size="small"
                  v-model="pageCtl.conditions.dateRange"
                  type="monthrange"
                  unlink-panels
                  range-separator="~"
                  format="YYYY/MM"
                  value-format="YYYY/MM"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  :picker-options="{
                  disabledDate(e) {
                    let time = e.getTime() - new Date().getTime()
                    return time > 5356800000 // 最多只能往前选62天
                  }
                }"
                  :clearable="false"
                  prefix-icon=null
                  style="width: 200px !important;border: 0;border-bottom: solid 1px var(--scp-border-color); position:absolute; top:10px;right: 10px">
              </el-date-picker>
              <scp-subscript id="COSO"/>
              <scp-table
                  :contextMenuItems="pageCtl.contextWeeklyMenuItems"
                  ref="otcWeeklyTableRef"
                  :columns="_otcWeeklyColumns"
                  :fixedColumnsLeft="_field.length"
                  :nested-headers="_weeklyNestedHeaders"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :tableStriped="false"
                  :dropdownMenu="false"
                  :row-headers="false"
                  :pagging="false"
                  :max-height="winHeight"
                  :afterSelect="afterWeeklySelect"
                  :pagging-setting-enable="false"
                  url="/customer/otc/query_otc_weekly"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- weekly details-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.detailsWeeklyVisible" :title="pageCtl.weeklyDetailsTitle">
      <template v-slot="{ height }">
        <scp-table style="margin-bottom:10px;"
                   ref="weeklyDetailsTableRef"
                   url="/customer/otc/query_otc_weekly_details"
                   download-url="/customer/otc/download_otc_weekly_details"
                   :lazy="true"
                   :max-height="height - 150"
                   :page-sizes="[20, 50, 100, 200, 500]"
                   :params="pageCtl.conditions"
                   :editable="false"/>
      </template>
    </scp-draggable-resizable>

    <!-- view weekly chart-->
    <scp-draggable-resizable w="60vw" h="450px" :title="pageCtl.weeklyChartTitle" v-model="pageCtl.chartsWeeklyVisible">
      <template v-slot="{ height }">
        <chart ref="weeklyLineChartRef" :style="{ height : height - 150 + 'px'}" :option="_weeklyLineOpt"
               v-if="!pageCtl.loading.weeklyChart && (pageCtl.weeklyChartDisplayType === 'Line')"/>
      </template>
    </scp-draggable-resizable>
    <!--
    view weekly summary
    <scp-draggable-resizable w="60vw" h="450px" title="View Official Summary" v-model="pageCtl.summaryWeeklyVisible">
      <template v-slot="{ height }">
        <el-row style="margin-top:5px;margin-left:5px" class="search-box">
          <el-col :span="6">
            <el-select v-model="pageCtl.summarySelectedWeeks" size="small" placeholder="Weeks" multiple collapse-tags style="min-width: 150px">
              <el-option
                  v-for="item in pageCtl.selectedWeeks"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="11">
            <el-radio-group v-model="pageCtl.summaryDisplayType" size="small">
              <el-radio-button value="Lite">Lite</el-radio-button>
              <el-radio-button value="Full">Full</el-radio-button>
            </el-radio-group>
          </el-col>
        </el-row>
        <scp-table
            ref="weeklySummaryTableRef"
            :columns="_otcWeeklySummaryColumns"
            :fixedColumnsLeft="1"
            :max-height="height - 200"
            :nested-headers="_otcWeeklySummaryNestedHeaders"
            :lazy="true"
            :params="pageCtl.conditions"
            :dropdown-menu="false"
            :column-sorting="true"
            :row-headers="true"
            :pagging="false"
            :pagging-setting-enable="false"
            url="/customer/otds/query_otds_weekly_summary"/>
      </template>
    </scp-draggable-resizable>
     -->
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, onUpdated, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $join: any = inject('$join')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')
const $startWith: any = inject('$startWith')
const $endWith: any = inject('$endWith')
const $message: any = inject('$message')
const $viewDetails: any = inject('$viewDetails')

const searchRef = ref()
const otcWeeklyTableRef = ref()
const dailyDetailsTableRef = ref()
const weeklyDetailsTableRef = ref()
const weeklyLineChartRef = ref()
const weeklySummaryTableRef = ref()
const winHeight = ref(400)

onUpdated(() => {
  winHeight.value = document.body.clientHeight - 225
})

const viewWeeklySummary = () => {
  pageCtl.summaryWeeklyVisible = true
  weeklySummaryTableRef.value.clearAndSearch()
}

const viewWeeklyDetails = () => {
  pageCtl.detailsWeeklyVisible = true
  pageCtl.weeklyDetailsUpdate = {}
  weeklyDetailsTableRef.value.clearAndSearch()
}

const viewWeeklyChart = () => {
  pageCtl.loading.weeklyChart = true
  pageCtl.chartsWeeklyVisible = true
  $axios({
    method: 'post',
    url: '/customer/otc/query_otc_weekly_chart',
    data: pageCtl.conditions
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.weeklyChart = false
  })
}

const pageCtl = reactive({
  pageWidth: document.documentElement.clientWidth * 0.6 - 10,
  dailyRange: 'This Week',
  dailyDetailsTitle: '',
  weeklyDetailsTitle: '',
  weeklyChartTitle: '',
  totalType: 'Total',
  loading: {
    weeklyChart: false,
    dailyChart: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    reportOrderBy: 'PERCENTAGE',
    field: ['ENTITY'],
    dateRange: [] as any,
    selectedField: [],
    selectedDate: [] as any,
    selectedType: '',
    selectedValue: '',
    warningValue: 97.7
  },
  monthNames: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OTC', 'NOV', 'DEC'],
  selectedYears: [],
  selectedMonths: [],
  selectedWeeks: [],
  selectedWeeksAsc: [],
  selectedDays: [],
  stripName: '',
  stripedClass: '',
  timer: null as any,
  contextWeeklyMenuItems: {
    view_details: {
      name: 'View details',
      disabled: () => {
        return pageCtl.conditions.selectedType === 'On Time' || !pageCtl.conditions.selectedValue
      },
      callback: viewWeeklyDetails
    },
    view_charts: {
      name: 'View chart',
      callback: viewWeeklyChart
    },
    /*
    view_summary: {
      name: 'View summary',
      callback: viewWeeklySummary
    },
     */
    view_split0: { name: '---------' }
  },
  detailsDailyVisible: false,
  detailsWeeklyVisible: false,
  chartsWeeklyVisible: false,
  summaryWeeklyVisible: false,
  detailsColumns: [{}],
  dailyData: [] as any,
  dailyChart: [] as any,
  weeklyChart: [] as any,
  weeklyChart2: [] as any,
  monthlyData: [],
  chartWidth: '100%',
  chartHeight: '100%',
  dailyDetailsUpdate: {},
  weeklyDetailsUpdate: {},
  weeklyDetailsUpdate2: {},
  rcaTips: {},
  rcaCode: [],
  summarySelectedWeeks: [],
  summaryDisplayType: 'Lite',
  weeklyChartDisplayType: 'Line'
})

watch(() => pageCtl.conditions.warningValue, () => {
  delayer(() => {
    otcWeeklyTableRef.value.redrawTable()
  })
})

watch(() => pageCtl.conditions.dateRange, (newVal, oldVal) => {
  if (oldVal.length > 0) {
    search()
  }
})

const _field = computed(() => {
  if (pageCtl.conditions.field.length > 0) {
    return pageCtl.conditions.field
  } else {
    return ['ENTITY']
  }
})

const _weeklyLineOpt = computed(() => {
  const markLine = [] as any
  markLine.push({
    name: 'Target',
    yAxis: pageCtl.conditions.warningValue
  })

  const ytd: any = _selectedWeeklyRatio.value.ytd
  for (let i = 0; i < ytd.length; i++) {
    markLine.push({
      name: ytd[i].name,
      yAxis: ytd[i].value * 100 || 0
    })
  }

  return {
    title: {
      text: 'On-Time trends'
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker

        const tip = [] as any
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        const sourceData = otcWeeklyTableRef.value.getData()
        const selectData = sourceData.filter(e => {
          let r = true
          for (let i = 0; i < _field.value.length; i++) {
            if (e[_field.value[i]] !== pageCtl.conditions.selectedField[i]) {
              r = false
            }
          }
          return r
        })

        let ontime = 0
        let delay = 0

        for (const i in selectData) {
          if (selectData.hasOwnProperty(i)) {
            if (selectData[i].TYPE === 'On Time') {
              ontime = selectData[i][params.name]
            } else if (selectData[i].TYPE === 'Delay') {
              delay = selectData[i][params.name]
            }
          }
        }

        // ontime
        tip.push('<div style="width:6rem;">')
        tip.push('On Time: ' + '<span style="float:right">' + ontime + '<span>')
        tip.push('</div>')
        // delay
        tip.push('<div style="width:6rem;">')
        tip.push('Delay: ' + '<span style="float:right">' + delay + '<span>')
        tip.push('</div>')
        // ratio
        tip.push('<div style="width:6rem;">')
        tip.push('Ratio: <span style="float:right">' + params.value + '%</span>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: [{
      top: 50,
      bottom: 0,
      left: 20,
      right: 20,
      containLabel: true
    }],
    xAxis: [{
      type: 'category',
      data: _selectedWeeklyRatio.value.x
    }],
    yAxis: [{
      type: 'value',
      scale: true,
      splitLine: {
        show: true
      }
    }],
    visualMap: {
      show: false,
      dimension: 1,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.conditions.warningValue,
        color: '#c12e34'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    },
    series: [{
      type: 'line',
      smooth: false,
      label: {
        position: 'top',
        show: true
      },
      data: _selectedWeeklyRatio.value.y,
      markLine: {
        symbol: 'none',
        lineStyle: {
          type: 'solid'
        },
        label: {
          position: 'insideEndTop', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
          formatter: '{b} - {c}',
          fontSize: 10
        },
        data: markLine
      }
    }]
  }
})

const _otcWeeklyColumns = computed(() => {
  const columns = [] as any

  for (let i = 0; i < _field.value.length; i++) {
    const name = _field.value[i]
    columns.push({
      title: name,
      data: name,
      render: renderTableStriped
    })
  }

  for (let i = 0; i < pageCtl.selectedYears.length; i++) {
    columns.push({
      title: pageCtl.selectedYears[i],
      data: pageCtl.selectedYears[i],
      render: renderRatioWeekly
    })
  }

  for (let i = 0; i < pageCtl.selectedMonths.length; i++) {
    columns.push({
      title: $firstCharUpperCase(pageCtl.selectedMonths[i]),
      data: pageCtl.selectedMonths[i],
      render: renderRatioWeekly
    })
  }

  for (let i = 0; i < pageCtl.selectedWeeks.length; i++) {
    columns.push({
      title: pageCtl.selectedWeeks[i],
      data: pageCtl.selectedWeeks[i],
      render: renderRatioWeekly
    })
  }
  return columns
})

watch(() => pageCtl.conditions.selectedDate, () => {
  let start = 0
  switch (pageCtl.dailyRange) {
    case 'This Week':
      start = 7
      break
    case 'Last Week':
      start = 0
      break
    case 'Next Week':
      start = 14
      break
  }
  pageCtl.dailyData = []
  for (let i = start; i < start + 7; i++) {
    pageCtl.dailyData.push(pageCtl.selectedDays[i])
  }
})

const _weeklyNestedHeaders = computed(() => {
  return [
    [
      {
        label: '',
        colspan: _field.value.length
      },
      {
        label: 'Monthly',
        colspan: pageCtl.selectedMonths.length + pageCtl.selectedYears.length
      }, {
        label: 'Weekly',
        colspan: pageCtl.selectedWeeks.length
      }
    ]
  ]
})

const _dailyNestedHeaders = computed(() => {
  return [
    [
      {
        label: '',
        colspan: _field.value.length
      }, {
        label: 'Daily',
        colspan: 7
      }
    ]
  ]
})

const _selectedWeeklyRatio = computed(() => {
  const table = otcWeeklyTableRef.value
  let data = []
  if (table) {
    data = table.getData()
  }
  const es = data.filter((e: any) => {
    let r = true
    for (let i = 0; i < _field.value.length; i++) {
      if (e[_field.value[i]] !== pageCtl.conditions.selectedField[i]) {
        r = false
      }
    }
    return r && e.TYPE === 'RATIO'
  })
  let e = {}
  if (es && es.length > 0) {
    e = es[0]
  }
  const y = [] as any
  const x = [] as any
  const ytd = [] as any
  for (let i = 0; i < pageCtl.selectedWeeksAsc.length; i++) {
    let v: any = e[pageCtl.selectedWeeksAsc[i]]
    if (v !== undefined && v !== null) {
      v = (v * 100).toFixed(1)
      x.push(pageCtl.selectedWeeksAsc[i])
      y.push(v)
    }
  }

  for (let i = 0; i < pageCtl.selectedYears.length; i++) {
    ytd.push({
      name: pageCtl.selectedYears[i],
      value: e[pageCtl.selectedYears[i]]
    })
  }

  return {
    x,
    y,
    ytd
  }
})

const _otcWeeklySummaryNestedHeaders = computed(() => {
  if (pageCtl.summaryDisplayType === 'Full') {
    const headers: any = ['']
    const weeks = pageCtl.summarySelectedWeeks.slice()
    weeks.sort((e1, e2) => e1 > e2 ? -1 : 1)
    for (let i = 0; i < weeks.length; i++) {
      headers.push({
        label: weeks[i],
        colspan: 3
      })
    }
    return [headers]
  } else {
    return []
  }
})

const _otcWeeklySummaryColumns = computed(() => {
  const columns = [] as any

  for (let i = 0; i < _field.value.length; i++) {
    const name = _field.value[i]
    columns.push({
      title: name,
      data: name,
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  const weeks = pageCtl.summarySelectedWeeks.slice()
  weeks.sort((e1, e2) => e1 > e2 ? -1 : 1)
  for (let i = 0; i < weeks.length; i++) {
    if (pageCtl.summaryDisplayType === 'Full') {
      columns.push({
        title: '%',
        data: '\'' + weeks[i] + '\'_RATIO',
        render: renderRatioWeeklySummary
      }, {
        title: 'CFMD',
        data: '\'' + weeks[i] + '\'_CONFIRMED',
        render: renderRatioWeeklySummary
      }, {
        title: 'TOT',
        data: '\'' + weeks[i] + '\'_FAIL',
        render: renderRatioWeeklySummary
      })
    } else {
      columns.push({
        title: weeks[i],
        data: '\'' + weeks[i] + '\'_RATIO',
        render: renderRatioWeeklySummary
      })
    }
  }
  return columns
})

onMounted(() => {
  initPage()
  winHeight.value = document.documentElement.clientHeight - 200
})

const afterWeeklySelect = (r, rc, c, column) => {
  // disable select event when details windows shown
  if (pageCtl.detailsWeeklyVisible === true) {
    return
  }
  pageCtl.conditions.selectedValue = rc
  const selected = [] as any
  for (let i = 0; i < _field.value.length; i++) {
    selected.push(r[_field.value[i]])
  }
  pageCtl.conditions.selectedField = selected
  if (column >= _field.value.length) {
    pageCtl.conditions.selectedDate = c
  } else {
    pageCtl.conditions.selectedDate = '-1'
  }
  pageCtl.conditions.selectedType = r.TYPE
  const title = pageCtl.conditions.selectedDate === '-1' ? pageCtl.conditions.dateRange.join(' - ') : pageCtl.conditions.selectedDate
  pageCtl.weeklyDetailsTitle = 'View Weekly Details - ' + $join(...selected) + '  ' + title + '  ' + (r.TYPE === 'RATIO' ? '(On Time,Delay)' : r.TYPE)
  pageCtl.weeklyChartTitle = 'View Official Chart - ' + $join(...selected)
}

const initPage = () => {
  initDatePicker()
  searchRef.value.loadAndClick()
}

const initDatePicker = () => {
  let plus = new Date().getTime()
  if (new Date().getDate() < 7) {
    plus -= 86400000 * 7
  }
  const end = new Date(plus)
  const start = new Date(plus)
  end.setMonth(end.getMonth())
  let startMonth: any = start.getMonth() + 1
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  let endMonth: any = end.getMonth() + 1
  if (endMonth < 10) {
    endMonth = '0' + endMonth
  }
  pageCtl.conditions.dateRange = [start.getFullYear() + '/' + startMonth, end.getFullYear() + '/' + endMonth]
}

const search = () => {
  pageCtl.stripedClass = ''
  otcWeeklyTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/otc/query_columns_by_daterange',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.selectedYears = body.years
    pageCtl.selectedMonths = body.months
    pageCtl.selectedWeeks = body.weeks
    pageCtl.summarySelectedWeeks = body.weeks

    if (pageCtl.selectedWeeks) {
      pageCtl.selectedWeeksAsc = []
      for (let i = pageCtl.selectedWeeks.length - 1; i >= 0; i--) {
        pageCtl.selectedWeeksAsc.push(pageCtl.selectedWeeks[i])
      }
    }

    pageCtl.selectedDays = body.days
    otcWeeklyTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

// 为ratio添加百分号
const renderRatioWeekly = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      if (value * 100 < pageCtl.conditions.warningValue) {
        td.style = 'color: var(--scp-text-color-error) !important'
      }
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }

    const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
    if (r && r.TYPE === 'Delay' && prop.indexOf('W') !== -1) {
      if (value === 0) {
        const style = 'background-image: linear-gradient(to right, #2c821d 100%, white 100%) !important;color: #fff !important;'
        td.title = '100%'
        td.style = style
      } else if (value) {
        const finish = r[prop + '_CONFIRMED'] || 0
        const percent = finish / value * 100
        const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
        const t = Math.min(percent + 2, 100)

        let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

        td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

        const tdWidth = td.offsetWidth // 单元格宽度
        const textWidth = $px2Rem((td.innerHTML + '').length * 8 + 4) // 文字宽度 = 文字个数 * 每个字符宽度8px + 左边距4px
        if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
          style += 'color: #fff !important;'
        }
        td.style = style
      }
    }
  }
}
const renderRatioDaily = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      if (value * 100 < pageCtl.conditions.warningValue) {
        td.style = 'color: var(--scp-text-color-error) !important'
      }
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }
  }
}
const renderRatioWeeklySummary = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  const failed = prop.replace('_RATIO', '_FAIL')
  if ($endWith(prop, '_RATIO')) {
    if (value === 0) {
      let style = 'background-image: linear-gradient(to right, #2c821d 100%, var(--scp-bg-color) 100%) !important;color: #fff !important;'
      td.title = '100%'
      if (r[failed]) {
        style = 'background-image: linear-gradient(to right, #c12e34 1%, var(--scp-bg-color) 1%) !important;'
        td.title = '0%'
      }

      td.style = style
      td.innerHTML = '0'
    } else if (value) {
      const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
      const percent = value
      const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
      const t = Math.min(percent + 2, 100)

      let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

      td.innerHTML = value + (pageCtl.summaryDisplayType === 'Full' ? '' : '%')
      const tdWidth = td.offsetWidth // 单元格宽度
      const textWidth = $px2Rem((td.innerHTML + '').length * 7 + 4) // 文字宽度 = 文字个数 * 每个字符宽度7px + 左边距5px
      if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
        style += 'color: #fff !important;'
      }
      td.style = style
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }
  }
  // 染色
  for (let i = 0; i < _field.value.length; i++) {
    if (r[_field.value[i]] === 'Total') {
      td.style.fontWeight = 'bold'
      break
    }
  }
}

// 表格添加斑马纹
const renderTableStriped = (hotInstance, td, row, column, prop, value) => {
  if (row % 3 === 0) {
    pageCtl.stripedClass = 'striped-even3-tr'
  } else {
    pageCtl.stripedClass = 'striped-odd3-tr'
  }

  if (td.parentNode) {
    td.parentNode.className = pageCtl.stripedClass
  }
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && r.TYPE === 'RATIO') {
    td.innerHTML = value || ''
  } else {
    if (column === _field.value.length - 1) {
      td.innerHTML = r.TYPE || ''
    } else {
      td.innerHTML = ''
    }

    td.style.textAlign = 'right'
    if (r.TYPE === 'Delay') {
      td.style.color = 'var(--scp-text-color-error)'
    } else if (r.TYPE === 'On Time') {
      td.style.color = 'var(--scp-text-color-success)'
      td.style.fontWeight = '700'
    }
  }
}
const delayer = (action, delay = 600) => {
  if (pageCtl.timer) {
    clearTimeout(pageCtl.timer)
  }
  pageCtl.timer = setTimeout(() => {
    action.call()
  }, delay)
}
</script>

<style lang="scss">
#otc {
  .el-date-editor.el-input__wrapper {
    box-shadow: 1 0 0 1px var(--scp-border-color, var(--scp-border-color)) inset;
  }
}
</style>
