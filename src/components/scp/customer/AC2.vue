<template>
  <div class="left-sidebar" style="width: 100%">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['AC2_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.searchDateType"
                       style="width: 100%"
                       class="search-group-left">
              <el-option value="Filter by Month"></el-option>
              <el-option value="Filter by Week"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5" v-if="pageCtl.conditions.searchDateType == 'Filter by Month'">
            <el-date-picker
                size="small"
                class="search-group-right"
                v-model="pageCtl.conditions.dateRange"
                type="monthrange"
                unlink-panels
                range-separator="~"
                format="YYYYMM"
                value-format="YYYYMM"
                start-placeholder="Start date"
                end-placeholder="End date"
                :clearable="false">
            </el-date-picker>
          </el-col>
          <el-col :span="5" v-else>
            <el-select class="search-group-right" v-model="pageCtl.conditions.weekRange" size="small" placeholder="Pick Weeks" filterable collapse-tags multiple clearable>
              <el-option v-for="item in pageCtl.weekRangeOpts"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.reportCalType" size="small" filterable collapse-tags>
              <el-option v-for="item in pageCtl.reportCalTypeOpts"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-input-number v-model="pageCtl.conditions.warningValue[pageCtl.conditions.reportCalType]" size="small"
                             :precision="1" :step="0.1" :max="100" :min="0"
                             style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="COAT" ref="report1SubRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :key="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :value="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :label="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :label="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option v-for="item in [1, 2]"
                                   :label="item"
                                   :key="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox v-for="item in pageCtl.report1TooltipsOpts"
                                 :value="item"
                                 :label="item"
                                 :key="item"/>
                  </el-checkbox-group>

                  <div class="box-footer">
                    <el-button
                      @click="report1SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report1SubRef.toggleView();searchReport1()">Search
                  </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="COAR" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Filter Settings</div>
                  <el-row>
                    <el-col :span="6">Date Type</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.report2DateType" size="small" placeholder="Filter Field"
                                 filterable>
                        <el-option
                            v-for="item in ['By Week', 'By Month', 'By Quarter', 'By Year']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Stack By</el-col>
                    <el-col :span="18">
                      <el-select
                          v-model="pageCtl.conditions.report2ViewType" size="small" filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">AC2 Type</el-col>
                    <el-col :span="18">
                      <el-select
                          v-model="pageCtl.conditions.report2AC2Type" size="small" filterable clearable>
                        <el-option
                            v-for="item in pageCtl.report2AC2TypeOpts"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box-footer">
                  <el-button
                      @click="report2SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report2SubRef.toggleView();searchReport2()">Search
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.fields" size="small" placeholder="Filter Field" filterable
                             multiple collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-subscript id="COAB"/>
              <scp-table
                  :contextMenuItems="pageCtl.report3MenuItems"
                  ref="report3TableRef"
                  :columns="_report3Columns['columns']"
                  :nested-headers="_report3Columns['headers']"
                  :pagging="false"
                  :pagging-setting-enable="false"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :max-height="456"
                  :after-select="afterReport3Select"
                  url="/customer/ac2/query_report3"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!--  report1 contextmenu  -->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>

    <!-- report3 view details-->
    <scp-draggable-resizable w="60vw" h="420px" v-model="pageCtl.visible.report3Details"
                             :title="pageCtl.report3DetailsTitle">
      <template v-slot="{ height }">
        <scp-table ref="report3DetailsTableRef"
                   url="/customer/ac2/query_report3_details"
                   download-url="/customer/ac2/download_report3_details"
                   :lazy="true"
                   :editable="false"
                   :after-change="afterReport3DetailsChange"
                   :params="_conditions"
                   :columns="_report3DetailsColumn"
                   :context-menu-items="pageCtl.report3DetailsMenuItems"
                   :maxHeight="height - 150">
        </scp-table>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $deepClone: any = inject('$deepClone')
const $convertDateStr: any = inject('$convertDateStr')
const $firstCharUpperCase: any = inject('$firstCharUpperCase')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $message: any = inject('$message')

const searchRef = ref()
const report1SubRef = ref()
const report2SubRef = ref()
const report2ChartRef = ref()
const report3TableRef = ref()
const report3DetailsTableRef = ref()

const viewReport3Details = () => {
  const title = [] as any
  for (let i = 0; i < pageCtl.report3SelectedValues.length; i++) {
    if (_fields.value.indexOf(pageCtl.report3SelectedValues[i]) === -1) {
      title.push(pageCtl.report3SelectedValues[i])
    }
  }
  pageCtl.report3DetailsTitle = 'View Details [' + $join(...title) + ']'
  pageCtl.report3SelectedType = (pageCtl.report3SelectedValues && pageCtl.report3SelectedValues.length > 0) ? pageCtl.report3SelectedValues[0] : ''
  pageCtl.visible.report3Details = true
  nextTick(() => {
    report3DetailsTableRef.value.search()
  })
}

const saveReport3Details = () => {
  if (report3DetailsTableRef.value.getLoading() === true) {
    return
  }
  if (JSON.stringify(pageCtl.report3DetailsUpdate) === '{}') {
    $message.error('No changes detected')
    return
  }
  report3DetailsTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/ac2/save_report3_details',
    data: {
      report3DetailsUpdate: pageCtl.report3DetailsUpdate
    }
  }).then(() => {
    $message.success('RCA code(s) saved')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report3DetailsUpdate = {}
    report3DetailsTableRef.value.setLoading(false)
  })
}

const pageCtl = reactive({
  rcaTips: {},
  rcaCode: [],
  loading: {
    report1: false,
    report2: false
  },
  weekRangeOpts: [],
  conditions: {
    $scpFilter: {
      cascader: [
        ['SE_SCOPE', 'SECI_OG'],
        ['BOM_CATEGORY', 'NORMAL'],
        ['BOM_CATEGORY', 'BOM_ITEM']
      ],
      filter: []
    },
    searchDateType: 'Filter by Month',
    currentWeekRecord: '',
    weekRange: [] as any,
    resultType: 'Line',
    level1: 'ENTITY',
    level2: 'AC2_TYPE',
    level3: 'BOM_CATEGORY',
    level4: 'ABC',
    level5: 'CALCULATED_ABC',
    leafDepth: 1,
    report1Tooltips: [],
    selectedTreePath: '',
    report2DateType: 'By Week',
    warningValue: {
      FAILED_NONE: 100,
      'LATE C2_OTHERS': 100,
      'LATE C2': 8,
      AC2_EARLY: 70,
      AC2: 70
    },
    fields: ['ENTITY'],
    ac2Type: 'AC2',
    report2SelectedType: '',
    report2AC2Type: '',
    report2ViewType: 'AC2_TYPE',
    dateRange: [] as any,
    reportCalType: 'LATE C2'
  },
  filterOpts: [],
  report1Data: [],
  report1TooltipsOpts: ['COUNT'],
  selectedCurrentLevel: '',
  selectedParentLevel: '',
  visible: {
    report3Details: false
  },
  report2Data: {
    xAxis: [],
    yAxis: {},
    lineData: []
  },
  report2yAxisMax: 100,
  report2SelectedType: '',
  report2SelectedDate: '',
  report2AC2TypeOpts: ['AC2_DELAY', 'AC2_EARLY', 'AC2_ON_TIME', 'AC2_OTHERS'],
  report3SelectedYears: [],
  report3SelectedMonths: [],
  report3SelectedWeeks: [],
  report3SelectedValues: [] as any,
  report3SelectedType: '',
  report3MenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3DetailsTitle: '',
  report3DetailsMenuItems: {
    save_rca: {
      name: '<b>Save RCA</b>',
      callback: saveReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3DetailsUpdate: {},
  reportCalTypeOpts: [
    'AC2',
    'AC2_EARLY',
    'LATE C2',
    'LATE C2_OTHERS',
    'FAILED_NONE'
  ]
})

const _report3DetailsColumn = computed(() => {
  const result = [] as any

  const selectedType = (pageCtl.report3SelectedValues && pageCtl.report3SelectedValues.length > 0) ? pageCtl.report3SelectedValues[0] : ''
  if (selectedType === 'DELAY') {
    result.push({
      title: 'RCA Result',
      data: 'RCA_RESULT',
      type: 'autocomplete',
      source: (query, process) => {
        if (query) {
          process(pageCtl.rcaCode.filter((e: any) => e.toLowerCase().indexOf(query.toLowerCase()) !== -1))
        } else {
          process(pageCtl.rcaCode)
        }
      },
      render: (hotInstance, td, row, column, prop, value) => {
        let tips = ''
        if (value) {
          tips = '- 【' + value + '】 ' + pageCtl.rcaTips[value]
        }

        if (tips) {
          let html = '<div title="' + tips + '">'
          html += value
          html += '</div>'
          td.innerHTML = html
        } else {
          td.innerHTML = value
        }
      }
    }, {
      title: 'Comments',
      data: 'RCA_COMMENTS'
    }, {
      title: 'Recom.RCA Code',
      data: 'RECOM_RCA_CODE'
    }, {
      title: 'RCA Tips',
      data: 'RCA_TIPS',
      render: (hotInstance, td, row, column, prop, value) => {
        const r = hotInstance.getSourceDataAtRow(row)
        const tips = [] as any
        if (pageCtl.rcaTips[value]) {
          tips.push('- 【' + value + '】 ' + pageCtl.rcaTips[value])
        }
        if (r.RCA_REMARK) {
          tips.push('- 【Remark】 ' + r.RCA_REMARK)
        }

        if (tips.length > 0) {
          let html = '<div title="#content#">'
          html += value
          html += '</div>'
          td.innerHTML = html.replace('#content#', tips.join('&#13;'))
        } else {
          td.innerHTML = value
        }
      }
    })
  }

  const columns = [
    { data: 'SALES_ORDER_NUMBER' },
    { data: 'SALES_ORDER_ITEM' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'BOM_CATEGORY' },
    { data: 'SE_SCOPE' },
    { data: 'ENTITY' },
    { data: 'PLANT_CODE' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'HIGHER_LEVEL_ITEM' },
    { data: 'MATERIAL_CATEGORY' },
    { data: 'VENDOR_CODE' },
    { data: 'MATERIAL_TYPE' },
    { data: 'ABC' },
    { data: 'CALCULATED_ABC' },
    { data: 'CALCULATED_FMR' },
    { data: 'ACTIVENESS' },
    { data: 'STOCKING_POLICY' },
    { data: 'COUNTRY_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'BU' },
    { data: 'PRODUCT_LINE' },
    { data: 'CLUSTER_NAME' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'REPL_STRATEGY' },
    { data: 'GRA_TYPE' },
    { data: 'GRA_EVENT' },
    { data: 'GRA_METHOD' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MRP_CONTROLLER' },
    { data: 'AVAILABILITY_CHECK' },
    { data: 'PLANT_TYPE' },
    { data: 'COMPLETE_DELIV_IND' },
    { data: 'DELIVERY_PRIORITY' },
    { data: 'DIS_CHANNEL' },
    { data: 'VIP_SO_INDICATOR' },
    { data: 'AC2_TYPE' },
    { data: 'AC2_RANGE' },
    { data: 'LATEST_321_POSTING_DATE' }
  ]
  result.push(...columns)
  return result
})

onMounted(() => {
  initPage()
  report2ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.report2SelectedType = params.seriesName
    pageCtl.report2SelectedDate = params.name

    $viewDetails({
      title: 'View Details [' + $join(params.seriesName, params.name) + ']',
      url: '/customer/ac2/query_report2_details',
      durl: '/customer/ac2_new/download_report2_details',
      params: _conditions.value
    })
  })
})

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
}

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 7 * 4)
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyyMM'),
    $dateFormatter(end, 'yyyyMM')
  ]
  $axios({
    method: 'post',
    url: '/customer/ac2/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.rcaTips = body.rcaTips
    pageCtl.rcaCode = body.rcaCode
    pageCtl.conditions.currentWeekRecord = body.currentWeek
    pageCtl.conditions.weekRange = [body.currentWeek]
    pageCtl.weekRangeOpts = body.weekOptions
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
  })
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/ac2/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/ac2/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
    switch (pageCtl.conditions.reportCalType) {
      case 'LATE C2':
        pageCtl.report2yAxisMax = 40
        break
      case 'LATE C2_OTHERS':
        pageCtl.report2yAxisMax = 40
        break
      case 'FAILED_NONE':
        pageCtl.report2yAxisMax = 10
        break
      default :
        pageCtl.report2yAxisMax = 100
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  report3TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/customer/ac2/query_report3_columns',
    data: _conditions.value
  }).then((body) => {
    pageCtl.report3SelectedYears = body.years
    pageCtl.report3SelectedMonths = body.months
    pageCtl.report3SelectedWeeks = body.weeks
    report3TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const afterReport3Select = (r, rc, c) => {
  const values = [r.TYPE, c]
  for (let i = 0; i < _fields.value.length; i++) {
    values.push(r[_fields.value[i]])
  }
  pageCtl.report3SelectedValues = values
}

const afterReport3DetailsChange = (changes) => {
  if (changes) {
    const ht = report3DetailsTableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'RCA_RESULT' || prop === 'RCA_COMMENTS') {
          const key = r.SALES_ORDER_NUMBER + '#' + r.SALES_ORDER_ITEM
          pageCtl.report3DetailsUpdate[key] = { RCA_RESULT: r.RCA_RESULT, RCA_COMMENTS: r.RCA_COMMENTS, RCA_TYPE: pageCtl.report3SelectedType }
        }
      }
    })
  }
}

const renderReport3Ratio = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r.TYPE === 'RATIO') {
    td.style.background = 'var(--scp-bg-color-fill)'
  } else {
    td.style.background = 'var(--scp-bg-color)'
  }

  if (_fields.value.indexOf(prop) !== -1) {
    if (prop !== _fields.value[_fields.value.length - 1]) {
      if (r.TYPE !== 'RATIO') {
        td.innerHTML = ''
      } else {
        td.innerHTML = value
      }
    } else {
      if (r.TYPE === 'RATIO') {
        td.innerHTML = value
      } else if (r.TYPE === 'ONTIME') {
        td.innerHTML = 'On Time'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-success)'
        // td.style.fontWeight = '700'
      } else if (r.TYPE === 'DELAY') {
        td.innerHTML = 'Delay'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-error)'
      } else if (r.TYPE === 'EARLY') {
        td.innerHTML = 'Early'
        td.style.textAlign = 'right'
        td.style.color = 'var(--scp-text-color-success)'
      } else if (r.TYPE === 'OTHERS') {
        td.innerHTML = 'Others'
        td.style.textAlign = 'right'
      }
    }
  } else {
    td.style.textAlign = 'left'
    if (r.TYPE === 'RATIO') {
      if (['LATE C2', 'FAILED_NONE', 'LATE C2_OTHERS'].includes(pageCtl.conditions.reportCalType)) {
        if (value && !isNaN(value)) {
          td.innerHTML = (value * 100).toFixed(1) + '%'
          if (value * 100 > pageCtl.conditions.warningValue[pageCtl.conditions.reportCalType]) {
            td.style.color = 'var(--scp-text-color-error)'
          }
        } else {
          td.innerHTML = value
        }
      } else {
        if (value && !isNaN(value)) {
          td.innerHTML = (value * 100).toFixed(1) + '%'
          if (value * 100 < pageCtl.conditions.warningValue[pageCtl.conditions.reportCalType]) {
            td.style.color = 'var(--scp-text-color-error)'
          }
        } else {
          td.innerHTML = value
          if (value === 0) {
            td.style.color = 'var(--scp-text-color-error)'
          }
        }
      }
    } else if (r.TYPE === 'DELAY' && value && prop.indexOf('W') === 0) { // 为Delay的单元格执行背景染色
      const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
      const finish = r[prop + '_CONFIRMED'] || 0
      const percent = finish / value * 100
      const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
      const t = Math.min(percent + 2, 100)

      let style = 'background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

      td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

      const tdWidth = td.offsetWidth // 单元格宽度
      const textWidth = $px2Rem((td.innerHTML + '').length * 8 + 4) // 文字宽度 = 文字个数 * 每个字符宽度8px + 左边距4px
      if (textWidth < tdWidth * t / 100) { // 如果背景色宽度大于文字宽度, 则将文字颜色设置为白色
        style += 'color: #fff !important;'
      }
      td.style = style
      td.innerHTML = $thousandBitSeparator(value, 1)
    } else {
      td.innerHTML = $thousandBitSeparator(value, 1)
    }
  }
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
}

const _conditions = computed(() => {
  const cond = $deepClone(pageCtl.conditions)
  cond.report2SelectedType = pageCtl.report2SelectedType
  cond.report2SelectedDate = pageCtl.report2SelectedDate
  cond.report3SelectedValues = pageCtl.report3SelectedValues
  return cond
})

const _fields = computed(() => {
  let fields = pageCtl.conditions.fields
  if (fields.length === 0) {
    fields = ['ENTITY']
  }
  return fields
})

const _report1Opt = computed(() => {
  const rootName = 'AC2'
  return {
    title: {
      text: 'AC2 by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width: 11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)
          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)
            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  const series = [] as any
  let legend = [] as any
  const colorMap = {
    AC2_DELAY: '#d25924',
    AC2_OTHERS: '#a5a5a5',
    AC2_EARLY: '#f7a80b',
    AC2_ON_TIME: '#70ad47',
    '<-30': '#c02d33',
    '-15~-30': '#cf5227',
    '-8~-14': '#e1b501',
    '-3~-7': '#619615',
    '-2~0': '#2c841c',
    '1~7': '#619615',
    '8~14': '#e1b501',
    '15~30': '#cf5227',
    '>30': '#c02d33',
    NO_AC2: '#cccccc'
  }

  const yAxisData = pageCtl.report2Data.yAxis
  if (pageCtl.conditions.report2ViewType === 'AC2_RANGE') {
    legend = pageCtl.report2Data.xAxis.length > 0 ? ['<-30', '-15~-30', '-8~-14', '-3~-7', '-2~0', '1~7', '8~14', '15~30', '>30', 'NO_AC2'] : []
  } else {
    for (const key in yAxisData) {
      if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
        legend.push(key)
      }
    }
  }
  legend.push(pageCtl.conditions.reportCalType + '(%)')
  series.push({
    name: pageCtl.conditions.reportCalType + '(%)',
    type: 'line',
    smooth: false,
    yAxisIndex: 1,
    data: pageCtl.report2Data.lineData,
    color: '#3dcd58',
    label: {
      show: true,
      fontSize: 10,
      formatter: (data) => {
        return data.data + '%'
      }
    }
  })

  for (let i = 0; i < legend.length; i++) {
    const key = legend[i]
    const data = pageCtl.report2Data.yAxis[key]
    series.push({
      name: key,
      stack: 'total',
      data,
      type: 'bar',
      itemStyle: {
        color: colorMap[key]
      }
    })
  }

  let subTitle = ''

  if (pageCtl.conditions.report2DateType === 'By Week') {
    subTitle = 'Week'
  } else if (pageCtl.conditions.report2DateType === 'By Month') {
    subTitle = 'Month'
  } else if (pageCtl.conditions.report2DateType === 'By Quarter') {
    subTitle = 'Quarter'
  } else if (pageCtl.conditions.report2DateType === 'By Year') {
    subTitle = 'Year'
  }

  return {
    title: {
      text: 'AC2 by ' + subTitle +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    grid: $grid(),
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        let total = 0
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName !== pageCtl.conditions.reportCalType + '(%)') {
            total += (params[i].value || 0)
          }
        }
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName !== pageCtl.conditions.reportCalType + '(%)') {
            tip.push('<div style="width:9rem;">')
            tip.push(params[i].marker)
            tip.push(params[i].seriesName)
            tip.push('<div style="width: 2rem;font-size: 90%;float: right;text-align: right">')
            tip.push(' (')
            tip.push(total === 0 ? '0' : ((params[i].value || 0) * 100 / total).toFixed(1) + '%')
            tip.push(')')
            tip.push('</div>')
            tip.push('<div style="width: 2rem;font-size: 90%;float: right;text-align: right">')
            tip.push($shortenNumber(params[i].value, 1))
            tip.push('</div></div>')
          }
        }
        tip.push('<div style="width:9rem;">')
        tip.push('Total')
        tip.push('<div style="width: 2rem; font-size: 90%; float: right; text-align: right">')
        tip.push(' (')
        tip.push('100%')
        tip.push(')')
        tip.push('</div>')
        tip.push('<div style="width: 2rem; font-size: 90%; float: right; text-align: right">')
        tip.push($shortenNumber(total))
        tip.push('</div>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    xAxis: {
      type: 'category',
      data: pageCtl.report2Data.xAxis
    },
    yAxis: [{
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      max: pageCtl.report2yAxisMax,
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0) + '%'
        }
      }
    }],
    series,
    visualMap: {
      seriesIndex: 0,
      show: false,
      pieces: [
        {
          gt: 0,
          lte: pageCtl.conditions.warningValue[pageCtl.conditions.reportCalType],
          color: ['LATE C2', 'FAILED_NONE', 'LATE C2_OTHERS'].includes(pageCtl.conditions.reportCalType) ? '#3dcd58' : '#f62707'
        },
        {
          gt: pageCtl.conditions.warningValue[pageCtl.conditions.reportCalType],
          color: ['LATE C2', 'FAILED_NONE', 'LATE C2_OTHERS'].includes(pageCtl.conditions.reportCalType) ? '#f62707' : '#3dcd58'
        }
      ]
    }
  }
})

watch(() => pageCtl.conditions.reportCalType, () => {
  searchReport2()
  searchReport3()
})

const _report3Columns = computed(() => {
  const columns = [] as any
  // 为了可以让warningValue的修改触发表格更新
  if (pageCtl.conditions.warningValue[pageCtl.conditions.reportCalType] < 0) {
    return [{}]
  }

  for (let i = 0; i < _fields.value.length; i++) {
    columns.push({
      data: _fields.value[i],
      render: renderReport3Ratio
    })
  }

  for (let i = 0; i < pageCtl.report3SelectedYears.length; i++) {
    columns.push({
      data: 'Y' + pageCtl.report3SelectedYears[i],
      title: pageCtl.report3SelectedYears[i] + 'YTD',
      render: renderReport3Ratio
    })
  }

  for (let i = 0; i < pageCtl.report3SelectedMonths.length; i++) {
    const name = pageCtl.report3SelectedMonths[i]
    let title = $convertDateStr(name)
    title = $firstCharUpperCase(title.split('-')[0].toLowerCase())
    columns.push({
      data: 'M' + name,
      title,
      render: renderReport3Ratio
    })
  }

  for (let i = 0; i < pageCtl.report3SelectedWeeks.length; i++) {
    const name: any = pageCtl.report3SelectedWeeks[i]
    const title = 'W' + name.substr(4, 2)
    columns.push({
      data: 'W' + pageCtl.report3SelectedWeeks[i],
      title,
      render: renderReport3Ratio
    })
  }

  return {
    headers: [
      [{
        label: '',
        colspan: _fields.value.length
      }, {
        label: 'Monthly',
        colspan: pageCtl.report3SelectedYears.length + pageCtl.report3SelectedMonths.length
      }, {
        label: 'Weekly',
        colspan: pageCtl.report3SelectedWeeks.length
      }
      ]
    ],
    columns
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>

<style lang="scss" scoped>
.back-filter {
  margin: 5px;
}
</style>
