<template>
  <div class="left-sidebar" id="rta" style="width: 100%">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['MYCP_RTA_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="3">
            <el-select
                style="width: 100% !important;"
                class="search-group-left"
                v-model="pageCtl.conditions.dateColumn"
                placeholder="Select Date Column"
                size="small">
              <el-option
                  v-for="item in pageCtl.dateColumnOpts"
                  :key="item"
                  :value="item"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.reportDateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Line']"
                  :value="item"
                  :label="item"
                  :key="item"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.reportViewType" size="small">
              <el-option label="View by Day" value="VIEW_BY_DAY"/>
              <el-option label="View by Week" value="VIEW_BY_WEEK"/>
              <el-option label="View by Month" value="VIEW_BY_MONTH"/>
              <el-option label="View by Quarter" value="VIEW_BY_QUARTER"/>
              <el-option label="View by Year" value="VIEW_BY_YEAR"/>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['reportDateRange']"/>
          </el-col>
        </el-row>
        <el-row class="rta-widget">
          <el-col :span="4">
            <el-tooltip
                :content="('Total Quantity: ' + $thousandBitSeparator(pageCtl.conditions.report5Data.TOTAL_QTY, 1) || '--') + ''"
                effect="light" placement="bottom">
              <el-card shadow="hover" v-loading="pageCtl.loading.report5" @dblclick="viewReport5Details('TOTAL_QTY')">
                <h4>{{
                    $isEmpty(pageCtl.conditions.report5Data.TOTAL_QTY) ? '--' : $shortenNumber(pageCtl.conditions.report5Data.TOTAL_QTY)
                  }}
                  <span style="display: inline-block; font-size: 0.8em; font-weight: initial"> Pcs</span></h4>
                <h6>Total Quantity</h6>
              </el-card>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip
                :content="('Finished Quantity: ' + $thousandBitSeparator(pageCtl.conditions.report5Data.FINISHED_QTY, 1) || '--') + ''"
                effect="light" placement="bottom">
              <el-card shadow="hover" v-loading="pageCtl.loading.report5"
                       @dblclick="viewReport5Details('FINISHED_QTY')">
                <h4>{{
                    $isEmpty(pageCtl.conditions.report5Data.FINISHED_QTY) ? '--' : $shortenNumber(pageCtl.conditions.report5Data.FINISHED_QTY)
                  }}
                  <span style="display: inline-block; font-size: 0.8em; font-weight: initial"> Pcs</span></h4>
                <h6>Finished Quantity</h6>
              </el-card>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip
                :content="('Average Finish Days: ' + $thousandBitSeparator(pageCtl.conditions.report5Data.AVG_FINISH_DAYS, 1) || '--') + ''"
                effect="light" placement="bottom">
              <el-card shadow="hover" v-loading="pageCtl.loading.report5"
                       @dblclick="viewReport5Details('AVG_FINISH_DAYS')">
                <h4>{{
                    $isEmpty(pageCtl.conditions.report5Data.AVG_FINISH_DAYS) ? '--' : $shortenNumber(pageCtl.conditions.report5Data.AVG_FINISH_DAYS)
                  }}
                  <span style="display: inline-block; font-size: 0.8em; font-weight: initial"> Days</span></h4>
                <h6>Average Finish Days</h6>
              </el-card>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip
                :content="('Pending Quantity: ' + $thousandBitSeparator(pageCtl.conditions.report5Data.PENDING_QUANTITY, 1) || '--') + ''"
                effect="light" placement="bottom">
              <el-card shadow="hover" v-loading="pageCtl.loading.report5"
                       @dblclick="viewReport5Details('PENDING_QUANTITY')">
                <h4>
                  {{ $isEmpty(pageCtl.conditions.report5Data.PENDING_QUANTITY) ? '--' : $shortenNumber(pageCtl.conditions.report5Data.PENDING_QUANTITY) }}
                  <span style="display: inline-block; font-size: 0.8em; font-weight: initial"> Pcs</span></h4>
                <h6>Pending Quantity</h6>
              </el-card>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip
                :content="('Average of Total TAT: ' + $thousandBitSeparator(pageCtl.conditions.report5Data.AVG_TAT, 1) || '--') + ''"
                effect="light" placement="bottom">
              <el-card shadow="hover" v-loading="pageCtl.loading.report5" @dblclick="viewReport5Details('AVG_TAT')">
                <h4>{{
                    $isEmpty(pageCtl.conditions.report5Data.AVG_TAT) ? '--' : $shortenNumber(pageCtl.conditions.report5Data.AVG_TAT)
                  }}
                  <span style="display: inline-block; font-size: 0.8em; font-weight: initial"> Days</span></h4>
                <h6>Average of Total TAT</h6>
              </el-card>
            </el-tooltip>
          </el-col>
          <el-col :span="4">
            <el-tooltip
                :content="('Total Lines: ' + $thousandBitSeparator(pageCtl.conditions.report5Data.TOTAL_LINES, 2) || '--')"
                effect="light" placement="bottom">
              <el-card shadow="hover" v-loading="pageCtl.loading.report5" @dblclick="viewReport5Details('TOTAL_LINES')"
                       style="margin-right: 0 !important;">
                <h4>{{
                    $isEmpty(pageCtl.conditions.report5Data.TOTAL_LINES) ? '--' : $shortenNumber(pageCtl.conditions.report5Data.TOTAL_LINES, 2)
                  }}
                  <span style="display: inline-block; font-size: 0.8em; font-weight: initial"> Lines</span></h4>
                <h6>Total Lines</h6>
              </el-card>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="RTA1" ref="report1SubRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :key="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :value="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :label="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :label="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option v-for="item in [1, 2]"
                                   :label="item"
                                   :key="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox v-for="item in pageCtl.report1TooltipsOpts"
                                 :value="item"
                                 :label="item"
                                 :key="item"/>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button
                        @click="report1SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report1SubRef.toggleView();searchReport1()">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="14" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="RTA2" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Filter</div>
                  <el-row>
                    <el-col :span="8">Sort by</el-col>
                    <el-col :span="14">
                      <scp-cascader
                          class="report2_cascade"
                          :multiple="false"
                          v-model="pageCtl.conditions.report2OrderBy"
                          :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                          :options="pageCtl.report2FilterOpts"/>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">Dimension</el-col>
                    <el-col :span="14">
                      <scp-cascader
                          class="report2_cascade"
                          v-model="pageCtl.conditions.report2DimensionsList"
                          :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                          :options="pageCtl.report2FilterOpts"/>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">yAxis</el-col>
                    <el-col :span="14">
                      <el-select v-model="pageCtl.conditions.report2yAxis">
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :value="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">Top Range</el-col>
                    <el-col :span="14">
                      <el-select v-model="pageCtl.conditions.report2TopQuantities" filterable>
                        <el-option v-for="item in 500"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button @click="report2SubRef.toggleView()">Back</el-button>
                    <el-button
                        type="primary"
                        @click="report2SubRef.toggleView();searchReport2()">
                      Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container subscript-container-right">
              <scp-subscript id="RTA3" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ShowType"
                             size="small">
                    <el-option v-for="item in ['VIEW_BY_VALUE', 'VIEW_BY_PERCENT']"
                               :label="item"
                               :value="item"
                               :key="item"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ViewType" size="small" placeholder="Columns" filterable>
                    <el-option v-for="item in _pivotColumns"
                               :label="item"
                               :key="item"
                               :value="item"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ResultType" size="small">
                    <el-option
                        v-for="item in ['Average Finish Days', 'Quantity', 'Line']"
                        :value="item"
                        :label="item"
                        :key="item"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report3ChartRef" :height="350" :option="_report3Opt"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report4">
            <div class="subscript-container">
              <scp-subscript id="RTA4" ref="report4SubRef"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report4SelectedColumns" size="small" placeholder="Columns"
                             multiple collapse-tags
                             clearable filterable>
                    <el-option v-for="item in _pivotColumns"
                               :label="item"
                               :key="item"
                               :value="item"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report4ResultType" size="small">
                    <el-option
                        v-for="item in ['Average Finish Days', 'Quantity', 'Line']"
                        :value="item"
                        :label="item"
                        :key="item"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport4">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  url="/customer/rta/query_report4"
                  download-url="/customer/rta/download_report4"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :show-total="true"
                  :pagging="false"
                  :fixed-columns-left="_report4SelectedColumns.length"
                  :max-height="375"
                  :context-menu-items="pageCtl.report4ContextItems"
                  :after-select="afterReport4Select"
                  ref="report4TableRef"
                  :columns="pageCtl.report4Columns"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!--  report1 contextmenu  -->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $tooltip: any = inject('$echarts.tooltip')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $isEmpty: any = inject('$isEmpty')

const searchRef = ref()
const report1SubRef = ref()
const report1ContextmenuRef = ref()
const report2SubRef = ref()
const report2ChartRef = ref()
const report3SubRef = ref()
const report3ChartRef = ref()
const report4SubRef = ref()
const report4TableRef = ref()

const viewReport4Details = () => {
  let title = $join(
    pageCtl.conditions.report4SelectedDate,
    ...pageCtl.conditions.report4SelectedValues
  )
  if (title) {
    title = ' [' + title + ']'
  }

  $viewDetails({
    url: '/customer/rta/query_report4_details',
    durl: '/customer/rta/download_report4_details',
    params: pageCtl.conditions,
    title: 'View Details' + title
  })
}

const pageCtl = reactive({
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    report5: false
  },
  conditions: {
    $scpFilter: {
      cascader: [
        ['PROCESS_EXCLUSION', 'N']
      ],
      filter: []
    },
    dateColumn: 'SERFINDT',
    report2OrderBy: ['QUANTITY', 'SUM'],
    report2DimensionsList: [['RTA_OCT', 'AVG'], ['TOTAL_TAT', 'AVG'], ['QUANTITY', 'SUM']],
    resultType: 'Quantity',
    report3ResultType: 'Quantity',
    report4ResultType: 'Quantity',
    level1: 'ENTITY',
    level2: 'CUSTOMER_NAME',
    level3: 'CUSTOMER_CODE',
    level4: 'MATERIAL_GROUP',
    level5: 'PLANT_CODE',
    leafDepth: 1,
    selectedTreePath: '',
    reportViewType: 'VIEW_BY_MONTH',
    reportDateRange: [] as any,
    report1Tooltips: [],
    report2Data: [] as any,
    report2LineCol: ['AVG(TOTAL_TAT)', 'AVG(RTA_OCT)'],
    report2yAxis: 'CUSTOMER_NAME',
    report2SelectedValue: '',
    report2TopQuantities: 20,
    report2DownloadType: 'CURRENT_VIEW_RESULT',
    report3ShowType: 'VIEW_BY_VALUE',
    report3SeriesType: 'line',
    report3ViewType: 'ENTITY',
    report3SelectedDate: '',
    report3SelectedSeriesName: '',
    report4SelectedColumns: ['ENTITY'],
    report4SelectedDate: '',
    report4SelectedValues: [],
    report4ValueType: 'Value',
    report4ColumnNames: [],
    report5Data: {
      TOTAL_QTY: 0,
      FINISHED_QTY: 0,
      AVG_FINISH_DAYS: 0,
      PENDING_QUANTITY: 0,
      AVG_TAT: 0,
      TOTAL_LINES: 0
    }
  },
  dateColumnOpts: [],
  filterOpts: [],
  report2FilterOpts: [{
    value: 'QUANTITY',
    label: 'QUANTITY',
    children: [{ value: 'SUM', label: 'SUM' }, { value: 'AVG', label: 'AVG' }, { value: 'MAX', label: 'MAX' }, {
      value: 'MIN',
      label: 'MIN'
    }]
  },
  {
    value: 'COST',
    label: 'COST',
    children: [{ value: 'SUM', label: 'SUM' }, { value: 'AVG', label: 'AVG' }, {
      value: 'MAX',
      label: 'MAX'
    }, { value: 'MIN', label: 'MIN' }]
  },
  {
    value: 'RTA_OCT',
    label: 'RTA_OCT',
    children: [{ value: 'SUM', label: 'SUM' }, { value: 'AVG', label: 'AVG' }, {
      value: 'MAX',
      label: 'MAX'
    }, { value: 'MIN', label: 'MIN' }]
  },
  {
    value: 'TOTAL_TAT',
    label: 'TOTAL_TAT',
    children: [{ value: 'SUM', label: 'SUM' }, { value: 'AVG', label: 'AVG' }, {
      value: 'MAX',
      label: 'MAX'
    }, { value: 'MIN', label: 'MIN' }]
  }
  ],
  selectedCurrentLevel: '',
  selectedParentLevel: '',
  report1Data: [],
  report1TooltipsOpts: ['QUANTITY'],
  report2SeriesLabelDisplay: false,
  report3SeriesLabelDisplay: false,
  report3Data: [] as any,
  report3LegendSelected: [],
  report4ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport4Details
    },
    view_split0: { name: '---------' }
  },
  report4Columns: [],
  report5DetailsType: '',
  booleanDisplay: false
})

onMounted(() => {
  initFilter()
  report2ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report2SelectedValue = obj.name === undefined ? obj.value : obj.name

    $viewDetails({
      title: pageCtl.conditions.report2SelectedValue === undefined ? 'View Details' : 'View Details [' + pageCtl.conditions.report2SelectedValue.replaceAll('\\t', '\t') + ']',
      url: '/customer/rta/query_report2_details',
      durl: '/customer/rta/download_report2_details',
      params: pageCtl.conditions
    })
  })
  report3ChartRef.value.chart().on('dblclick', (obj) => {
    let title = ''
    if (obj.componentType === 'series') {
      pageCtl.conditions.report3SelectedDate = obj.name
      pageCtl.conditions.report3SelectedSeriesName = obj.seriesName
      title = 'View Details [' + [obj.name, obj.seriesName].join(', ') + ']'
    } else {
      pageCtl.conditions.report3SelectedDate = obj.value
      pageCtl.conditions.report3SelectedSeriesName = ''
      title = 'View Details [' + [obj.value].join(', ') + ']'
    }
    $viewDetails({
      url: '/customer/rta/query_report3_details',
      durl: '/customer/rta/download_report3_details',
      params: pageCtl.conditions,
      title
    })
  })

  report3ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report3SeriesType = obj.currentType
    }
  })
})

watch(() => pageCtl.conditions.resultType, () => {
  pageCtl.conditions.report3ResultType = pageCtl.conditions.resultType
  pageCtl.conditions.report4ResultType = pageCtl.conditions.resultType
})

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
  searchReport5()
}
const initFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getFullYear(), 0, 1)
  pageCtl.conditions.reportDateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/rta/query_filters'
  }).then((body) => {
    pageCtl.filterOpts = body.CASCADER
    pageCtl.dateColumnOpts = body.dateColumns
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const sumValues = (node) => {
  // 如果有子节点，则递归计算子节点的 value 之和
  if (node.children && node.children.length > 0) {
    let sum = 0
    node.children.forEach(child => {
      sum += sumValues(child)
    })
    node.value = sum // 将子节点的和赋值给当前节点
    node.children.sort((a, b) => b.value - a.value)
  }
  return node.value || 0
}

const calculateValues = (tree) => {
  tree.forEach(node => sumValues(node))
  tree.sort((a, b) => b.value - a.value)
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/customer/rta/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    calculateValues(body)
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  if (pageCtl.conditions.report2DimensionsList.length === 0) {
    pageCtl.conditions.report2DimensionsList = [['QUANTITY', 'SUM']]
  }
  if (!pageCtl.conditions.report2DimensionsList.some(subArray => subArray[0] === 'RTA_OCT' && subArray[1] === 'AVG')) {
    pageCtl.conditions.report2DimensionsList.push(['RTA_OCT', 'AVG'])
  }
  if (!pageCtl.conditions.report2OrderBy) {
    pageCtl.conditions.report2OrderBy = ['QUANTITY', 'SUM']
  }

  $axios({
    method: 'post',
    url: '/customer/rta/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/rta/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}
const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/customer/rta/query_report4_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report4ColumnNames = body
    pageCtl.report4Columns = parseReport4Columns()
    report4TableRef.value.clearAndSearch()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const afterReport4Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report4SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report4SelectedDate = ''
  }
  const selected = [] as any
  for (let i = 0; i < _report4SelectedColumns.value.length; i++) {
    let v = r[_report4SelectedColumns.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report4SelectedValues = selected
}
const parseReport4Columns = () => {
  const result = [] as any
  for (let i = 0; i < _report4SelectedColumns.value.length; i++) {
    result.push({
      data: _report4SelectedColumns.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  for (let i = 0; i < pageCtl.conditions.report4ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report4ColumnNames[i],
      data: '\'' + pageCtl.conditions.report4ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (pageCtl.conditions.report4ValueType === 'Value') {
          td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
        } else {
          td.innerHTML = value ? (value * 100).toFixed(2) + '%' : '0'
        }
      }
    })
  }
  return result
}

const searchReport5 = () => {
  pageCtl.loading.report5 = true
  $axios({
    method: 'post',
    url: '/customer/rta/query_report5',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report5Data = body || {} as any
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report5 = false
  })
}

const viewReport5Details = (type) => {
  let title = 'View Details ['
  switch (type) {
    case 'TOTAL_QTY':
      title += 'Total Quantity'
      break
    case 'FINISHED_QTY':
      title += 'Finished Quantity'
      break
    case 'AVG_FINISH_DAYS':
      title += 'Average Finish Days'
      break
    case 'PENDING_QUANTITY':
      title += 'Pending Quantity'
      break
    case 'AVG_TAT':
      title += 'Average of Total TAT'
      break
    case 'TOTAL_LINES':
      title += 'Total Lines'
      break
  }
  title += ']'
  pageCtl.report5DetailsType = type
  $viewDetails({
    url: '/customer/rta/query_report5_details',
    durl: '/customer/rta/download_report5_details',
    params: pageCtl.conditions,
    title
  })
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}
const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}

const _report1Opt = computed(() => {
  const rootName = 'RTA'
  return {
    title: {
      text: 'Technical Return Analysis by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: $tooltip({
      callback: (e) => {
        pageCtl.selectedCurrentLevel = e.selectedCurrentLevel
        pageCtl.selectedParentLevel = e.selectedParentLevel
      }
    }, pageCtl.report1Data),
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  let series: any = []
  const yAxisData = pageCtl.conditions.report2Data

  const report2OrderBy = pageCtl.conditions.report2OrderBy === null ? ['QUANTITY', 'SUM'] : pageCtl.conditions.report2OrderBy

  const sortedKeys = Object.keys(yAxisData).sort((a, b) => {
    if (a.indexOf(report2OrderBy[0].toUpperCase()) !== -1) {
      return -1
    } else if (b.indexOf(report2OrderBy[0].toUpperCase()) !== -1) {
      return 1
    }
    return 0
  })

  series = sortedKeys.map(key => {
    if (['xAxis', 'legend'].concat(pageCtl.conditions.report2LineCol).indexOf(key) === -1) {
      return {
        name: key,
        type: 'bar',
        stack: 'total',
        smooth: false,
        areaStyle: {},
        data: yAxisData[key] || [],
        label: {
          show: pageCtl.report2SeriesLabelDisplay,
          position: 'inside', // outside 外部显示  inside 内部显示
          formatter: (item) => {
            return item.data.toFixed(0)
          },
          color: '#ffffff', // 颜色
          fontSize: 10 // 字体大小
        }
      }
    }
    return undefined
  }).filter(item => item !== undefined)

  for (const col of pageCtl.conditions.report2LineCol) {
    series.push({
      name: col + ' Days',
      type: 'line',
      smooth: false,
      yAxisIndex: 1,
      data: yAxisData[col],
      color: col === 'AVG(TOTAL_TAT)' ? '#73c0de' : '#3dcd58',
      label: {
        show: col === 'AVG(RTA_OCT)' ? true : pageCtl.report2SeriesLabelDisplay,
        fontSize: 10,
        formatter: (data) => {
          return data.data.toFixed(1)
        }
      }
    })
  }

  return {
    title: {
      text: 'Technical Return Top ' + pageCtl.conditions.report2TopQuantities + ' Analysis' +
          (pageCtl.conditions.selectedTreePath ? ' by  [' + pageCtl.conditions.selectedTreePath + ']' : ''),
      triggerEvent: true
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        const a = params[0].name.split('\\t')
        tip.push(a)
        tip.push('<br>')
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }
          const value = p[i].value || 0
          if ($shortenNumber(value) !== 0) {
            tip.push('<div style="width:9.5rem;">')
            const marker = p[i].marker
            tip.push(marker)
            tip.push(p[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(value))
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend(),
    grid: $grid(),
    xAxis: [{
      position: 'left',
      type: 'category',
      data: yAxisData.xAxis || [],
      axisLabel: {
        width: 60,
        overflow: 'truncate',
        // interval: 0, // 控制x坐标轴是否显示全
        textStyle: {
          fontSize: 10
        }
      },
      align: 'left',
      triggerEvent: true
    }],
    yAxis: [{
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      },
      triggerEvent: true
    }, {
      type: 'value',
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }],
    toolbox: $toolbox({
      opts: ['line', 'stack'],
      custom: {
        myTool1: {
          show: true,
          title: 'Show Series Label',
          icon: 'path://M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667zM490.666667 490.666667h-149.482667A21.269333 21.269333 0 0 0 320 512c0 11.861333 9.493333 21.333333 21.184 21.333333H490.666667v149.482667c0 11.690667 9.557333 21.184 21.333333 21.184 11.861333 0 21.333333-9.493333 21.333333-21.184V533.333333h149.482667A21.269333 21.269333 0 0 0 704 512c0-11.861333-9.493333-21.333333-21.184-21.333333H533.333333v-149.482667A21.269333 21.269333 0 0 0 512 320c-11.861333 0-21.333333 9.493333-21.333333 21.184V490.666667z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            pageCtl.report2SeriesLabelDisplay = pageCtl.report2SeriesLabelDisplay !== true
          }
        }
      }
    }),
    series,
    visualMap: {
      seriesIndex: [series.length - 1],
      show: false,
      pieces: [
        {
          gt: 0,
          lte: 8,
          color: '#3dcd58'
        },
        {
          gt: 8,
          color: '#f62707'
        }
      ]
    }
  }
})

const _report3Opt = computed(() => {
  const series = [] as any
  const legend = [] as any

  let yAxisData = pageCtl.report3Data

  // 转换数字为百分比
  if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report3Data.xAxis

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis' && key !== 'totalLineList') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report3Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis' && key !== 'totalLineList') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report3Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis' && key !== 'totalLineList') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report3SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  const totalLineColor = '#ff5c33'

  if (pageCtl.report3SeriesLabelDisplay) {
    series.push({
      type: 'line',
      name: 'Total Line',
      symbol: 'none',
      smooth: false,
      data: yAxisData.totalLineList || [],
      itemStyle: {
        color: 'rgba(251,0,0,.0)'
      },
      markLine: {
        symbol: 'none',
        label: {
          position: 'middle',
          formatter: pageCtl.conditions.report3ResultType + ' to Current Month',
          fontSize: 12,
          color: totalLineColor
        },
        data: [{
          silent: false,
          lineStyle: {
            type: 'solid',
            color: totalLineColor
          },
          yAxis: (yAxisData.totalLineList !== undefined && yAxisData.totalLineList.length > 0) ? yAxisData.totalLineList[0] : 0
        }]
      }
    })
  }
  return {
    title: {
      text: 'Technical Return Trend Analysis' +
          (pageCtl.conditions.selectedTreePath ? ' by  [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          let marker = p[i].marker
          marker = marker.replace('rgba(251,0,0,.0)', totalLineColor)
          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend, selected: pageCtl.report3LegendSelected }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report3SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({
      opts: ['line', 'stack', 'no-details'],
      custom: {
        myTool1: {
          show: pageCtl.conditions.report3ResultType === 'Average Finish Days',
          title: 'Display the baseline',
          icon: 'path://M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667zM490.666667 490.666667h-149.482667A21.269333 21.269333 0 0 0 320 512c0 11.861333 9.493333 21.333333 21.184 21.333333H490.666667v149.482667c0 11.690667 9.557333 21.184 21.333333 21.184 11.861333 0 21.333333-9.493333 21.333333-21.184V533.333333h149.482667A21.269333 21.269333 0 0 0 704 512c0-11.861333-9.493333-21.333333-21.184-21.333333H533.333333v-149.482667A21.269333 21.269333 0 0 0 512 320c-11.861333 0-21.333333 9.493333-21.333333 21.184V490.666667z',
          iconStyle: {
            color: 'var(--scp-text-color-secondary)',
            borderColor: 'var(--scp-text-color-secondary)',
            borderWidth: 0.5,
            borderType: 'solid'
          },
          onclick: () => {
            pageCtl.report3SeriesLabelDisplay = pageCtl.report3SeriesLabelDisplay !== true
          }
        }
      }
    }),
    series
  }
})

const _report4SelectedColumns = computed(() => {
  if (pageCtl.conditions.report4SelectedColumns.length > 0) {
    return pageCtl.conditions.report4SelectedColumns
  } else {
    return ['ENTITY']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>

<style lang="scss">
#rta {
  .rta-widget {
    margin-bottom: var(--scp-widget-margin);

    .el-card {
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      border-radius: 0 !important;
      margin: 0 var(--scp-widget-margin) 0 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary) !important;
          margin: 0 !important;
        }

      }
    }
  }
}
</style>

<style lang="scss" scoped>
.back-filter {
  margin: 5px;
}
</style>
