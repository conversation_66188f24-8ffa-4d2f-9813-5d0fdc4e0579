<template>
  <div class="left-sidebar" id="CloStructure">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.cloVersion" size="small" @change="search">
              <el-option
                  v-for="item in pageCtl.cloVersionOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['CLO_STRUCTURE_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.valueType" size="small">
              <el-option
                  v-for="item in ['Net Net Price', 'Quantity', 'SO Line', 'Material Count']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['report2SOTimeRange', 'report3TimeRange','report2TargetDate','report2ReferenceDate']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2" style="height: 320px">
              <scp-subscript id="CSBD" ref="csbdRef"/>
              <div class="front">
                <el-table
                    :data="pageCtl.report2Data"
                    style="width: 100%"
                    height="310"
                    row-key="id"
                    lazy
                    :load="loadReport2"
                    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                    @row-dblclick="report2RowDblick"
                    @sort-change="report2SortChange"
                    class="tree-table report2" @contextmenu.prevent="">
                  <el-table-column :min-width="110" prop="category" label="Category" sortable :sort-method="(a, b) => report2Sort(a, b, 'category')">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.report2Categories" size="small" style="border:0 !important;width:calc(100% - 40px)"
                                 placeholder="Pivot Columns"
                                 @click.stop="(e)=>e.preventDefault()"
                                 collapse-tags collapse-tags-tooltip clearable multiple :multiple-limit="7" filterable :loading="pageCtl.loading.filter">
                        <el-option
                            v-for="item in pageCtl.pivotOpts"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="1000" :content="scope.row.category" placement="bottom-end">
                        <span v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" @contextmenu.prevent="report2RightClick2">
                          {{ scope.row.category }}
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="failOTDS" label="F.OTDS" sortable :sort-method="(a, b) => report2Sort(a, b, 'failOTDS')" width="85"
                                   class-name="meet-header">
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" placement="top-end">
                        <template #content>
                          Meet OTDS: {{ $thousandBitSeparator(scope.row.failOTDS, 1) }}{{ scope.row.failOTDS ? '%' : '' }}
                        </template>
                        <span v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" data-value2="fail"
                              @contextmenu.prevent="report2RightClick2">
                          {{ $thousandBitSeparator(scope.row.failOTDS, 1) }}{{ scope.row.failOTDS ? '%' : '' }}
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="fail" label="Fail" sortable :sort-method="(a, b) => report2Sort(a, b, 'fail')" width="180" class-name="fail-header">
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="2000" placement="top-end">
                        <template #content>
                          CLO Failed: {{ $thousandBitSeparator(scope.row.fail, 1) }}
                          ({{ $toFixed(scope.row.fail * 100 / (scope.row.meet + scope.row.fail), 1) }}%)
                        </template>
                        <div v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" data-value2="fail" @contextmenu.prevent="report2RightClick2">
                          <div style="width: 180px;display: flex;justify-content: right;align-items:center;" v-if="scope.row.category !== 'Total'">
                            <!-- CLO Fail显示文本 -->
                            <div style="font-size: 80%;cursor: pointer;" :style="{color: pageCtl.color.fail}" @click="switchReport2DisplayMode">
                              {{ report2DislayValue(scope.row, 'fail') }}
                            </div>

                            <div style="height:19px;margin-left: 2px" :style="{width: scope.row.failWidth + '%', backgroundColor: pageCtl.color.fail}">
                              &nbsp;
                            </div>
                          </div>
                          <div v-else style="text-align: right;cursor: pointer" :style="{color: pageCtl.color.fail}" @click="switchReport2DisplayMode">
                            {{ report2DislayValue(scope.row, 'fail') }}
                          </div>
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="meet" label="Meet" sortable :sort-method="(a, b) => report2Sort(a, b, 'meet')" width="180" class-name="meet-header">
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="2000" placement="top-end">
                        <template #content>
                          CLO Meet: {{ $thousandBitSeparator(scope.row.meet, 1) }}
                          ({{ $toFixed(scope.row.meet * 100 / (scope.row.meet + scope.row.fail), 1) }}%)
                        </template>
                        <div v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" data-value2="meet" @contextmenu.prevent="report2RightClick2">
                          <div style="width: 180px;display: flex;justify-content: left;align-items:center;" v-if="scope.row.category !== 'Total'">
                            <div style="height:19px;;margin-right: 2px" :style="{width: scope.row.meetWidth + '%', backgroundColor: pageCtl.color.meet}">&nbsp;
                            </div>
                            <div style="font-size: 80%;cursor: pointer" :style="{color: pageCtl.color.meet}" @click="switchReport2DisplayMode">
                              {{ report2DislayValue(scope.row, 'meet') }}
                            </div>
                          </div>
                          <div v-else style="text-align: left;cursor: pointer" :style="{color: pageCtl.color.meet}" @click="switchReport2DisplayMode">
                            {{ report2DislayValue(scope.row, 'meet') }}
                          </div>
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="meetOTDS" label="M.OTDS" sortable :sort-method="(a, b) => report2Sort(a, b, 'meetOTDS')" width="85"
                                   class-name="meet-header">
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" placement="top-end">
                        <template #content>
                          Meet OTDS: {{ $thousandBitSeparator(scope.row.meetOTDS, 1) }}{{ scope.row.meetOTDS ? '%' : '' }}
                        </template>
                        <span v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" data-value2="meet"
                              @contextmenu.prevent="report2RightClick2">
                          {{ $thousandBitSeparator(scope.row.meetOTDS, 1) }}{{ scope.row.meetOTDS ? '%' : '' }}
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="better" label="Shorten" sortable :sort-method="(a, b) => report2Sort(a, b, 'better')" width="85">
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" placement="top-end">
                        <template #content>
                          {{ $thousandBitSeparator(scope.row.better, 0) }}
                        </template>
                        <span v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" data-value2="better"
                              @contextmenu.prevent="report2RightClick2">
                          {{ $shortenNumber(scope.row.better, 1) }}
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="worse" label="Extend" sortable :sort-method="(a, b) => report2Sort(a, b, 'worse')" width="85"
                                   class-name="meet-header">
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" placement="top-end">
                        <template #content>
                          {{ $thousandBitSeparator(scope.row.worse, 0) }}
                        </template>
                        <span v-contextmenu:contextmenu2Ref :data-value="JSON.stringify(scope.row)" data-value2="worse"
                              @contextmenu.prevent="report2RightClick2">
                          {{ $shortenNumber(scope.row.worse, 1) }}
                        </span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box" style="padding: 5px;">
                  <div class="box-title">Report Settings</div>
                  <el-row>
                    <el-col :span="6">SO Time Range</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          style="width: calc(100% - 35px)"
                          size="small"
                          v-model="pageCtl.conditions.report2SOTimeRange"
                          type="monthrange"
                          unlink-panels
                          format="YYYYMM"
                          value-format="YYYYMM"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{}">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">Reference Date</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          style="width: var(--scp-input-width)"
                          v-model="pageCtl.conditions.report2ReferenceDate"
                          align="right"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          type="date"
                          :clearable="false"
                          placeholder="Date">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">Target Date</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          style="width: var(--scp-input-width)"
                          v-model="pageCtl.conditions.report2TargetDate"
                          align="right"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          type="date"
                          :clearable="false"
                          placeholder="Date">
                      </el-date-picker>
                    </el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button @click="csbdRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="csbdRef.toggleView();search()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="subscript-container subscript-container-right" style="height: 320px;" v-loading="pageCtl.loading.report3">
              <scp-subscript id="CSDD" ref="csddRef"/>
              <div class="front">
                <chart ref="report3Ref" style="height: 100%" :option="_report3Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box" style="padding: 5px;">
                  <div class="box-title">Report Settings</div>
                  <el-row>
                    <el-col :span="6">SO Time Range</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          style="width: calc(100% - 35px)"
                          size="small"
                          v-model="pageCtl.conditions.report2SOTimeRange"
                          type="monthrange"
                          unlink-panels
                          format="YYYYMM"
                          value-format="YYYYMM"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{}">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">CLO Time Range</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          style="width: calc(100% - 35px)"
                          size="small"
                          v-model="pageCtl.conditions.report3TimeRange"
                          type="daterange"
                          unlink-panels
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{}">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">Display by</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.report3DateType" style="width: calc(100% - 15px)">
                        <el-option v-for="item in ['Day', 'Week', 'Month']"
                                   :key="item"
                                   :label="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button @click="csddRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="csddRef.toggleView();search()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" style="min-height: 350px">
              <scp-subscript id="CS01" ref="cs01Ref"/>
              <div class="report1 front">
                <el-row class="search-box">
                  <el-col :span="5">
                    <el-select v-model="pageCtl.conditions.report1SelectedColumns" placeholder="More Columns..." multiple
                               collapse-tags filterable clearable>
                      <el-option
                          v-for="item in pageCtl.report3PivotColumn"
                          :key="item"
                          :label="item"
                          :value="item">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="1">
                    <el-button size="small" @click="searchReport1" :loading="pageCtl.loading.report1">
                      <font-awesome-icon icon="search"/>
                    </el-button>
                  </el-col>
                </el-row>
                <scp-table ref="report1TableRef"
                           :max-height="768"
                           :lazy="true"
                           :editable="false"
                           url="/customer/clo_structure/query_report1"
                           download-url="/customer/clo_structure/download_report1"
                           :params="pageCtl.conditions"
                           :columns="_report1Columns['columns']"
                           :after-change="afterReport1TableChange"
                           :context-menu-items="pageCtl.report1TableContextItems"
                           :nested-headers="_report1Columns['headers']">
                </scp-table>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box" style="padding: 5px;">
                  <div class="box-title">Report Settings</div>
                  <el-row>
                    <el-col :span="3">SO Time Range</el-col>
                    <el-col :span="10">
                      <el-date-picker
                          style="width: calc(100% - 35px)"
                          size="small"
                          v-model="pageCtl.conditions.report2SOTimeRange"
                          type="monthrange"
                          unlink-panels
                          format="YYYYMM"
                          value-format="YYYYMM"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{}">
                      </el-date-picker>
                    </el-col>
                    <el-col :span="11">&nbsp;</el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button @click="cs01Ref.toggleView()">Back</el-button>
                    <el-button type="primary" @click="cs01Ref.toggleView();search()">Search</el-button>
                  </div>
                </div>
                <div class="clearfix"></div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>

  <v-contextmenu ref="contextmenu2Ref">
    <v-contextmenu-item @click="report2ViewDetails">
      View Details
    </v-contextmenu-item>
    <v-contextmenu-item @click="report2ViewTrends">
      View Trends
    </v-contextmenu-item>
  </v-contextmenu>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $toFixed: any = inject('$toFixed')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $message: any = inject('$message')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $scaleLabel: any = inject('$echarts.label.scaleLable')

const csbdRef = ref()
const csddRef = ref()
const cs01Ref = ref()
const searchRef = ref()
const report1TableRef = ref()
const contextmenu2Ref = ref()
const report3Ref = ref()

const saveReport1TableComments = () => {
  const comments = []
  for (const key in pageCtl.report1Comments) {
    comments.push(pageCtl.report1Comments[key])
  }
  $axios({
    method: 'post',
    url: '/customer/clo_structure/save_report1_comments',
    data: {
      comments
    }
  }).then(() => {
    pageCtl.report1Comments = {}
    $message.success('Comments saved.')
  })
}

const pageCtl = reactive({
  cloVersionOpts: [],
  filterOpts: [],
  pivotOpts: [],
  improvTips: {},
  report3PivotColumn: [],
  color: {
    meet: '#61a3f1',
    fail: '#fc6464'
  },
  report1SelectedColumns: [],
  report1SelectedValueType: '',
  conditions: {
    $scpFilter: {
      cascader: [
        ['T0_ACTIVENESS', 'Active'],
        ['T0_ACTIVENESS', 'Others'],
        ['T0_DES_EXCEPTION', 'SER-Services'],
        ['T0_DES_EXCEPTION', 'GSC-Global SC (Strategy/Projects)'],
        ['T0_DES_EXCEPTION', 'LAU-Launch'],
        ['T0_DES_EXCEPTION', 'MTS-Must be Stocked'],
        ['T0_DES_EXCEPTION', 'LTB-Last Time Buy'],
        ['T0_DES_EXCEPTION', 'OFO-Order for Order'],
        ['T0_DES_EXCEPTION', 'CST-Customer Stock'],
        ['T0_DES_EXCEPTION', 'MTO-Must be Non-Stocked'],
        ['T0_DES_EXCEPTION', 'PRO-Promotion'],
        ['T0_DES_EXCEPTION', 'Others'],
        ['T0_DES_EXCEPTION', 'SPI-Supplier issue'],
        ['T0_DES_EXCEPTION', 'VCS-Vendor Consignment Stock'],
        ['T0_DES_EXCEPTION', 'UNKNOWN'],
        ['T0_DES_EXCEPTION', 'MRK-Marketing request'],
        ['T0_DES_EXCEPTION', 'MSK-Maintenance stock'],
        ['T0_DES_EXCEPTION', 'RED-Red Alert / Quota']
      ],
      filter: []
    },
    cloVersion: '',
    report1SelectedValues: '',
    report1SelectedColumns: ['T0_LAST_CLO_LT_WD', 'T0_CLO_LT_CHANGE_WD', 'T0_LAST_CLO_LT_CHANGE_DATE'],
    subjects: [],
    valueType: 'Net Net Price',
    report2DisplayMode: 'value',
    report2Categories: ['T0_BU', 'T0_PRODUCT_LINE', 'T0_ENTITY', 'T0_VENDOR_NAME', 'T0_STOCKING_POLICY'],
    report2SelectedValues: '',
    report2SelectedType: '',
    report2SOTimeRange: [],
    report2ReferenceDate: '',
    report2TargetDate: '',
    report3TimeRange: [],
    report3DateType: 'Week',
    report3SelectedDate: '',
    report3SelectedValues: ''
  } as any,
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false
  },
  report1TableContextItems: {
    save_comments: {
      name: function () {
        if (Object.keys(pageCtl.report1Comments).length === 0) {
          return 'Save comments'
        }
        return '<b>Save comments</b>'
      },
      disabled: function () {
        return Object.keys(pageCtl.report1Comments).length === 0
      },
      callback: saveReport1TableComments
    },
    view_split0: { name: '---------' }
  },
  report1FixedColumns: 1,
  report1Columns: {
    headers: null as any,
    columns: []
  },
  report1Comments: {},
  report2Data: [],
  report2RightClickData: '',
  report2RightClickType: '',
  report3Title: '',
  report3Data: {}
})

onMounted(() => {
  initFilter()

  report3Ref.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report3SelectedDate = obj.name

    $viewDetails({
      title: 'View Details [' + obj.name + ']',
      url: '/customer/clo_structure/query_report3_details',
      durl: '/customer/clo_structure/download_report3_details',
      params: pageCtl.conditions
    })
  })
})

const initFilter = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  pageCtl.conditions.report2ReferenceDate = $dateFormatter(today, 'yyyy/MM/dd')
  pageCtl.conditions.report2TargetDate = $dateFormatter(new Date(year, month, day - 14), 'yyyy/MM/dd')
  pageCtl.conditions.report2SOTimeRange = [$dateFormatter(new Date(year, month - 6, 1), 'yyyyMM'), $dateFormatter(new Date(year, month - 1, 1), 'yyyyMM')]
  pageCtl.conditions.report3TimeRange = [$dateFormatter(new Date(year, month - 2, 1), 'yyyy/MM/dd'), $dateFormatter(today, 'yyyy/MM/dd')]

  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/customer/clo_structure/init_page',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.cloVersionOpts = body.cloVersionOpts
    if (pageCtl.cloVersionOpts.length > 0) {
      pageCtl.conditions.cloVersion = pageCtl.cloVersionOpts[0]
    }
    pageCtl.filterOpts = body.cascader
    pageCtl.pivotOpts = body.pivotOpts
    pageCtl.improvTips = body.improvTips
    pageCtl.report3PivotColumn = body.availableColumns
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  pageCtl.conditions.report1SelectedValues = ''
  pageCtl.conditions.report2SelectedValues = ''
  pageCtl.conditions.report2SelectedType = ''
  pageCtl.conditions.report3SelectedValues = ''
  pageCtl.report3Title = ''
  pageCtl.report2RightClickData = ''
  pageCtl.report2RightClickType = ''
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  report1TableRef.value.setLoading(true)
  pageCtl.report1SelectedColumns = $deepClone(pageCtl.conditions.report1SelectedColumns)
  pageCtl.report1SelectedValueType = pageCtl.conditions.valueType
  report1TableRef.value.clearAndSearch()
  pageCtl.loading.report1 = false
}

const afterReport1TableChange = (changes) => {
  if (changes) {
    const ht = report1TableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'COMMENTS' && r.T0_MATERIAL && r.T0_PLANT_CODE) {
          const key = r.T0_MATERIAL + '#' + r.T0_PLANT_CODE
          pageCtl.report1Comments[key] = {
            material: r.T0_MATERIAL,
            plantCode: r.T0_PLANT_CODE,
            comments: r.COMMENTS
          }
        }
      }
    })
  }
}

const _report1Columns = computed(() => {
  const columns = [{ data: 'T0_MATERIAL' }, { data: 'T0_PLANT_CODE' }, { data: 'COMMENTS' }] as any

  let valueTitle = ''
  if (pageCtl.report1SelectedValueType === 'Net Net Price') {
    valueTitle = 'SO Value'
  } else if (pageCtl.report1SelectedValueType === 'Quantity') {
    valueTitle = 'SO Qty'
  } else if (pageCtl.report1SelectedValueType === 'SO Line') {
    valueTitle = 'SO Line'
  } else if (pageCtl.report1SelectedValueType === 'Material Count') {
    valueTitle = 'Material Count'
  }
  columns.push({ data: 'MATERIAL_VALUE', title: valueTitle, type: 'numeric', width: 80 })
  columns.push({ data: 'T0_MEET_OR_FAIL', title: 'T0 Meet/Fail', width: 80 })
  columns.push({ data: 'T0_CLO_LT_TARGET_WD', title: 'T0 CLO LT Target WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T0_CLO_LT_WD', title: 'T0 CLO LT WD', type: 'numeric', width: 80 })

  for (let i = 0; i < pageCtl.report1SelectedColumns.length; i++) {
    const columnName = pageCtl.report1SelectedColumns[i]
    if (columnName === 'T0_IMPROVEMENT_TIPS') {
      columns.push({
        data: pageCtl.report1SelectedColumns[i],
        render: (hotInstance, td, row, column, prop, value) => {
          const tips = [] as any
          if (value) {
            const vs = value.split(',')
            for (let i = 0; i < vs.length; i++) {
              const v = vs[i]
              if (pageCtl.improvTips[v]) {
                tips.push('-【' + v + '】 ' + pageCtl.improvTips[v])
              } else {
                tips.push('-【' + v + '】')
              }
            }
          }

          if (tips.length > 0) {
            let html = '<div title="#content#">'
            html += value
            html += '</div>'
            td.innerHTML = html.replace('#content#', tips.join('&#13;'))
          } else {
            td.innerHTML = value
          }
        }
      })
    } else if (columnName === 'T0_PDT' || columnName === 'T1_PDT') {
      columns.push({ data: pageCtl.report1SelectedColumns[i], type: 'numeric' })
    } else if (columnName === 'T0_LAST_CLO_LT_WD' || columnName === 'T0_CLO_LT_CHANGE_WD') {
      columns.push({ data: pageCtl.report1SelectedColumns[i], type: 'numeric' })
    } else {
      columns.push({ data: pageCtl.report1SelectedColumns[i] })
    }
  }

  columns.push({ data: 'T0_PO_PRS_LT_WD', title: 'PO Prs LT WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T0_TRLT_WD', title: 'TRLT WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T0_ZMGC_CD', title: 'ZMGC CD', type: 'numeric', width: 80 })
  columns.push({ data: 'T0_GR_LT_WD', title: 'GR WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T0_PICK_PACK_LT_WD', title: 'Pick/Pack WD', type: 'numeric', width: 80 })

  columns.push({ data: 'T1_EXW_LT_WD', title: 'EXW LT WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T1_IH_LT_WD', title: 'IHLT WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T1_GR_LT_WD', title: 'GR WD', type: 'numeric', width: 80 })
  columns.push({ data: 'T1_PICK_PACK_WD', title: 'Pick/Pack WD', type: 'numeric', width: 80 })

  return {
    headers: [
      [{
        label: '',
        colspan: pageCtl.report1SelectedColumns.length + 9
      }, {
        label: 'Tier0 Lead Time',
        colspan: 5
      }, {
        label: 'Tier1 Lead Time',
        colspan: 4
      }]
    ],
    columns
  }
})

const switchReport2DisplayMode = () => {
  if (pageCtl.conditions.report2DisplayMode === 'value') {
    pageCtl.conditions.report2DisplayMode = 'percent'
  } else if (pageCtl.conditions.report2DisplayMode === 'percent') {
    pageCtl.conditions.report2DisplayMode = 'value'
  }
}

const report2DislayValue = (row, type) => {
  if (pageCtl.conditions.report2DisplayMode === 'value') {
    return $shortenNumber(row[type])
  } else if (pageCtl.conditions.report2DisplayMode === 'percent') {
    const result = $toFixed(row[type] * 100 / (row.fail + row.meet), 1)
    if (result) {
      return result + '%'
    } else {
      return '-'
    }
  }
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/customer/clo_structure/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const loadReport2 = (
  row: any,
  treeNode: any,
  resolve: (date: any[]) => void
) => {
  $axios({
    method: 'post',
    url: '/customer/clo_structure/query_report2_sub',
    data: {
      ...pageCtl.conditions,
      parent: row.parent || [],
      expandValue: row.category,
      parentMaxWidth: row.parentMaxWidth
    }
  }).then((body) => {
    resolve(body)
  }).catch((error) => {
    console.log(error)
  })
}

const report2SortCache = {}
const report2SortChange = (e) => {
  report2SortCache[e.prop] = e.order
}

const report2Sort = (a, b, category) => {
  const beforeOrder = report2SortCache[category]
  if (a.category === 'Total') {
    return beforeOrder === 'ascending' ? -1 : 1
  } else {
    return a[category] > b[category] ? 1 : (a[category] === b[category] ? 0 : -1)
  }
}

const report2RightClick2 = (e) => {
  let target = e.target
  let maxLoop = 5
  while (maxLoop-- > 0) {
    if (target && target.dataset.value) {
      break
    }
    target = target.parentElement
  }
  // 用户点击右键时, 不一定继续点击右键弹出菜单, 所以找个临时变量来存一下
  pageCtl.report2RightClickData = target.dataset.value
  pageCtl.report2RightClickType = target.dataset.value2
}

const report2ViewDetails = () => {
  pageCtl.conditions.report2SelectedValues = pageCtl.report2RightClickData
  pageCtl.conditions.report2SelectedType = pageCtl.report2RightClickType
  const title = JSON.parse(pageCtl.conditions.report2SelectedValues)
  $viewDetails({
    url: '/customer/clo_structure/query_report2_details',
    durl: '/customer/clo_structure/download_report2_details',
    params: pageCtl.conditions,
    title: 'View Details [' + [...title.parent, title.category].join(', ') + ']',
    editable: false
  })
}

const report2ViewTrends = () => {
  pageCtl.conditions.report1SelectedValues = pageCtl.report2RightClickData
  pageCtl.conditions.report3SelectedValues = pageCtl.report2RightClickData
  const selected = JSON.parse(pageCtl.report2RightClickData)
  const value = [...selected.parent, selected.category]
  pageCtl.report3Title = 'Trend of Competitive CLO LT [' + value.join(', ') + ']'
  searchReport1()
  searchReport3()
}

const report2RowDblick = (row) => {
  pageCtl.report2RightClickData = JSON.stringify(row)
  report2ViewTrends()
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/customer/clo_structure/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const _report3Opt = computed(() => {
  const series = []
  const data = pageCtl.report3Data.series || []
  const meetColor = {
    '1. D+1': '#2c821d',
    '2. <1W': '#37861b',
    '3. 1-2W': '#468b17',
    '4. 2-4W': '#579214',
    '5. 1-2M': '#6c9b11',
    '6. 2-3M': '#81a20d',
    '7. 3-6M': '#afb106',
    '8. 6-12M': '#cab500',
    '9. >1Y': '#ddb600'
  }
  const failColor = {
    '1. D+1': '#e3b102',
    '2. <1W': '#e2a107',
    '3. 1-2W': '#e28f0f',
    '4. 2-4W': '#db6e1b',
    '5. 1-2M': '#d45e21',
    '6. 2-3M': '#ce4e27',
    '7. 3-6M': '#c8412d',
    '8. 6-12M': '#c43730',
    '9. >1Y': '#c12e34'
  }
  for (let i = 0; i < data.length; i++) {
    series.push({
      name: data[i].name,
      stack: 'total',
      type: 'bar',
      barWidth: '60%',
      data: data[i].meetVal,
      itemStyle: {
        color: meetColor[data[i].name],
        borderColor: meetColor[data[i].name]
      }
    })
  }
  for (let i = 0; i < data.length; i++) {
    series.push({
      name: data[i].name,
      stack: 'total',
      type: 'bar',
      barWidth: '60%',
      data: data[i].failVal,
      itemStyle: {
        color: failColor[data[i].name],
        borderColor: failColor[data[i].name]
      }
    })
  }
  series.push({
    name: 'Competitive CLO LT(%)',
    type: 'line',
    yAxisIndex: 1,
    symbolSize: 0,
    data: pageCtl.report3Data.percent,
    itemStyle: {
      color: '#c43730',
      borderColor: '#c43730'
    },
    label: {
      show: true,
      valueAnimation: true,
      formatter: function (params) {
        return $scaleLabel(params, pageCtl.report3Data.xAxis.length, 15)
      }
    }
  })
  return {
    title: {
      text: pageCtl.report3Title || 'Trend of Competitive CLO LT'
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const pdata = {}
        for (let i = 0; i < params.length; i++) {
          const pt = pdata[params[i].seriesName] || []
          pt.push(params[i])
          pdata[params[i].seriesName] = pt
        }
        const tip = [] as any
        tip.push('<div style="width:16rem;">')
        tip.push(params[0].name)
        tip.push('<br>')
        let mtotal = 0
        let ftotal = 0
        for (const key in pdata) {
          const pt = pdata[key]
          if (pt.length > 0) {
            tip.push('<div style="display: flex;justify-content: space-between">')
            tip.push('<div style="text-align: left;width: 4.5rem">')
            tip.push(pt[0].marker)
            tip.push(pt[0].seriesName)
            tip.push('</div>')
            tip.push('<div style="text-align: left;width: 4rem">')
            tip.push('M: ')
            mtotal += pt[0].data
            tip.push($shortenNumber(pt[0].data))
            tip.push('</div>')
            tip.push('<div style="text-align: left;width: 3rem">')
            tip.push('F: ')
            if (pt.length > 1) {
              ftotal += pt[1].data
              tip.push($shortenNumber(pt[1].data * -1))
            } else {
              tip.push(0)
            }
            tip.push('</div>')
            tip.push('</div>')
          }
        }
        tip.push('<div style="display: flex;justify-content: space-between">')
        tip.push('<div style="text-align: left;width: 4.5rem">')
        tip.push('Total')
        tip.push('</div>')
        tip.push('<div style="text-align: left;width: 4rem">')
        tip.push('M: ')
        tip.push($shortenNumber(mtotal))
        tip.push('</div>')
        tip.push('<div style="text-align: left;width: 3rem">')
        tip.push('F: ')
        tip.push($shortenNumber(ftotal * -1))
        tip.push('</div>')
        tip.push('</div>')
        tip.push('</div>')

        return tip.join('')
      }
    },
    grid: $grid(),
    toolbox: $toolbox({ opts: ['no-details'] }),
    legend: $legend(),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report3Data.xAxis,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [{
      type: 'value',
      splitLine: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      min: pageCtl.report3Data.minPercent,
      splitLine: {
        show: false
      }
    }],
    visualMap: {
      show: false,
      dimension: 1,
      seriesIndex: [series.length - 1],
      pieces: [{
        gt: -1,
        lte: 95,
        color: '#c12e34'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    },
    series
  }
})

</script>

<style lang="scss">
#CloStructure {
  .report1.front {
    .wtHolder {
      position: unset !important;
    }
  }

  .report2 {
    .el-table__row {
      cursor: pointer;
    }
  }

  .tree-table {
    table {
      tr:last-child {
        font-weight: bold;
      }

      tr {
        background-color: transparent;

        th.fail-header {
          border-right: solid 2.5px var(--scp-bg-color) !important;
        }

        th.meet-header {
          border-left: solid 2.5px var(--scp-bg-color) !important;
        }

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill) !important;

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.45rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              margin-left: 10px;
              width: calc(100% - 40px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__wrapper {
                .el-select__selected-item:first-child {
                  max-width: calc(100% - 40px) !important;
                }
                .el-tag:nth-child(2) {
                  font-size: .4rem;
                  color: var(--scp-text-color-secondary);
                }

                .el-tag {
                  padding: 0 !important;
                  margin-left: 0 !important;
                  font-size: 0.45rem;
                  color: var(--scp-text-color-primary);
                  background-color: transparent !important;

                  .el-tag__close {
                    display: none !important;
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td:not(:first-child) {
          text-align: right;
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-table__header {
      .el-select__wrapper {
        background-color: transparent !important;
        --el-input-border-color: transparent !important;
        box-shadow: none !important;
      }
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table tr:hover {
    td {
      * {
        color: #fff !important;
      }
    }
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }
}
</style>
