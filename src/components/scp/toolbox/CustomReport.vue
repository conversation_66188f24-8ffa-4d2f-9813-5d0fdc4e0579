<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-container @contextmenu.prevent="">
          <!--  side menu  -->
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);margin-right:var(--scp-widget-margin)" class="left-container" v-show="pageCtl.visible.tree" :style="{maxHeight: _initHeight + 'px'}">
            <scp-tree-menu
                ref="treeRef"
                url="/toolbox/custom_report/query_report_list"
                :new-click="()=>pageCtl.visible.newReport = true"
                :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <!--  right body -->
          <el-main style="padding: 0">
            <el-button size="small" @click="toggleTable">
              <font-awesome-icon :icon="pageCtl.treeClass"/>
            </el-button>
            <b style="margin-left: var(--scp-widget-margin)">{{ pageCtl.selectedName }}</b>
            <span style="font-size: 0.45rem;color:var(--scp-text-color-secondary);float: right;">
              <span v-for="(item,index) in pageCtl.selectedGroup" :key="index" style="margin-right: var(--scp-widget-margin)">
                <font-awesome-icon icon="angle-right" v-show="!!item"/>&nbsp;
                <span v-show="!!item">{{item}}</span>
              </span>

              <font-awesome-icon icon="share-alt" style="margin-right: var(--scp-widget-margin);color: var(--scp-text-color-highlight);cursor: pointer" @click="copyShareLink" title="分享报表"/>
              <font-awesome-icon icon="edit" style="margin-right: var(--scp-widget-margin);color: var(--scp-text-color-success);cursor: pointer" @click="queryReportConfig" title="编辑" v-show="pageCtl.selectedName !== ''"/>
            </span>
            <div v-if="pageCtl.errorMessage.length > 0">
              <hr>
              <pre style="color: var(--scp-text-color-error)">{{ pageCtl.errorMessage }}</pre>
            </div>
            <hr style="margin: var(--scp-widget-margin) 0">
            <div v-show="pageCtl.visible.execResult === false">
              <h1 style="text-align: center;font-size: 1.6rem; color: var(--scp-text-color-lighter);height:350px;line-height: 350px">Customized Report</h1>
            </div>
            <div v-show="pageCtl.visible.execResult">
              <scp-table
                  :max-height="2000"
                  ref="reportRef"
                  :lazy="true"
                  url="/toolbox/custom_report/query_report"
                  download-url="/toolbox/custom_report/download_report"
                  :params="pageCtl.searchConditions"
                  :columns="pageCtl.columns"
                  :download-specify-column="false"
                  :after-search="afterReportSearch"
                  :editable="false"/>
            </div>
          </el-main>
        </el-container>
      </div>
    </div>

    <!-- new report -->
    <scp-draggable-resizable id="newReport" w="1000" h="550" v-model="pageCtl.visible.newReport" title="New Report" :save="saveNewReport" :save-loading="pageCtl.loading.save">
      <template v-slot="{height}">
        <div class="new-report">
          <el-row>
            <el-col :span="3" class="title">Report Name</el-col>
            <el-col :span="10" class="content">
              <el-input  v-model="pageCtl.newReport.name"></el-input>
            </el-col>
            <el-col :span="3" class="title" style="text-align: center">Groups</el-col>
            <el-col :span="5" class="content" style="padding-right: 0 !important;">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newReport.groups"
                  :maxlength="30"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
                  style="width: 100% !important;"
              ></el-autocomplete>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="3" class="title">Share To</el-col>
            <el-col :span="14" class="content">
              <el-select placeholder="Share to" v-model="pageCtl.newReport.shareTo"  style="width: 100% !important;" multiple collapse-tags filterable clearable>
                <el-option
                    v-for="item in pageCtl.shareTo"
                    :key="item['VALUE']"
                    :label="item['NAME']"
                    :value="item['VALUE']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" style="padding-left: var(--scp-widget-margin)">
              <el-select placeholder="Cache Type" v-model="pageCtl.newReport.cacheable"  style="width: 100% !important;">
                <el-option
                    v-for="item in [{label: 'Enable Cache', value: 'Y'}, {label: 'Disable Cache', value: 'N'}]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>

        <scp-ace-editor v-model="pageCtl.newReport.sql" lang="sql" :style="{'height': (height - 225) + 'px'}"></scp-ace-editor>

        <div style="padding-left: 15px">
          <el-button  @click="()=>pageCtl.newReport.sql = ''" style="margin-top:20px">
            <font-awesome-icon icon="eraser"/>
          </el-button>
          <el-button  @click="()=> formatSQL(pageCtl.newReport.sql, 'newReport')" style="margin-top:20px" :loading="pageCtl.loading.format">
            <font-awesome-icon icon="align-left"/>
            Format SQL
          </el-button>
          <el-button  @click="()=>search(null, pageCtl.newReport.sql)" style="margin-top:20px" :loading="pageCtl.loading.execute" type="primary">
            <font-awesome-icon icon="search"/>
            Execute SQL
          </el-button>
        </div>
      </template>
    </scp-draggable-resizable>

    <!-- modify report -->
    <scp-draggable-resizable id="modifyReport" w="1000" h="550" v-model="pageCtl.visible.modifyReport" title="Modify Report" >
      <template v-slot="{height}">
        <div class="new-report">
          <el-row>
            <el-col :span="3" class="title">Report Name</el-col>
            <el-col :span="10" class="content">
              <el-input  v-model="pageCtl.modifyReport.name"></el-input>
            </el-col>
            <el-col :span="3" class="title" style="text-align: center">Groups</el-col>
            <el-col :span="5" class="content" style="padding-right: 0 !important;">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.modifyReport.groups"
                  :maxlength="30"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
                  style="width: 100% !important;"
              ></el-autocomplete>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="3" class="title">Share To</el-col>
            <el-col :span="14" class="content">
              <el-select placeholder="Share to" v-model="pageCtl.modifyReport.shareTo"  style="width: 100% !important;" multiple collapse-tags filterable clearable>
                <el-option
                    v-for="item in pageCtl.shareTo"
                    :key="item['VALUE']"
                    :label="item['NAME']"
                    :value="item['VALUE']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" style="padding-left: var(--scp-widget-margin)">
              <el-select placeholder="Cache Type" v-model="pageCtl.modifyReport.cacheable"  style="width: 100% !important;">
                <el-option
                    v-for="item in [{label: 'Enable Cache', value: 'Y'}, {label: 'Disable Cache', value: 'N'}]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>

        <scp-ace-editor v-model="pageCtl.modifyReport.sql" lang="sql" :style="{'height': (height - 225) + 'px'}"></scp-ace-editor>

        <div style="padding-left: 15px">
          <el-button  @click="()=>pageCtl.modifyReport.sql = ''" style="margin-top:20px">
            <font-awesome-icon icon="eraser"/>
          </el-button>
          <el-button  @click="()=> formatSQL(pageCtl.modifyReport.sql, 'modifyReport')" style="margin-top:20px" :loading="pageCtl.loading.format">
            <font-awesome-icon icon="align-left"/>
            Format SQL
          </el-button>
          <el-button  @click="()=>search(null, pageCtl.modifyReport.sql)" style="margin-top:20px" :loading="pageCtl.loading.execute" type="primary">
            <font-awesome-icon icon="search"/>
            Execute SQL
          </el-button>
        </div>
      </template>

      <template v-slot:footer>
        <el-popconfirm title="确定删除此报告? 你只能删除自己创建的信息"
                         iconColor="var(--scp-text-color-error)"
                         @confirm="deleteReport"
                         confirmButtonType="danger"
                         confirmButtonText='确定'
                         cancelButtonText='取消'
                         style="margin-left:15px">
            <template #reference>
              <el-button type="danger" size="small" style="float: left"  v-show="pageCtl.modifyReport.create_by === pageCtl.currentUser || pageCtl.isAdmin">
                Delete
              </el-button>
            </template>
          </el-popconfirm>
          <el-button size="small" @click="pageCtl.visible.modifyReport = false">
            Close
          </el-button>
          <el-button size="small" type="primary"  @click="modifySelectedReport" :loading="pageCtl.loading.save" v-show="pageCtl.modifyReport.create_by === pageCtl.currentUser || pageCtl.isAdmin">
            Save
          </el-button>
      </template>
    </scp-draggable-resizable>

  </div>
</template>

<script lang='ts' setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import '@/assets/css/customer.scss'

const $route = useRoute()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const treeRef = ref()
const reportRef = ref()

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 150
})

const pageCtl = reactive({
  isAdmin: false,
  currentUser: (localStorage.getItem('username') || '').toUpperCase(),
  shareTo: [] as any,
  existsGroup: [] as any,
  selectedId: '',
  selectedName: '',
  selectedGroup: [] as any,
  searchConditions: {
    selectedId: '',
    sql: '',
    cacheable: ''
  },
  newReport: {
    name: '',
    groups: '',
    sql: '',
    shareTo: [] as any,
    cacheable: 'Y'
  },
  modifyReport: {
    reportID: '',
    name: '',
    groups: '',
    sql: '',
    shareTo: [] as any,
    create_by: '',
    cacheable: ''
  },
  columns: [] as any,
  errorMessage: '',
  loading: {
    execute: false,
    format: false,
    save: false,
    tree: false
  },
  visible: {
    tree: true,
    newReport: false,
    modifyReport: false,
    execResult: false
  },
  treeClass: 'caret-left'
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/toolbox/custom_report/init_page'
  }).then((body) => {
    pageCtl.shareTo = body.shareTo
    pageCtl.existsGroup = body.existsGroup
    pageCtl.isAdmin = body.isAdmin
  }).catch((error) => {
    console.log(error)
  })
}

const search = (selectedId, sql) => {
  pageCtl.visible.execResult = true
  pageCtl.errorMessage = ''
  pageCtl.loading.execute = true

  pageCtl.searchConditions.selectedId = selectedId
  pageCtl.searchConditions.sql = sql

  reportRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/toolbox/custom_report/query_report_headers',
    data: pageCtl.searchConditions
  }).then((body) => {
    pageCtl.selectedGroup = []
    if (typeof body === 'string') {
      pageCtl.errorMessage = body
      pageCtl.loading.execute = false
      reportRef.value.clearData()
    } else {
      const columns: any = []
      const headers = body.headers
      const headerTypes = body.headerTypes
      if (body.group) {
        pageCtl.selectedGroup = body.group
      }
      if (body.name) {
        pageCtl.selectedName = body.name
      }
      for (let i = 0; i < headers.length; i++) {
        columns.push({
          data: headers[i],
          type: headerTypes[i],
          precision: 1
        })
      }
      pageCtl.columns = columns
      pageCtl.searchConditions.cacheable = body.cacheable
      reportRef.value.clearAndSearch()
    }
  }).catch((error) => {
    console.log(error)
  })
}

// region edit_area
const formatSQL = (sql, key) => {
  pageCtl.loading.format = true
  $axios({
    method: 'post',
    url: '/home/<USER>/format_sql',
    data: {
      scripts: sql
    }
  }).then((body) => {
    if (body) {
      pageCtl[key].sql = body
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.format = false
  })
}
const afterReportSearch = () => {
  pageCtl.loading.execute = false
}
const deleteReport = () => {
  $axios({
    method: 'post',
    url: '/toolbox/custom_report/delete_report',
    data: {
      selectedId: pageCtl.selectedId
    }
  }).then(() => {
    pageCtl.visible.modifyReport = false
    $message.success('Report deleted!')
    loadTree()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.format = false
  })
}
const saveNewReport = () => {
  if (pageCtl.newReport.name === '' || pageCtl.newReport.groups === '') {
    $message.error('Report Name or Group cannot be empty')
    return
  }
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/toolbox/custom_report/save_new_report',
    data: pageCtl.newReport
  }).then(() => {
    pageCtl.visible.newReport = false
    $message.success('Report saved!')
    loadTree()
    if (pageCtl.existsGroup.indexOf(pageCtl.newReport.groups) === -1) {
      pageCtl.existsGroup.push(pageCtl.newReport.groups)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}
const modifySelectedReport = () => {
  if (pageCtl.modifyReport.name === '' || pageCtl.modifyReport.groups === '') {
    $message.error('Report Name or Group cannot be empty')
    return
  }
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/toolbox/custom_report/modify_selected_report',
    data: pageCtl.modifyReport
  }).then(() => {
    pageCtl.visible.modifyReport = false
    $message.success('Report modified!')
    loadTree()
    if (pageCtl.existsGroup.indexOf(pageCtl.modifyReport.groups) === -1) {
      pageCtl.existsGroup.push(pageCtl.modifyReport.groups)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}
const queryReportConfig = () => {
  $axios({
    method: 'post',
    url: '/toolbox/custom_report/query_report_config',
    data: {
      selectedId: pageCtl.selectedId
    }
  }).then((body) => {
    pageCtl.visible.modifyReport = true

    pageCtl.modifyReport.reportID = body.REPORT_ID
    pageCtl.modifyReport.name = body.REPORT_NAME
    pageCtl.modifyReport.groups = body.GROUP_NAME
    pageCtl.modifyReport.sql = body.SQL
    pageCtl.modifyReport.shareTo = body.shareTo
    pageCtl.modifyReport.create_by = body.CREATE_BY$
    pageCtl.modifyReport.cacheable = body.ENABLE_CACHE
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.format = false
  })
}
// endregion

const toggleTable = () => {
  pageCtl.visible.tree = !pageCtl.visible.tree
  if (pageCtl.visible.tree) {
    pageCtl.treeClass = 'caret-left'
  } else {
    pageCtl.treeClass = 'caret-right'
  }
}

const loadTree = () => {
  pageCtl.visible.execResult = false
  pageCtl.loading.tree = true
  pageCtl.selectedId = ''
  pageCtl.selectedName = ''
  treeRef.value.search()
}

const clickNode = (e) => {
  pageCtl.selectedId = e.key
  pageCtl.selectedName = e.label
  search(e.key, null)
}

const copyShareLink = () => {
  let url = window.location.href
  url = url.split('?')[0]
  url = url + '?cid=' + pageCtl.selectedId
  $copyText(url)
  $message.success('分享链接已复制到剪切板')
}

onMounted(() => {
  initPage()
  const cid = $route.query.cid
  if (cid) {
    clickNode({
      key: cid
    })
  }
})

</script>

<style lang="scss">
.new-report {
  padding-top: 15px;
  padding-left: var(--scp-widget-margin);

  .title {
    text-align: right;
    padding-right: 8px;
    font-weight: bold;
    line-height: 2;
  }

  .content {
    padding-right: 5px;
  }

  .el-row {
    margin-bottom: 12px;
  }
}
</style>
