<template>
  <div class="left-sidebar" id="weChatAutoReply">
    <div class="widget">
      <div class="widget-body">
        <el-container @contextmenu.prevent="">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);margin-right:10px;min-height: 450px"
                    :style="{height: pageCtl.clientHeight + 'px'}"
                    class="left-container">
            <scp-tree-menu
                ref="treeRef"
                url="/toolbox/wechat_auto_reply/query_wechat_auto_reply_list"
                :new-click="showNewNoticeWin"
                :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 1px">
            <div v-show="pageCtl.selectedId === ''">
              <h1 style="text-align: center;font-size: 1.6rem; color: var(--scp-text-color-lighter);height:350px;line-height: 350px">WeChat Auto Reply</h1>
            </div>
            <el-tabs v-model="pageCtl.currentTab" v-show="pageCtl.selectedId !== ''">
              <el-tab-pane label="Last Execution" name="first">
                <div v-loading="pageCtl.loading.report1">
                  <div v-show="!pageCtl.loading.report1">
                    <h5 style="margin: 5px 15px 0 0;font-size: .583rem;display: inline;">{{
                        pageCtl.report1Data['NAME']
                      }}</h5>
                    <el-tag :type="pageCtl.report1Data['STATUS'] === 'Inactive' ? 'danger' : ''" effect="dark"
                            style="margin-right: 5px">
                      {{ pageCtl.report1Data['STATUS'] || '&nbsp;' }}
                    </el-tag>
                    <el-tag :type="pageCtl.report1Data['LAST_REPLY_RESULT_CODE'] !== '200' ? 'danger' : ''" effect="dark"
                            style="margin-right: 5px"
                            v-show="_showLastReplyLogs">
                      {{ pageCtl.report1Data['LAST_REPLY_RESULT_CODE'] === '200' ? 'No Errors' : 'Interrupted' }}
                    </el-tag>
                    <el-tag effect="dark" style="margin-right: 5px" v-show="_showLastReplyLogs">{{
                        pageCtl.report1Data['LAST_REPLY_TIME'] || '&nbsp;'
                      }}
                    </el-tag>
                    <el-tag effect="dark">maintain by {{
                        pageCtl.report1Data['USER_NAME']
                      }}({{ pageCtl.report1Data['SESA_CODE'] }})
                    </el-tag>
                    <hr style="margin: 10px 0 15px 0">
                  </div>
                  <pre style="height: 280px; overflow: auto">{{ pageCtl.report1Data['LOG_FIELD'] }}</pre>
                </div>
                <hr>
                <scp-table
                    ref="report2Ref"
                    url="/toolbox/wechat_auto_reply/query_report2"
                    :lazy="true"
                    :params="{id: pageCtl.selectedId}"
                    :columns="pageCtl.report2TableColumn"
                    :editable="false"
                />
                <hr style="margin-bottom: 1px; margin-top: 3px; opacity: 0">
              </el-tab-pane>
              <el-tab-pane label="Job Info" name="second">
                <div class="new-notice" style="height: var(--scp-input-width);overflow: auto" v-loading="pageCtl.loading.report1">
                  <el-row>
                    <el-col :span="2" class="title">Job Name</el-col>
                    <el-col :span="10" class="content">
                      <el-input v-model="pageCtl.modifyNotice.name"></el-input>
                    </el-col>
                    <el-col :span="12" class="input-tips">
                      任务名, 尽量用最少的字描述任务内容
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Job Code</el-col>
                    <el-col :span="4" class="content">
                      <el-input v-model="pageCtl.modifyNotice.jobCode" :maxlength="4" show-word-limit/>
                    </el-col>
                    <el-col :span="10" class="input-tips">
                      任务Code, 在询问微信的时候使用, 需要4个字符
                    </el-col>
                  </el-row>
                  <el-row v-show="pageCtl.isAdmin">
                    <el-col :span="2" class="title" style="color:red">Owner</el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyNotice.owner" placeholder="Owner" filterable collapse-tags style="width: 100%">
                        <el-option
                            v-for="item in pageCtl.userOpts"
                            :key="item['SESA_CODE']"
                            :label="item['USER_NAME'] + ' [' + item['SESA_CODE'] +']'"
                            :value="item['SESA_CODE']">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="10" class="input-tips">
                      任务归属, 仅Admin权限可见
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Groups</el-col>
                    <el-col :span="4" class="content">
                      <el-autocomplete
                          class="inline-input"
                          v-model="pageCtl.modifyNotice.groups"
                          :maxlength="30"
                          :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                          placeholder="Group"
                          show-word-limit
                      ></el-autocomplete>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      任务分组, 可以使用 <b>.</b> 进行层级分类, 如Level.Leve2.Leve3, 这样会生成三层目录
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Status</el-col>
                    <el-col :span="4" class="content">
                      <el-select v-model="pageCtl.modifyNotice.status">
                        <el-option
                            v-for="item in ['Active','Inactive']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      任务状态, 如果暂时不使用的任务可以设置为Inactive, 确定不再使用的任务, 建议直接删除
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Sleep</el-col>
                    <el-col :span="4" class="content">
                      <el-input-number v-model="pageCtl.modifyNotice.sleep" controls-position="right" :min="10" :max="90"
                                       style="width: 100%"/>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      抓取等待, 单位秒, 爬虫无法确认页面何时加载完毕, 只能预设一个等待时间, 在等待后, 执行截图操作, 大部分页面15秒足够
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Zoom</el-col>
                    <el-col :span="4" class="content">
                      <el-input-number v-model="pageCtl.modifyNotice.zoom" controls-position="right" :min="0.67" :max="1.2"
                                       :step="0.01" style="width: 100%"/>
                    </el-col>
                    <el-col :span="18" class="input-tips">
                      默认抓取分辨率是1920*1080, 但有些页面内容较多, 可以通过调整zoom的值来缩放页面, 以达到可以在一个页面显示所有内容的目的
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="2" class="title">Reply Type</el-col>
                    <el-col :span="8" class="content">
                      <el-radio-group v-model="pageCtl.modifyNotice.replyType" size="small">
                        <el-radio-button key="table" value="table">Data Table</el-radio-button>
                        <el-radio-button key="screenshot" value="screenshot">Screenshot</el-radio-button>
                      </el-radio-group>
                    </el-col>
                  </el-row>
                  <hr>
                  <div v-show="pageCtl.modifyNotice.replyType === 'table'">
                    <el-row>
                      <el-col :span="2" class="title">Prepend</el-col>
                      <el-col :span="17" class="content">
                        <scp-ace-editor v-model="pageCtl.modifyNotice.prepend" lang="html"
                                        style="width:100%;height: 70px;border: 1px dotted var(--scp-border-color);float:right"/>
                      </el-col>
                      <el-col :span="5" class="input-tips">
                        写在表格上面的文字<br>支持MD和HTML
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">SQL Script</el-col>
                      <el-col :span="17" class="content">
                        <scp-ace-editor v-model="pageCtl.modifyNotice.sql" lang="sql"
                                        style="width:100%;height: 150px;border: 1px dotted var(--scp-border-color);float:right"/>
                      </el-col>
                      <el-col :span="5" class="input-tips">
                        参数会以parameter1,2,3的方式传入<br>且固定为数组格式
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">Append</el-col>
                      <el-col :span="17" class="content">
                        <scp-ace-editor v-model="pageCtl.modifyNotice.append" lang="html"
                                        style="width:100%;height: 70px;border: 1px dotted var(--scp-border-color);float:right"/>
                      </el-col>
                      <el-col :span="5" class="input-tips">
                        写在表格下面的文字<br>支持MD和HTML<br>如果表格过长, 这部分数据可能不会显示
                      </el-col>
                    </el-row>
                  </div>

                  <div v-show="pageCtl.modifyNotice.replyType === 'screenshot'">
                    <el-row>
                      <el-col :span="2" class="title">URL</el-col>
                      <el-col :span="10" class="content">
                        <el-input v-model="pageCtl.modifyNotice.url" style="width: 100%"/>
                      </el-col>
                      <el-col :span="12" class="input-tips">
                        需要截图发送的链接, 可以使用DSS的Variant Sharing, 仅支持DSS
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">Area ID</el-col>
                      <el-col :span="6" class="content">
                        <el-select v-model="pageCtl.modifyNotice.areaid" placeholder="Area ID" filterable clearable
                                   style="width: 100%">
                          <el-option
                              v-for="item in pageCtl.reportIDList"
                              :key="item['REPORT_ID']"
                              :label="item['REPORT_ID'] + ' [' + item['TITLE'] +']'"
                              :value="item['REPORT_ID']">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="16" class="input-tips">
                        如果需要发送页面某一张报表的截图, 可以选择对应的报表ID. 如果需要整页截图, 此项留空. 报表ID可在角标处查看
                      </el-col>
                    </el-row>
                  </div>
                  <hr>
                  <el-row>
                    <el-col :span="2" class="title">Remarks</el-col>
                    <el-col :span="17" class="content">
                      <el-input :style="{'padding-bottom':'2px'}" type="textarea"
                                v-model="pageCtl.modifyNotice.remarks"></el-input>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="23" style="text-align: right">
                      <el-popconfirm title="确定删除任务?这个操作不可恢复"
                                     iconColor="var(--scp-text-color-error)"
                                     @confirm="deleteNotice"
                                     confirmButtonType="danger"
                                     cancelButtonType="text"
                                     confirmButtonText='确定'
                                     cancelButtonText='取消'>
                        <template #reference>
                          <el-button type="danger" :loading="pageCtl.loading.delete">
                            <font-awesome-icon icon="times"/>&nbsp;
                            Delete
                          </el-button>
                        </template>
                      </el-popconfirm>
                      <el-button type="primary" @click="modifyCurrentNotice" :loading="pageCtl.loading.modify">
                        <font-awesome-icon icon="save"/>&nbsp;
                        Save
                      </el-button>
                    </el-col>
                    <el-col :span="1">&nbsp;</el-col>
                  </el-row>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-main>
        </el-container>
      </div>
    </div>

    <scp-draggable-resizable id="newNotice" w="1200" h="580" v-model="pageCtl.visible.newNotice" title="New Reply" :save="saveNewNotice"
                             :save-loading="pageCtl.loading.save">
      <template v-slot="{height}">
        <div class="new-notice" :style="{'height':(height - 100) + 'px'}" style="overflow: auto">
          <el-row>
            <el-col :span="2" class="title">Job Name</el-col>
            <el-col :span="10" class="content">
              <el-input v-model="pageCtl.newNotice.name"></el-input>
            </el-col>
            <el-col :span="12" class="input-tips">
              任务名, 尽量用最少的字描述任务内容
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Job Code</el-col>
            <el-col :span="4" class="content">
              <el-input v-model="pageCtl.newNotice.jobCode" :maxlength="4" show-word-limit @change="checkJobCode"/>
            </el-col>
            <el-col :span="10" class="input-tips">
              任务Code, 在询问微信的时候使用, 需要4个字符
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Groups</el-col>
            <el-col :span="4" class="content">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newNotice.groups"
                  :maxlength="30"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
            <el-col :span="18" class="input-tips">
              任务分组, 可以使用 <b>.</b> 进行层级分类, 如Level.Leve2.Leve3, 这样会生成三层目录
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Status</el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newNotice.status">
                <el-option
                    v-for="item in ['Active','Inactive']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="18" class="input-tips">
              任务状态, 如果暂时不使用的任务可以设置为Inactive, 确定不再使用的任务, 建议直接删除
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Sleep</el-col>
            <el-col :span="4" class="content">
              <el-input-number v-model="pageCtl.newNotice.sleep" controls-position="right" :min="10" :max="90"
                               style="width: 100%"/>
            </el-col>
            <el-col :span="18" class="input-tips">
              抓取等待, 单位秒, 爬虫无法确认页面何时加载完毕, 只能预设一个等待时间, 在等待后, 执行截图操作, 大部分页面15秒足够
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Zoom</el-col>
            <el-col :span="4" class="content">
              <el-input-number v-model="pageCtl.newNotice.zoom" controls-position="right" :min="0.67" :max="1.2"
                               :step="0.01" style="width: 100%"/>
            </el-col>
            <el-col :span="18" class="input-tips">
              默认抓取分辨率是1920*1080, 但有些页面内容较多, 可以通过调整zoom的值来缩放页面, 以达到可以在一个页面显示所有内容的目的
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Reply Type</el-col>
            <el-col :span="8" class="content">
              <el-radio-group v-model="pageCtl.newNotice.replyType" size="small">
                <el-radio-button key="table" value="table">Data Table</el-radio-button>
                <el-radio-button key="screenshot" value="screenshot">Screenshot</el-radio-button>
              </el-radio-group>
            </el-col>
          </el-row>
          <hr>
          <div v-show="pageCtl.newNotice.replyType === 'table'">
            <el-row>
              <el-col :span="2" class="title">Prepend</el-col>
              <el-col :span="17" class="content">
                <scp-ace-editor v-model="pageCtl.newNotice.prepend" lang="html"
                                style="width:100%;height: 80px;border: 1px dotted var(--scp-border-color);float:right"/>
              </el-col>
              <el-col :span="5" class="input-tips">
                写在表格上面的文字<br>支持MD和HTML
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="2" class="title">SQL Script</el-col>
              <el-col :span="17" class="content">
                <scp-ace-editor v-model="pageCtl.newNotice.sql" lang="sql"
                                style="width:100%;height: 180px;border: 1px dotted var(--scp-border-color);float:right"/>
              </el-col>
              <el-col :span="5" class="input-tips">
                参数会以parameter1,2,3的方式传入<br>且固定为数组格式
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="2" class="title">Append</el-col>
              <el-col :span="17" class="content">
                <scp-ace-editor v-model="pageCtl.newNotice.append" lang="html"
                                style="width:100%;height: 80px;border: 1px dotted var(--scp-border-color);float:right"/>
              </el-col>
              <el-col :span="5" class="input-tips">
                写在表格下面的文字<br>支持MD和HTML<br>如果表格过长, 这部分数据可能不会显示
              </el-col>
            </el-row>
          </div>

          <div v-show="pageCtl.newNotice.replyType === 'screenshot'">
            <el-row>
              <el-col :span="2" class="title">URL</el-col>
              <el-col :span="10" class="content">
                <el-input v-model="pageCtl.newNotice.url" style="width: 100%"/>
              </el-col>
              <el-col :span="12" class="input-tips">
                需要截图发送的链接, 可以使用DSS的Variant Sharing, 仅支持DSS
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="2" class="title">Area ID</el-col>
              <el-col :span="6" class="content">
                <el-select v-model="pageCtl.newNotice.areaid" placeholder="Area ID" filterable clearable
                           style="width: 100%">
                  <el-option
                      v-for="item in pageCtl.reportIDList"
                      :key="item['REPORT_ID']"
                      :label="item['REPORT_ID'] + ' [' + item['TITLE'] +']'"
                      :value="item['REPORT_ID']">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="16" class="input-tips">
                如果需要发送页面某一张报表的截图, 可以选择对应的报表ID. 如果需要整页截图, 此项留空. 报表ID可在角标处查看
              </el-col>
            </el-row>
          </div>
          <hr>
          <el-row>
            <el-col :span="2" class="title">Remarks</el-col>
            <el-col :span="17" class="content">
              <el-input :style="{'padding-bottom':'2px'}" type="textarea"
                        v-model="pageCtl.newNotice.remarks"></el-input>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>

  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $message: any = inject('$message')
const $axios: any = inject('$axios')
const $startWith: any = inject('$startWith')
const $randomString: any = inject('$randomString')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const report2Ref = ref()
const treeRef = ref()

const pageCtl = reactive({
  isAdmin: false,
  clientHeight: 0,
  userOpts: [],
  existsGroup: [] as any,
  reportIDList: [],
  currentTab: 'first',
  loading: {
    report1: false,
    syncUser: false,
    delete: false,
    modify: false,
    save: false
  },
  visible: {
    newNotice: false
  },
  newNotice: {
    jobCode: '',
    name: '',
    groups: '',
    status: 'Active',
    prepend: '',
    sql: '',
    append: '',
    remarks: '',
    sleep: 15,
    zoom: 1,
    replyType: 'table',
    url: '',
    areaid: ''
  },
  modifyNotice: {
    id: '',
    jobCode: '',
    name: '',
    groups: '',
    status: 'Active',
    prepend: '',
    sql: '',
    append: '',
    remarks: '',
    sleep: 15,
    zoom: 1,
    replyType: 'table',
    url: '',
    areaid: '',
    owner: ''
  },
  report1Data: {} as any,
  selectedId: '',
  selectedName: '',
  report2TableColumn: [
    { data: 'ASK_TEXT', title: 'MESSAGE' },
    { data: 'REPLY_TIME' },
    { data: 'REPLY_TO' },
    { data: 'RESULT_CODE' }
  ]
})

const _showLastReplyLogs = computed(() => {
  return !!pageCtl.report1Data.LAST_REPLY_RESULT_CODE
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/toolbox/wechat_auto_reply/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
    pageCtl.reportIDList = body.reportIDList
    pageCtl.userOpts = body.userOpts
    pageCtl.isAdmin = body.isAdmin
  }).catch((error) => {
    console.log(error)
  })
}

const clickNode = (e) => {
  pageCtl.selectedId = e.key
  pageCtl.selectedName = e.label
  search()
}

const checkJobCode = () => {
  if (pageCtl.newNotice.jobCode.length === 4) {
    $axios({
      method: 'post',
      url: '/toolbox/wechat_auto_reply/check_job_code',
      data: {
        id: pageCtl.newNotice.jobCode.length
      }
    }).then((body) => {
      if (body > 0) {
        $message.error('[' + pageCtl.newNotice.jobCode + '] 已被占用, 请重新选择四位代码')
        pageCtl.newNotice.jobCode = ''
      }
    }).catch((error) => {
      console.log(error)
    })
  }
}

const search = () => {
  searchReport1()
  searchReport2()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/toolbox/wechat_auto_reply/query_report1',
    data: {
      id: pageCtl.selectedId
    }
  }).then((body) => {
    pageCtl.report1Data = body

    pageCtl.modifyNotice.id = body.ID
    pageCtl.modifyNotice.jobCode = body.JOB_CODE
    pageCtl.modifyNotice.owner = body.SESA_CODE
    pageCtl.modifyNotice.name = body.NAME
    pageCtl.modifyNotice.groups = (body.GROUP_NAME || '')
    pageCtl.modifyNotice.status = body.STATUS
    pageCtl.modifyNotice.remarks = body.REMARKS
    pageCtl.modifyNotice.sleep = body.sleep
    pageCtl.modifyNotice.zoom = body.zoom
    pageCtl.modifyNotice.url = body.url
    pageCtl.modifyNotice.prepend = body.prepend
    pageCtl.modifyNotice.sql = body.sql
    pageCtl.modifyNotice.append = body.append
    pageCtl.modifyNotice.replyType = body.replyType
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  nextTick(() => {
    report2Ref.value.search()
  })
}

// region operate_notice_job
const showNewNoticeWin = () => {
  pageCtl.visible.newNotice = true
}

const saveNewNotice = () => {
  pageCtl.newNotice.jobCode = pageCtl.newNotice.jobCode.trim()
  if (pageCtl.newNotice.name === '') {
    $message.error('Job name invalid')
  } else if (pageCtl.newNotice.groups === '') {
    $message.error('Groups is required')
  } else if (pageCtl.newNotice.replyType === 'screenshot' && pageCtl.newNotice.url.trim() === '') {
    $message.error('URL invalid')
  } else if (pageCtl.newNotice.replyType !== 'screenshot' && pageCtl.newNotice.sql.trim() === '') {
    $message.error('SQL script is required.')
  } else if (pageCtl.newNotice.jobCode.length !== 4) {
    $message.error('Job code must be 4 characters')
  } else {
    pageCtl.loading.save = true
    $axios({
      method: 'post',
      url: '/toolbox/wechat_auto_reply/save_notice',
      data: pageCtl.newNotice
    }).then((body) => {
      if (body) {
        $message.error(body)
      } else {
        initPage()
        loadTree()
        pageCtl.visible.newNotice = false
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.save = false
    })
  }
}

const modifyCurrentNotice = () => {
  pageCtl.modifyNotice.jobCode = pageCtl.modifyNotice.jobCode.trim()
  if (pageCtl.modifyNotice.name === '') {
    $message.error('Job name invalid')
  } else if (pageCtl.modifyNotice.groups === '') {
    $message.error('Groups is required')
  } else if (pageCtl.modifyNotice.replyType === 'screenshot' && pageCtl.modifyNotice.url.trim() === '') {
    $message.error('URL invalid')
  } else if (pageCtl.modifyNotice.replyType !== 'screenshot' && pageCtl.modifyNotice.sql.trim() === '') {
    $message.error('SQL script required.')
  } else if (pageCtl.modifyNotice.jobCode.length !== 4) {
    $message.error('Job code must be 4 characters')
  } else {
    pageCtl.loading.modify = true
    $axios({
      method: 'post',
      url: '/toolbox/wechat_auto_reply/modify_notice',
      data: pageCtl.modifyNotice
    }).then((body) => {
      if (body) {
        $message.error(body)
      } else {
        initPage()
        loadTree()
        search()
        $message.success('Job saved')
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.modify = false
    })
  }
}

const deleteNotice = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/toolbox/wechat_auto_reply/delete_notice',
    data: {
      id: pageCtl.selectedId
    }
  }).then(() => {
    initPage()
    loadTree()
    pageCtl.selectedId = ''
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const loadTree = () => {
  treeRef.value.search()
}
// endregion

onMounted(() => {
  initPage()

  pageCtl.clientHeight = document.documentElement.clientHeight - 80
})

</script>

<style lang="scss">
#weChatAutoReply {
  .new-notice {
    padding-top: 15px;
    padding-left: 10px;

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 5px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }

  .input-tips {
    padding-left: 5px;
    font-style: italic;
    color: var(--scp-text-color-secondary);
    line-height: 2;
  }
}
</style>
