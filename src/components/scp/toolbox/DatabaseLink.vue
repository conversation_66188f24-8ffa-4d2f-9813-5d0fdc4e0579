<template>
  <div class="left-sidebar" id="databaseLink">
    <div class="widget">
      <div class="widget-body">
        <el-row>
          <el-col :span="12">
            <div class="subscript-container">
              <el-descriptions
                  title="DSS Temporary Database Link [密码每日更新]"
                  :column="2"
                  size="small"
                  border
              >
                <template #extra>
                  <div style="text-align: center">
                    <input
                        type="text"
                        :value="pageCtl.conditions.dbConfig2.Password"
                        ref="passwordInput"
                        style="position: absolute; left: -9999px;"
                    />
                    <el-button type="primary" @click="copyPassword">Copy Password</el-button>
                  </div>
                </template>

                <el-descriptions-item
                    v-for="(item, index) in pageCtl.conditions.dbConfig"
                    :key="index"
                    :span="item.fullLine ? 2 : 1"
                >
                  <template #label>
                    <div class="cell-item">
                      {{ item.label }}
                    </div>
                  </template>
                  {{ item.value }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
const $axios: any = inject('$axios')

const passwordInput = ref()

const pageCtl = reactive({
  filterOpts: [],
  tableLoading: false,
  conditions: {
    dbConfig: [],
    dbConfig2: {
      Database: 'ORACLE',
      Service: 'SCPDB02',
      Host: '************',
      Port: '1521',
      User: 'scpa_temp',
      Password: '*****',
      URL: '*********************************************'
    } as any
  }
})

const copyPassword = () => {
  const passwordField = passwordInput.value
  passwordField.select()
  document.execCommand('copy')

  ElMessage.success('Temporary password copied to clipboard!')
}

onMounted(() => {
  queryDatabaseAuth()
})

const queryDatabaseAuth = () => {
  $axios({
    method: 'post',
    url: '/toolbox/database_link/query_database_auth'
  }).then((response) => {
    pageCtl.conditions.dbConfig2.Host = response.IP
    pageCtl.conditions.dbConfig2.User = response.USERNAME
    pageCtl.conditions.dbConfig2.Password = response.PASSWORD

    pageCtl.conditions.dbConfig = Object.entries(pageCtl.conditions.dbConfig2).map(([label, value]) => ({
      label,
      value,
      fullLine: ['User', 'Password', 'URL'].includes(label)
    }))
  }).catch((error) => {
    console.log(error)
  })
}
</script>

<style scoped>
.cell-item {
  display: flex;
  align-items: center;

  width: 30px;
  text-align: right;
  padding-right: 10px;
  box-sizing: border-box;
}

>>> .el-descriptions__header {
  margin-bottom: 5px;
}

>>> .el-descriptions__title {
  font-size: 12px;
}
</style>
