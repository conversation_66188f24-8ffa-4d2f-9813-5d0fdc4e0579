<template>
  <!--
  1. report1: 小组件
  2. report2: 时序图
  3. report3: 执行时间top15
  4. report4: 上次执行记录
  5. report5: 执行步骤记录
  6. report6: 修改JOB
  7. report7: kettle diagram
  8. report8: 执行历史
  9. report9: 数据流图
  10. report10: 桑吉图
  -->
  <div class="left-sidebar" id="dataMart">
    <div class="widget">
      <div class="widget-body">
        <el-container style="min-height: 400px;">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);" class="left-container">
            <scp-tree-menu
                ref="treeRef"
                url="/toolbox/data_mart/query_task_list"
                :new-click="() => pageCtl.visible.newJob = true"
                :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0 var(--scp-widget-margin);overflow-y: auto;" :style="{height: pageCtl.dashboardHeight}">
            <div v-show="pageCtl.display === 'dashboard'" style="height: 80%">
              <el-row class="search-box">
                <el-col :span="4">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.date"
                      type="date"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      :clearable="false"
                      @change="search"
                      style="width: 100%">
                  </el-date-picker>
                </el-col>
                <el-col :span="4" style="padding-left: var(--scp-widget-margin)">
                  <el-radio-group v-model="pageCtl.conditions.type" size="small" @change="search">
                    <el-radio-button value="By Time">By Time</el-radio-button>
                    <el-radio-button value="By Rows">By Rows</el-radio-button>
                  </el-radio-group>
                </el-col>
              </el-row>
              <el-row class="dm-widget">
                <el-col :span="4">
                  <el-card shadow="hover" v-loading="pageCtl.loading.report1">
                    <h4>{{ pageCtl.report1Data.WIDGET1 }}</h4>
                    <h6>TASK HEALTHY</h6>
                  </el-card>
                </el-col>
                <el-col :span="4">
                  <el-card shadow="hover" v-loading="pageCtl.loading.report1">
                    <h4>{{ $isEmpty(pageCtl.report1Data.WIDGET2) ? '--' : $thousandBitSeparator(pageCtl.report1Data.WIDGET2) }}</h4>
                    <h6>TIME COST(MIN)</h6>
                  </el-card>
                </el-col>
                <el-col :span="4">
                  <el-card shadow="hover" v-loading="pageCtl.loading.report1">
                    <h4>{{ $isEmpty(pageCtl.report1Data.WIDGET3) ? '--' : $thousandBitSeparator(pageCtl.report1Data.WIDGET3) }}</h4>
                    <h6>AVG SPEED(R/S)</h6>
                  </el-card>
                </el-col>
                <el-col :span="4">
                  <el-card shadow="hover" v-loading="pageCtl.loading.report1">
                    <h4>{{ $isEmpty(pageCtl.report1Data.WIDGET4) ? '--' : $thousandBitSeparator(pageCtl.report1Data.WIDGET4) }}</h4>
                    <h6>EXPORTED ROWS</h6>
                  </el-card>
                </el-col>
                <el-col :span="4">
                  <el-card shadow="hover" v-loading="pageCtl.loading.report1">
                    <h4>{{ $isEmpty(pageCtl.report1Data.WIDGET5) ? '--' : $thousandBitSeparator(pageCtl.report1Data.WIDGET5) }}</h4>
                    <h6>EXPORTED SIZE(MB)</h6>
                  </el-card>
                </el-col>
                <el-col :span="4">
                  <el-card shadow="hover" v-loading="pageCtl.loading.report1" style="margin-right: 0 !important;">
                    <h4>{{ pageCtl.report1Data.WIDGET6 || '--' }}</h4>
                    <h6>OUTPUT EFFICIENCY</h6>
                  </el-card>
                </el-col>
              </el-row>
              <hr>
              <chart style="width: 100%; height: 150px;margin-top: 10px" :option="_report2Opt" v-loading="pageCtl.loading.report2"/>
              <hr>
              <el-row>
                <el-col :span="10">
                  <chart :style="{width: '100%', height: _report9Height + 'px', marginTop: '10px'}" :option="_report3Opt" v-loading="pageCtl.loading.report3"/>
                </el-col>
                <el-col :span="14">
                  <chart :style="{width: '100%', height: _report9Height + 'px', marginTop: '10px'}" :option="_report9Opt" v-loading="pageCtl.loading.report9"/>
                </el-col>
              </el-row>
              <hr>
              <el-row class="search-box">
                <el-col :span="5">
                  <el-time-picker
                      v-model="pageCtl.conditions.time"
                      is-range
                      arrow-control
                      range-separator="~"
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      start-placeholder="Start time"
                      end-placeholder="End time"
                      @change="searchReport10"
                  />
                </el-col>
              </el-row>
              <div ref="report10Ref" style="height: 100%" v-loading="pageCtl.loading.report10"/>
            </div>
            <div v-show="pageCtl.display === 'details'">
              <el-tabs v-model="pageCtl.currentTab" @tab-click="handleTabClick">
                <el-tab-pane label="Last Execution" name="first">
                  <div v-show="pageCtl.hasLastExecution">
                    <div v-loading="pageCtl.loading.report4">
                      <h3 style="margin: 5px 15px 0 0;display: inline;">{{ pageCtl.report4Data['DISPLAY_NAME'] }}</h3>
                      <el-tag :type="pageCtl.report4Data['STATUS'] === 'Finished' ? '' : 'danger'" size="small" effect="dark" style="margin-right: 10px">
                        {{ pageCtl.report4Data['STATUS'] }}
                      </el-tag>
                      <el-tag :type="pageCtl.report4Data['ERRORS'] === 0 ? '' : 'danger'" size="small" effect="dark" style="margin-right: 10px">
                        {{ pageCtl.report4Data['ERRORS'] === 0 ? 'No Errors' : pageCtl.report4Data['ERRORS'] + ' Error(s)' }}
                      </el-tag>
                      <el-tag size="small" effect="dark" style="margin-right: 10px">{{ pageCtl.report4Data['END_DATE'] }}</el-tag>
                      <el-tag size="small" effect="dark" style="margin-right: 10px">{{ _corn }}</el-tag>
                      <el-tag size="small" effect="dark">maintain by {{ pageCtl.report4Data['USER_NAME'] }}({{ pageCtl.report4Data['SESA_CODE'] }})</el-tag>
                      <hr style="margin: 10px 0 15px 0">
                      <el-row class="dm-widget">
                        <el-col :span="4">
                          <el-card shadow="hover" v-loading="pageCtl.loading.report4">
                            <h4>{{ $isEmpty(pageCtl.report4Data['TIME_COST']) ? '--' : $thousandBitSeparator(pageCtl.report4Data['TIME_COST']) }}</h4>
                            <h6>TIME COST(S)</h6>
                          </el-card>
                        </el-col>
                        <el-col :span="4">
                          <el-card shadow="hover" v-loading="pageCtl.loading.report4">
                            <h4>{{ $isEmpty(pageCtl.report4Data['AVG_SPEED']) ? '--' : $thousandBitSeparator(pageCtl.report4Data['AVG_SPEED']) }}</h4>
                            <h6>AVG SPEED(R/S)</h6>
                          </el-card>
                        </el-col>
                        <el-col :span="4">
                          <el-card shadow="hover" v-loading="pageCtl.loading.report4">
                            <h4>{{ $isEmpty(pageCtl.report4Data['LINES_INPUT']) ? '--' : $thousandBitSeparator(pageCtl.report4Data['LINES_INPUT']) }}</h4>
                            <h6>LINES EXPORTED</h6>
                          </el-card>
                        </el-col>
                        <el-col :span="4">
                          <el-card shadow="hover" v-loading="pageCtl.loading.report4">
                            <h4>{{ $isEmpty(pageCtl.report4Data['LINES_OUTPUT']) ? '--' : $thousandBitSeparator(pageCtl.report4Data['LINES_OUTPUT']) }}</h4>
                            <h6>LINES RECIVED</h6>
                          </el-card>
                        </el-col>
                        <el-col :span="4">
                          <el-card shadow="hover" v-loading="pageCtl.loading.report4">
                            <h4>{{ $isEmpty(pageCtl.report4Data['SIZE_IN_MB']) ? '--' : $thousandBitSeparator(pageCtl.report4Data['SIZE_IN_MB']) }}</h4>
                            <h6>EXPORTED SIZE(MB)</h6>
                          </el-card>
                        </el-col>
                        <el-col :span="4">
                          <el-card shadow="hover" v-loading="pageCtl.loading.report4" style="margin-right: 0 !important;">
                            <h4>{{ pageCtl.report4Data['EFFICIENCY'] || '--' }}</h4>
                            <h6>OUTPUT EFFICIENCY</h6>
                          </el-card>
                        </el-col>
                      </el-row>
                      <hr>
                      <pre style="height: 200px; overflow: auto">{{ pageCtl.report4Data['LOG_FIELD'] }}</pre>
                    </div>
                    <hr>
                    <scp-table
                        ref="report5TableRef"
                        :columns="_report5TableColumns"
                        :lazy="true"
                        :params="_report5TableParams"
                        url="/toolbox/data_mart/query_report5"
                    />
                  </div>
                  <div v-show="pageCtl.hasLastExecution === false">
                    <h1 style='text-align: center; margin-top: 100px; color: var(--scp-text-color-lighter)'>No Records!</h1>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="Job Info" name="second">
                  <div class="modify-job" v-loading="pageCtl.loading.report6">
                    <el-row>
                      <el-col :span="2" class="title">Job Name</el-col>
                      <el-col :span="12" class="content">
                        <el-input size="small" v-model="pageCtl.modifyJob.name"></el-input>
                      </el-col>
                      <el-col :span="2" class="title" style="text-align: center">Groups</el-col>
                      <el-col :span="5" class="content">
                        <el-autocomplete
                            class="inline-input"
                            v-model="pageCtl.modifyJob.groups"
                            :maxlength="30"
                            size="small"
                            :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                            placeholder="Group"
                            show-word-limit
                        ></el-autocomplete>
                      </el-col>
                      <el-col :span="2" class="content" style="text-align: right">
                        <el-tooltip :content="pageCtl.modifyJob.enable === 'Y' ? 'Enable' : 'Disable'" placement="top" effect="light">
                          <el-switch
                              v-model="pageCtl.modifyJob.enable"
                              active-value="Y"
                              inactive-value="N"
                              active-color="#13ce66"
                              inactive-color="#ff4949">
                          </el-switch>
                        </el-tooltip>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">Script</el-col>
                      <el-col :span="5" class="content">
                        <el-upload
                            accept=".ktr"
                            :multiple="false"
                            :before-upload="onModifyUploadStart"
                            :on-success="onModifyUploadEnd"
                            :headers="pageCtl.uploadHeader"
                            :file-list="pageCtl.uploadModifyFileList"
                            :on-change="onModifyUploadChange"
                            :action="pageCtl.uploadUrl">
                          <template #trigger>
                            <el-button type="primary" size="small" :loading="pageCtl.loading.newUpload">Select a Kettle Script</el-button>
                          </template>
                        </el-upload>
                      </el-col>
                      <el-col :span="6" class="content" style="text-align: left">
                        <el-link type="primary" @click="downloadFiles(pageCtl.modifyJob.uploadedFilePath)" style="height: 50%">
                          {{ pageCtl.modifyJob.uploadedFileName }}
                        </el-link>
                      </el-col>
                      <el-col :span="10" class="content" style="text-align: right">
                        <el-link type="primary" @click="showDownloadTemplateWin">Download Kettle Template</el-link>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">Trigger</el-col>
                      <el-col :span="4" class="content">
                        <el-select v-model="pageCtl.modifyJob.month" size="small" placeholder="Month" filterable collapse-tags multiple style="width: 100%">
                          <el-option
                              v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="4">
                        <el-select v-model="pageCtl.modifyJob.dayType" size="small" placeholder="Day Type" class="search-group-left"
                                   @change="pageCtl.modifyJob.day = []" style="width: 100%">
                          <el-option
                              v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK']"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="4" class="content">
                        <el-select v-model="pageCtl.modifyJob.day" size="small" :placeholder="pageCtl.modifyJob.dayType" filterable collapse-tags multiple
                                   class="search-group-right" style="width: 100%">
                          <el-option
                              v-for="item in _dayRangeModify"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="4" class="content">
                        <el-select v-model="pageCtl.modifyJob.hour" size="small" placeholder="Hour" filterable collapse-tags multiple style="width: 100%">
                          <el-option
                              v-for="item in _hourRange"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="4" class="content">
                        <el-select v-model="pageCtl.modifyJob.minute" size="small" placeholder="Minute" filterable collapse-tags multiple style="width: 100%">
                          <el-option
                              v-for="item in _minuteRange"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">Remarks</el-col>
                      <el-col :span="21" class="content">
                        <el-input style="height: auto !important;" type="textarea" v-model="pageCtl.modifyJob.remarks"></el-input>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="2" class="title">Parameters</el-col>
                      <el-col :span="21" class="content">
                        <el-row v-for="(item, index) in pageCtl.modifyJob.parameters" style="margin-bottom: 5px" :key="item['key']">
                          <el-col :span="9">
                            <el-input v-model="pageCtl.modifyJob.parameters[index]['pkey']" placeholder="Key" size="small"></el-input>
                          </el-col>
                          <el-col :span="2" style="text-align: center;line-height: 2">
                            <font-awesome-icon icon="long-arrow-alt-right"/>
                          </el-col>
                          <el-col :span="12">
                            <el-input v-model="pageCtl.modifyJob.parameters[index]['pvalue']" placeholder="Value" size="small"></el-input>
                          </el-col>
                          <el-col :span="1" style="text-align: center; line-height: 2;" v-if="index === 0">
                            <font-awesome-icon icon="plus" style="color:var(--scp-text-color-highlight);cursor: pointer" @click="addModifyParameterLine()"/>
                          </el-col>
                          <el-col :span="1" style="text-align: center; line-height: 2;" v-if="index > 0">
                            <font-awesome-icon icon="times" style="color:var(--scp-text-color-error);cursor: pointer" @click="delModifyParameterLine(index)"/>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="23" style="text-align: right">
                        <el-popconfirm title="确定删除任务?这个操作不可恢复"
                                       iconColor="var(--scp-text-color-error)"
                                       @confirm="deleteJob"
                                       confirmButtonType="danger"
                                       cancelButtonType="primary"
                                       confirmButtonText='确定'
                                       cancelButtonText='取消'>
                          <template #reference>
                            <el-button type="danger" size="small">
                              <font-awesome-icon icon="times"/>
                              &nbsp;Delete
                            </el-button>
                          </template>
                        </el-popconfirm>
                        <el-popconfirm title="如果出现【Timeout或超时】错误，请勿重复执行，耐心等待即可。确定要执行任务? "
                                       iconColor="orange"
                                       style="margin-left:10px;margin-right:10px"
                                       @confirm="runJob"
                                       confirmButtonType="warning"
                                       cancelButtonType="primary"
                                       confirmButtonText='确定'
                                       cancelButtonText='取消'>
                          <template #reference>
                            <el-button type="primary" size="small" :loading="pageCtl.loading.run">
                              <font-awesome-icon icon="play" style="font-size: 90%;"/>
                              &nbsp;Run
                            </el-button>
                          </template>
                        </el-popconfirm>
                        <el-button size="small" type="primary" @click="modifyCurrentJob">
                          <font-awesome-icon icon="save"/>
                          &nbsp;Save
                        </el-button>
                      </el-col>
                      <el-col :span="1">&nbsp;</el-col>
                    </el-row>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="Diagram" name="third">
                  <chart style="width: 1000px; height: 700px" :option="_report7Opt" v-loading="pageCtl.loading.report7"/>
                </el-tab-pane>
                <el-tab-pane label="History" name="fourth">
                  <el-row class="search-box">
                    <el-col :span="6">
                      <el-date-picker
                          size="small"
                          v-model="pageCtl.conditions.historyDateRange"
                          type="daterange"
                          :clearable="false"
                          unlink-panels
                          range-separator="to"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          start-placeholder="Start month"
                          @change="searchReport8"
                          end-placeholder="End month">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                  <scp-table
                      ref="report8TableRef"
                      :columns="_report8TableColumns"
                      :lazy="true"
                      :params="_report8TableParams"
                      :contextMenuItems="pageCtl.report8TableMenuItems"
                      :afterSelect="afterReport8Select"
                      url="/toolbox/data_mart/query_report8"/>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-main>
        </el-container>
      </div>
    </div>

    <scp-draggable-resizable w="1000px" h="550px" v-model="pageCtl.visible.newJob" title="New Job" :save="saveNewJob" :save-loading="pageCtl.loading.save">
      <template v-slot="{height}">
        <div class="new-job" :style="{height: (height - 120) + 'px'}" style="overflow: auto">
          <el-row>
            <el-col :span="2" class="title">Job Name</el-col>
            <el-col :span="12" class="content">
              <el-input size="small" v-model="pageCtl.newJob.name"></el-input>
            </el-col>
            <el-col :span="2" class="title" style="text-align: center">Groups</el-col>
            <el-col :span="5" class="content">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newJob.groups"
                  :maxlength="30"
                  size="small"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
            <el-col :span="2" class="content" style="text-align: right">
              <el-switch
                  v-model="pageCtl.newJob.enable"
                  active-value="Y"
                  inactive-value="N"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
              </el-switch>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Script</el-col>
            <el-col :span="5" class="content">
              <el-upload
                  accept=".ktr"
                  :multiple="false"
                  :before-upload="onNewUploadStart"
                  :on-success="onNewUploadEnd"
                  :headers="pageCtl.uploadHeader"
                  :file-list="pageCtl.uploadNewFileList"
                  :on-change="onNewUploadChange"
                  :action="pageCtl.uploadUrl">
                <template #trigger>
                  <el-button type="primary" size="small" :loading="pageCtl.loading.newUpload" :class="pageCtl.newJob.btnClass">Select a Kettle Script
                  </el-button>
                </template>
              </el-upload>
            </el-col>
            <el-col :span="6" class="content">
              <el-link type="primary" @click="downloadFiles(pageCtl.newJob.uploadedFilePath)">{{ pageCtl.newJob.uploadedFileName }}</el-link>
            </el-col>
            <el-col :span="10" class="content" style="text-align: right">
              <el-link type="primary" @click="showDownloadTemplateWin">Download Kettle Template</el-link>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Trigger</el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newJob.month" size="small" placeholder="Month" filterable collapse-tags multiple style="width: 100%">
                <el-option
                    v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="pageCtl.newJob.dayType" size="small" placeholder="Day Type" class="search-group-left" @change="pageCtl.newJob.day = []"
                         style="width: 100%">
                <el-option
                    v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK']"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newJob.day" size="small" :placeholder="pageCtl.newJob.dayType" filterable collapse-tags multiple
                         class="search-group-right" style="width: 100%">
                <el-option
                    v-for="item in _dayRange"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newJob.hour" size="small" placeholder="Hour" filterable collapse-tags multiple style="width: 100%">
                <el-option
                    v-for="item in _hourRange"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="content">
              <el-select v-model="pageCtl.newJob.minute" size="small" placeholder="Minute" filterable collapse-tags multiple style="width: 100%">
                <el-option
                    v-for="item in _minuteRange"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Remarks</el-col>
            <el-col :span="21" class="content">
              <el-input style="height: auto !important;" type="textarea" v-model="pageCtl.newJob.remarks"></el-input>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Parameters</el-col>
            <el-col :span="21" class="content">
              <el-row v-for="(item, index) in pageCtl.newJob.parameters" style="margin-bottom: 5px" :key="item['key']">
                <el-col :span="9">
                  <el-input v-model="pageCtl.newJob.parameters[index]['pkey']" placeholder="Key" size="small"></el-input>
                </el-col>
                <el-col :span="2" style="text-align: center;line-height: 2">
                  <font-awesome-icon icon="long-arrow-alt-right"/>
                </el-col>
                <el-col :span="12">
                  <el-input v-model="pageCtl.newJob.parameters[index]['pvalue']" placeholder="Value" size="small"></el-input>
                </el-col>
                <el-col :span="1" style="text-align: center; line-height: 2;" v-if="index === 0">
                  <font-awesome-icon icon="plus" style="color:var(--scp-text-color-highlight);cursor: pointer" @click="addNewParameterLine()"/>
                </el-col>
                <el-col :span="1" style="text-align: center; line-height: 2;" v-if="index > 0">
                  <font-awesome-icon icon="times" style="color:var(--scp-text-color-error);cursor: pointer" @click="delNewParameterLine(index)"/>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="2" class="title">Disk Mapping</el-col>
            <el-col :span="21" class="content">
              <scp-table2
                  :data="_diskMapping"
                  :columns="pageCtl.diskMappingColumns"
                  :max-height="300"
                  stretchH="none"
              >
              </scp-table2>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="70vw" h="600px" v-model="pageCtl.visible.report8Logs" title="View Logs">
      <template v-slot="{height}">
        <pre style="overflow-y: auto" :style="{height: (height - 120) + 'px'}">{{ pageCtl.report8Logs }}</pre>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="70vw" h="400px" v-model="pageCtl.visible.report8Steps" title="View Steps">
      <template v-slot="{height}">
        <scp-table
            ref="report8StepsTableRef"
            :columns="_report8StepsTableColumns"
            :lazy="true"
            :params="{
            key: pageCtl.selectedReport8BatchID
          }"
            :max-height="height - 120"
            url="/toolbox/data_mart/query_report8_steps"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="800px" h="150px" v-model="pageCtl.visible.downloadTemplate" title="Download Template"
                             :save="downloadTemplate" save-text="Download">
      <template #default>
        <el-row style="padding-top: 10px">
          <el-col :span="3" style="text-align: center; font-weight: bold">Source</el-col>
          <el-col :span="8">
            <el-select v-model="pageCtl.downloadTemplateParam.source" size="small" placeholder="Source" filterable collapse-tags multiple style="width: 100%">
              <el-option
                  v-for="item in pageCtl.dbconfig.source"
                  :key="item['ROW_ID']"
                  :label="item['NAME']"
                  :value="item['ROW_ID']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" style="text-align: center; font-weight: bold">Destination</el-col>
          <el-col :span="8">
            <el-select v-model="pageCtl.downloadTemplateParam.destination" size="small" placeholder="Destination" filterable collapse-tags multiple
                       style="width: 100%">
              <el-option
                  v-for="item in pageCtl.dbconfig.destination"
                  :key="item['ROW_ID']"
                  :label="item['NAME']"
                  :value="item['ROW_ID']">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang='ts' setup>
import { nextTick, inject, onMounted, reactive, ref, computed } from 'vue'
import { gantt } from 'dhtmlx-gantt'
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $notify: any = inject('$notify')

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $isEmpty: any = inject('$isEmpty')
const $downloadFile: any = inject('$downloadFile')
const $shortenNumber: any = inject('$shortenNumber')
const $baseUrl: any = inject('$baseUrl')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const treeRef = ref()
const report5TableRef = ref()
const report8TableRef = ref()
const report8StepsTableRef = ref()
const report2DataRef: any = ref([])
const report10Ref: any = ref()

const $dateFormatter: any = inject('$dateFormatter')

const pageCtl = reactive({
  display: 'dashboard',
  dashboardHeight: '600px',
  hasLastExecution: true,
  conditions: {
    date: '',
    time: ['06:00:00', '23:59:59'],
    type: 'By Time',
    historyDateRange: [] as Array<any>
  },
  currentTab: 'first',
  loading: {
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    report6: false,
    report7: false,
    report9: false,
    report10: false,
    newUpload: false,
    modifyUpload: false,
    save: false,
    run: false
  },
  visible: {
    newJob: false,
    report8Logs: false,
    report8Steps: false,
    downloadTemplate: false
  },
  report1Data: {
    WIDGET1: '--',
    WIDGET2: '--',
    WIDGET3: '--',
    WIDGET4: '--',
    WIDGET5: '--',
    WIDGET6: '--'
  },
  report3Data: {
    xAxis: [],
    yAxis: []
  } as any,
  report4Data: {
    TIME_COST: '--',
    AVG_SPEED: '--',
    LINES_INPUT: '--',
    LINES_OUTPUT: '--',
    SIZE_IN_MB: '--',
    EFFICIENCY: '--'
  } as any,
  selectedTaskID: '',
  selectedBatchID: '',
  report8TableMenuItems: {
    view_logs: {
      name: 'View logs',
      callback: () => {
        searchReport8Logs()
      }
    },
    view_steps: {
      name: 'View steps',
      callback: () => {
        searchReport8Steps()
      }
    },
    view_split0: { name: '---------' }
  },
  report8Logs: '',
  selectedReport8BatchID: '',
  uploadHeader: { token: localStorage.getItem('token') },
  uploadUrl: $baseUrl + '/toolbox/data_mart/upload_job_script',
  uploadNewFileList: [] as Array<any>,
  uploadModifyFileList: [] as Array<any>,
  existsGroup: [],
  newJob: {
    name: '',
    groups: '',
    dayType: 'DAY_OF_MONTH',
    month: ['*'],
    day: [],
    hour: [],
    minute: [],
    enable: 'Y',
    remarks: '',
    btnText: 'Select a Kettle Script',
    btnClass: 'el-button--default',
    uploadedFileName: '',
    uploadedFilePath: '',
    parameters: [
      { pkey: '', pvalue: '' }
    ]
  },
  modifyJob: {
    trans_name: '',
    name: '',
    groups: '',
    dayType: 'DAY_OF_MONTH',
    month: ['*'],
    day: [],
    hour: [],
    minute: [],
    enable: 'Y',
    remarks: '',
    btnText: 'Select a Kettle Script',
    btnClass: 'el-button--default',
    uploadedFileName: '',
    uploadedFilePath: '',
    parameters: [
      { pkey: '', pvalue: '' }
    ]
  },
  dbconfig: {
    source: [],
    destination: []
  },
  downloadTemplateParam: {
    source: [],
    destination: []
  },
  report7Data: {
    data: [],
    links: []
  },
  report9Data: {
    data: [],
    links: []
  },
  diskMappingData: [],
  diskMappingColumns: [
    { data: 'SHARED_DISK', title: 'Available Shared Disk' },
    { data: 'MAPPING_DISK', title: 'Mapping Path' }
  ]
})

const initPage = () => {
  const now = new Date()
  const yesterday = new Date()
  yesterday.setTime(now.getTime() - 24 * 60 * 60 * 1000)
  pageCtl.conditions.date = $dateFormatter(now, 'yyyy/MM/dd')
  const start = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.historyDateRange = [$dateFormatter(start, 'yyyy/MM/dd'), $dateFormatter(end, 'yyyy/MM/dd')]

  $axios({
    method: 'post',
    url: '/toolbox/data_mart/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
    pageCtl.dbconfig.source = body.source
    pageCtl.dbconfig.destination = body.destination
    search()
  }).catch((error) => {
    console.log(error)
  })
  searchDiskMapping()
}

const loadTree = () => {
  search()
  treeRef.value.search()
}

const clickNode = (e) => {
  pageCtl.selectedTaskID = e.key
  nextTick(() => {
    search2()
  })
}

const search = () => {
  pageCtl.display = 'dashboard'
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport9()
  searchReport10()
}

const search2 = () => {
  searchReport4()
  searchReport6()
  searchReport7()
  searchReport8()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    report2DataRef.value = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  if (pageCtl.selectedTaskID) {
    pageCtl.loading.report4 = true
    $axios({
      method: 'post',
      url: '/toolbox/data_mart/query_report4',
      data: {
        key: pageCtl.selectedTaskID
      }
    }).then((body) => {
      pageCtl.hasLastExecution = ($isEmpty(body.STATUS) === false)
      pageCtl.report4Data = body
      pageCtl.selectedBatchID = body.ID_BATCH
      pageCtl.display = 'details'
      nextTick(() => {
        searchReport5()
      })
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.report4 = false
    })
  }
}

const searchReport5 = () => {
  report5TableRef.value.search()
}

const searchReport6 = () => {
  pageCtl.loading.report6 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report6',
    data: {
      key: pageCtl.selectedTaskID
    }
  }).then((body) => {
    pageCtl.modifyJob.trans_name = body.TRANS_NAME
    pageCtl.modifyJob.name = body.DISPLAY_NAME
    pageCtl.modifyJob.groups = body.GROUPS
    pageCtl.modifyJob.dayType = body.DAY_TYPE
    pageCtl.modifyJob.month = JSON.parse(body.MONTH)
    pageCtl.modifyJob.day = JSON.parse(body.DAY)
    pageCtl.modifyJob.hour = JSON.parse(body.HOUR)
    pageCtl.modifyJob.minute = JSON.parse(body.MINUTE)
    pageCtl.modifyJob.enable = body.STATUS
    pageCtl.modifyJob.remarks = body.REMARKS
    let path = body.SCRIPT_PATH
    path = path.replace(/\\/g, '/')
    pageCtl.modifyJob.uploadedFileName = path.substring(path.lastIndexOf('/') + 1)
    pageCtl.modifyJob.uploadedFilePath = body.SCRIPT_PATH
    pageCtl.modifyJob.parameters = JSON.parse(body.PARAMETERS)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report6 = false
  })
}

const searchReport7 = () => {
  pageCtl.loading.report7 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report7',
    data: {
      key: pageCtl.selectedTaskID
    }
  }).then((body) => {
    pageCtl.report7Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report7 = false
  })
}

const searchReport8 = () => {
  report8TableRef.value.search()
}

const searchReport9 = () => {
  pageCtl.loading.report9 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report9',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report9Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report9 = false
  })
}

const initReport10 = () => {
  gantt.config.xml_date = '%Y/%m/%d %H:%i:%s'
  gantt.config.scale_unit = 'hour'
  gantt.config.duration_unit = 'minute'
  gantt.config.date_scale = '%H:%i'
  gantt.config.fit_tasks = true
  gantt.config.readonly = true
  gantt.config.task_height = 20
  gantt.config.row_height = 26
  gantt.config.columns = [
    {
      name: 'text',
      label: 'Task Name',
      width: '220',
      template: function (obj) {
        const title = obj.text
        const ts1 = obj.text.split('.')
        const no = ts1[0]
        const taskName = ts1.slice(1).join('.')

        const taskNames = taskName.split(' - ')
        const groups = taskNames[0]
        const name = taskNames.slice(1).join(' - ')

        let html = no + '. <b>' + groups + '</b> - ' + name
        if (obj.text.indexOf('[running]') !== -1) {
          html = '<span style="color:var(--scp-text-color-success)">' + html + '</span>'
        }
        return '<span title="' + title + '">' + html + '</span>'
      }
    },
    {
      name: 'duration',
      label: 'Duration',
      align: 'right',
      template: function (obj) {
        return obj.duration + 'min'
      }
    }
  ]
  gantt.templates.task_class = function (start, end, task) {
    if (task.text.indexOf('[running]') !== -1) {
      return 'running'
    }
    return 'finish'
  }
}

const searchReport10 = () => {
  pageCtl.loading.report10 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report10',
    data: pageCtl.conditions
  }).then((body) => {
    const data = [] as Array<any>
    for (let i = 0; i < body.length; i++) {
      const e = body[i]
      data.push({
        id: (i + 1),
        text: e.KEY,
        start_date: e.START_DATE,
        duration: e.DURATION
      })
    }

    gantt.init(report10Ref.value)
    gantt.clearAll()
    gantt.parse({
      data
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report10 = false
  })
}

const searchDiskMapping = () => {
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_disk_mapping'
  }).then((body) => {
    pageCtl.diskMappingData = body
  }).catch((error) => {
    console.log(error)
  })
}

const afterReport8Select = (r) => {
  pageCtl.selectedReport8BatchID = r.KEY
}

const searchReport8Logs = () => {
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/query_report8_logs',
    data: {
      key: pageCtl.selectedReport8BatchID
    }
  }).then((body) => {
    pageCtl.visible.report8Logs = true
    pageCtl.report8Logs = body
  }).catch((error) => {
    console.log(error)
  })
}

const searchReport8Steps = () => {
  pageCtl.visible.report8Steps = true
  report8StepsTableRef.value.search()
}

const handleTabClick = (e) => {
  if (e.name === 'third') {

  }
}

const onNewUploadStart = () => {
  pageCtl.loading.newUpload = true
  pageCtl.newJob.btnText = 'Uploading'
  pageCtl.newJob.btnClass = 'el-button--default'
}

const onNewUploadChange = (file) => {
  pageCtl.uploadNewFileList = [file]
}

const onNewUploadEnd = (res) => {
  if (res.header.status !== 200) {
    $notify({
      title: 'Upload Failed',
      message: res.header.message,
      dangerouslyUseHTMLString: true,
      type: 'error'
    })
    pageCtl.newJob.btnText = 'Upload Failed'
    pageCtl.newJob.btnClass = 'el-button--danger'
  } else {
    pageCtl.newJob.uploadedFilePath = res.body.path
    pageCtl.newJob.uploadedFileName = res.body.name
    pageCtl.newJob.btnText = 'Upload Success'
    pageCtl.newJob.btnClass = 'el-button--primary'
  }
  pageCtl.loading.newUpload = false
}

const onModifyUploadStart = () => {
  pageCtl.loading.modifyUpload = true
  pageCtl.modifyJob.btnText = 'Uploading'
  pageCtl.modifyJob.btnClass = 'el-button--default'
}

const onModifyUploadChange = (file) => {
  pageCtl.uploadModifyFileList = [file]
}

const onModifyUploadEnd = (res) => {
  if (res.header.status !== 200) {
    $notify({
      title: 'Upload Failed',
      message: res.header.message,
      dangerouslyUseHTMLString: true,
      type: 'error'
    })
    pageCtl.modifyJob.btnText = 'Upload Failed'
    pageCtl.modifyJob.btnClass = 'el-button--danger'
  } else {
    pageCtl.modifyJob.uploadedFilePath = res.body.path
    pageCtl.modifyJob.uploadedFileName = res.body.name
    pageCtl.modifyJob.btnText = 'Upload Success'
    pageCtl.modifyJob.btnClass = 'el-button--primary'
  }
  pageCtl.loading.modifyUpload = false
}

const delNewParameterLine = (i) => {
  pageCtl.newJob.parameters.splice(i, 1)
}

const addNewParameterLine = () => {
  pageCtl.newJob.parameters.push({ pkey: '', pvalue: '' })
}

const delModifyParameterLine = (i) => {
  pageCtl.modifyJob.parameters.splice(i, 1)
}

const addModifyParameterLine = () => {
  pageCtl.modifyJob.parameters.push({ pkey: '', pvalue: '' })
}

const saveNewJob = () => {
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/save_new_job',
    data: pageCtl.newJob
  }).then((body) => {
    if (parseInt(body + '') === 500) {
      $message.error('发现未经授权的数据源!')
    } else {
      $message.success('Job Created')
      pageCtl.visible.newJob = false
      loadTree()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const showDownloadTemplateWin = () => {
  pageCtl.visible.downloadTemplate = true
}

const downloadTemplate = () => {
  $downloadFile('/toolbox/data_mart/download_kettle_template', pageCtl.downloadTemplateParam, null, {
    type: 'text/xml'
  })
}

const downloadFiles = (path) => {
  $downloadFile('/toolbox/data_mart/download_file', {
    path
  }, null, {
    type: 'text/xml'
  })
}

const deleteJob = () => {
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/delete_job',
    data: {
      trans_name: pageCtl.modifyJob.trans_name
    }
  }).then(() => {
    $message.success('Job Deleted!')
    loadTree()
  }).catch((error) => {
    console.log(error)
  })
}

const modifyCurrentJob = () => {
  pageCtl.loading.report6 = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/modify_job',
    data: pageCtl.modifyJob
  }).then((body) => {
    if (parseInt(body + '') === 500) {
      $message.error('发现未经授权的数据源!')
    } else {
      $message.success('Job Modified')
      search2()
    }
    loadTree()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report6 = false
  })
}

const runJob = () => {
  pageCtl.loading.run = true
  $axios({
    method: 'post',
    url: '/toolbox/data_mart/run_job',
    data: pageCtl.modifyJob
  }).then((body) => {
    if (body) {
      $message.error(body)
    } else {
      $message.success('Job executed')
      pageCtl.currentTab = 'first'
      searchReport4()
      searchReport5()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.run = false
  })
}

const _corn = computed(() => {
  const array = [] as Array<any>
  if (pageCtl.report4Data && pageCtl.report4Data.MONTH) {
    array.push(JSON.parse(pageCtl.report4Data.MONTH).join(',') || '-')
    array.push(JSON.parse(pageCtl.report4Data.DAY).join(',') || '-')
    array.push(JSON.parse(pageCtl.report4Data.HOUR).join(',') || '-')
    array.push(JSON.parse(pageCtl.report4Data.MINUTE).join(',') || '-')
  }
  return '[' + array.join('] [') + ']'
})

const _dayRange = computed(() => {
  const result = ['*']
  if (pageCtl.newJob.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.newJob.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  }
  return result
})

const _dayRangeModify = computed(() => {
  const result = ['*']
  if (pageCtl.modifyJob.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.modifyJob.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  }
  return result
})

const _hourRange = computed(() => {
  const result = [] as Array<any>
  for (let i = 7; i < 23; i++) {
    result.push(i + '')
  }
  return result
})

const _minuteRange = computed(() => {
  const result = [] as Array<any>
  for (let i = 0; i <= 59; i++) {
    result.push(i + '')
  }
  return result
})

const _report2Opt = computed(() => {
  return {
    color: ['#fc8452', '#3dcd58'],
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      position: 'top',
      formatter: (params) => {
        const data = params.data
        const tip = [] as Array<any>
        tip.push('<div style="width:7rem;">')
        tip.push(params.marker)
        tip.push('Peak(%) - ')
        tip.push(data[0] + ': ')
        tip.push('<span style="font-size: 90%;float: right">')
        tip.push((data[1] * 100).toFixed(1) + '%')
        tip.push('</span></div>')
        return tip.join('')
      }
    },
    singleAxis: [{
      left: 0,
      right: 0,
      bottom: 20,
      type: 'category',
      boundaryGap: false,
      splitLine: { show: false },
      data: ['', '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '']
    }],
    series: [{
      singleAxisIndex: 0,
      coordinateSystem: 'singleAxis',
      type: 'scatter',
      data: report2DataRef.value.DESC,
      symbolSize: (dataItem) => {
        return dataItem[1] * 100
      }
    }, {
      singleAxisIndex: 0,
      coordinateSystem: 'singleAxis',
      type: 'scatter',
      data: report2DataRef.value.SOURCE,
      symbolSize: (dataItem) => {
        return dataItem[1] * 100
      }
    }]
  }
})

const _report3Opt = computed(() => {
  return {
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const data = params[0]
        const tip = [] as Array<any>
        let names = data.name.split('.')
        const name = names[names.length - 1]
        names = names.slice(0, names.length - 1)
        if (names.length > 0) {
          tip.push(names.join(' > '))
          tip.push('<br>')
        }
        tip.push(data.marker)
        tip.push(name)
        tip.push(':&nbsp;&nbsp;&nbsp;&nbsp;')
        if (pageCtl.conditions.type === 'By Time') {
          tip.push($thousandBitSeparator(data.value, 0) + ' S')
        } else if (pageCtl.conditions.type === 'By Rows') {
          tip.push($thousandBitSeparator(data.value, 1) + 'K Rows')
        }
        return tip.join('')
      }
    },
    grid: $grid({ top: 30, left: 10, right: 10, bottom: 0 }),
    xAxis: {
      type: 'value',
      splitLine: { show: false }
    },
    yAxis: {
      type: 'category',
      axisLabel: {
        show: false
      },
      itemStyle: {
        color: pageCtl.report3Data.colors
      },
      data: pageCtl.report3Data.yAxis
    },
    series: [
      {
        type: 'bar',
        data: pageCtl.report3Data.xAxis,
        itemStyle: {
          color: (params) => {
            return pageCtl.report3Data.colors[params.dataIndex]
          }
        }
      }
    ]
  }
})

const _report5TableParams = computed(() => {
  return {
    key: pageCtl.selectedBatchID
  }
})

const _report5TableColumns = computed(() => {
  return [
    { data: 'STEP_NAME', title: 'Step Name' },
    { data: 'LINES_READ', title: 'Lines Read', type: 'numeric' },
    { data: 'LINES_WRITTEN', title: 'Lines Written', type: 'numeric' },
    { data: 'LINES_UPDATED', title: 'Lines Updated', type: 'numeric' },
    { data: 'LINES_INPUT', title: 'Lines Input', type: 'numeric' },
    { data: 'LINES_OUTPUT', title: 'Lines Output', type: 'numeric' },
    { data: 'LINES_REJECTED', title: 'Lines Rejected', type: 'numeric' },
    { data: 'ERRORS', title: 'Errors', type: 'numeric' },
    { data: 'STATUS_DESC', title: 'Status Desc' },
    {
      data: 'SPEED',
      title: 'Speed (r/s)',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    },
    { data: 'TIMECOST', title: 'Timecost (s)', type: 'numeric', precision: 1 }
  ]
})

const _report7Opt = computed(() => {
  return {
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      formatter: (a) => {
        if (a.data.type) {
          return '[' + a.data.type + '] - ' + a.data.name
        } else {
          return null
        }
      }
    },
    grid: $grid({ top: 30, left: 10, right: 10, bottom: 0 }),
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [
      {
        type: 'graph',
        layout: 'none',
        data: pageCtl.report7Data.data,
        links: pageCtl.report7Data.links,
        edgeSymbol: [null, 'arrow']
      }
    ]
  }
})

const _report8TableColumns = computed(() => {
  return [
    { data: 'TRANS_NAME' },
    {
      data: 'STATUS',
      render: (hotInstance, td, row, column, prop, value) => {
        td.innerHTML = value
        if (value !== 'Finished') {
          td.style.color = '#f83a05'
        }
      }
    },
    { data: 'START_DATE' },
    { data: 'END_DATE' },
    { data: 'ERRORS', type: 'numeric' },
    { data: 'SPEED', title: 'Speed (r/s)', type: 'numeric', precision: 1 },
    { data: 'TIME_COST', title: 'Timecost (s)', type: 'numeric', precision: 1 },
    { data: 'LINES_INPUT', type: 'numeric' },
    { data: 'LINES_OUTPUT', type: 'numeric' },
    { data: 'LINES_UPDATED', type: 'numeric' },
    { data: 'LINES_REJECTED', type: 'numeric' },
    { data: 'LINES_READ', type: 'numeric' },
    { data: 'LINES_WRITTEN', type: 'numeric' }
  ]
})

const _report8TableParams = computed(() => {
  return {
    key: pageCtl.selectedTaskID,
    dateRange: pageCtl.conditions.historyDateRange
  }
})

const _report8StepsTableColumns = computed(() => {
  return [
    { data: 'STEP_NAME', title: 'Step Name' },
    { data: 'LINES_READ', title: 'Lines Read', type: 'numeric' },
    { data: 'LINES_WRITTEN', title: 'Lines Written', type: 'numeric' },
    { data: 'LINES_UPDATED', title: 'Lines Updated', type: 'numeric' },
    { data: 'LINES_INPUT', title: 'Lines Input', type: 'numeric' },
    { data: 'LINES_OUTPUT', title: 'Lines Output', type: 'numeric' },
    { data: 'LINES_REJECTED', title: 'Lines Rejected', type: 'numeric' },
    { data: 'ERRORS', title: 'Errors', type: 'numeric' },
    { data: 'STATUS_DESC', title: 'Status Desc' },
    {
      data: 'SPEED',
      title: 'Speed (r/s)',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    },
    { data: 'TIMECOST', title: 'Timecost (s)', type: 'numeric', precision: 1 }
  ]
})

const _report9Height = computed(() => {
  return Math.max(document.documentElement.clientHeight - 550, 300)
})

const _report9Opt = computed(() => {
  return {
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      formatter: (a) => {
        let html = a.name
        html += '<br/>'
        html += 'Rows: ' + $shortenNumber(a.value) + '<br/>'
        return html
      }
    },
    grid: $grid({ top: 30, left: 10, right: 10, bottom: 0 }),
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [
      {
        type: 'sankey',
        data: pageCtl.report9Data.data,
        links: pageCtl.report9Data.links
      }
    ]
  }
})

const _diskMapping = computed(() => {
  const result = [] as Array<any>
  for (let i = 0; i < pageCtl.diskMappingData.length; i++) {
    const data = pageCtl.diskMappingData[i]
    result.push({
      SHARED_DISK: data[0],
      MAPPING_DISK: data[1]
    })
  }
  return result
})

onMounted(() => {
  initPage()
  pageCtl.dashboardHeight = (document.documentElement.clientHeight - 90) + 'px'
  initReport10()
})

</script>

<style lang="scss">
#dataMart {
  .dm-widget {
    margin-top: 10px;

    .el-card {
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      border-radius: 0 !important;
      margin: 0 var(--scp-widget-margin) 0 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary) !important;
          margin: 0 !important;
        }
      }

    }
  }

  .new-job {
    padding-top: 15px;
    padding-left: 10px;

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 5px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }

  .modify-job {
    background: var(--scp-bg-color);
    border: 1px solid var(--scp-border-color);
    border-radius: 2px;
    padding-top: 15px;
    padding-left: 10px;

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 10px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }

  .gantt_cell {
    font-size: 0.5rem;
    color: var(--scp-text-color-primary)
  }

  .gantt_row, .gantt_task_row, .gantt_grid_scale, .gantt_task_scale, .gantt_task_vscroll {
    background-color: var(--scp-bg-color) !important;
  }

  .gantt_row, .gantt_task_row {
    border-bottom: 1px solid var(--scp-border-color-lighter) !important;
  }

  .gantt_task_cell {
    border-right: 1px solid var(--scp-border-color-lighter) !important;
  }

  .gantt_bars_area {
    .running {
      background-color: var(--scp-bg-color-success);
      border: 1px solid var(--scp-border-color-success);
    }
  }
}
</style>
