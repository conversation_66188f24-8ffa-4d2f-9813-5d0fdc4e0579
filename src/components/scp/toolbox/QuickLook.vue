<template>
  <div class="left-sidebar" style="width: 100%;display: flex" id="quickLook" :style="{height: pageCtl.layout.commentsHeight + 'px'}">
    <div :style="{width : pageCtl.layout.documentWidth, position: 'relative'}">
      <div class="quicklook-header" :style="{width: _clientWidth}">
        <div style="display: flex;justify-content: center;align-items: center">
          <img src="/img/brain.svg" alt="Quick Access" style="height: 2rem;"/>
          <img src="/img/quick-access.svg" alt="Quick Access" style="height: 3rem;"/>
        </div>
        <el-autocomplete
            v-model="pageCtl.keywords"
            :maxlength="100"
            size="large"
            :fetch-suggestions="queryKeywords"
            placeholder="Table Name / Column Name / View Name / Document Word"
            show-word-limit
            clearable
            style="width: 100% !important;"
            @select="search">
          <template #append>
            <el-button-group>
              <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Copy Share Link">
                <el-button style="width: 2rem" @click="shareLink" :disabled="pageCtl.currentElement.value===''">
                  <font-awesome-icon icon="share-alt"/>
                </el-button>
              </el-tooltip>
              <el-button style="border-left: 1px solid var(--scp-border-color);width: 2rem" v-if="pageCtl.isAdmin" @click="pageCtl.visible.create = true">
                <font-awesome-icon icon="plus"/>
              </el-button>
            </el-button-group>
          </template>
          <template v-slot="{ item }">
            <el-row>
              <el-col :span="20">
                <div v-html="hightLight(item.value)"></div>
              </el-col>
              <el-col :span="4" style="text-align: right;color: var(--scp-text-color-secondary)">{{ item.label }}</el-col>
            </el-row>
          </template>
        </el-autocomplete>
      </div>
      <div style="margin:auto;" v-loading="pageCtl.loading.search" :style="{width: _clientWidth}">
        <scp-md-preview v-model="pageCtl.result"/>
      </div>
      <div class="aside-open-close" @click="showComments" v-if="pageCtl.isAvailable" :style="{right: pageCtl.isExpanded? 'calc(30% + 8px)' : '8px'}">
        <i>
          <font-awesome-icon :icon="['far', 'message']"/>
        </i>
      </div>
    </div>
    <div class="quicklook-comment" v-if="pageCtl.isExpanded"
         :style="{width : pageCtl.layout.commentsWidth, height: pageCtl.layout.commentsHeight + 'px', zIndex: 1000}">
      <div style="padding:0 10px">
        <h1 style="font-size: 0.6rem;text-align: center">Discussions</h1>
        <!--输入框-->
        <div class="input-comment">
          <el-input v-model="pageCtl.textareaContent" placeholder="Looking forward to your comments..." type="textarea"/>
          <div style="margin-top: 100px">
            <el-button @click="sendComment" style="float: right" :loading="pageCtl.loading.comments">Submit</el-button>
          </div>
        </div>
        <!--评论列表-->
        <div style="margin-top: 10px">
          <div style="font-weight: bold;margin-bottom: 0.2rem">{{ pageCtl.comments.length }} comments · <span style="color: var(--scp-text-color-lighter)">{{_repliesCnt}} replies</span></div>
          <div class="comment" v-for="item in pageCtl.comments" :key="item">
            <div style="padding: 10px">
              <div style="display: flex;">
                <span>
                  <el-avatar :size="20" :src="'https://scp-dss.cn.schneider-electric.com/avatar/' + item.USER_ID + '.jpg'">
                    <img :src="item.IS_MAINTAINER === 'Y' ? '/img/avatar-admin.png': '/img/avatar.png'" alt="">
                  </el-avatar>
                </span>
                <span style="margin-left: 10px">
                  {{ item.USER_NAME }}
                  <span style="margin-left: 10px;color: var(--scp-text-color-secondary);font-size: 90%">{{ item.CREATE_DATE }}</span>
                </span>
                <span style="margin-left: auto;color: var(--scp-text-color-secondary);font-size: 90%">{{ item.REPLIES_CNT }} replies</span>
                <span style="cursor: pointer;margin-left: 20px" @click="openReply(item)">
                <el-tooltip :show-after="1000" effect="light" placement="top" content="Reply">
                  <font-awesome-icon icon="fa-reply"/>
                </el-tooltip>
              </span>
                <span style="cursor: pointer;margin-left: 2%" class="hover-icon" @click="deleteComment(item)" v-if="item.IS_ME">
                <el-tooltip :show-after="1000" effect="light" placement="top" content="Delete">
                  <font-awesome-icon :icon="pageCtl.loading.delete ? 'spinner' : 'fa-trash-alt'" :spin="pageCtl.loading.delete"/>
                </el-tooltip>
              </span>
              </div>
              <div>{{ item.CONTENT }}</div>
              <div class="input-reply" v-if="item.openReply">
                <el-input v-model="pageCtl.replyContent" :placeholder="item.placeholder" type="textarea"/>
                <el-button @click="sendReply(item)" style="float: right;margin-top: 70px" :loading="pageCtl.loading.comments">Submit</el-button>
              </div>
            </div>
            <!--回复列表-->
            <div style="padding: 12px 10px 0 10px;background-color: var(--scp-bg-color-fill);border-radius: 0 0 6px 6px" v-if="item.REPLIES.length > 0">
              <div style="padding-bottom: 7px;" v-for="reply in item.REPLIES" :key="reply.REPLY_ID">
                <div class="reply-top">
                  <span>
                    <el-avatar :size="20" :src="'https://scp-dss.cn.schneider-electric.com/avatar/' + reply.USER_ID + '.jpg'">
                      <img :src="reply.IS_MAINTAINER === 'Y' ? '/img/avatar-admin.png': '/img/avatar.png'" alt="">
                    </el-avatar>
                  </span>
                  <span style="margin-left: 10px">
                    {{ reply.USER_NAME }}
                    <span style="margin-left: 10px;color: var(--scp-text-color-secondary);font-size: 90%">{{ reply.CREATE_DATE }}</span>
                  </span>
                  <span style="cursor: pointer;margin-left: auto" class="hover-icon" @click="deleteReply(reply)" v-if="reply.IS_ME">
                  <el-tooltip :show-after="1000" effect="light" placement="top" content="Delete">
                    <font-awesome-icon :icon="pageCtl.loading.delete ? 'fa-spinner' : 'fa-trash-alt'" :spin="pageCtl.loading.delete"/>
                  </el-tooltip>
                </span>
                </div>
                <div class="reply-content">{{ reply.CONTENT }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.visible.histComments" title="History Comments">
      <template v-slot="{ height }">
        <scp-md-preview :style="{ height : height - 100 + 'px'}" v-model="pageCtl.histComments"/>
      </template>
    </scp-draggable-resizable>
  </div>

  <scp-draggable-resizable h="600" w="1160" v-model="pageCtl.visible.create" title="New Comments" :save="saveComments" :save-loading="pageCtl.loading.save"
    :draft-text="pageCtl.newComments.editorType === 'html'? 'Switch to Markdown Editor': 'Switch to HTML Editor'" :draft="switchEditor"
    delete-confirm-text="确定导入表格Comments模版? 此操作会覆盖未保存的内容" delete-text="使用表格Comments模版" delete-btn-type="default"
    :delete="pageCtl.newComments.columnName === '[OBJECT_COMMENTS]'? importCommentsTemplate : undefined">
    <template v-slot="{height}">
      <el-form :model="pageCtl.newComments" label-width="100px" style="padding:20px 15px 5px 5px" :rules="pageCtl.rules">
        <el-form-item label="Object" prop="tableName">
          <el-row style="width: 100%">
            <el-col :span="23">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newComments.tableName"
                  :maxlength="100"
                  :fetch-suggestions="queryKeywords"
                  :placeholder="pageCtl.loading.init ? 'Loading...' : 'Table / View / MView - ' + $thousandBitSeparator(_allObjectOpts.length) + ' items avalible'"
                  clearable
                  show-word-limit
                  style="width: 100% !important;"
                  @select="tableNameChange">
                <template #default="{ item }">
                  <el-row>
                    <el-col :span="21">{{ item.value }}</el-col>
                    <el-col :span="3" style="text-align: right;color: var(--scp-text-color-secondary)">{{ item.label }}</el-col>
                  </el-row>
                </template>
              </el-autocomplete>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="Column" prop="columnName">
          <el-row style="width: 100%">
            <el-col :span="23">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newComments.columnName"
                  :maxlength="100"
                  :fetch-suggestions="(query, cb) => autoCompleteSuggestion(pageCtl.columnOpts, query, cb)"
                  :placeholder="pageCtl.loading.column ? 'Loading...' : 'Column Name - ' + $thousandBitSeparator(pageCtl.columnOpts.length) + ' items avalible'"
                  show-word-limit
                  clearable
                  select-when-unmatched
                  style="width: 100% !important;"
                  @select="columnNameChange">
                <template #default="{ item }">
                  <el-row>
                    <el-col :span="21">{{ item.value }}</el-col>
                    <el-col :span="3" style="text-align: right;color: var(--scp-text-color-secondary)">{{ item.label }}</el-col>
                  </el-row>
                </template>
              </el-autocomplete>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="Comments" required prop="comments">
          <el-row style="width: 100%">
            <el-col :span="23">
              <scp-ck-editor v-model="pageCtl.newComments.comments" :placeholder="pageCtl.loading.comments? 'Loading...' : 'Comments'"
                             style="width: 100%" :style="{height: (height - 215) + 'px'}" v-if="pageCtl.newComments.editorType === 'html'"/>
              <scp-md-editor v-model="pageCtl.newComments.comments" :style="{'height': (height - 215) + 'px'}" v-if="pageCtl.newComments.editorType === 'markdown'"/>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </template>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'

const $store = useStore()
const $route = useRoute()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')

const _clientWidth = computed(() => {
  if (document.documentElement.clientWidth > 1600) {
    if (pageCtl.layout.documentWidth === '100%') {
      return '1280px'
    } else {
      return '896px'
    }
  } else {
    if (pageCtl.layout.documentWidth === '100%') {
      return '1024px'
    } else {
      return '716px'
    }
  }
})

const pageCtl = reactive({
  query: '',
  result: '',
  histComments: '',
  currentElement: {
    value: '',
    label: ''
  },
  allAvalibleObjects: '',
  isAdmin: false,
  isAvailable: false,
  isExpanded: false,
  textareaContent: '',
  replyContent: '',
  comments: [] as Array<any>,
  visible: {
    create: false,
    histComments: false
  },
  loading: {
    init: false,
    column: false,
    comments: false,
    delete: false,
    save: false,
    search: false
  },
  newComments: {
    tableName: '',
    columnName: '',
    comments: '',
    editorType: 'markdown'
  },
  columnOpts: [],
  rules: {
    tableName: [
      { required: true, message: 'Please select an object', trigger: 'change' }
    ],
    columnName: [
      { required: true, message: 'Please select a column', trigger: 'change' }
    ]
  },
  keywords: '',
  splitedWords: [],
  timeout: null as any,
  layout: {
    documentWidth: '100%',
    commentsWidth: '0',
    commentsHeight: 0
  }
})

const queryAdmin = () => {
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/query_admin'
  }).then((body) => {
    pageCtl.isAdmin = body
  }).catch((error) => {
    console.log(error)
  })
}

const _allObjectOpts = computed(() => {
  const result = []
  result.push({
    value: '([a-z0-9A-Z_])+',
    label: '所有对象'
  }, {
    value: '([a-z0-9A-Z_])+_V',
    label: '所有视图'
  }, {
    value: '([a-z0-9A-Z_])+_FILTER_V',
    label: '所有Filter视图'
  })
  for (let i = 0; i < pageCtl.allAvalibleObjects.length; i++) {
    const e = pageCtl.allAvalibleObjects[i] as any
    if (e.label && e.label !== 'COLUMN') {
      result.push({
        value: e.value,
        label: e.label
      })
    }
  }
  return result
})

const tableNameChange = (clearColumnName = true) => {
  if (pageCtl.newComments.tableName) {
    pageCtl.loading.column = true
    $axios({
      method: 'post',
      url: '/toolbox/quick_look/query_tab_cols',
      data: {
        tableName: pageCtl.newComments.tableName
      }
    }).then((body) => {
      if (clearColumnName) {
        pageCtl.newComments.columnName = ''
      }
      const columnOpts = [{ label: 'OBJECT COMMENTS', value: '[OBJECT_COMMENTS]' }]
      columnOpts.push(...body)
      pageCtl.columnOpts = columnOpts
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.column = false
    })
  }
}

const columnNameChange = () => {
  if (pageCtl.newComments.columnName) {
    pageCtl.loading.comments = true
    $axios({
      method: 'post',
      url: '/toolbox/quick_look/query_column_comments',
      data: {
        tableName: pageCtl.newComments.tableName,
        columnName: pageCtl.newComments.columnName
      }
    }).then((body) => {
      pageCtl.newComments.comments = body.COMMENTS || ''
      pageCtl.newComments.editorType = body.COMMENTS_TYPE || 'markdown'
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.comments = false
    })
  }
}

const saveComments = () => {
  if (pageCtl.newComments.tableName === '') {
    $message.error('Please select a object')
    return
  }
  if (pageCtl.newComments.columnName === '') {
    $message.error('Please select a column')
    return
  }

  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/save_comments',
    data: pageCtl.newComments
  }).then(() => {
    $message.success('Comments Saved.')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const search = (item) => {
  pageCtl.isExpanded = false
  pageCtl.isAvailable = false
  pageCtl.layout.documentWidth = '100%'
  pageCtl.layout.commentsWidth = '0'
  if (item && item.label) {
    pageCtl.currentElement.value = item.value
    pageCtl.currentElement.label = item.label
    pageCtl.loading.search = true
    $axios({
      method: 'post',
      url: '/toolbox/quick_look/search',
      data: {
        type: item.label,
        name: item.value
      }
    }).then((body) => {
      pageCtl.result = body || ''
      if (pageCtl.currentElement.label === 'TABLE' || pageCtl.currentElement.label === 'MVIEW') {
        pageCtl.isAvailable = true
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.search = false
    })
  }
}

const queryHistComments = (obj: HTMLLinkElement) => {
  const table = obj.getAttribute('data-table')
  const column = obj.getAttribute('data-column')
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/query_hist_comments',
    data: {
      tableName: table,
      columnName: column
    }
  }).then((body) => {
    pageCtl.histComments = body || ''
    pageCtl.visible.histComments = true
  }).catch((error) => {
    console.log(error)
  })
}
window.quickLook_queryHistComments = queryHistComments

const shareLink = () => {
  let url = window.location.href
  url = url.split('?')[0]
  url = url + '?s=' + pageCtl.currentElement.value
  url = url + '&t=' + pageCtl.currentElement.label
  $copyText(url)
  $message.success('Share link copied!')
}

const autoCompleteSuggestion = (source, query, cb) => {
  const results = query ? (source || []).filter(createAutoComplete(query)) : source || []
  cb(results.slice(0, 100).sort((e1, e2) => {
    if (e2.value.indexOf('[') !== -1) {
      return 1
    }
    if (e1.value.length !== e2.value.length) {
      return e1.value.length - e2.value.length
    } else {
      return e1.value.localeCompare(e2.value)
    }
  }))
}

const openReply = (item) => {
  item.openReply = !item.openReply
  item.placeholder = '回复@' + item.USER_NAME
}

const showComments = () => {
  if (pageCtl.layout.commentsWidth === '0') {
    queryComments(() => {
      pageCtl.layout.documentWidth = '70%'
      pageCtl.layout.commentsWidth = '30%'
    })
  } else {
    pageCtl.layout.documentWidth = '100%'
    pageCtl.layout.commentsWidth = '0'
    pageCtl.isExpanded = false
  }
}

const sendComment = () => {
  pageCtl.loading.comments = true
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/send_comment',
    data: {
      commentTo: pageCtl.currentElement.value,
      type: pageCtl.currentElement.label,
      content: pageCtl.textareaContent
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.textareaContent = ''
      pageCtl.loading.comments = false
    })
  })
}

const deleteComment = (item) => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/delete_comment',
    data: {
      commentId: item.COMMENT_ID
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.loading.delete = false
      pageCtl.textareaContent = ''
    })
  })
}

const sendReply = (item) => {
  pageCtl.loading.comments = true
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/send_reply',
    data: {
      commentId: item.COMMENT_ID,
      recipientId: item.USER_ID,
      content: pageCtl.replyContent
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.replyContent = ''
      pageCtl.loading.comments = false
    })
  })
}

const deleteReply = (reply) => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/delete_reply',
    data: {
      replyId: reply.REPLY_ID
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryComments(() => {
      pageCtl.loading.delete = false
      pageCtl.textareaContent = ''
    })
  })
}

const queryComments = (afterQuery?: Function) => {
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/query_comments',
    data: {
      type: pageCtl.currentElement.label,
      name: pageCtl.currentElement.value
    }
  }).then((body) => {
    pageCtl.comments = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.isExpanded = true
    if (afterQuery) {
      afterQuery.apply(null)
    }
  })
}

const hightLight = (val) => {
  if (val) {
    const position = {}
    // 先将所有的文本用占位符替换
    for (const i in pageCtl.splitedWords) {
      const word = pageCtl.splitedWords[i]
      val = val.replace(word, '{' + i + '}')
      position['{' + i + '}'] = word
    }

    // 再用高亮替换占位符
    let i = 0
    for (const k in position) {
      val = val.replace('{' + (i++) + '}', '<span style="color: var(--scp-text-color-highlight)">' + position[k] + '</span>')
    }
    return val
  }
}

const queryContent = (obj: HTMLLinkElement) => {
  const docId = obj.getAttribute('doc-id')
  if (docId) {
    pageCtl.currentElement.value = docId
    pageCtl.currentElement.label = 'DOCUMENT'
  }
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/query_content',
    data: {
      docId
    }
  }).then((body) => {
    pageCtl.result = body || ''
    if (pageCtl.currentElement.label === 'DOCUMENT') {
      pageCtl.isAvailable = true
    }
  }).catch((error) => {
    console.log(error)
  })
}
window.quickLook_queryContent = queryContent

const editObject = (obj: HTMLLinkElement) => {
  pageCtl.newComments.comments = ''
  pageCtl.visible.create = true
  pageCtl.newComments.tableName = obj.getAttribute('doc-table-name') || ''
  tableNameChange(false)
  pageCtl.newComments.columnName = obj.getAttribute('doc-column-name') || ''
  columnNameChange()
}
window.quickLook_edit_object = editObject

const queryKeywords = (keywords, cb) => {
  const results = [] as any
  if (keywords === '') {
    cb(_allObjectOpts.value)
  } else {
    clearTimeout(pageCtl.timeout)
    pageCtl.timeout = setTimeout(() => {
      $axios({
        method: 'post',
        url: '/toolbox/quick_look/query_keywords',
        data: {
          keywords
        }
      }).then((body) => {
        pageCtl.splitedWords = body.words
        body.result.forEach((item) => {
          results.push({
            value: item.value,
            label: item.label
          })
        })
        cb(results)
      }).catch((error) => {
        console.log(error)
      })
    }, 650)
  }
}

const createAutoComplete = (query) => {
  return (source) => {
    const q = query.toLowerCase().trim()
    const s = source.value.toLowerCase()
    const ss = s.split('_')
    let f = ''
    for (let i = 0; i < ss.length; i++) {
      if (ss[i].length > 0) {
        f += ss[i].substring(0, 1)
      }
    }
    return (s.indexOf(q) !== -1) || f.indexOf(q) !== -1
  }
}

const switchEditor = () => {
  if (pageCtl.newComments.editorType === 'html') {
    pageCtl.newComments.editorType = 'markdown'
  } else {
    pageCtl.newComments.editorType = 'html'
  }
}

const importCommentsTemplate = () => {
  $axios({
    method: 'post',
    url: '/toolbox/quick_look/query_comments_template'
  }).then((body) => {
    if (body) {
      pageCtl.newComments.editorType = 'markdown'
      pageCtl.newComments.comments = body
    } else {
      $message.error('未找到Comments模版, 请联系管理员!')
    }
  }).catch((error) => {
    console.log(error)
  })
}

const _repliesCnt = computed(() => {
  let result = 0
  for (let i = 0; i < pageCtl.comments.length; i++) {
    result += pageCtl.comments[i].REPLIES.length
  }
  return result
})

onMounted(() => {
  queryAdmin()
  pageCtl.layout.commentsHeight = document.documentElement.clientHeight - 60
  window.onresize = () => {
    pageCtl.layout.commentsHeight = document.documentElement.clientHeight - 60
  }
  // find query string
  const searchStr = $route.query.s as string
  const searchType = $route.query.t as string
  if (searchStr) {
    pageCtl.keywords = searchStr
    search({
      label: searchType,
      value: searchStr
    })
  }
})
</script>
<style lang="scss">
#quickLook {
  .scp-draggable-resizable {
    .md-editor-preview-wrapper {
      padding: var(--scp-widget-margin) var(--scp-widget-margin) !important;
    }
  }

  .quicklook-header {
    padding: 20px 0 30px 0;
    margin: auto;
    text-align: center;
  }

  .md-editor-preview-wrapper {
    padding: var(--scp-widget-margin) 0 !important;
  }

  .aside-open-close {
    position: fixed;
    right: 8px;
    top: 20%;
    width: 30px;
    height: 30px;
    line-height: 60px;
    color: #fff;
    background-color: var(--scp-text-color-lighter);
    border-radius: 6px 0 0 6px;
    font-size: 20px;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .aside-open-close:hover {
    background-color: var(--scp-bg-color-highlight);
    color: #fff;
  }

  .hover-icon:hover {
    color: var(--scp-text-color-error);
  }

  .quicklook-comment {
    position: fixed;
    right: 8px;
    top: 45px;
    overflow: auto;
    background-color: #fff;
    border-left: 1px dotted var(--scp-border-color-lighter);
  }

  .input-comment {
    height: 150px;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid var(--scp-text-color-lighter);
  }

  .input-comment .el-textarea__inner {
    width: 100%;
    height: 115px;
    border: 0;
    border-radius: 6px;
    outline: none;
    resize: none;
    background-color: var(--scp-bg-color-fill-lighter);
  }

  .comment {
    border-radius: 6px;
    border: 1px solid var(--scp-text-color-lighter);
    margin-bottom: 0.4rem;
  }

  .input-reply {
    margin-top: 5px;
    height: 130px;
  }

  .input-reply textarea {
    height: 80px;
    width: 100%;
    border: 0 solid;
    outline: none;
    resize: none;
    border-radius: 6px;
    background-color: var(--scp-bg-color-fill-lighter);
  }

  .reply-top {
    display: flex;
  }

  .reply-content {
    margin: 0.2rem 0;
  }

  table {
    tr {
      td {
        padding: 6px 10px;
        font-size: 10px;
        max-width: 20rem;

        .quicklook-link {
          color: inherit !important;
        }

        figure {
          width: auto !important;
        }

        p {
          padding: 0 !important;
          margin: 0 !important;
        }
      }

      th {
        padding: 6px 10px;
        font-size: 10px;
      }
    }
  }

  blockquote {
    margin: 0 0.5rem !important;
  }

  @media print {
    .quicklook-header {
      display: none;
    }
  }
}
</style>
