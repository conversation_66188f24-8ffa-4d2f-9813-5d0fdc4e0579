<template>
  <scp-md-editor v-model="pageCtl.content" :style="{height: pageCtl.height + 'px'}"/>
</template>

<script lang="ts" setup>
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'
import { onMounted, reactive, watch } from 'vue'
import { useStore } from 'vuex'

const $store = useStore()
const pageCtl = reactive({
  content: '',
  height: 0
})

watch(() => pageCtl.content, () => {
  localStorage.setItem('md-editor.content', pageCtl.content)
})

onMounted(() => {
  const content = localStorage.getItem('md-editor.content')
  if (content) {
    pageCtl.content = content
  }
  pageCtl.height = document.documentElement.clientHeight - 100
})
</script>
