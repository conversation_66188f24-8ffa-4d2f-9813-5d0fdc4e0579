<template>
  <div class="left-sidebar" id="DemandCollection">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['MR3_DEMAND_COLLECTION']"
                        :after-apply="searchReport1"/>
          </el-col>
          <el-col :span="4">
            <el-tooltip placement="bottom" effect="light" content="Display Columns">
              <el-select v-model="pageCtl.conditions.selectedColumns" multiple clearable collapse-tags>
                <el-option v-for="item in pageCtl.columnsOpts"
                           :value=item
                           :label=item
                           :key=item
                           :disabled="isRequired(item)"
                />
              </el-select>
            </el-tooltip>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="searchReport1" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <scp-subscript id="DCR1"/>
        <scp-table
            ref="report1Ref"
            :row-height="46.8"
            :lazy="true"
            :max-height="780"
            :after-save="searchReport1"
            :columns="pageCtl.report1Columns"
            :nested-headers="pageCtl.report1Headers"
            :primary-key-id="['ROW_ID']"
            :page-sizes="[25, 100, 200, 500]"
            :params="pageCtl.conditions"
            :hidden-columns="{columns: [0]}"
            url="/toolbox/demand_collection/query_report1_details"
            download-url="/toolbox/demand_collection/download_report1_details"
            save-url="/toolbox/demand_collection/save_report1_details"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $setDefaultMaterialOwner: any = inject('$setDefaultMaterialOwner')
const $join: any = inject('$join')
const $viewDetails: any = inject('$viewDetails')
const $message: any = inject('$message')
const searchRef = ref()
const report1Ref = ref()

const pageCtl = reactive({
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    selectedColumns: [
      'ANSWER',
      'COMMENTS',
      'ACTION'],
    firstLoading: true
  },
  columnsOpts: [],
  filterOpts: [],
  questionList: [] as any,
  selectedType: '',
  selectedQuestion: '',
  report1EditDetailsTitle: '',
  report1Headers: [],
  report1Columns: []
})

const isRequired = (value) => ['QUESTION_TYPE', 'QUESTION'].includes(value)

onMounted(() => {
  report1Ref.value.setLoading(true)
  initPage()
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/toolbox/demand_collection/init_page'
  }).then((body) => {
    pageCtl.columnsOpts = body.availableColumns
    pageCtl.filterOpts = body.cascader
    pageCtl.questionList = body.questionList
    pageCtl.conditions.firstLoading = false
    $setDefaultMaterialOwner(pageCtl.filterOpts, pageCtl.conditions.$scpFilter.cascader, 'QUESTION_OWNER_SESA')
    report1Columns()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    searchRef.value.loadAndClick()
    report1Ref.value.getHotInstance().getPlugin('hiddenColumns').hideColumns([0])
    report1Ref.value.getHotInstance().getPlugin('collapsibleColumns').collapseAll()
  })
}

const searchReport1 = async () => {
  report1Columns()
  await report1Ref.value.search()
  report1Ref.value.getHotInstance().getPlugin('hiddenColumns').hideColumns([0])
  report1Ref.value.getHotInstance().getPlugin('collapsibleColumns').collapseAll()
}

const report1Columns = () => {
  const cols = [
    { data: 'ROW_ID', filter: false },
    {
      data: 'QUESTION_OWNER_NAME',
      render: renderMiddle
    },
    {
      data: 'QUESTION_TYPE',
      editor: 'select',
      selectOptions: [...new Set(pageCtl.questionList.map(item => item.QUESTION_TYPE))],
      render: (hotInstance, td, row, column, prop, value) => {
        value = value ?? ''
        if (!report1Ref.value.getLoading() && (!value || !pageCtl.questionList.map(item => item.QUESTION_TYPE).includes(value)) && !pageCtl.conditions.firstLoading) {
          td.style.backgroundColor = 'rgb(64, 158, 255)'
        }
        td.style.textAlign = 'left' // 水平靠左
        td.style.verticalAlign = 'middle' // 垂直居中
        // 设置单元格的值
        td.innerText = value
      }
    },
    {
      data: 'QUESTION',
      editor: 'select',
      selectOptions: [...new Set(pageCtl.questionList.map(item => item.QUESTION))],
      render: (hotInstance, td, row, column, prop, value) => {
        value = value ?? ''
        if (!report1Ref.value.getLoading() && (!value || !pageCtl.questionList.map(item => item.QUESTION).includes(value)) && !pageCtl.conditions.firstLoading) {
          td.style.backgroundColor = 'rgb(64, 158, 255)'
        }
        let html = '<div>'
        html += value
        html += '</div>'
        td.style.textAlign = 'left'
        td.style.verticalAlign = 'middle'
        td.innerHTML = html
      }
    }
  ] as any

  const headers: any = [
    [
      {
        label: '',
        colspan: 1,
        dropdownMenu: false
      },
      {
        label: '',
        colspan: 3
      }
    ]
  ]

  for (const item of pageCtl.conditions.selectedColumns) {
    if (item === 'PRIORITY') {
      cols.push({
        data: item,
        editor: 'select',
        selectOptions: ['High', 'Medium', 'Low', ''],
        render: renderMiddle
      })
    } else if (item === 'BUSINESS_TYPE') {
      cols.push({
        data: item,
        editor: 'select',
        selectOptions: ['CSF', 'MPP', 'SIOP', 'Inventory Management', 'KPI Management', 'Supply Management', 'Production Scheduling', 'Shortage Mangemant', 'DSR', 'Upstream Management', 'Inbound Delivery', 'Outbound Delivery', 'Capacity', 'PWP/PEP/OCP Management', 'Master Data Management', 'Supply Chain Project', 'Others', ''],
        render: renderMiddle
      })
    } else {
      cols.push({
        data: item,
        width: 200,
        render: renderMiddle
      })
    }

    headers[0].push({
      label: '',
      colspan: 1
    })
  }
  pageCtl.report1Columns = cols
  pageCtl.report1Headers = headers
}

const renderMiddle = (hotInstance, td, row, column, prop, value) => {
  // 设置单元格样式
  td.style.textAlign = 'left' // 水平靠左
  td.style.verticalAlign = 'middle' // 垂直居中
  // 设置单元格的值
  td.innerText = value
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
</script>
