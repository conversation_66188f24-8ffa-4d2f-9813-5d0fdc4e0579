<template>
  <div class="left-sidebar" id="CsfValidation">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.displayTab" @tab-click="handleTabClick">
          <el-tab-pane label="CSF Validation" name="CSF_VALIDATION">
            <el-row class="search-box">
              <el-col :span="5">
                <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                            :filter-base="['CSF_VALIDATION_V']"
                            :after-apply="searchReport1"/>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pageCtl.conditions.report1GroupByColumns" size="small"
                           placeholder="Group by Columns"
                           filterable multiple collapse-tags>
                  <el-option
                      v-for="item in _pivotColumns"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.report1CompareType" size="small"
                           filterable>
                  <el-option
                      v-for="item in pageCtl.conditions.report1CompareTypeOpts"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.report1DateType" size="small" placeholder="Date Type"
                           filterable>
                  <el-option
                      v-for="item in _report1DateTypeOpts"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pageCtl.conditions.report1SelectedDates" size="small" placeholder="Select Date"
                           filterable clearable multiple collapse-tags>
                  <el-option
                      v-for="item in ['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType) ? pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType] : pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType]"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.report1ValueType" size="small" placeholder="Date Type"
                           filterable>
                  <el-option
                      v-for="item in ['VALUE', 'QUANTITY']"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-col>
              <el-col :span="2">
                <scp-search ref="report1SearchRef" :click-native="search" :data="pageCtl.conditions"
                            :data-exclude="[]"
                            :afterExpand="moreConditions"/>
              </el-col>
            </el-row>
            <el-row class="search-box" v-show="pageCtl.conditionsExpanded">
              <el-col :span="3" v-show="pageCtl.conditions.report1CompareType === 'History Forecast'">
                <el-select v-model="pageCtl.conditions.report1ForecastVersion" size="small"
                           filterable collapse-tags>
                  <el-option
                      v-for="item in pageCtl.conditions.report1ForecastVersionOptions"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.report1CalcResult" size="small"
                           filterable multiple collapse-tags>
                  <el-option
                      v-for="item in pageCtl.conditions.report1CalcResultOpts"
                      :key="item"
                      :label="item"
                      :value="item"/>
                </el-select>
              </el-col>
            </el-row>
            <scp-table2
                style="margin-bottom: 10px"
                ref="report1Ref"
                :max-height="650"
                :after-select="afterReport1Selected"
                :column-sorting="false"
                :data="pageCtl.report1Data"
                :params="pageCtl.conditions"
                :nested-headers="pageCtl.report1Headers"
                :context-menu-items="pageCtl.report1ContextItems"
                :columns="pageCtl.report1Columns"/>
            <div class="subscript-container" v-show="pageCtl.showReport1Details">
              <el-col :span="20">
                <h4 style="margin: 10px 0">{{ pageCtl.report1EditDetailsTitle }}</h4>
              </el-col>
              <scp-table
                  ref="report1DetailsRef"
                  url="/toolbox/csf_validation/query_report1_details"
                  download-url="/toolbox/csf_validation/download_report1_details"
                  save-url="/toolbox/csf_validation/save_report1_details"
                  :after-save="searchReport1"
                  :columns="_report1DetailsColumn"
                  :lazy="true"
                  :primary-key-id="['MATERIAL', 'SALES_ORGANIZATION', 'CUSTOMER_CODE', 'SALES_GROUP']"
                  :params="pageCtl.conditions"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Calculate Configuration" name="CALCULATE_CONFIGURATION">
            <el-collapse v-model="pageCtl.conditions.report1ActiveNames" accordion>
              <el-collapse-item v-show="!['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType)" name="currentAverage">
                <template #title>
                  <b>1. Compare with current average -&nbsp;</b>Compare the current value with the historical value
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.currentAverage.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.currentAverage.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.currentAverage.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.currentAverage.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.currentAverage.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="!['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType)" name="average">
                <template #title>
                  <b>2. Compare with current STDEV -&nbsp;</b>Compare the current value with the historical value
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.currentStandardDeviation.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.currentStandardDeviation.nStdev" class="ratio-style" style="width: 100%">
                      <template #prepend>Average  + </template>
                      <template #append>* STDEV</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="!['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType)" name="histAverage">
                <template #title>
                  <b>3. Compare with historical averages -&nbsp;</b>Compare the current value with the historical value
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.histAverage.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.histAverage.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.histAverage.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.histAverage.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.histAverage.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="!['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType)" name="histStandardDeviation">
                <template #title>
                  <b>4. Compare with historical STDEV -&nbsp;</b>Compare the current value with the historical standard deviation
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.histStandardDeviation.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.histStandardDeviation.nStdev" class="ratio-style" style="width: 100%">
                      <template #prepend>Average  + </template>
                      <template #append>* STDEV</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="!['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType)" name="lastYear">
                <template #title>
                  <b>5. Compare with last year -&nbsp;</b>Compare the current value with the historical value
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.lastYear.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.lastYear.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.lastYear.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.lastYear.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.lastYear.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="!['History Forecast', 'Backlog'].includes(pageCtl.conditions.report1CompareType)" name="annualProportion">
                <template #title>
                  <b>6. Compare with annual proportion -&nbsp;</b>Compare the current value as a percentage of the current year
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.annualProportion.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.annualProportion.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.annualProportion.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.annualProportion.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.annualProportion.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="pageCtl.conditions.report1CompareType === 'History Forecast'" name="forecastSamePeriod">
                <template #title>
                  <b>7. Compare with another version's forecast in the same period -&nbsp;</b>Compare the simultaneous forecast of different versions at the same time
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.forecastSamePeriod.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.forecastSamePeriod.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.forecastSamePeriod.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.forecastSamePeriod.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.forecastSamePeriod.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="pageCtl.conditions.report1CompareType === 'History Forecast'" name="forecastAnnualProportion">
                <template #title>
                  <b>8. Compare the annual proportion forecast with another version in the same period -&nbsp;</b>Compare the annual proportions forecast simultaneously by different versions
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.forecastAnnualProportion.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.forecastAnnualProportion.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.forecastAnnualProportion.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.forecastAnnualProportion.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.forecastAnnualProportion.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item v-show="pageCtl.conditions.report1CompareType === 'Backlog'" name="backlog">
                <template #title>
                  <b>9. Compare with backlog -&nbsp;</b>Compare the current value with the backlog value
                </template>
                <el-row style="margin-bottom: 10px">
                  <el-col :span="2">
                    Compare Method:
                  </el-col>
                  <el-col :span="3">
                    <el-select style="width: 100%" v-model="pageCtl.report1CalcConfig.backlog.method" size="small"
                               filterable collapse-tags>
                      <el-option
                          v-for="item in ['Greater than', 'Less than', 'Out of Range']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="2">
                    Scope:
                  </el-col>
                  <el-col :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.backlog.range[0]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.backlog.method === 'Out of Range'" :span="1" class="ratio-style">
                    ~
                  </el-col>
                  <el-col v-show="pageCtl.report1CalcConfig.backlog.method === 'Out of Range'" :span="3">
                    <el-input v-model="pageCtl.report1CalcConfig.backlog.range[1]" class="ratio-style">
                      <template #append>%</template>
                    </el-input>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </el-tab-pane>
          <el-tab-pane label="Upload Forecast" name="UPLOAD_FORECAST">
            <scp-table
                url="/toolbox/csf_validation/query_report2"
                download-url="/toolbox/csf_validation/download_report2"
                ref="report2TableRef"
                :contextMenuItemsReverse="true"
                :contextMenuItems="pageCtl.report2MenuItems"
                :lazy="true"
                :columns="_report2TableColumn"/>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <scp-upload
        ref="upload2Ref"
        title="Upload Forecast"
        :show-btn="false"
        w="600px"
        h="250px"
        upload-url='/toolbox/csf_validation/upload_report2'
        download-template-url='/toolbox/csf_validation/download_report2_template'
        :on-upload-end="() => {
          report2TableRef.search()
          report1Ref.search()
        }">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $join: any = inject('$join')
const $viewDetails: any = inject('$viewDetails')

const report1SearchRef = ref()
const report1Ref = ref()
const report1SubRef = ref()
const report1DetailsRef = ref()
const upload2Ref = ref()
const report2TableRef = ref()

const handleTabClick = (tab) => {
  if (tab.paneName === 'UPLOAD_FORECAST') {
    report2TableRef.value.search()
  }
}

const viewReport1Details = () => {
  $viewDetails({
    title: pageCtl.report1DetailsTitle,
    url: '/toolbox/csf_validation/query_report1_details',
    durl: '/toolbox/csf_validation/download_report1_details',
    params: pageCtl.conditions
  })
}

const editReport1Details = () => {
  pageCtl.showReport1Details = true
  pageCtl.report1FinialErrorMonths = pageCtl.errorMonthList
  pageCtl.report1EditDetailsTitle = pageCtl.report1DetailsTitleBackup
  report1DetailsRef.value.setLoading(true)
  report1DetailsRef.value.search()
}

const pageCtl = reactive({
  conditionsExpanded: false,
  displayTab: 'CSF_VALIDATION',
  report1Data: [],
  loading: {
    report1: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    report1ActiveNames: '',
    report1ValueType: 'VALUE',
    report1CompareType: 'Sales',
    report1CompareTypeOpts: ['CRD', 'Sales', 'Order Intake', 'History Forecast', 'Backlog'],
    report1ForecastVersion: '' as any,
    report1ForecastVersionOptions: [],
    report1DateType: 'CALENDAR_MONTH',
    report1SelectedDates: [],
    report1FcstDatesOpts: [],
    report1FcstColumns: {},
    report1GroupByColumns: ['LOCAL_PRODUCT_FAMILY'],
    report1SelectedColumn: '',
    report1CalcResult: ['HISTORY_AVG', 'HISTORY_STDEV', 'CURRENT_AVG', 'CURRENT_STDEV', 'NEGATIVE_VALUE_CHECK', 'NON_INTEGER_CHECK', 'LAST_YEAR', 'ANNUAL_PROPORTION'],
    report1CalcResultOpts: ['HISTORY_AVG', 'HISTORY_STDEV', 'CURRENT_AVG', 'CURRENT_STDEV', 'NEGATIVE_VALUE_CHECK', 'NON_INTEGER_CHECK', 'LAST_YEAR'],
    report1SelectedValue: [],
    fullDateOptions: [],
    dateOptions: []
  },
  filterOpts: [],
  report1Columns: [],
  report1Headers: [],
  showReport1Details: false,
  report1DetailsTitle: '',
  report1EditDetailsTitle: '',
  report1DetailsTitleBackup: '',
  report1ContextItems: {
    view_details: {
      name: 'View Details',
      callback: viewReport1Details
    },
    edit_details: {
      name: 'Edit Details',
      disabled: () => {
        return !['HISTORY_AVG_COUNT', 'HISTORY_STDEV_COUNT', 'CURRENT_AVG_COUNT', 'CURRENT_STDEV_COUNT', 'NEGATIVE_VALUE_CHECK_COUNT', 'NON_INTEGER_CHECK_COUNT', 'LAST_YEAR_COUNT', 'ANNUAL_PROPORTION_COUNT', 'FORECAST_SAME_PERIOD_COUNT', 'FORECAST_ANNUAL_PROPORTION_COUNT', 'BACKLOG_COUNT'].includes(pageCtl.conditions.report1SelectedColumn)
      },
      callback: editReport1Details
    },
    view_split0: { name: '---------' }
  },
  errorMonthList: [],
  report1FinialErrorMonths: [],
  report2MenuItems: {
    upload: {
      name: 'Upload',
      callback: () => {
        upload2Ref.value.showUploadWin()
      }
    },
    view_split: { name: '---------' }
  },
  report1CalcConfig: {
    currentAverage: {
      method: 'Greater than',
      range: [100, 100]
    },
    currentStandardDeviation: {
      method: 'Greater than',
      nStdev: 2
    },
    histAverage: {
      method: 'Greater than',
      range: [100, 100]
    },
    histStandardDeviation: {
      method: 'Greater than',
      nStdev: 2
    },
    lastYear: {
      method: 'Greater than',
      range: [100, 100]
    },
    annualProportion: {
      method: 'Greater than',
      range: [30, 30]
    },
    forecastSamePeriod: {
      method: 'Greater than',
      range: [100, 100]
    },
    forecastAnnualProportion: {
      method: 'Greater than',
      range: [5, 5]
    },
    backlog: {
      method: 'Less than',
      range: [100, 100]
    }
  }
})

onMounted(() => {
  const fcstList = generateReport1FcstMonthlyColumns()
  convertFcstColumns(fcstList)
  initPage()
})

const initPage = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/toolbox/csf_validation/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.conditions.dateOptions = body.dateOptions
    pageCtl.conditions.fullDateOptions = body.fullDateOptions
    pageCtl.conditions.$scpFilter.cascader = body.defaultFilterResults
    pageCtl.conditions.report1ForecastVersion = body.maxFcstVersion
    pageCtl.conditions.report1ForecastVersionOptions = body.forecastVersionOptions
    report1SearchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(
    pageCtl.loading.report1 = false
  )
}

watch(
  () => ({
    report1DateType: pageCtl.conditions.report1DateType,
    report1DateValue: pageCtl.conditions.report1CompareType
  }),
  () => {
    let calcResult = []
    if (pageCtl.conditions.report1CompareType === 'History Forecast') {
      switch (pageCtl.conditions.report1DateType) {
        case 'CALENDAR_MONTH':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 25)
          break
        case 'CALENDAR_QUARTER':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b.replace('Q', 0) - a.replace('Q', 0)).slice(0, 8)
          break
        case 'CALENDAR_HALF_YEAR':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b.replace('H', 0) - a.replace('H', 0)).slice(0, 4)
          break
        case 'CALENDAR_YEAR':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 2)
          break
        default:
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 2)
      }
      const fcstList = generateReport1FcstMonthlyColumns()
      convertFcstColumns(fcstList)
      calcResult = ['FORECAST_SAME_PERIOD', 'FORECAST_ANNUAL_PROPORTION']
      if (pageCtl.conditions.report1DateType === 'CALENDAR_YEAR') {
        pageCtl.conditions.report1DateType = 'CALENDAR_MONTH'
      }
    } else if (pageCtl.conditions.report1CompareType === 'Backlog') {
      switch (pageCtl.conditions.report1DateType) {
        case 'CALENDAR_MONTH':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 25)
          break
        case 'CALENDAR_QUARTER':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b.replace('Q', 0) - a.replace('Q', 0)).slice(0, 8)
          break
        case 'CALENDAR_HALF_YEAR':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b.replace('H', 0) - a.replace('H', 0)).slice(0, 4)
          break
        case 'CALENDAR_YEAR':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 2)
          break
        default:
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.fullDateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 2)
      }
      calcResult = ['BACKLOG']
    } else {
      switch (pageCtl.conditions.report1DateType) {
        case 'CALENDAR_MONTH':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 24)
          calcResult = ['HISTORY_AVG', 'HISTORY_STDEV', 'CURRENT_AVG', 'CURRENT_STDEV', 'NEGATIVE_VALUE_CHECK', 'NON_INTEGER_CHECK', 'LAST_YEAR']
          break
        case 'CALENDAR_QUARTER':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b.replace('Q', 0) - a.replace('Q', 0)).slice(0, 8)
          calcResult = ['HISTORY_AVG', 'CURRENT_AVG', 'LAST_YEAR', 'ANNUAL_PROPORTION']
          break
        case 'CALENDAR_HALF_YEAR':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b.replace('H', 0) - a.replace('H', 0)).slice(0, 4)
          calcResult = ['LAST_YEAR', 'ANNUAL_PROPORTION']
          break
        case 'CALENDAR_YEAR':
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 2)
          calcResult = ['LAST_YEAR']
          break
        default:
          pageCtl.conditions.report1SelectedDates = pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 2)
          calcResult = ['LAST_YEAR']
      }
    }
    pageCtl.conditions.report1CalcResult = calcResult
    pageCtl.conditions.report1CalcResultOpts = calcResult
  },
  { deep: true, immediate: false }
)

const convertFcstColumns = (fcstList) => {
  switch (pageCtl.conditions.report1DateType) {
    case 'CALENDAR_MONTH':
      pageCtl.conditions.report1FcstColumns = fcstList
      break
    case 'CALENDAR_QUARTER':
      pageCtl.conditions.report1FcstColumns = convertMonthToQuarter(fcstList)
      break
    case 'CALENDAR_HALF_YEAR':
      pageCtl.conditions.report1FcstColumns = convertMonthToHalfYear(fcstList)
      break
    case 'CALENDAR_YEAR':
      pageCtl.conditions.report1FcstColumns = convertMonthToYear(fcstList)
      break
    default:
      break
  }
}

const afterReport1Selected = (r, c, a, col, row) => {
  const ht = report1Ref.value.getHotInstance()
  switch (a) {
    case 'HISTORY_AVG_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).HISTORY_AVG_COUNT_LIST
      break
    case 'HISTORY_STDEV_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).HISTORY_STDEV_COUNT_LIST
      break
    case 'CURRENT_AVG_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).CURRENT_AVG_COUNT_LIST
      break
    case 'CURRENT_STDEV_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).CURRENT_STDEV_COUNT_LIST
      break
    case 'NEGATIVE_VALUE_CHECK_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).NEGATIVE_VALUE_CHECK_COUNT_LIST
      break
    case 'NON_INTEGER_CHECK_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).NON_INTEGER_CHECK_COUNT_LIST
      break
    case 'LAST_YEAR_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).LAST_YEAR_COUNT_LIST
      break
    case 'ANNUAL_PROPORTION_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).ANNUAL_PROPORTION_COUNT_LIST
      break
    case 'FORECAST_SAME_PERIOD_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).FORECAST_SAME_PERIOD_COUNT_LIST
      break
    case 'FORECAST_ANNUAL_PROPORTION_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).FORECAST_ANNUAL_PROPORTION_COUNT_LIST
      break
    case 'BACKLOG_COUNT':
      pageCtl.errorMonthList = ht.getSourceDataAtRow(row).BACKLOG_COUNT_LIST
      break
  }
  pageCtl.conditions.report1SelectedColumn = a
  if (r[_report1SelectedColumns.value[0]] === 'Total') {
    pageCtl.conditions.report1SelectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _report1SelectedColumns.value.length; i++) {
      selectedValue.push(r[_report1SelectedColumns.value[i]])
    }
    pageCtl.conditions.report1SelectedValue = selectedValue
  }
  const title = $join(...pageCtl.conditions.report1SelectedValue)
  if (title) {
    pageCtl.report1DetailsTitleBackup = 'Modify CSF - [' + title + ']' + ' - ' + a
    pageCtl.report1DetailsTitle = 'View Details [' + title + ']'
  } else {
    if (pageCtl.conditions.report1SelectedValue.length > 0) {
      pageCtl.report1DetailsTitleBackup = 'Modify CSF - [' + $join(...pageCtl.conditions.report1SelectedValue) + ']' + ' - ' + a
      pageCtl.report1DetailsTitle = 'View Details [' + $join(...pageCtl.conditions.report1SelectedValue) + ']'
    } else {
      pageCtl.report1DetailsTitleBackup = 'Modify CSF - [Total]' + ' - ' + a
      pageCtl.report1DetailsTitle = 'View Details [Total]'
    }
  }
}

const convertMonthToQuarter = (fcstList) => {
  // 用于存储转换后的季度数据
  const quarterData = {}

  // 辅助函数，用于确定月份属于哪个季度
  const getQuarter = (month) => {
    return Math.floor((month - 1) / 3) + 1
  }

  // 遍历月份数据进行转换
  Object.entries(fcstList).forEach(([key, value]) => {
    const formattedDate = key.replace('CURRENT_', '') // 获取 "YYYYMM" 格式的键
    const monthKey = parseInt(formattedDate.slice(-2), 10) // 获取月份部分
    const quarter = getQuarter(monthKey) // 确定季度
    const monthName = value[0] // 获取月份对应的名称

    // 季度的键，例如 '2024Q4'
    const quarterKey = `${formattedDate.slice(0, 4)}Q${quarter}`

    // 检查季度数据中是否已有该季度的条目
    if (!Object.keys(quarterData).includes(quarterKey)) {
      quarterData[quarterKey] = []
    }

    // 将月份名称添加到相应季度的数组中
    quarterData[quarterKey].push(monthName)
  })
  return quarterData
}

const convertMonthToHalfYear = (monthData) => {
// 用于存储转换后的半年数据
  const halfYearData = {}

  // 辅助函数，用于确定月份属于哪个半年
  const getHalfYear = (month) => {
    return Math.floor((month - 1) / 6) + 1
  }

  // 遍历月份数据进行转换
  Object.entries(monthData).forEach(([key, value]) => {
    const formattedDate = key.replace('CURRENT_', '') // 获取 "YYYYMM" 格式的键
    const monthKey = parseInt(formattedDate.slice(-2), 10) // 获取月份部分
    const halfYear = getHalfYear(monthKey) // 确定半年
    const monthName = value[0] // 获取月份对应的名称

    // 半年的键，例如 '2024H2'
    const halfYearKey = `${formattedDate.slice(0, 4)}H${halfYear}`

    // 检查季度数据中是否已有该季度的条目
    if (!Object.keys(halfYearData).includes(halfYearKey)) {
      halfYearData[halfYearKey] = []
    }
    // 将月份名称添加到相应季度的数组中
    halfYearData[halfYearKey].push(monthName)
  })

  // 输出转换后的半年数据
  return halfYearData
}

const convertMonthToYear = (monthData) => {
  // 用于存储转换后的年数据
  const yearlyData = {}

  // 遍历月份数据进行转换
  Object.entries(monthData).forEach(([key, value]) => {
    const formattedDate = key.replace('CURRENT_', '') // 获取 "YYYYMM" 格式的键
    const year = formattedDate.slice(0, 4) // 提取年份部分
    const monthName = value[0] // 获取月份对应的名称

    // 检查年数据中是否已有该年的条目
    if (!Object.keys(yearlyData).includes(year)) {
      yearlyData[year] = []
    }
    // 将月份名称添加到相应年的数组中
    yearlyData[year].push(monthName)
  })
  return yearlyData
}

const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}

const search = () => {
  searchReport1()
  pageCtl.showReport1Details = false
}

const searchReport1 = () => {
  if (pageCtl.conditions.report1SelectedDates.length === 0) {
    pageCtl.conditions.report1SelectedDates = pageCtl.conditions.dateOptions[pageCtl.conditions.report1DateType].slice().sort((a, b) => b - a).slice(0, 24)
  }

  report1Ref.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/toolbox/csf_validation/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
    convertReport1Data()
    report1Column()
    report1Header()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report1Ref.value.getHotInstance().getPlugin('collapsibleColumns').collapseAll()
    // report1Ref.value.getHotInstance().getPlugin('collapsibleColumns').expandSection({ row: -1, col: 0 })
    report1Ref.value.setLoading(false)
  })
}

const _report1DetailsColumn = computed(() => {
  return [{ data: 'MATERIAL' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'CUSTOMER_CODE' },
    { data: 'IS_VALID$' },
    { data: 'SALES_GROUP' },
    { data: 'NET_NET_PRICE_RMB' },
    { data: 'NET_NET_PRICE_HKD' },
    { data: 'MONTH01', render: renderReport1Details },
    { data: 'MONTH02', render: renderReport1Details },
    { data: 'MONTH03', render: renderReport1Details },
    { data: 'MONTH04', render: renderReport1Details },
    { data: 'MONTH05', render: renderReport1Details },
    { data: 'MONTH06', render: renderReport1Details },
    { data: 'MONTH07', render: renderReport1Details },
    { data: 'MONTH08', render: renderReport1Details },
    { data: 'MONTH09', render: renderReport1Details },
    { data: 'MONTH10', render: renderReport1Details },
    { data: 'MONTH11', render: renderReport1Details },
    { data: 'MONTH12', render: renderReport1Details },
    { data: 'MONTH13', render: renderReport1Details },
    { data: 'MONTH14', render: renderReport1Details },
    { data: 'MONTH15', render: renderReport1Details },
    { data: 'MONTH16', render: renderReport1Details },
    { data: 'MONTH17', render: renderReport1Details },
    { data: 'MONTH18', render: renderReport1Details },
    { data: 'MONTH19', render: renderReport1Details },
    { data: 'MONTH20', render: renderReport1Details },
    { data: 'MONTH21', render: renderReport1Details },
    { data: 'MONTH22', render: renderReport1Details },
    { data: 'MONTH23', render: renderReport1Details },
    { data: 'MONTH24', render: renderReport1Details },
    { data: 'MONTH25', render: renderReport1Details }
  ]
})

const renderReport1Details = (hotInstance, td, row, column, prop, value) => {
  pageCtl.report1FinialErrorMonths.forEach(item => {
    if (pageCtl.conditions.report1FcstColumns[item].includes(prop)) {
      td.style.backgroundColor = 'red'
      td.style.color = 'white'
    }
  })
  td.innerHTML = value
}

const _report2TableColumn = computed(() => {
  return [
    { data: 'MATERIAL' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'CUSTOMER_CODE' },
    { data: 'IS_VALID$' },
    { data: 'SALES_GROUP' },
    { data: 'NET_NET_PRICE_RMB' },
    { data: 'NET_NET_PRICE_HKD' },
    { data: 'MONTH01' },
    { data: 'MONTH02' },
    { data: 'MONTH03' },
    { data: 'MONTH04' },
    { data: 'MONTH05' },
    { data: 'MONTH06' },
    { data: 'MONTH07' },
    { data: 'MONTH08' },
    { data: 'MONTH09' },
    { data: 'MONTH10' },
    { data: 'MONTH11' },
    { data: 'MONTH12' },
    { data: 'MONTH13' },
    { data: 'MONTH14' },
    { data: 'MONTH15' },
    { data: 'MONTH16' },
    { data: 'MONTH17' },
    { data: 'MONTH18' },
    { data: 'MONTH19' },
    { data: 'MONTH20' },
    { data: 'MONTH21' },
    { data: 'MONTH22' },
    { data: 'MONTH23' },
    { data: 'MONTH24' },
    { data: 'MONTH25' }
  ]
})

const convertReport1Data = () => {
  const currentMonthList = []
  for (const key of Object.keys(pageCtl.conditions.report1FcstColumns)) {
    currentMonthList.push(key)
  }
  pageCtl.report1Data.forEach((t: any) => {
    const current = [] as any
    const history = [] as any
    for (const item of Object.keys(t)) {
      if (item.startsWith('HISTORY_')) {
        history.push(t[item])
      }
      if (currentMonthList.includes(item)) {
        current.push(t[item])
      }
    }
    t.CURRENT_AVG = calculateAverage(current)
    t.CURRENT_STDEV = calculateStandardDeviation(current)
    t.HISTORY_AVG = calculateAverage(history)
    t.HISTORY_STDEV = calculateStandardDeviation(history)

    const fullDateValue = {}
    Object.entries(t).forEach(([key, value]) => {
      if (/^HISTORY_.*\d$/.test(key) && !Object.keys(fullDateValue).includes(key.replace('HISTORY_', ''))) {
        fullDateValue[key.replace('HISTORY_', '')] = value
      } else if (/^CURRENT_.*\d$/.test(key)) {
        fullDateValue[key.replace('CURRENT_', '')] = value
      }
    })

    let currentStdevCount = 0
    let currentAvgCount = 0
    let historyStdevCount = 0
    let historyAvgCount = 0
    let negativeValueCheckCount = 0
    let nonIntegerCheckCount = 0
    let lastYearCount = 0
    let annualProportionCount = 0
    let forecastSamePeriodCount = 0
    let forecastAnnualProportionCount = 0
    let backlogCount = 0
    const report1FcstColumns = _report1FcstColumns.value
    // 通过report1CalcConfig汇总error数量
    const currentAvgCountList = []
    const currentStdevCountList = []
    const historyAvgCountList = []
    const historyStdevCountList = []
    const negativeValueCheckCountList = []
    const nonIntegerCheckCountList = []
    const lastYearCountList = []
    const annualProportionCountList = []
    const forecastSamePeriodCountList = []
    const forecastAnnualProportionCountList = []
    const backlogCountList = []
    for (const index in report1FcstColumns) {
      const item = report1FcstColumns[index]
      // currentAverage
      const currentAverageMethod = convertCompareMethod(pageCtl.report1CalcConfig.currentAverage.method)
      if (currentAverageMethod(t[item], t.CURRENT_AVG, pageCtl.report1CalcConfig.currentAverage.range)) {
        currentAvgCountList.push(item)
        currentAvgCount += 1
      }
      // currentStandardDeviation
      const currentStdevMethod = convertCompareMethod(pageCtl.report1CalcConfig.currentStandardDeviation.method)
      if (currentStdevMethod(t[item], t.CURRENT_AVG + pageCtl.report1CalcConfig.currentStandardDeviation.nStdev * t.CURRENT_STDEV, [100, 100])) {
        currentStdevCount += 1
        currentStdevCountList.push(item)
      }
      // histAverage
      const averageMethod = convertCompareMethod(pageCtl.report1CalcConfig.histAverage.method)
      if (averageMethod(t[item], t.HISTORY_AVG, pageCtl.report1CalcConfig.histAverage.range)) {
        historyAvgCountList.push(item)
        historyAvgCount += 1
      }
      // histStandardDeviation
      const historyStdevMethod = convertCompareMethod(pageCtl.report1CalcConfig.histStandardDeviation.method)
      if (historyStdevMethod(t[item], t.HISTORY_AVG + pageCtl.report1CalcConfig.histStandardDeviation.nStdev * t.HISTORY_STDEV, [100, 100])) {
        historyStdevCount += 1
        historyStdevCountList.push(item)
      }

      // negativeValue
      if (t[item] < 0) {
        negativeValueCheckCount += 1
        negativeValueCheckCountList.push(item)
      }
      // nonInteger
      if (!Number.isInteger(t[item])) {
        nonIntegerCheckCount += 1
        nonIntegerCheckCountList.push(item)
      }

      // Same-Period comparison
      const lastYearMethod = convertCompareMethod(pageCtl.report1CalcConfig.lastYear.method)
      const year = parseInt(item.replace('CURRENT_', '').substring(0, 4), 10) - 1
      const lastYear = `${year}${item.replace('CURRENT_', '').substring(4)}`
      if (lastYearMethod(t[item], fullDateValue[lastYear], pageCtl.report1CalcConfig.lastYear.range)) {
        lastYearCountList.push(item)
        lastYearCount += 1
      }

      // Annual proportion
      const annualProportionMethod = convertAnnualCompareMethod(pageCtl.report1CalcConfig.annualProportion.method)
      // 当前占比
      let yearlyTotal = 0
      const currentYear = item.replace('CURRENT_', '').substring(0, 4)
      Object.entries(fullDateValue).forEach(([key, value]) => {
        if (key.substring(0, 4) === currentYear) {
          yearlyTotal += value
        }
      })
      if (annualProportionMethod(t[item] / yearlyTotal * 100, 0, pageCtl.report1CalcConfig.annualProportion.range)) {
        annualProportionCountList.push(item)
        annualProportionCount += 1
      }

      if (pageCtl.conditions.report1CompareType === 'History Forecast') {
        const fcstFullDateValue = {}
        Object.entries(t).forEach(([key, value]) => {
          if (/^CURRENT_.*\d$/.test(key) && !Object.keys(fcstFullDateValue).includes(key)) {
            fcstFullDateValue[key.replace('CURRENT_', '')] = value
          }
        })
        Object.entries(t).forEach(([key, value]) => {
          if (/^HISTORY_.*\d$/.test(key) && !Object.keys(fcstFullDateValue).includes(key)) {
            fcstFullDateValue[key.replace('HISTORY_', '')] = value
          }
        })

        // Forecast Same Period
        const forecastSamePeriodMethod = convertCompareMethod(pageCtl.report1CalcConfig.forecastSamePeriod.method)
        const histItem = item.replace('CURRENT_', 'HISTORY_')
        if (Object.keys(t).includes(histItem) && forecastSamePeriodMethod(t[item], t[histItem], pageCtl.report1CalcConfig.forecastSamePeriod.range)) {
          forecastSamePeriodCount += 1
          forecastSamePeriodCountList.push(item)
        }

        // Forecast Annual Proportion
        let currentYearlyTotal = 0
        let historyYearlyTotal = 0
        Object.entries(t).forEach(([key, value]) => {
          if (/^CURRENT_.*\d$/.test(key) && Object.keys(fcstFullDateValue).includes(key.replace('CURRENT_', '')) && item.replace('CURRENT_', '').substring(0, 4) === key.replace('CURRENT_', '').substring(0, 4)) {
            currentYearlyTotal = currentYearlyTotal + value
          } else if (/^HISTORY_.*\d$/.test(key) && Object.keys(fcstFullDateValue).includes(key.replace('HISTORY_', '')) && item.replace('CURRENT_', '').substring(0, 4) === key.replace('HISTORY_', '').substring(0, 4)) {
            historyYearlyTotal = historyYearlyTotal + value
          }
        })
        const forecastAnnualProportionMethod = convertAnnualCompareMethod(pageCtl.report1CalcConfig.forecastAnnualProportion.method)
        if (forecastAnnualProportionMethod(t[item] / currentYearlyTotal * 100, t[item.replace('CURRENT_', 'HISTORY_')] / historyYearlyTotal * 100, pageCtl.report1CalcConfig.forecastAnnualProportion.range)) {
          forecastAnnualProportionCount += 1
          forecastAnnualProportionCountList.push(item)
        }
      }
      if (pageCtl.conditions.report1CompareType === 'Backlog') {
        // backlog
        const backlogMethod = convertCompareMethod(pageCtl.report1CalcConfig.backlog.method)
        const histItem = item.replace('CURRENT_', 'HISTORY_')
        if (backlogMethod(t[item], t[histItem] === undefined ? 0 : t[histItem], pageCtl.report1CalcConfig.backlog.range)) {
          backlogCountList.push(item)
          backlogCount += 1
        }
      }
    }
    t.CURRENT_STDEV_COUNT = currentStdevCount
    t.CURRENT_STDEV_COUNT_LIST = currentStdevCountList
    t.CURRENT_AVG_COUNT = currentAvgCount
    t.CURRENT_AVG_COUNT_LIST = currentAvgCountList
    t.HISTORY_STDEV_COUNT = historyStdevCount
    t.HISTORY_STDEV_COUNT_LIST = historyStdevCountList
    t.HISTORY_AVG_COUNT = historyAvgCount
    t.HISTORY_AVG_COUNT_LIST = historyAvgCountList
    t.NEGATIVE_VALUE_CHECK_COUNT = negativeValueCheckCount
    t.NEGATIVE_VALUE_CHECK_COUNT_LIST = negativeValueCheckCountList
    t.NON_INTEGER_CHECK_COUNT = nonIntegerCheckCount
    t.NON_INTEGER_CHECK_COUNT_LIST = nonIntegerCheckCountList
    t.LAST_YEAR_COUNT = lastYearCount
    t.LAST_YEAR_COUNT_LIST = lastYearCountList
    t.ANNUAL_PROPORTION_COUNT = annualProportionCount
    t.ANNUAL_PROPORTION_COUNT_LIST = annualProportionCountList
    t.FORECAST_SAME_PERIOD_COUNT = forecastSamePeriodCount
    t.FORECAST_SAME_PERIOD_COUNT_LIST = forecastSamePeriodCountList
    t.FORECAST_ANNUAL_PROPORTION_COUNT = forecastAnnualProportionCount
    t.FORECAST_ANNUAL_PROPORTION_COUNT_LIST = forecastAnnualProportionCountList
    t.BACKLOG_COUNT = backlogCount
    t.BACKLOG_COUNT_LIST = backlogCountList
  })
}

const convertCompareMethod = (method) => {
  let compareFunction
  switch (method) {
    case 'Greater than':
      compareFunction = (a, b, c) => a > (b * c[0] / 100)
      break
    case 'Less than':
      compareFunction = (a, b, c) => a < (b * c[0] / 100)
      break
    case 'Out of Range':
      compareFunction = (a, b, c) => a < b * ((100 - c[0]) / 100) || a > b * ((100 + c[1]) / 100)
      break
    default:
      throw new Error('Invalid comparison method')
  }
  return compareFunction
}

const convertAnnualCompareMethod = (method) => {
  let compareFunction
  switch (method) {
    case 'Greater than':
      compareFunction = (a, b, c) => a > (b + c[0])
      break
    case 'Less than':
      compareFunction = (a, b, c) => a < (b - c[0])
      break
    case 'Out of Range':
      compareFunction = (a, b, c) => a < b - c[0] || a > b + c[1]
      break
    default:
      throw new Error('Invalid comparison method')
  }
  return compareFunction
}

// 方差
const calculateVariance = (numbers, avg) => {
  return numbers.reduce((acc, val) => acc + (val - avg) ** 2, 0) / numbers.length
}

// 平均
const calculateAverage = (numbers) => {
  const sum = numbers.reduce((acc, val) => acc + val, 0)
  return sum / numbers.length
}

// 标准差
const calculateStandardDeviation = (numbers) => {
  const avg = calculateAverage(numbers)
  const variance = calculateVariance(numbers, avg)
  return Math.sqrt(variance)
}

const camelCaseStartPlaceholder = (word: string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const generateReport1FcstMonthlyColumns = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()

  const dateList = {}
  // 循环25次，每次增加一个月
  for (let i = 1; i <= 25; i++) {
    let newMonth = (month + i) % 12
    if (newMonth === 0) {
      newMonth = 12
    }
    const newYear = year + Math.floor((month + i - 1) / 12)
    const formattedDate = `${newYear}${newMonth.toString().padStart(2, '0')}`
    dateList['CURRENT_' + formattedDate] = [`MONTH${i.toString().padStart(2, '0')}`]
  }
  return dateList
}

const report1Column = () => {
  const columns = [] as any
  for (let i = 0; i < _report1SelectedColumns.value.length; i++) {
    columns.push({
      data: _report1SelectedColumns.value[i],
      title: camelCaseStartPlaceholder(_report1SelectedColumns.value[i]),
      render: mergeCellStyle
    })
  }
  for (const fcst of _report1FcstColumns.value) {
    columns.push(
      { data: fcst, type: 'numeric' }
    )
  }
  const selectedDates = ['Backlog', 'History Forecast'].includes(pageCtl.conditions.report1CompareType) ? pageCtl.conditions.report1SelectedDates.reverse() : pageCtl.conditions.report1SelectedDates
  for (const calendar of selectedDates) {
    const column = 'HISTORY_' + calendar
    columns.push(
      { data: column, title: camelCaseStartPlaceholder(column), type: 'numeric' }
    )
  }
  for (const count of pageCtl.conditions.report1CalcResult) {
    if (count === 'HISTORY_STDEV') {
      columns.push(
        {
          data: 'HISTORY_STDEV_COUNT',
          title: camelCaseStartPlaceholder('HISTORY_STDEV_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'HISTORY_STDEV_COUNT_LIST')
          }
        }
      )
    } else if (count === 'HISTORY_AVG') {
      columns.push(
        {
          data: 'HISTORY_AVG_COUNT',
          title: camelCaseStartPlaceholder('HISTORY_AVG_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'HISTORY_AVG_COUNT_LIST')
          }
        }
      )
    } else if (count === 'CURRENT_AVG') {
      columns.push(
        {
          data: 'CURRENT_AVG_COUNT',
          title: camelCaseStartPlaceholder('CURRENT_AVG_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'CURRENT_AVG_COUNT_LIST')
          }
        }
      )
    } else if (count === 'CURRENT_STDEV') {
      columns.push(
        {
          data: 'CURRENT_STDEV_COUNT',
          title: camelCaseStartPlaceholder('CURRENT_STDEV_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'CURRENT_STDEV_COUNT_LIST')
          }
        }
      )
    } else if (count === 'NEGATIVE_VALUE_CHECK') {
      columns.push(
        {
          data: 'NEGATIVE_VALUE_CHECK_COUNT',
          title: camelCaseStartPlaceholder('NEGATIVE_VALUE_CHECK_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'NEGATIVE_VALUE_CHECK_COUNT_LIST')
          }
        }
      )
    } else if (count === 'NON_INTEGER_CHECK') {
      columns.push(
        {
          data: 'NON_INTEGER_CHECK_COUNT',
          title: camelCaseStartPlaceholder('NON_INTEGER_CHECK_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'NON_INTEGER_CHECK_COUNT_LIST')
          }
        }
      )
    } else if (count === 'LAST_YEAR') {
      columns.push(
        {
          data: 'LAST_YEAR_COUNT',
          title: camelCaseStartPlaceholder('LAST_YEAR_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'LAST_YEAR_COUNT_LIST')
          }
        }
      )
    } else if (count === 'ANNUAL_PROPORTION') {
      columns.push(
        {
          data: 'ANNUAL_PROPORTION_COUNT',
          title: camelCaseStartPlaceholder('ANNUAL_PROPORTION_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'ANNUAL_PROPORTION_COUNT_LIST')
          }
        }
      )
    } else if (count === 'FORECAST_SAME_PERIOD') {
      columns.push(
        {
          data: 'FORECAST_SAME_PERIOD_COUNT',
          title: camelCaseStartPlaceholder('FORECAST_SAME_PERIOD_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'FORECAST_SAME_PERIOD_COUNT_LIST')
          }
        }
      )
    } else if (count === 'FORECAST_ANNUAL_PROPORTION') {
      columns.push(
        {
          data: 'FORECAST_ANNUAL_PROPORTION_COUNT',
          title: camelCaseStartPlaceholder('FORECAST_ANNUAL_PROPORTION_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'FORECAST_ANNUAL_PROPORTION_COUNT_LIST')
          }
        }
      )
    } else if (count === 'BACKLOG') {
      columns.push(
        {
          data: 'BACKLOG_COUNT',
          title: camelCaseStartPlaceholder('BACKLOG_COUNT'),
          type: 'numeric',
          render: (hotInstance, td, row, column, prop, value) => {
            renderReport1(hotInstance, td, row, column, prop, value, 'BACKLOG_COUNT_LIST')
          }
        }
      )
    }
  }
  pageCtl.report1Columns = columns
}

const report1Header = () => {
  const headers: any = [
    [
      {
        label: '',
        colspan: pageCtl.conditions.report1GroupByColumns.length
      }
    ]
  ]
  headers[0].push({
    label: 'Forecast',
    colspan: _report1FcstColumns.value.length
  })
  headers[0].push({
    label: pageCtl.conditions.report1CompareType,
    colspan: pageCtl.conditions.report1SelectedDates.length
  })
  headers[0].push({
    label: 'Summary of Compare',
    colspan: pageCtl.conditions.report1CalcResult.length
  })
  pageCtl.report1Headers = headers
}

const mergeCellStyle = (hotInstance, td, row, column, prop, value) => {
  td.style.verticalAlign = 'middle'
  if (value === 'Total') {
    td.style.fontWeight = 'bold'
    td.style.textAlign = 'center'
    td.innerHTML = value
  } else {
    td.innerHTML = value
  }
}

const renderReport1 = (hotInstance, td, row, column, prop, value, contentList) => {
  let tips = ''
  const r = hotInstance.getSourceDataAtRow(row)
  let name = ''
  switch (pageCtl.conditions.report1DateType) {
    case 'CALENDAR_YEAR':
      name = 'Error Years: '
      break
    case 'CALENDAR_HALF_YEAR':
      name = 'Error Half-years: '
      break
    case 'CALENDAR_QUARTER':
      name = 'Error Quarters: '
      break
    case 'CALENDAR_MONTH':
      name = 'Error Months: '
      break
    default:
      name = 'Error Months: '
  }

  const monthList: string[] = r[contentList]
  tips = name + '\t\n\t' + monthList.join('\t\n\t')

  if (tips) {
    let html = '<div title="' + tips + '">'
    if (value > 0) {
      html += '<b>' + value + '</b>'
    }
    html += '</div>'
    td.innerHTML = html
  } else {
    td.innerHTML = value
  }
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts).concat(['MATERIAL'])
})

const _report1SelectedColumns = computed(() => {
  if (pageCtl.conditions.report1GroupByColumns.length > 0) {
    return pageCtl.conditions.report1GroupByColumns
  } else {
    return ['LOCAL_PRODUCT_FAMILY']
  }
})

const _report1FcstColumns = computed(() => {
  return Object.keys(pageCtl.conditions.report1FcstColumns)
})

// const _report1CalcResultOpts = computed(() => {
//   let result = []
//   if (pageCtl.conditions.report1CompareType === 'History Forecast') {
//     result = ['FORECAST_SAME_PERIOD', 'FORECAST_ANNUAL_PROPORTION']
//   } else {
//     switch (pageCtl.conditions.report1DateType) {
//       case 'CALENDAR_MONTH':
//         result = ['HISTORY_AVG', 'HISTORY_STDEV', 'CURRENT_AVG', 'CURRENT_STDEV', 'NEGATIVE_VALUE_CHECK', 'NON_INTEGER_CHECK', 'LAST_YEAR']
//         break
//       case 'CALENDAR_QUARTER':
//         result = ['HISTORY_AVG', 'CURRENT_AVG', 'LAST_YEAR', 'ANNUAL_PROPORTION']
//         break
//       case 'CALENDAR_HALF_YEAR':
//         result = ['LAST_YEAR', 'ANNUAL_PROPORTION']
//         break
//       case 'CALENDAR_YEAR':
//         result = ['LAST_YEAR']
//         break
//     }
//   }
//   return result
// })

const _report1DateTypeOpts = computed(() => {
  let result = []
  if (pageCtl.conditions.report1CompareType === 'History Forecast') {
    result = ['CALENDAR_HALF_YEAR', 'CALENDAR_QUARTER', 'CALENDAR_MONTH']
  } else {
    result = ['CALENDAR_YEAR', 'CALENDAR_HALF_YEAR', 'CALENDAR_QUARTER', 'CALENDAR_MONTH']
  }
  return result
})
</script>

<style lang="scss" scoped>

.back .el-collapse .el-collapse-item .el-row:not(:last-child) {
  margin-bottom: 10px;
}

::v-deep .el-collapse-item__content {
  padding: 10px 0 !important;
}
::v-deep .el-collapse-item__header.is-active {
  border-bottom: 1px solid var(--el-collapse-border-color);
}
::v-deep .el-tabs__nav-wrap::after {
  height: 1px;
}

.el-collapse-item {
  .el-row {
    padding-left: 10px;
  }
}
</style>

<style lang="scss">
.ratio-style {
  .el-input__inner {
    text-align: center;
  }
  width: 100%;
  text-align: center;
}
</style>
