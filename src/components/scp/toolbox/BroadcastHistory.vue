<template>
  <div class="left-sidebar" id="broadcastHistory">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['SY_DIALOG_CONFIG_V']"
                        :after-apply="searchBroadcast"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                style="width: calc(100% - 35px)"
                v-model="pageCtl.conditions.report1DateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
            ></el-date-picker>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="searchBroadcast"
                        :data="pageCtl.conditions"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <scp-subscript id="BHR1"/>
          <el-collapse v-loading="pageCtl.tableLoading" v-model="pageCtl.conditions.report1ActiveNames" accordion>
            <el-collapse-item v-for="item in pageCtl.report1Data"
                              :title="item.ROW_ID"
                              :key="item.ROW_ID">
              <template #title>
                <div style="width: 100%; height: 100%; align-content: center">
                  <el-row>
                    <el-col :span="20" style="text-align: left; font-size: 12px">
                      <span
                          style="display: inline-block; width: 4px; height: 4px; background-color: #3dcd58; border-radius: 0; margin-right: 4px;"></span>
                      {{ item.REMIND_SUBJECT }}
                    </el-col>
                    <el-col :span="4" style="text-align: right; font-size: 11px; color: grey">
                      {{ item.CREATED_DATE }}
                    </el-col>
                  </el-row>
                </div>
              </template>
              <el-descriptions
                  :column="2"
                  :size="size"
                  label-width="20px"
                  border
              >
                <el-descriptions-item>
                  <template #label>
                    <div>
                      <el-icon :style="iconStyle">
                        <office-building/>
                      </el-icon>
                      Title
                    </div>
                  </template>
                  {{ item.REMIND_SUBJECT }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template #label>
                    <div>
                      <el-icon :style="iconStyle">
                        <location/>
                      </el-icon>
                      Effective Page
                    </div>
                  </template>
                  {{ item.PAGE_NAME }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template #label>
                    <div>
                      <el-icon :style="iconStyle">
                        <iphone/>
                      </el-icon>
                      Creation Date
                    </div>
                  </template>
                  {{ item.CREATED_DATE }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template #label>
                    <div>
                      <el-icon :style="iconStyle">
                        <user/>
                      </el-icon>
                      Created By
                    </div>
                  </template>
                  {{ item.CREATED_BY_NAME }}
                </el-descriptions-item>
                <el-descriptions-item :span="2">
                  <template #label>
                    <div>
                      <el-icon :style="iconStyle">
                        <tickets/>
                      </el-icon>
                      Content
                    </div>
                  </template>
                  <div class="html-content" v-html="item.DESCRIPTION"></div>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
          <el-pagination
              style="margin:10px 5px 0 10px;"
              v-model:current-page="pageCtl.conditions.page.currentPage"
              v-model:page-size="pageCtl.conditions.page.length"
              :page-sizes="[10, 20, 30, 50, 100 ]"
              size="small"
              :background="background"
              layout="total, sizes, ->, prev, pager, next"
              :total="pageCtl.conditions.page.total"
              @size-change="searchBroadcast"
              @current-change="searchBroadcast"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import {
  Iphone,
  Location,
  OfficeBuilding,
  Tickets,
  User
} from '@element-plus/icons-vue'
import type { ComponentSize } from 'element-plus'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const treeRef = ref()
const searchRef = ref()
const background = ref(true)
const disabled = ref(false)

interface report1DataType {
  URL: string
  REMIND_SUBJECT: string
  VALID_FROM: string
  VALID_TO: string
  FREQUENCY: string
  REMIND_COUNT: string
  DESCRIPTION: string
  CREATED_BY_SESA: string
  CREATED_BY_NAME: string
  CREATED_DATE: string
  ROW_ID: string
  PAGE_NAME: string
}

const size = ref<ComponentSize>('small')

const iconStyle = computed(() => {
  const marginMap = {
    large: '8px',
    default: '6px',
    small: '4px'
  }
  return {
    marginRight: marginMap[size.value] || marginMap.default
  }
})

const pageCtl = reactive({
  filterOpts: [],
  tableLoading: false,
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    report1DateRange: [],
    page: {
      currentPage: 1,
      pagging: true,
      start: 0,
      length: 10,
      total: 0
    },
    report1ActiveNames: '1'
  },
  report1Data: [] as Array<report1DataType>
})

onMounted(() => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 365)
  pageCtl.conditions.report1DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  queryBroadcastCascader()
})

const queryBroadcastCascader = () => {
  $axios({
    method: 'post',
    url: '/toolbox/broadcast_history/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
  })
}

const queryBroadcastData = () => {
  pageCtl.tableLoading = true
  $axios({
    method: 'post',
    url: '/toolbox/broadcast_history/query_broadcast_config',
    data: pageCtl.conditions
  }).then((response) => {
    pageCtl.conditions.page.total = response.total
    pageCtl.report1Data = response.data
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.tableLoading = false
  })
}

const searchBroadcast = () => {
  pageCtl.conditions.page.start = (pageCtl.conditions.page.currentPage - 1) * pageCtl.conditions.page.length
  queryBroadcastData()
}
</script>

<style lang="scss">
#broadcastHistory {
  .el-collapse {
    border-top: none;

    .el-step__title {
      font-size: 12px;
      line-height: 28px;
    }

    .el-step__description {
      font-size: 10px;
    }

    .el-collapse-item__wrap {
      border-top: 1px solid var(--el-collapse-border-color);
    }

    .el-collapse-item .el-collapse-item__header {
      height: 40px;
    }

    .el-collapse-item .el-collapse-item__header:hover {
      background-color: #f3f3f3;
    }

    .el-collapse-item__content {
      line-height: 15px;
      margin: 10px;
      padding-bottom: 0;
    }

    .html-content img {
      max-width: 800px; /* 设置图片最大宽度为容器宽度 */
      height: auto; /* 保持图片宽高比 */
    }
  }
}

</style>
