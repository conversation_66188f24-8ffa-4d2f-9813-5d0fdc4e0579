<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <div class="subscript-container">
          <scp-subscript id="PSMT"/>
          <scp-table
            url="/po/shipping_mode_tracking/query_report1"
            download-url="/po/shipping_mode_tracking/download_report1"
            :columns="pageCtl.report1TableColumn"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

const pageCtl = reactive({
  report1TableColumn: [
    { data: 'MAIL_ID' },
    { data: 'SEND_TIME' },
    { data: 'SEND_BY' },
    { data: 'PO_NUMBER' },
    { data: 'PO_ITEM' },
    { data: 'PR_NUMBER' },
    { data: 'PR_ITEM' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'ORDER_QUANTITY' },
    { data: 'UNIT_WEIGHT_IN_KG' },
    { data: 'TOTAL_WEIGHT_IN_KG' },
    { data: 'SAP_UNIT_WEIGHT' },
    { data: 'RCA' },
    { data: 'REASON_DETAILS' },
    { data: 'VENDOR_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'COUNTRY_CODE' },
    { data: 'SHORTAGE_FLAG' },
    { data: 'AB_LA' },
    { data: 'CREATE_DATE' },
    { data: 'DELIVERY_DATE' },
    { data: 'PUR_GROUP' },
    { data: 'SHIP_MODE' },
    { data: 'PRODUCT_LINE' },
    { data: 'ENTITY' },
    { data: 'SOH' },
    { data: 'OPEN_SO' },
    { data: 'AVALIABLE_STOCK' },
    { data: 'SS' },
    { data: 'Cur.Mon M-1 Fulfill' },
    { data: 'Last.Mon M-1 Fulfill' },
    { data: 'Cur.Mon M-3 Fulfill' },
    { data: 'Last.Mon M-3 Fulfill' },
    { data: 'Cur.Mon M-1 FCST' },
    { data: 'Last.Mon M-1 FCST' },
    { data: 'Cur.Mon M-3 FCST' },
    { data: 'Last.Mon M-3 FCST' },
    { data: 'Cur.Mon CRD' },
    { data: 'Last.Mon CRD' }
  ]
})
</script>
