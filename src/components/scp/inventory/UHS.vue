<template>
  <div class="left-sidebar" id="UHS">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base="UHS_RCA_FILTER_V" :filter-base="['UHS_RCA_V']"
                                    :after-apply="search"/>
          </el-col>
          <!--type-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small" placeholder="Type">
              <el-option
                  v-for="item in ['Net', 'Gross', 'Special']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- search -->
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['week','report1SelectedWeek']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="subscript-container subscript-container-left" style="height: 445px">
              <h3 style="text-align: left;margin-top: 10px">UHS Report</h3>
              <el-select v-model="pageCtl.conditions.week" size="small" placeholder="Week"
                         style="width: 150px !important;position:absolute; top:10px; right: 10px" @change="search2">
                <el-option
                    v-for="item in pageCtl.weekOpts"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
              <scp-subscript id="IMUR"/>
              <scp-table
                  v-loading="pageCtl.loading.report1"
                  :contextMenuItems="pageCtl.report1MenuItems"
                  ref="report1TableRef"
                  :columns="_report1Columns['columns']"
                  :fixedColumnsLeft="1"
                  :nested-headers="_report1Columns['headers']"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :tableStriped="false"
                  :dropdownMenu="false"
                  :row-headers="false"
                  :pagging="false"
                  :max-height="380"
                  :after-select="report1AfterSelect"
                  url="/inventory/uhs/query_report1"/>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="subscript-container subscript-container-right" style="height: 445px">
              <scp-subscript id="IMUT" ref="imutRef"/>
              <div class="front">
                <chart ref="treeChartRef" style="width: 100%;height:445px" :option="_report2Opt"
                       v-loading="pageCtl.loading.report2 || pageCtl.loading.filter"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Report Settings</div>
                  <el-row>
                    <el-col :span="6">Week Range</el-col>
                    <el-col :span="8">
                      <el-select v-model="pageCtl.conditions.report2WeekStart" placeholder="Start">
                        <el-option
                            v-for="(item, index) in  pageCtl.weekOpts"
                            :key="index"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="1" style="font-weight: bold;text-align: center;line-height: 2">~</el-col>
                    <el-col :span="9">
                      <el-select v-model="pageCtl.conditions.report2WeekEnd" placeholder="End">
                        <el-option
                            v-for="(item, index) in  pageCtl.weekOpts"
                            :key="index"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                  <div class="box-footer">
                    <el-button @click="imutRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="imutRef.toggleView();searchReport2()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container report3" v-loading="pageCtl.loading.filter">
              <scp-subscript id="IMUL"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.HSStatus" size="small" style="width: var(--scp-input-width) !important;"
                             placeholder="HS Status" multiple clearable collapse-tags filterable>
                    <el-option label="Excess" value="EXCESS"/>
                    <el-option label="Excess Special Event" value="EXCESS_SPECIAL"/>
                    <el-option label="Theo.provision" value="PROV"/>
                    <el-option label="Missing" value="MISSING"/>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-select v-model="pageCtl.conditions.topNType" size="small" style="width: 100% !important;" class="search-group-left"
                             @change="topNTypeChange">
                    <el-option label="By Row" value="ROW"/>
                    <el-option label="By %" value="PERCENT"/>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-input-number v-model="pageCtl.conditions.topN" :min="1" :max="pageCtl.conditions.topNType === 'ROW' ? 50000 : 100"
                                   label="TOP N" size="small" controls-position="right" style="width: var(--scp-input-width) !important;"
                                   class="search-group-right"/>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="report3TableRef"
                  :contextMenuItems="pageCtl.report3MenuItems"
                  :columns="_report3Columns"
                  :nested-headers="_report3NestedHeader"
                  :primary-key-id="['COD_MATERIAL_COM','COD_PLANT']"
                  :lazy="true"
                  :dropdown-menu="false"
                  :params="pageCtl.conditions"
                  :max-height="400"
                  url="/inventory/uhs/query_report3"
                  download-url="/inventory/uhs/download_report3"
                  save-url="/inventory/uhs/save_report3"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- report3 upload-->
    <scp-upload
        ref="uploadRef"
        title="UHS Data Upload"
        text="Select a file"
        w="600px"
        h="200px"
        upload-url='/inventory/uhs/upload_report3_result'
        download-template-url='/inventory/uhs/download_report3_result_template'
        :show-btn="false"
        :on-upload-end="onUploadEnd">
    </scp-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $downloadFile: any = inject('$downloadFile')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1TableRef = ref()
const treeChartRef = ref()
const report3TableRef = ref()
const uploadRef = ref()
const imutRef = ref()

const viewReport1Details = () => {
  $viewDetails({
    url: '/inventory/uhs/query_report1_details',
    durl: '/inventory/uhs/download_report1_details',
    params: pageCtl.conditions,
    title: pageCtl.report1DetailsTitle
  })
}

const pageCtl = reactive({
  weekOpts: [],
  loading: {
    filter: false,
    report1: false,
    report2: false
  },
  conditions: {
    week: '',
    $scpFilter: {
      cascader: [],
      filter: []
    },
    type: 'Gross',
    report1SelectedWeek: '' as any,
    report1SelectedType: '',
    report1SelectedEntity: '',
    report2WeekStart: '',
    report2WeekEnd: '',

    topNType: 'ROW',
    topN: 10000,
    HSStatus: ['EXCESS', 'EXCESS_SPECIAL']
  },
  report1DetailsTitle: '',
  report1StripedClass: '',
  report1Weeks: [],
  report1MenuItems: {
    view_details: {
      name: 'View details',
      disabled: () => {
        return isNaN(pageCtl.conditions.report1SelectedWeek)
      },
      callback: viewReport1Details
    },
    view_split0: { name: '---------' }
  },
  report2Data: {
    xAxis: [],
    yAxis1: [],
    yAxis2: [],
    yAxis3: [],
    yAxis4: [],
    yAxis5: []
  },
  report3Dropdown: {
    RCA_ACTION: [],
    COD_ACTION_RCA: [],
    COD_FREQ_RCA: [],
    COD_STATUS_RCA: []
  },
  report3MenuItems: {
    download_details: {
      name: '1. Download recommend RCA',
      callback: () => {
        $downloadFile('/inventory/uhs/download_report3_recommend_rca', pageCtl.conditions)
      }
    },
    upload_rca: {
      name: '2. Upload RCA',
      callback: () => {
        uploadRef.value.showUploadWin()
      }
    },
    download_for_onemm: {
      name: '3. Download for oneMM',
      callback: () => {
        downloadReport3OneMM()
      }
    },
    view_split0: { name: '---------' }
  }
})

onMounted(() => {
  initPage()
})

const initPage = () => {
  pageCtl.loading.filter = true
  report1TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/uhs/init_page'
  }).then((body) => {
    pageCtl.weekOpts = body.week
    if (pageCtl.weekOpts.length > 0) {
      pageCtl.conditions.week = pageCtl.weekOpts[0]
      pageCtl.conditions.report2WeekStart = pageCtl.weekOpts[Math.min(8, pageCtl.weekOpts.length)]
      pageCtl.conditions.report2WeekEnd = pageCtl.weekOpts[0]
    }
    pageCtl.report3Dropdown = body.report3Dropdown
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  searchReport1()
  searchReport2()
  searchReport3()
}
const search2 = () => {
  searchReport1()
  searchReport2()
}
const searchReport1 = () => {
  pageCtl.report1StripedClass = ''
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/inventory/uhs/query_report1_weeks',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Weeks = body
    nextTick(() => {
      report1TableRef.value.clearAndSearch()
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/inventory/uhs/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  report3TableRef.value.search()
}
const report1AfterSelect = (r, rc, c, column) => {
  pageCtl.conditions.report1SelectedWeek = c
  pageCtl.conditions.report1SelectedType = r.ENTITY === r.PARENT ? '' : r.ENTITY
  pageCtl.conditions.report1SelectedEntity = r.PARENT || ''
  const header = _report1Columns.value && _report1Columns.value.columns && _report1Columns.value.columns[column] ? _report1Columns.value.columns[column].title : ''

  let subTilte = $join(pageCtl.conditions.report1SelectedEntity, pageCtl.conditions.report1SelectedType, header)
  if (subTilte) {
    subTilte = '[' + subTilte + ']'
  }
  pageCtl.report1DetailsTitle = 'View Details ' + subTilte
}
const renderTableStriped = (hotInstance, td, row) => {
  if (row % 5 === 0) {
    pageCtl.report1StripedClass = 'striped-even3-tr'
  } else {
    pageCtl.report1StripedClass = 'striped-odd3-tr'
  }

  if (td.parentNode) {
    td.parentNode.className = pageCtl.report1StripedClass
  }
  const r = hotInstance.getSourceDataAtRow(row)
  if (r && (r.ENTITY === 'Excess' || r.ENTITY === 'Theo.provision' || r.ENTITY === 'Missing' || r.ENTITY === 'Total Stock')) {
    td.innerHTML = r.ENTITY
    td.style.textAlign = 'right'
    if (r.ENTITY !== 'Total Stock') {
      td.style.color = 'var(--scp-text-color-error)'
    }
  } else {
    td.innerHTML = r.ENTITY
    td.style.textAlign = 'left'
    if (r.ENTITY === 'Total') {
      td.style.fontWeight = 'bold'
    }
  }
}
const renderRatio = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  td.style.textAlign = 'right'
  if (r && (r.ENTITY !== 'Excess' && r.ENTITY !== 'Theo.provision' && r.ENTITY !== 'Missing' && r.ENTITY !== 'Total Stock')) {
    if (value === 0) {
      td.innerHTML = '0'
    } else if (value) {
      td.innerHTML = (value * 100).toFixed(1) + '%'
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 0)
    } else {
      td.innerHTML = value
    }
  }
}
const topNTypeChange = () => {
  if (pageCtl.conditions.topNType === 'ROW') {
    pageCtl.conditions.topN = 10000
  } else {
    pageCtl.conditions.topN = 80
  }
}
const onUploadEnd = () => {
  initPage()
}
const downloadReport3OneMM = () => {
  $downloadFile('/inventory/uhs/download_report3_one_mm', pageCtl.conditions)
}

const _report1Columns = computed(() => {
  const columns: any = [
    { data: 'ENTITY', render: renderTableStriped }
  ]

  for (let i = 0; i < pageCtl.report1Weeks.length; i++) {
    const c: any = pageCtl.report1Weeks[i]
    c.render = renderRatio
    columns.push(c)
  }

  return {
    columns,
    headers: [[
      '',
      {
        label: 'Summary',
        colspan: 3
      },
      {
        label: 'Weekly',
        colspan: pageCtl.report1Weeks.length - 3
      }
    ]]
  }
})

const _report2Opt = computed(() => {
  return {
    title: {
      text: 'UHS Trend Chart'
    },
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          const seriesName = params[i].seriesName

          tip.push('<div style="width:9rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push('<span style="float: right">')
          if (seriesName.indexOf('%') !== -1) {
            tip.push(value + '%')
          } else {
            tip.push($shortenNumber(value))
          }

          if (seriesName.indexOf('Percent') !== -1) {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: $grid(),
    xAxis: [{
      type: 'category',
      data: pageCtl.report2Data.xAxis
    }],
    yAxis: [{
      type: 'value',
      splitLine: {
        show: true
      },
      scale: true,
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      scale: true,
      axisLabel: {
        show: true,
        interval: 'auto',
        formatter: '{value} %'
      },
      splitLine: {
        show: false
      }
    }],
    legend: $legend({ data: ['Stock Value', 'Net UHS', 'Special Events', 'Special Events%', 'Net UHS%'] }),
    series: [{
      name: 'Stock Value',
      data: pageCtl.report2Data.yAxis1,
      type: 'line',
      areaStyle: {},
      label: {
        show: true,
        color: '#5470c6',
        position: 'top',
        formatter: (param) => {
          return $shortenNumber(param.data, 0)
        }
      }
    },
    {
      name: 'Net UHS',
      data: pageCtl.report2Data.yAxis2,
      type: 'bar',
      stack: 'Total',
      zlevel: 20,
      label: {
        show: true,
        position: 'top',
        color: 'rgba(0,0,0,0)',
        formatter: (param) => {
          return $shortenNumber(param.data, 0)
        }
      }
    },
    {
      name: 'Special Events',
      data: pageCtl.report2Data.yAxis3,
      type: 'bar',
      stack: 'Total',
      zlevel: 20,
      label: {
        show: true,
        color: '#fac858',
        position: 'top',
        formatter: (param) => {
          return $shortenNumber(param.data, 0)
        }
      }
    },
    {
      name: 'Special Events%',
      data: pageCtl.report2Data.yAxis4,
      type: 'line',
      yAxisIndex: 1,
      zlevel: 20,
      label: {
        show: true,
        color: '#ee6666',
        position: 'top',
        formatter: '{c}%'
      }
    },
    {
      name: 'Net UHS%',
      data: pageCtl.report2Data.yAxis5,
      type: 'line',
      yAxisIndex: 1,
      zlevel: 20,
      label: {
        show: true,
        color: '#73c0de',
        position: 'top',
        formatter: '{c}%'
      }
    }]
  }
})

const _report3NestedHeader = computed(() => {
  return [
    [
      { label: '', colspan: 1 },
      { label: 'COD_PLANT', colspan: 1 },
      { label: 'COD_MATERIAL_COM', colspan: 1 },
      { label: '', colspan: 1 },
      { label: 'HS_XSMIS_RCA', colspan: 1 },
      { label: '', colspan: 1 },
      { label: 'ACTION_RECOMMEND', colspan: 1 },
      { label: 'COD_ACTION_RCA', colspan: 1 },
      { label: 'FINAL_ACTION', colspan: 1 },
      { label: 'HS_XSMIS_ACT', colspan: 1 },
      { label: 'DATE_INI_BTN', colspan: 1 },
      { label: 'DATE_FIN_BTN', colspan: 1 },
      { label: 'COD_STATUS_RCA', colspan: 1 },
      { label: 'COD_FREQ_RCA', colspan: 1 },
      { label: 'STOCK_VS_HS', colspan: 1 },
      { label: 'GROSS_UNHEALTHY_VALUE', colspan: 1 },
      { label: 'JNI_STOCK_VALUE', colspan: 1 },
      { label: 'HS_EXCESS_VALUE', colspan: 1 },
      { label: 'THEO_PROV_VALUE', colspan: 1 },
      { label: 'HS_MISSING_VALUE', colspan: 1 },
      { label: 'DECLARED_SPECIAL_EVENTS', colspan: 1 },
      { label: 'JNI_SS_VALUE', colspan: 1 },
      { label: 'SS', colspan: 1 },
      { label: 'SS_VALUE', colspan: 1 },
      { label: 'STOCK', colspan: 1 },
      { label: 'STOCK_VALUE', colspan: 1 },
      { label: 'ADU_ADF_BASIS', colspan: 1 },
      { label: 'ADU_TOTAL', colspan: 1 },
      { label: 'ADU_TOTAL_VALUE', colspan: 1 },
      { label: 'ADF', colspan: 1 },
      { label: 'ADF_VALUE', colspan: 1 },
      { label: 'OPEN_SO_ALL_QTY_HS', colspan: 1 },
      { label: 'OPEN_SO_ALL_VAL_HS', colspan: 1 },
      { label: 'OPEN_SO_ALL_QTY', colspan: 1 },
      { label: 'OPEN_SO_ALL_VAL', colspan: 1 },
      { label: 'OPEN_PR_QTY_HS', colspan: 1 },
      { label: 'OPEN_PR_VAL_HS', colspan: 1 },
      { label: 'FIXED_LOT_SIZE', colspan: 1 },
      { label: 'JNI_LOT_SIZE', colspan: 1 },
      { label: 'STOCKING_POLICY', colspan: 1 },
      { label: 'MTO_MANUAL_RULE', colspan: 1 },
      { label: 'DATE_SALES', colspan: 1 },
      { label: 'BLOCKED_QTY', colspan: 1 },
      { label: 'BLOCKED_VAL', colspan: 1 },
      { label: 'COD_SYSTEM', colspan: 1 },
      { label: 'DATE_ACTION_RCA', colspan: 1 },
      { label: 'COD_ACTION_LEADER', colspan: 1 },
      { label: 'SHORT_NAME_LEADER', colspan: 1 },
      { label: 'COD_ACTION_OWNER', colspan: 1 },
      { label: 'SHORT_NAME_OWNER', colspan: 1 },
      { label: 'STAKE', colspan: 1 },
      { label: '', colspan: 29 },
      { label: 'COD_PM0', colspan: 1 },
      { label: 'ID_SQ_MRP_CONTROLLER', colspan: 1 },
      { label: 'NEW_PRODUCTS', colspan: 1 },
      { label: 'FLG_FIXED_SUPPLIER', colspan: 1 }
    ]
  ]
})

const _report3Columns = computed(() => {
  return [
    { data: 'GROSS_UHS_VALUE_OF_ALL', title: 'Rank%', type: 'numeric', precision: 2 },
    { data: 'COD_PLANT', title: 'Plant Code' },
    { data: 'COD_MATERIAL_COM', title: 'Commercial Ref.' },
    { data: 'RCA_RECOMMEND', title: 'RCA Recomd.' },
    { data: 'HS_XSMIS_RCA', title: 'Current RCA' },
    {
      data: 'FINAL_RCA',
      title: 'Final RCA',
      editor: 'select',
      selectOptions: pageCtl.report3Dropdown.RCA_ACTION
    },
    { data: 'ACTION_RECOMMEND', title: 'Action Recomd.' },
    { data: 'COD_ACTION_RCA', title: 'Current Action' },
    {
      data: 'FINAL_ACTION',
      title: 'Final Action',
      editor: 'select',
      selectOptions: pageCtl.report3Dropdown.COD_ACTION_RCA
    },
    { data: 'HS_XSMIS_ACT', title: 'Action Details' },
    { data: 'DATE_INI_BTN', title: 'Initial BTN', type: 'date' },
    { data: 'DATE_FIN_BTN', title: 'Last BTN', type: 'date' },
    {
      data: 'COD_STATUS_RCA',
      title: 'Action Status',
      editor: 'select',
      selectOptions: pageCtl.report3Dropdown.COD_STATUS_RCA
    },
    {
      data: 'COD_FREQ_RCA',
      title: 'Review Freqency',
      editor: 'select',
      selectOptions: pageCtl.report3Dropdown.COD_FREQ_RCA
    },
    { data: 'STOCK_VS_HS', title: 'HS Status' },
    { data: 'GROSS_UNHEALTHY_VALUE', title: 'GUHS Value', type: 'numeric' },
    { data: 'JNI_STOCK_VALUE', title: 'Sto. Val of Last Wk', type: 'numeric' },
    { data: 'HS_EXCESS_VALUE', title: 'Excess Val.', type: 'numeric' },
    { data: 'THEO_PROV_VALUE', title: 'Theo.Pro Val.', type: 'numeric' },
    { data: 'HS_MISSING_VALUE', title: 'Missing Val.', type: 'numeric' },
    { data: 'DECLARED_SPECIAL_EVENTS', title: 'Special Events Val.', type: 'numeric' },
    { data: 'JNI_SS_VALUE', title: 'Theo. SS Val.', type: 'numeric' },
    { data: 'SS', title: 'Current SS Qty', type: 'numeric' },
    { data: 'SS_VALUE', title: 'Current SS val.', type: 'numeric' },
    { data: 'STOCK', title: 'Current stock Qty', type: 'numeric' },
    { data: 'STOCK_VALUE', title: 'Current Stock val.', type: 'numeric' },
    { data: 'ADU_ADF_BASIS', title: 'Calcu Basis' },
    { data: 'ADU_TOTAL', title: 'ADU Qty', type: 'numeric' },
    { data: 'ADU_TOTAL_VALUE', title: 'ADU Val.', type: 'numeric' },
    { data: 'ADF', title: 'ADF Qty', type: 'numeric' },
    { data: 'ADF_VALUE', title: 'ADF Val.', type: 'numeric' },
    { data: 'OPEN_SO_ALL_QTY_HS', title: 'Open SO Qty(HS)', type: 'numeric' },
    { data: 'OPEN_SO_ALL_VAL_HS', title: 'Open SO Value(HS)', type: 'numeric' },
    { data: 'OPEN_SO_ALL_QTY', title: 'Open SO Qty_All', type: 'numeric' },
    { data: 'OPEN_SO_ALL_VAL', title: 'Open SO Val_All', type: 'numeric' },
    { data: 'OPEN_PR_QTY_HS', title: 'Open Rreqmt Qty(HS)', type: 'numeric' },
    { data: 'OPEN_PR_VAL_HS', title: 'Open Reqmt Val.(HS)', type: 'numeric' },
    { data: 'FIXED_LOT_SIZE', title: 'MOQ', type: 'numeric' },
    { data: 'JNI_LOT_SIZE', title: 'Theo.MOQ', type: 'numeric' },
    { data: 'STOCKING_POLICY', title: 'Stock Policy' },
    { data: 'MTO_MANUAL_RULE', title: 'Manual Stock Policy' },
    { data: 'DATE_SALES', title: 'Days Dince Last Consptn', type: 'numeric' },
    { data: 'BLOCKED_QTY', title: 'B.Qty', type: 'numeric' },
    { data: 'BLOCKED_VAL', title: 'B.Val.', type: 'numeric' },
    { data: 'COD_SYSTEM', title: 'System' },
    { data: 'DATE_ACTION_RCA', title: 'Action Date', type: 'date' },
    { data: 'COD_ACTION_LEADER', title: 'Action Leader SESA#' },
    { data: 'SHORT_NAME_LEADER', title: 'Action Leader' },
    { data: 'COD_ACTION_OWNER', title: 'Action Owner SESA#' },
    { data: 'SHORT_NAME_OWNER', title: 'Action Owner' },
    { data: 'STAKE', title: 'Stake' },
    { data: 'BU', title: 'BU' },
    { data: 'CLUSTER_NAME', title: 'Cluster' },
    { data: 'PLANT_TYPE', title: 'Plant Type' },
    { data: 'ENTITY', title: 'Entity' },
    { data: 'PLANT_CODE' },
    { data: 'PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MRP_CONTROLLER' },
    { data: 'VENDOR_NAME' },
    { data: 'EXISTING_IN_BOM' },
    { data: 'MATERIAL_TYPE' },
    { data: 'SPECIAL_PROC_CODE' },
    { data: 'COMMODITY_CODE' },
    { data: 'PRODUCT_LINE_INV' },
    { data: 'ACTIVENESS' },
    { data: 'PROCUREMENT_TYPE' },
    { data: 'LOCATION_CATEGORY' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'REPL_STRATEGY' },
    { data: 'MATERIAL_CATEGORY' },
    { data: 'CALCULATED_FMR' },
    { data: 'PRODUCT_FAMILY_INV' },
    { data: 'PRODUCTION_LINE' },
    { data: 'PROPOSED_FIN_PROV_QTY', type: 'numeric' },
    { data: 'PROPOSED_FIN_PROV_VALUE', type: 'numeric' },
    { data: 'PROPOSED_PROVISION_RULE', type: 'numeric' },
    { data: 'NETTED_STOCK_QTY', type: 'numeric' },
    { data: 'NETTED_STOCK_VALUE', type: 'numeric' },
    { data: 'M1_NOC_QTY', type: 'numeric' },
    { data: 'M1_NOC_VAL', type: 'numeric' },
    { data: 'COD_PM0', title: 'PM0' },
    { data: 'ID_SQ_MRP_CONTROLLER', title: 'MRP CN' },
    { data: 'NEW_PRODUCTS', title: 'New Product' },
    { data: 'FLG_FIXED_SUPPLIER', title: 'Vendor Code' }
  ]
})

</script>
<style lang="scss">
#UHS {
  .report3 {
    .htCore {
      thead {
        tr:first-child {
          font-size: 0.4rem;
        }
      }
    }
  }
}
</style>
