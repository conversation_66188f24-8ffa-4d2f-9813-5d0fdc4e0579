<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">

          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['DISTRIBUTOR_INVENTORY_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <div class="search-group-right" style="width: var(--scp-input-width) !important;">
              <el-date-picker
                  v-model="pageCtl.conditions.dateRange"
                  type="monthrange"
                  unlink-panels
                  format="YYYYMM"
                  value-format="YYYYMM"
                  range-separator="to"
                  :clearable="false"
                  class="search-group-right"
                  style="width: calc(100% - 20px) !important;">
              </el-date-picker>
            </div>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.sort" size="small">
              <el-option
                  v-for="item in ['None', 'Top 50 Inventory','Top 20 Inventory','Top 50 Sales','Top 20 Sales']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :expand="true"
                        :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-col :span="24">
          <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report2">
            <scp-subscript id="COMC"/>
            <el-row>
              <el-col :span="6" class="report-sub-title-right"
                      :style="{ transform: 'translateX(-25px) translateY(2px)' }">
              </el-col>
            </el-row>
            <chart ref="report2Ref" :height="516" :option="_report2Opt"/>
          </div>
        </el-col>
        <el-row>
          <el-col :span="24">
            <el-row class="search-box">
              <el-col :span="5">
                <el-select v-model="pageCtl.conditions.categroy" placeholder="Categroy" filterable clearable multiple
                           collapse-tags>
                  <el-option
                      v-for="item in _pivotColumns"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.report3ResultType" size="small">
                  <el-option
                      v-for="item in ['SALES_VALUE', 'INVENTORY_VALUE',  'SPOT_DIN']"
                      :value="item"
                      :label="item"
                      :key="item"/>
                </el-select>
              </el-col>
              <el-button size="small" @click="search">
                <font-awesome-icon icon="search"/>
              </el-button>
            </el-row>
          </el-col>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <h3 style="text-align: left;">Distributor Inventory Summary</h3>
              <scp-subscript id="COCS" ref="report3SubRef"/>
              <scp-table
                  url="/inventory/distributor_inventory/query_report3"
                  download-url="/inventory/distributor_inventory/download_report3"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :show-total="pageCtl.showTotal"
                  :pagging="false"
                  :fixed-columns-left="_category.length"
                  :max-height="375"
                  :context-menu-items="pageCtl.report3ContextItems"
                  :after-select="afterReport3Select"
                  ref="report3TableRef"
                  :columns="pageCtl.report3Columns"
                  :editable="false"/>
            </div>
          </el-col>
        </el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <h3 style="text-align: left;">Distributor Inventory Detail</h3>
              <scp-subscript id="MDBO"/>
              <scp-table
                  ref="summaryTableRef"
                  :download-specify-column="false"
                  :columns="pageCtl.columns"
                  :show-total="true"
                  :params="pageCtl.conditions"
                  :after-select="afterSelect"
                  :fixedColumnsLeft="2"
                  :context-menu-items="pageCtl.contextItems"
                  :page-sizes="[20, 50, 100, 200, 500]"
                  :lazy="true"
                  url="/inventory/distributor_inventory/query_report1"
                  download-url="/inventory/distributor_inventory/download_report1"
                  :editable="false"/>
            </div>
          </el-col>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $toolbox: any = inject('$echarts.toolbox')
const $deepClone:any = inject('$deepClone')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $join:any = inject('$join')

const searchRef = ref()
const report3SubRef = ref()
const report3TableRef = ref()
const summaryTableRef = ref()

const viewMasterDataDetails = () => {
  $viewDetails({
    url: '/inventory/distributor_inventory/query_report1_details',
    durl: '/inventory/distributor_inventory/download_report1_details',
    params: pageCtl.conditions,
    title: 'View Summary Details ' + (pageCtl.conditions.selectedValue.length ? ('[' + pageCtl.conditions.selectedValue.join(', ') + ']') : '')
  })
}

const pageCtl = reactive({
  filterOpts: [],
  columns: [],
  showTotal: true,
  loading: {
    filter: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    dateRange: [] as any,
    type: 'Qty',
    calcType: 'Sum',
    categroy: ['PRODUCTION_LINE'],
    selectedValue: [],
    report2DateType: [],
    sort: 'None',
    report3SelectedValues: [],
    report3SelectedDate: '',
    report3ValueType: 'Value',
    report3ColumnNames: [],
    report3ResultType: 'SALES_VALUE',
    report3TopQuantities: 50
  },
  report2Data: {
    xAxis: [],
    yAxis1: [], // SPOT_DIN
    yAxis2: [], // Inventory value
    yAxis3: [] // sales value
  },
  report2SubTitle: '',
  report3ContextItems: {
    view_split0: { name: '---------' }
  },
  report3Columns: [],
  report4DownloadDialogVisible: false,
  contextItems: {
    view_details: {
      name: 'View details',
      callback: viewMasterDataDetails
    },
    view_split0: { name: '---------' }
  }
})
watch(() => pageCtl.conditions.report2DateType, () => {
  searchReport2()
})

onMounted(() => {
  initPage()
  search()
})
const initPage = () => {
  const now = new Date()
  pageCtl.loading.filter = true
  pageCtl.conditions.dateRange = [$dateFormatter(new Date(now.getFullYear(), now.getMonth() - 5, 1), 'yyyyMM'), $dateFormatter(new Date(), 'yyyyMM')]
  $axios({
    method: 'post',
    url: '/inventory/distributor_inventory/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  parseTableColumn()
  summaryTableRef.value.search()
  searchReport2()
  searchReport3()
  if (pageCtl.conditions.report3ResultType === 'SPOT_DIN') {
    pageCtl.showTotal = false
  } else {
    pageCtl.showTotal = true
  }
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/inventory/distributor_inventory/query_report2',
    data: _conditions.value
  }).then((body) => {
    pageCtl.report2SubTitle = ''
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/inventory/distributor_inventory/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.clearAndSearch()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}
const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }
  const selected = [] as any
  for (let i = 0; i < _category.value.length; i++) {
    let v = r[_category.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report3SelectedValues = selected
}
const parseReport3Columns = () => {
  const result = [] as any
  for (let i = 0; i < _category.value.length; i++) {
    result.push({
      data: _category.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (pageCtl.conditions.report3ValueType === 'Value') {
          td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
        } else {
          td.innerHTML = value ? (value * 100).toFixed(2) + '%' : '0'
        }
      }
    })
  }
  return result
}
const _category = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return ['CUSTOMER_CODE', 'CUSTOMER_NAME', 'PRODUCT_TYPE']
  }
})

const afterSelect = (row) => {
  if (row[_selectedCategroy.value[0]] === 'Total') {
    pageCtl.conditions.selectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _selectedCategroy.value.length; i++) {
      selectedValue.push(row[_selectedCategroy.value[i]])
    }
    pageCtl.conditions.selectedValue = selectedValue
  }
}
const _selectedCategroy = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return ['CUSTOMER_NAME']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
const parseTableColumn = () => {
  const columns = [] as any
  for (let i = 0; i < _selectedCategroy.value.length; i++) {
    columns.push({
      data: _selectedCategroy.value[i],
      title: _selectedCategroy.value[i]
    })
  }
  columns.push(
    { data: 'MONTH', type: 'numeric' },
    { data: 'SALES_VALUE', type: 'numeric' },
    { data: 'INVENTORY_VALUE', type: 'numeric' },
    { data: 'INVENTORY_QTY', type: 'numeric' },
    { data: 'SPOT_DIN', type: 'numeric' },
    { data: 'NET_VALUE', type: 'numeric' }
  )
  pageCtl.columns = columns
}

const _conditions = computed(() => {
  const cond = $deepClone(pageCtl.conditions)
  return cond
})

const _report2Opt = computed(() => {
  const option: any = {
    title: {
      text: 'Spot DIN Charts ' + pageCtl.report2SubTitle
    },
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'axis'
    },
    grid: $grid(),
    legend: $legend({ data: ['Inventory Value', 'Sales Value', 'SPOT DIN'] }),
    xAxis: {
      type: 'category',
      data: pageCtl.report2Data.xAxis
    },
    yAxis: [{
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }],
    visualMap: {
      show: false,
      dimension: 1,
      seriesIndex: 0
    },
    series: [
      {
        name: 'SPOT DIN',
        type: 'line',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report2Data.yAxis1,
        label: {
          show: true,
          fontSize: 10
        },
        lineStyle: {
          color: '#ff5c33'
        },
        itemStyle: {
          color: '#ff5c33'
        }
      },
      {
        name: 'Inventory Value',
        type: 'bar',
        data: pageCtl.report2Data.yAxis3,
        itemStyle: {
          color: '#1058ad'
        }
      },
      {
        name: 'Sales Value',
        type: 'bar',
        data: pageCtl.report2Data.yAxis2,
        itemStyle: {
          color: '#04b304'
        }
      }
    ]
  }
  return option
})
</script>
