<template>
  <div class="left-sidebar" style="width: 100%">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['BAD_INDENT_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Value', 'Line']"
                  :value="item"
                  :label="item"
                  :key="item"/>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['report2DateRange', 'report3SelectedDate', 'report3DateRange']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="IESC" ref="report1SubRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :key="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :value="item"
                                   :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :key="item"
                                   :label="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." filterable>
                        <el-option v-for="item in _pivotColumns"
                                   :value="item"
                                   :label="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option v-for="item in [1, 2]"
                                   :label="item"
                                   :key="item"
                                   :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox v-for="item in pageCtl.report1TooltipsOpts"
                                 :value="item"
                                 :label="item"
                                 :key="item"/>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button
                        @click="report1SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report1SubRef.toggleView();searchReport1()">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="14" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="IESD" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Filter</div>
                  <el-row>
                    <el-col :span="6">xAixs</el-col>
                    <el-col :span="12">
                      <el-select v-model="pageCtl.conditions.report2ShowType"
                                 size="small">
                        <el-option v-for="item in ['VIEW_BY_VALUE', 'VIEW_BY_PERCENT']"
                                   :label="item"
                                   :value="item"
                                   :key="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">yAixs</el-col>
                    <el-col :span="12">
                      <el-select v-model="pageCtl.conditions.report2SelectedType" size="small">
                        <el-option
                            v-for="item in ['VIEW_BY_DAY', 'VIEW_BY_WEEK', 'VIEW_BY_MONTH', 'VIEW_BY_QUARTER', 'VIEW_BY_YEAR']"
                            :value="item"
                            :label="item"
                            :key="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">Stack By</el-col>
                    <el-col :span="12">
                      <el-select
                          v-model="pageCtl.conditions.report2ViewType" size="small" filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">Date Range</el-col>
                    <el-col :span="12">
                      <el-date-picker
                          size="small"
                          v-model="pageCtl.conditions.report2DateRange"
                          type="daterange"
                          unlink-panels
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          range-separator="to"
                          :clearable="false"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{}"/>
                    </el-col>
                  </el-row>
                  <div class="box-footer">
                    <el-button @click="report2SubRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="report2SubRef.toggleView();searchReport2()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="IEST" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3SelectedColumns" size="small" placeholder="Columns"
                             multiple collapse-tags
                             clearable filterable>
                    <el-option v-for="item in _pivotColumns"
                               :label="item"
                               :key="item"
                               :value="item"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ViewType" size="small">
                    <el-option label="View by day" value="VIEW_BY_DAY"/>
                    <el-option label="View by week" value="VIEW_BY_WEEK"/>
                    <el-option label="View by month" value="VIEW_BY_MONTH"/>
                    <el-option label="View by quarter" value="VIEW_BY_QUARTER"/>
                    <el-option label="View by year" value="VIEW_BY_YEAR"/>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.report3DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}"/>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  url="/inventory/excess_so_stock/query_report3"
                  download-url="/inventory/excess_so_stock/download_report3"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :show-total="true"
                  :pagging="false"
                  :fixed-columns-left="_report3SelectedColumns.length"
                  :max-height="375"
                  :context-menu-items="pageCtl.report3ContextItems"
                  :after-select="afterReport3Select"
                  ref="report3TableRef"
                  :columns="pageCtl.report3Columns"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!--  report1 contextmenu  -->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1SubRef = ref()
const report2SubRef = ref()
const report2ChartRef = ref()
const report3SubRef = ref()
const report3TableRef = ref()
const report1ContextmenuRef = ref()

const viewReport3Details = () => {
  let title = $join(
    pageCtl.conditions.report3SelectedDate,
    ...pageCtl.conditions.report3SelectedValues
  )
  if (title) {
    title = ' [' + title + ']'
  }

  $viewDetails({
    url: '/inventory/excess_so_stock/query_report3_details',
    durl: '/inventory/excess_so_stock/download_report3_details',
    params: pageCtl.conditions,
    title: 'View Details' + title
  })
}

const pageCtl = reactive({
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    resultType: 'Value',
    level1: 'BU',
    level2: 'PLANT_CODE',
    level3: 'STORAGE_LOCATION',
    level4: 'PRODUCT_LINE',
    level5: 'MATERIAL_OWNER_NAME',
    leafDepth: 1,
    report2ShowType: 'VIEW_BY_VALUE',
    report2ViewType: 'PLANT_TYPE',
    report1Tooltips: [],
    selectedTreePath: '',
    report2DateRange: [] as any,
    report2SelectedType: 'VIEW_BY_DAY',
    report2SeriesType: 'line',
    report2SelectedValue: '',
    report3SelectedColumns: ['BU'],
    report3SelectedDate: '',
    report3SelectedValues: [],
    report3ValueType: 'Value',
    report3DateRange: [] as any,
    report3ViewType: 'VIEW_BY_DAY',
    report3ColumnNames: []
  },
  filterOpts: [],
  report1Data: [],
  report1TooltipsOpts: ['BAD_INDENT_QTY', 'BAD_INDENT_VALUE', 'OPEN_SO_QTY', 'STOCK_PRICE'],
  selectedCurrentLevel: '',
  selectedParentLevel: '',
  report2Data: [] as any,
  report3ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3Columns: []
})

onMounted(() => {
  initFilter()
  report2ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report2SelectedValue = obj.name

    $viewDetails({
      url: '/inventory/excess_so_stock/query_report2_details',
      durl: '/inventory/excess_so_stock/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + [obj.name].join(', ') + ']'
    })
  })
  report2ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report2SeriesType = obj.currentType
    }
  })
})

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
}
const initFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 7 * 4)
  pageCtl.conditions.report2DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/excess_so_stock/query_filters'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/inventory/excess_so_stock/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/inventory/excess_so_stock/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/inventory/excess_so_stock/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.clearAndSearch()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }
  const selected = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    let v = r[_report3SelectedColumns.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report3SelectedValues = selected
}
const parseReport3Columns = () => {
  const result = [] as any
  for (let i = 0; i < _report3SelectedColumns.value.length; i++) {
    result.push({
      data: _report3SelectedColumns.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (pageCtl.conditions.report3ValueType === 'Value') {
          td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
        } else {
          td.innerHTML = value ? (value * 100).toFixed(2) + '%' : '0'
        }
      }
    })
  }
  return result
}
const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
}
const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
}

const _report1Opt = computed(() => {
  const rootName = 'EXCESS'
  return {
    title: {
      text: 'Excess SO Stock by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width: 11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)
          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)
            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  const series = [] as any
  const legend = [] as any

  let yAxisData = pageCtl.report2Data

  // 转换数字为百分比
  if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report2Data.xAxis

    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report2Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report2Data) {
      if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report2Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report2SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }

  let subTitle = ''

  if (pageCtl.conditions.report2SelectedType === 'VIEW_BY_DAY') {
    subTitle = 'Date'
  } else if (pageCtl.conditions.report2SelectedType === 'VIEW_BY_WEEK') {
    subTitle = 'Week'
  } else if (pageCtl.conditions.report2SelectedType === 'VIEW_BY_MONTH') {
    subTitle = 'Month'
  } else if (pageCtl.conditions.report2SelectedType === 'VIEW_BY_QUARTER') {
    subTitle = 'Quarter'
  } else if (pageCtl.conditions.report2SelectedType === 'VIEW_BY_YEAR') {
    subTitle = 'Year'
  }

  return {
    title: {
      text: 'Evolution of Excess SO Stock by ' + subTitle +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report2ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report2SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report2Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const _report3SelectedColumns = computed(() => {
  if (pageCtl.conditions.report3SelectedColumns.length > 0) {
    return pageCtl.conditions.report3SelectedColumns
  } else {
    return ['BU']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>
