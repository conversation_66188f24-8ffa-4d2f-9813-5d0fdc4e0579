<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.fields" size="small" placeholder="Filter Field" filterable
                       multiple collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['PRE_STOCK_V']" :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="monthrange"
                unlink-panels="~"
                format="YYYYMM"
                value-format="YYYYMM"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                :clearable="false"/>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="PONB"/>
              <scp-table
                  ref="report1TableRef"
                  :download-specify-column="false"
                  :columns="pageCtl.columns"
                  :lazy="true"
                  :params="pageCtl.conditions"
                  :fixedColumnsLeft="7"
                  :after-select="afterReport1Select"
                  :after-change="afterReport1Change"
                  :context-menu-items="pageCtl.report1MenuItems"
                  :page-sizes="[10, 50, 100, 200, 500]"
                  url="/inventory/pre_stock/query_report1"
                  download-url="/inventory/pre_stock/download_report1"
                  :editable="false"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $message: any = inject('$message')

const searchRef = ref()
const report1TableRef = ref()

const saveReport1 = () => {
  if (report1TableRef.value.getLoading() === true) {
    return
  }
  if (JSON.stringify(pageCtl.report1Update) === '{}') {
    $message.error('No changes detected')
    return
  }
  report1TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/pre_stock/save_report1_details',
    data: {
      report1Update: pageCtl.report1Update
    }
  }).then(() => {
    $message.success('Remain stock update')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report1Update = {}
    report1TableRef.value.setLoading(false)
  })
}

const pageCtl = reactive({
  rcaTips: {},
  columns: [],
  rcaCode: [],
  userAuth: '',
  conditions: {
    $scpFilter: {
      cascader: [['STATUS', 'OPEN']]
    },
    resultType: 'Line',
    fields: ['ENTITY', 'PLANT_CODE', 'MATERIAL_OWNER_NAME', 'PROJECT_NAME', 'SPONSER_DEPARTMENT', 'SPONSER_SESA'],
    columns: [] as any,
    dateRange: [] as any
  },
  filterOpts: [],
  report1SelectedValues: [] as any,
  report1SelectedType: '',
  report1MenuItems: {
    update_remain: {
      name: 'Update remain stock or consumption date',
      callback: saveReport1
    },
    view_split0: { name: '---------' }
  },
  report1Update: {}
})

onMounted(() => {
  initPage()
  search()
})
const search = () => {
  report1TableRef.value.search()
}

const initPage = () => {
  const now = new Date()
  const start = new Date(now.getFullYear(), now.getMonth() - 6, 1)
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyyMM'),
    $dateFormatter(end, 'yyyyMM')
  ]
  $axios({
    method: 'post',
    url: '/inventory/pre_stock/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}
const afterReport1Change = (changes) => {
  if (changes) {
    const ht = report1TableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const r = ht.getSourceDataAtRow(row)
        if (prop === 'REMAIN_STOCK_QTY' || prop === 'NEW_ESTIMATED_CONSUMPTION_DATE') {
          const key = r.PURCH_ORDER_NUMBER + '#' + r.PURCH_ORDER_ITEM
          pageCtl.report1Update[key] = {
            REMAIN_STOCK_QTY: r.REMAIN_STOCK_QTY,
            NEW_ESTIMATED_CONSUMPTION_DATE: r.NEW_ESTIMATED_CONSUMPTION_DATE
          }
        }
      }
    }
    )
  }
}
const afterReport1Select = (r, rc, c) => {
  const values = [r.TYPE, c]
  for (let i = 0; i < _fields.value.length; i++) {
    values.push(r[_fields.value[i]])
  }
  pageCtl.report1SelectedValues = values
}

const _fields = computed(() => {
  let fields = pageCtl.conditions.fields
  if (fields.length === 0) {
    fields = ['ENTITY']
  }
  return fields
})

const _pivotColumns = computed(() => {
  // 首先获取基于 filterOpts 的列
  const columns = $getPivotColumnByFilter(pageCtl.filterOpts)
  // 然后过滤掉 'STATUS' 字段
  return columns.filter(column => column !== 'STATUS')
})

</script>
