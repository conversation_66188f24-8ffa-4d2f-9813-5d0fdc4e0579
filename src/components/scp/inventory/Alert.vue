<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.categories" size="small" placeholder="Categories" filterable clearable collapse-tags multiple>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.alertFilterOpts"
            />
          </el-col>
          <!--special type-->
          <el-col :span="2">
            <el-select v-model="pageCtl.conditions.specialType" size="small" style="width: 100% !important;" class="search-group-left">
              <el-option label="MATERIAL" value="MATERIAL"/>
              <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-input :placeholder="pageCtl.conditions.specialType" size="small" v-model="pageCtl.conditions.specialContent"
                      style="width: var(--scp-input-width) !important;" type="textarea" class="search-group-right"></el-input>
          </el-col>
          <!--type-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small" placeholder="Quantity/Value">
              <el-option
                  v-for="item in ['Quantity', 'Moving Average Price', 'Item']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!--selected date range-->
          <el-col :span="4">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="pageCtl.pickerOptions"
                style="width: calc(100% - 25px)">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report1">
            <div class="subscript-container" style="min-height: 500px">
              <scp-subscript id="INAS" ref="inasRef"/>
              <div class="front" style="transform-style: flat; !important;">
                <scp-table
                    :max-height="900"
                    ref="report1TableRef"
                    :lazy="true"
                    :page-sizes="[15, 30, 50, 100, 200]"
                    url="/inventory/alert/query_report1"
                    download-url="/inventory/alert/download_report1"
                    :params="pageCtl.conditions"
                    :columns="_report1Columns"
                    :context-menu-items="pageCtl.report1ContextMenuItems"
                    :show-total="true"
                    :afterSelect="afterReport1Select"
                />
              </div>
              <div class="back" id="alertConfig">
                <div class="box" style="width: calc(100% - 15px)">
                  <el-row style="margin:10px 0;border-bottom:1px dashed var(--scp-border-color-lighter)">
                    <el-col :span='2' style="font-weight: bold">SCOPE</el-col>
                    <el-col :span="10" style="text-align: left;">
                      <el-radio-group v-model="pageCtl.conditions.scope">
                        <el-radio-button value="All">All</el-radio-button>
                        <el-radio-button value="Exclude Consignment Stock">Exclude Consignment Stock</el-radio-button>
                      </el-radio-group>
                    </el-col>
                  </el-row>
                  <el-row style="font-weight: bold">
                    <el-col :span='2'>TYPE</el-col>
                    <el-col v-for="item in pageCtl.columnConfig" :key="item.name" :span="item.span" :style="{'background-color': item.color}">
                      {{ item.name }}
                    </el-col>
                  </el-row>
                  <el-row v-for="(item,index) in pageCtl.rowConfig" :key="item.key" :style="{'margin-top': index === 3 ? '20px' : '0'}">
                    <el-col :span='2'>
                      {{ item.name }}
                    </el-col>
                    <el-col :span="item2.span" v-for="item2 in pageCtl.columnConfig" :key="'sub-' + item2.name"
                            :style="{'background-color': index < 3 ? item2.color : '#fff'}">
                      <el-checkbox v-if="item2.config[index] === 'C'"
                                   v-model="pageCtl.conditions.params[item['key']][item2['name']]"></el-checkbox>
                      <el-input-number v-if="item2.config[index] === 'I'" v-model="pageCtl.conditions.params[item['key']][item2['name']]"
                                       style="width: 95%;" controls-position="right" :min="0" :max="1000" :step="0.1"
                                       placeholder="%"/>
                      <span v-if="item2.config[index] === ''">N/A</span>
                    </el-col>
                  </el-row>
                  <div class="box-footer">
                    <el-button @click="inasRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="inasRef.toggleView();searchReport1()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const searchRef = ref()
const inasRef = ref()
const report1TableRef = ref()

const viewReport1Details = () => {
  $viewDetails({
    url: '/inventory/alert/query_report1_details',
    durl: '/inventory/alert/download_report1_details',
    params: pageCtl.conditions,
    title: pageCtl.report1DetailsTitle
  })
}

const pageCtl = reactive({
  alertFilterOpts: [],
  conditions: {
    scope: 'All',
    filterList: [],
    specialType: 'MATERIAL',
    specialContent: '',
    type: 'Quantity',
    dateRange: [] as any,
    category1: '',
    categories: ['CLUSTER_NAME', 'ENTITY'],
    selectedCategories: [],
    selectedCategory3: '',
    params: {
      GOODS_RECEIVE: {
        AMF: 0,
        AMU: 100,
        UU_STOCK: true,
        STOCK_IN_QI: true,
        RESTRICTED_STOCK: false,
        BLOCKED_STOCK: false,
        GIT: false,
        OPEN_PO: false,
        OPEN_SO: true,
        OPEN_TRANSFER: true,
        MO_RESERVATION: true,
        LT_DEMAND: false,
        SAFETY_STOCK: true
      },
      LA_CREATION: {
        AMF: 0,
        AMU: 100,
        UU_STOCK: true,
        STOCK_IN_QI: true,
        RESTRICTED_STOCK: false,
        BLOCKED_STOCK: false,
        GIT: true,
        OPEN_PO: false,
        OPEN_SO: true,
        OPEN_TRANSFER: true,
        MO_RESERVATION: true,
        LT_DEMAND: true,
        SAFETY_STOCK: true
      },
      PO_CREATION: {
        AMF: 0,
        AMU: 100,
        UU_STOCK: true,
        STOCK_IN_QI: true,
        RESTRICTED_STOCK: false,
        BLOCKED_STOCK: false,
        GIT: false,
        OPEN_PO: true,
        OPEN_SO: true,
        OPEN_TRANSFER: true,
        MO_RESERVATION: true,
        LT_DEMAND: true,
        SAFETY_STOCK: true
      },
      NEW_BLOCK: {
        UU_STOCK: 0,
        STOCK_IN_QI: 0,
        RESTRICTED_STOCK: 0,
        BLOCKED_STOCK: 0,
        AMU: 30,
        AMF: 0
      },
      NEW_STOCK_GAIN: {
        UU_STOCK: 0,
        STOCK_IN_QI: 0,
        RESTRICTED_STOCK: 0,
        BLOCKED_STOCK: 0,
        AMU: 30,
        AMF: 0
      },
      NEW_GOODS_RETURN: {
        UU_STOCK: 0,
        STOCK_IN_QI: 0,
        RESTRICTED_STOCK: 0,
        BLOCKED_STOCK: 0,
        AMU: 30,
        AMF: 0
      },
      CONS_STOCK_RELEASE: {
        UU_STOCK: 0,
        STOCK_IN_QI: 0,
        RESTRICTED_STOCK: 0,
        BLOCKED_STOCK: 0,
        AMU: 30,
        AMF: 0
      }
    }
  },
  loading: {
    filter: false,
    report1: false
  },
  rowConfig: [
    { name: 'Goods Receive', key: 'GOODS_RECEIVE' },
    { name: 'LA Creation', key: 'LA_CREATION' },
    { name: 'PO Creation', key: 'PO_CREATION' },
    { name: 'New Block', key: 'NEW_BLOCK' },
    { name: 'New Stock Gain', key: 'NEW_STOCK_GAIN' },
    { name: 'New Goods Return', key: 'NEW_GOODS_RETURN' },
    { name: 'Cons Stock Release', key: 'CONS_STOCK_RELEASE' }
  ],
  columnConfig: [
    { span: 2, color: '#f2fff2', config: ['C', 'C', 'C', 'I', 'I', 'I', 'I'], name: 'UU_STOCK' },
    { span: 2, color: '#f2fff2', config: ['C', 'C', 'C', 'I', 'I', 'I', 'I'], name: 'STOCK_IN_QI' },
    { span: 2, color: '#f2fff2', config: ['C', 'C', 'C', 'I', 'I', 'I', 'I'], name: 'RESTRICTED_STOCK' },
    { span: 2, color: '#f2fff2', config: ['C', 'C', 'C', 'I', 'I', 'I', 'I'], name: 'BLOCKED_STOCK' },
    { span: 1, color: '#f2fff2', config: ['C', 'C', 'C', '', '', '', ''], name: 'GIT' },
    { span: 1, color: '#f2fff2', config: ['C', 'C', 'C', '', '', '', ''], name: 'OPEN_PO' },
    { span: 1, color: '#fff2f2', config: ['C', 'C', 'C', '', '', '', ''], name: 'OPEN_SO' },
    { span: 2, color: '#fff2f2', config: ['C', 'C', 'C', '', '', '', ''], name: 'OPEN_TRANSFER' },
    { span: 2, color: '#fff2f2', config: ['C', 'C', 'C', '', '', '', ''], name: 'MO_RESERVATION' },
    { span: 1, color: '#fff2f2', config: ['C', 'C', 'C', '', '', '', ''], name: 'LT_DEMAND' },
    { span: 2, color: '#fff2f2', config: ['C', 'C', 'C', '', '', '', ''], name: 'SAFETY_STOCK' },
    { span: 2, color: '#fff2f2', config: ['I', 'I', 'I', 'I', 'I', 'I', 'I'], name: 'AMU' },
    { span: 2, color: '#fff2f2', config: ['I', 'I', 'I', 'I', 'I', 'I', 'I'], name: 'AMF' }
  ],
  pickerOptions: {
    disabledDate: (e) => {
      const time = new Date().getTime() - e.getTime()
      return time < 86400 || time > 1296000000
    }
  },
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport1Details
    },
    view_split0: { name: '---------' }
  },
  report1DetailsTitle: ''
})

const _categories = computed(() => {
  if (pageCtl.conditions.categories.length > 0) {
    return pageCtl.conditions.categories
  } else {
    return ['CLUSTER_NAME', 'ENTITY']
  }
})
const _report1Columns = computed(() => {
  const columns = [] as any
  for (let i = 0; i < _categories.value.length; i++) {
    const name = _categories.value[i]
    if (i === 0) {
      columns.push({
        data: name,
        render: renderTotal
      })
    } else {
      columns.push({ data: name })
    }
  }

  columns.push({
    data: 'GOODS_RECEIVE',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'LA_CREATION',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'PO_CREATION',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'NEW_BLOCK',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'NEW_STOCK_GAIN',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'NEW_GOODS_RETURN',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'CONS_STOCK_RELEASE',
    render: renderNumber,
    type: 'numeric'
  },
  {
    data: 'SUB_TOTAL',
    render: renderNumber,
    type: 'numeric'
  })
  return columns
})
const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.alertFilterOpts, 'MATERIAL')
})

onMounted(() => {
  initPage()
})

const initPage = () => {
  const today = new Date()
  const start = $dateFormatter(new Date(today.getTime() - 604800000), 'yyyy/MM/dd')
  const end = $dateFormatter(new Date(today.getTime() - 86400000), 'yyyy/MM/dd')
  pageCtl.conditions.dateRange = [start, end]

  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/alert/init_page'
  }).then((body) => {
    pageCtl.alertFilterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  searchReport1()
}
const searchReport1 = () => {
  report1TableRef.value.search()
}

const renderNumber = (hotInstance, td, row, column, prop, value) => {
  td.style.textAlign = 'right'
  const r = hotInstance.getSourceDataAtRow(row)
  if (r[pageCtl.conditions.category1] === 'Total') {
    td.style.fontWeight = 'bold'
  }
  if (value) {
    td.innerHTML = $thousandBitSeparator(value, 0)
  } else {
    td.innerHTML = value
  }
}
const renderTotal = (hotInstance, td, row, column, prop, value) => {
  td.innerHTML = value
  if (value === 'Total') {
    td.style.fontWeight = 'bold'
  }
}
const afterReport1Select = (r, c, a, column) => {
  const selected = [] as any
  for (let i = 0; i < _categories.value.length; i++) {
    let v = r[_categories.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.selectedCategories = selected

  if (column >= _categories.value.length) {
    pageCtl.conditions.selectedCategory3 = a
  } else {
    pageCtl.conditions.selectedCategory3 = ''
  }

  pageCtl.report1DetailsTitle = 'View Details [' + ($join(...selected, pageCtl.conditions.selectedCategory3) || 'Total') + ']'
}
</script>

<style scoped lang="scss">
#alertConfig {
  font-size: 80% !important;

  .el-col {
    text-align: center;
    height: 1.5rem;
  }
}
</style>
<style lang="scss">
#alertConfig {
  .el-input__inner {
    font-size: 80% !important;
    text-align: left !important;
  }
}
</style>
