<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.inventoryFilterOpts"/>
          </el-col>
          <el-col :span="2">
            <el-select v-model="pageCtl.conditions.specialType" size="small" style="width: 100% !important;" class="search-group-left">
              <el-option label="MATERIAL" value="MATERIAL"/>
              <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
              <el-option label="STORAGE_LOCATION" value="STORAGE_LOCATION"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-input :placeholder="pageCtl.conditions.specialType" size="small" v-model="pageCtl.conditions.specialContent"
                      style="width: var(--scp-input-width) !important;" type="textarea" class="search-group-right"></el-input>
          </el-col>
          <!--type-->
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.stock" size="small" placeholder="Stock" multiple :collapse-tags="true">
              <el-option
                  v-for="item in ['BLOCKED_STOCK', 'INTER_STK_TRANSFER', 'RESTRICTED_STOCK', 'RETURNS_STOCK', 'STOCK_IN_QI', 'UU_STOCK']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option
                  v-for="item in ['Net Net Price','Moving Average Price','Quantity']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['projectionVersion','report1Date','projectionResultVersion']"/>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.filter">
              <scp-subscript id="INVS"/>
              <div class="subscript-title">SO & MO List and Priority</div>
              <el-row class="search-box">
                <!-- material -->
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.categroy" size="small" placeholder="Categroy" filterable clearable multiple
                             collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-date-picker
                      style="width: var(--scp-input-width)"
                      v-model="pageCtl.conditions.report1Date"
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      type="date"
                      size="small"
                      placeholder="Date">
                  </el-date-picker>
                </el-col>
              </el-row>
              <scp-table
                  ref="report1TableRef"
                  :columns="_report1Columns"
                  :lazy="true"
                  :max-height="300"
                  :show-total="true"
                  :params="pageCtl.conditions"
                  :after-select="afterReport1Select"
                  url="/inventory/summary_projection/query_report1"
                  download-url="/inventory/summary_projection/download_report1"
                  :context-menu-items="pageCtl.report1ContextItems"/>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.filter">
              <scp-subscript id="INVP"/>
              <div class="subscript-title">Inventory Projection Setting</div>
              <el-row class="search-box">
                <!-- version -->
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.projectionVersion" size="small" placeholder="Version" @change="searchReport2">
                    <el-option
                        v-for="item in pageCtl.projectionVersionOpts"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.projectionType" size="small" @change="searchReport2">
                    <el-option
                        v-for="item in ['My Projection','All Projection']"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <scp-cascader
                      v-model="pageCtl.conditions.projectionFilterList"
                      :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                      :options="pageCtl.projectionFilterOpts"/>
                </el-col>
                <el-col :span="4">
                  <el-input placeholder="MATERIAL" size="small" v-model="pageCtl.conditions.projectionMaterial"
                            style="width: var(--scp-input-width) !important;" class="search-group-right" type="textarea"></el-input>
                </el-col>
                <el-col :span="3">
                  <el-button size="small" @click="searchReport2">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
                <el-col :span="5" style="text-align: right">
                  <el-button type="primary" size="small" @click="splitInventoryProjection" :loading="pageCtl.loading.split"
                             v-show="pageCtl.isAdmin">Split
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="report2TableRef"
                  :columns="_report2Columns"
                  :lazy="true"
                  :after-save="initPersonalSettings"
                  url="/inventory/summary_projection/query_report2"
                  save-url="/inventory/summary_projection/save_report2"
                  download-url="/inventory/summary_projection/download_report2"
                  :primary-key-id="['ROW_ID']"
                  :hidden-columns="{columns: [0]}"
                  :params="pageCtl.conditions"
                  :after-select="afterReport2Select"
                  :context-menu-items="pageCtl.report2ContextItems"/>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.filter">
              <scp-subscript id="INVR"/>
              <div class="subscript-title">Inventory Projection Result</div>
              <el-row class="search-box">
                <!-- version -->
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.projectionResultVersion" size="small" placeholder="Version" @change="searchReport3">
                    <el-option
                        v-for="item in pageCtl.projectionVersionOpts"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.projectionResultCategroy" size="small" placeholder="Categroy" filterable clearable
                             multiple collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <scp-cascader
                      v-model="pageCtl.conditions.projectionResultFilterList"
                      :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                      :options="pageCtl.inventoryFilterOpts"/>
                </el-col>
                <el-col :span="2">
                  <el-select v-model="pageCtl.conditions.projectionResultSpecialType" size="small" style="width: 100% !important;"
                             class="search-group-left">
                    <el-option label="MATERIAL" value="MATERIAL"/>
                    <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
                    <el-option label="STORAGE_LOCATION" value="STORAGE_LOCATION"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-input :placeholder="pageCtl.conditions.projectionResultSpecialType" size="small"
                            v-model="pageCtl.conditions.projectionResultSpecialContent" style="width: var(--scp-input-width) !important;"
                            type="textarea" class="search-group-right"></el-input>
                </el-col>
                <el-col :span="3">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="report3TableRef"
                  :columns="_report3Columns"
                  :lazy="true"
                  :show-total="true"
                  :after-select="afterReport3Select"
                  :context-menu-items="pageCtl.report3ContextItems"
                  url="/inventory/summary_projection/query_report3"
                  download-url="/inventory/summary_projection/download_report3"
                  :params="pageCtl.conditions"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- report3 chart-->
    <scp-draggable-resizable h="450px" w="60vw" v-model="pageCtl.visible.report3Details" title="View Projection Trends">
      <template v-slot="{ height }">
        <chart :style="{ height : height - 120 + 'px', width : pageCtl.pageWidth }" :option="_report3DetailsOpt"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $viewDetails: any = inject('$viewDetails')
const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $getNext25Months: any = inject('$getNext25Months')
const $renderTdTitle: any = inject('$renderTdTitle')
const $convertDateStr: any = inject('$convertDateStr')
const $addMonth: any = inject('$addMonth')
const $message: any = inject('$message')
const $setDefaultMaterialOwner: any = inject('$setDefaultMaterialOwner')
const $shortenNumber: any = inject('$shortenNumber')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const viewReport1Details = () => {
  $viewDetails({
    url: '/inventory/summary_projection/query_report1_details',
    durl: '/inventory/summary_projection/download_report1_details',
    params: pageCtl.conditions,
    title: pageCtl.report1DetailsTitle
  })
}
const viewReport2Details = () => {
  pageCtl.report2DetailsTitle = pageCtl.report2DetailsTitleTemp
  $viewDetails({
    url: '/inventory/summary_projection/query_report2_details',
    durl: '/inventory/summary_projection/download_report2_details',
    params: pageCtl.conditions,
    title: pageCtl.report2DetailsTitle
  })
}

const viewReport3Details = () => {
  pageCtl.visible.report3Details = true
  parseReport3Data(pageCtl.report3SelectRow)
}

const pageCtl = reactive({
  pageWidth: document.body.clientWidth * 0.6 - 20 + 'px',
  inventoryFilterOpts: [],
  projectionFilterOpts: [],
  conditions: {
    categroy: ['ENTITY', 'PLANT_CODE', 'PLANT_TYPE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'PRODUCT_LINE_INV', 'PRODUCT_FAMILY_INV', 'SOURCE_CATEGORY', 'VENDOR_NAME'],
    filterList: [['SPECIAL_ST', 'W'], ['SPECIAL_ST', '#'], ['SPECIAL_ST', 'E'], ['SPECIAL_ST', 'O'], ['SPECIAL_ST', 'P'], ['SPECIAL_ST', 'Q'], ['SPECIAL_ST', 'V'],
      ['SPECIAL_ST', 'M'], ['SPECIAL_ST', 'Y']],
    specialType: 'MATERIAL',
    specialContent: '',
    stock: ['UU_STOCK', 'STOCK_IN_QI', 'RESTRICTED_STOCK', 'BLOCKED_STOCK', 'RETURNS_STOCK'],
    resultType: 'Moving Average Price',
    projectionVersion: '',
    projectionFilterList: [],
    projectionMaterial: '',
    report1Date: '',
    report1SelectedValues: [] as any,
    report2SelectedID: '',
    projectionType: '',

    projectionResultVersion: '',
    projectionResultFilterList: [],
    projectionResultCategroy: ['ENTITY', 'PLANT_CODE', 'PLANT_TYPE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'PRODUCT_LINE_INV', 'PRODUCT_FAMILY_INV', 'SOURCE_CATEGORY', 'VENDOR_NAME'],
    projectionResultSpecialType: 'MATERIAL',
    projectionResultSpecialContent: ''
  },
  loading: {
    filter: false,
    split: false,
    report3: false
  },
  visible: {
    report3Details: false
  },
  entityOpts: [],
  plantCodeOpts: [],
  plantTypeOpts: [],
  productLineInvOpts: [],
  productFamilyInvOpts: [],
  sourceCategoryOpts: [],
  vendorNameOpts: [],
  rcaOpts: [],
  projectionVersionOpts: [],
  report1DetailsTitle: '',
  report2DetailsTitle: '',
  report2DetailsTitleTemp: '', // 存放临时标题, 在窗口弹出后, 再将report2DetailsTitleTemp赋值给report2DetailsTitle, 触发渲染. 如果直接触发渲染, 第一次右键菜单会延迟甚至不出现
  report1ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport1Details
    },
    view_split0: { name: '---------' }
  },
  report2ContextItems: {
    view_details: {
      name: 'View details',
      disabled () {
        return pageCtl.conditions.report2SelectedID === ''
      },
      callback: viewReport2Details
    },
    view_split0: { name: '---------' }
  },
  report3ContextItems: {
    view_details: {
      name: 'View trend',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  rcaMap: {},
  isAdmin: false,
  report3SelectRow: {},
  report3DetailsData: {
    xAxis: [],
    yAxis: []
  }
})

const searchRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()
const report3TableRef = ref()

onMounted(() => {
  initPage()
})

watch(() => pageCtl.conditions.projectionVersion, (newVal, oldVal) => {
  if (oldVal) {
    searchReport2()
  }
})

const initPage = () => {
  pageCtl.conditions.report1Date = $dateFormatter(new Date(), 'yyyy/MM/') + '01'
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/summary_projection/init_page'
  }).then((body) => {
    pageCtl.inventoryFilterOpts = body.cascader
    pageCtl.projectionFilterOpts = body.projectionCascader
    pageCtl.rcaOpts = body.rca
    pageCtl.rcaMap = body.rcaMap || {}

    // default material owner
    $setDefaultMaterialOwner(pageCtl.inventoryFilterOpts, pageCtl.conditions.filterList)
    $setDefaultMaterialOwner(pageCtl.inventoryFilterOpts, pageCtl.conditions.projectionResultFilterList)

    initPersonalSettings()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const initPersonalSettings = () => {
  $axios({
    method: 'post',
    url: '/inventory/summary_projection/init_personal_settings'
  }).then((body) => {
    pageCtl.entityOpts = body.entity
    pageCtl.plantCodeOpts = body.plantCode
    pageCtl.plantTypeOpts = body.plantType
    pageCtl.projectionVersionOpts = body.projectionVersionOpts
    pageCtl.productLineInvOpts = body.productLineInv
    pageCtl.productFamilyInvOpts = body.productFamilyInv
    pageCtl.sourceCategoryOpts = body.sourceCategory
    pageCtl.vendorNameOpts = body.vendorName
    pageCtl.isAdmin = body.isAdmin || false
    if (body.isMaterialOwner) {
      pageCtl.conditions.projectionType = 'My Projection'
    } else {
      pageCtl.conditions.projectionType = 'All Projection'
    }

    if (pageCtl.projectionVersionOpts.length > 0) {
      pageCtl.conditions.projectionVersion = pageCtl.projectionVersionOpts[0]
      pageCtl.conditions.projectionResultVersion = pageCtl.projectionVersionOpts[0]
    }
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  })
}
const search = () => {
  searchReport1()
  searchReport2()
  searchReport3()
}
const searchReport1 = () => {
  report1TableRef.value.search()
}
const searchReport2 = () => {
  report2TableRef.value.search()
}

const searchReport3 = () => {
  report3TableRef.value.search()
}
const splitInventoryProjection = () => {
  pageCtl.loading.split = true
  $axios({
    method: 'post',
    url: '/inventory/summary_projection/split_inventory_projection'
  }).then(() => {
    $message.success('Splited')
    searchReport2()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.split = false
  })
}
const afterReport1Select = (r) => {
  pageCtl.conditions.report1SelectedValues = [] as any
  for (let i = 0; i < _categroy.value.length; i++) {
    pageCtl.conditions.report1SelectedValues.push(r[_categroy.value[i]])
  }
  pageCtl.report1DetailsTitle = 'View Details [' + $join(...pageCtl.conditions.report1SelectedValues) + ']'
}
const afterReport2Select = (r) => {
  pageCtl.conditions.report2SelectedID = r.SPLITED === 'Y' ? r.ROW_ID : ''
  pageCtl.report2DetailsTitleTemp = 'View Details [' + $join(r.ENTITY, r.PLANT_CODE, r.PLANT_TYPE, r.PRODUCT_LINE_INV, r.PRODUCT_FAMILY_INV, r.SOURCE_CATEGORY, r.VENDOR_NAME) + ']'
}

const afterReport3Select = (r) => {
  pageCtl.report3SelectRow = r
}
const parseReport3Data = (data) => {
  const xAxis = [] as any
  const yAxis = [] as any
  let selectedVersion = pageCtl.conditions.projectionResultVersion
  let startVersion = (parseInt(pageCtl.conditions.projectionResultVersion.substring(0, 4)) - 1) + '01'
  for (let i = 1; i <= 24; i++) {
    const key = 'ACTUAL' + (i < 10 ? '0' + i : '' + i)
    if (!data.hasOwnProperty(key)) {
      break
    }
    xAxis.push($convertDateStr(startVersion))
    yAxis.push(data[key])
    startVersion = $addMonth(startVersion, 1)
  }

  for (let i = 1; i <= 13; i++) {
    const key = 'PROJECTION' + (i < 10 ? '0' + i : '' + i)
    selectedVersion = $addMonth(selectedVersion, 1)
    xAxis.push($convertDateStr(startVersion))
    yAxis.push(data[key])
  }
  pageCtl.report3DetailsData = {
    xAxis,
    yAxis
  }
}

const _categroy = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return ['ENTITY', 'PLANT_CODE', 'PLANT_TYPE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'PRODUCT_LINE_INV', 'PRODUCT_FAMILY_INV', 'SOURCE_CATEGORY', 'VENDOR_NAME']
  }
})
const _projectionResultCategroy = computed(() => {
  if (pageCtl.conditions.projectionResultCategroy.length > 0) {
    return pageCtl.conditions.projectionResultCategroy
  } else {
    return ['ENTITY', 'PLANT_CODE', 'PLANT_TYPE', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA', 'PRODUCT_LINE_INV', 'PRODUCT_FAMILY_INV', 'SOURCE_CATEGORY', 'VENDOR_NAME']
  }
})
const _report1Columns = computed(() => {
  const columns = [] as any
  for (let i = 0; i < _categroy.value.length; i++) {
    columns.push({
      data: _categroy.value[i]
    })
  }
  columns.push({
    data: 'NO_OF_MATERIAL',
    type: 'numeric'
  }, {
    data: 'SOH',
    type: 'numeric'
  }, {
    data: 'GIT',
    type: 'numeric'
  }, {
    data: 'OPEN_PO',
    type: 'numeric'
  }, {
    data: 'OPEN_SO',
    type: 'numeric'
  }, {
    data: 'MO_RESERVATION',
    type: 'numeric'
  }, {
    data: 'AMU_ONE_MM',
    type: 'numeric'
  }, {
    data: 'AMF_ONE_MM',
    type: 'numeric'
  }, {
    data: 'UD_LT_7_CD',
    type: 'numeric'
  }, {
    data: 'UD_LT_30_CD',
    type: 'numeric'
  }, {
    data: 'UD_GT_30_CD',
    type: 'numeric'
  }, {
    data: 'SAFETY_STOCK',
    type: 'numeric'
  }, {
    data: 'SS2',
    type: 'numeric'
  }, {
    data: 'SS3',
    type: 'numeric'
  }, {
    data: 'REORDER_POINT',
    type: 'numeric'
  }, {
    data: 'EXCESS_ONE_MM',
    type: 'numeric'
  }, {
    data: 'MISSING_ONE_MM',
    type: 'numeric'
  }, {
    data: 'THEO_PROV_ONE_MM',
    type: 'numeric'
  }, {
    data: 'OPEN_AB',
    type: 'numeric'
  }, {
    data: 'OPEN_LA',
    type: 'numeric'
  }, {
    data: 'OPEN_NON_AB_LA',
    type: 'numeric'
  }, {
    data: 'GR_LAST_WD',
    type: 'numeric'
  }, {
    data: 'LA_LAST_WD',
    type: 'numeric'
  }, {
    data: 'PO_CREATION_LAST_WD',
    type: 'numeric'
  }, {
    data: 'MINIMUM_LOT_SIZE',
    type: 'numeric'
  }, {
    data: 'UNIT_COST',
    type: 'numeric'
  })

  return columns
})
const _report2Columns = computed(() => {
  const columns: any = [{
    data: 'ROW_ID'
  }, {
    data: 'ENTITY',
    editor: 'select',
    selectOptions: pageCtl.entityOpts
  }, {
    data: 'PLANT_CODE',
    editor: 'select',
    selectOptions: pageCtl.plantCodeOpts
  }, {
    data: 'PLANT_TYPE',
    editor: 'select',
    selectOptions: pageCtl.plantTypeOpts
  }, {
    data: 'PRODUCT_LINE_INV',
    editor: 'select',
    selectOptions: pageCtl.productLineInvOpts
  }, {
    data: 'PRODUCT_FAMILY_INV',
    editor: 'select',
    selectOptions: pageCtl.productFamilyInvOpts
  }, {
    data: 'SOURCE_CATEGORY',
    editor: 'select',
    selectOptions: pageCtl.sourceCategoryOpts
  }, {
    data: 'VENDOR_NAME',
    editor: 'select',
    selectOptions: pageCtl.vendorNameOpts
  }, {
    data: 'MATERIAL'
  }, {
    data: 'RCA_CATEGORY',
    title: 'Contributing Category',
    editor: 'select',
    selectOptions: ['Positive', 'Negative']
  }, {
    data: 'RCA_CODE',
    title: 'Contributing Factors',
    editor: 'select',
    selectOptions: pageCtl.rcaOpts,
    render: (hotInstance, td, row, column, prop, value) => $renderTdTitle(hotInstance, td, row, column, prop, value, pageCtl.rcaOpts, pageCtl.rcaMap)
  }]

  const months = $getNext25Months($dateFormatter(new Date(), 'yyyyMM'))
  for (let i = 1; i <= 13; i++) {
    const key = 'MONTH' + (i < 10 ? '0' + i : i)
    columns.push({
      data: key,
      title: months[i - 1],
      type: 'strict-positive-numeric'
    })
  }

  columns.push({
    data: 'CREATE_BY'
  }, {
    data: 'STATUS'
  }, {
    data: 'SPLITED'
  }, {
    data: 'SPLITED_TIME'
  })
  return columns
})
const _report3Columns = computed(() => {
  const columns = [] as any
  for (let i = 0; i < _projectionResultCategroy.value.length; i++) {
    columns.push({
      data: _projectionResultCategroy.value[i]
    })
  }
  columns.push({
    data: 'NO_OF_MATERIAL',
    title: 'No of Material',
    type: 'numeric'
  })

  let selectedVersion = pageCtl.conditions.projectionResultVersion
  let startVersion = (parseInt(pageCtl.conditions.projectionResultVersion.substring(0, 4)) - 1) + '01'
  let maxLoop = 24
  let i = 1
  while (parseInt(selectedVersion) >= parseInt(startVersion) && maxLoop > 0) {
    columns.push({
      data: 'ACTUAL' + (i < 10 ? '0' + i : '' + i),
      title: $convertDateStr(startVersion),
      type: 'numeric'
    })
    startVersion = $addMonth(startVersion, 1)
    maxLoop--
    i++
  }

  for (let j = 1; j <= 13; j++) {
    selectedVersion = $addMonth(selectedVersion, 1)
    columns.push({
      data: 'PROJECTION' + (j < 10 ? '0' + j : '' + j),
      title: $convertDateStr(selectedVersion),
      type: 'numeric'
    })
  }
  return columns
})
const _report3DetailsOpt = computed(() => {
  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:11.5rem;">')
          const marker = params[i].marker

          tip.push(marker)
          tip.push(params[i].seriesName.replace(/1/g, ''))
          tip.push('<span style="float: right">')
          if (typeof value === 'object') {
            const tv = value[2] - value[1]
            tip.push((tv > 0 ? '+' : '') + $shortenNumber(tv))
          } else {
            tip.push($shortenNumber(value))
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: ['Value'] }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: pageCtl.report3DetailsData.xAxis
    },
    yAxis: [{
      type: 'value',
      scale: true,
      axisLabel: {
        margin: 10,
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }],
    visualMap: {
      show: false,
      dimension: 0,
      seriesIndex: 0,
      pieces: [{
        gt: -1,
        lte: pageCtl.report3DetailsData.yAxis.length - 14,
        color: '#5470c6'
      }],
      outOfRange: {
        color: '#3dcd58'
      }
    },
    series: [{
      name: 'Value',
      type: 'line',
      smooth: false,
      symbol: 'none',
      data: pageCtl.report3DetailsData.yAxis,
      markLine: {
        symbol: ['none', 'none'],
        lineStyle: { type: 'dotted', color: '#3dcd58', width: 2 },
        label: { show: false },
        data: [
          { xAxis: $convertDateStr(pageCtl.conditions.projectionResultVersion) }
        ]
      }
    }]
  }
})
const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.inventoryFilterOpts)
})

</script>
