<template>
  <div class="left-sidebar" id="UHSStructure">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['UHS_RCA_HIST']"
                        :after-apply="search"/>
          </el-col>
          <!--Week-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.week" placeholder="Week" :loading="pageCtl.loading">
              <el-option
                  v-for="item in pageCtl.weekOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.currency" placeholder="Unit">
              <el-option
                  v-for="item in ['CNY', 'EUR']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- search -->
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['week']"/>
          </el-col>
        </el-row>

        <el-row class="uhs-widget" style="margin-bottom: var(--scp-widget-margin)">
          <el-col :span="3" v-for="(item, index) in report1Ctl.widgets" :key="item">
            <el-tooltip :content="report1Ctl.data[item.key] ? $thousandBitSeparator(report1Ctl.data[item.key], 1) : '--'" effect="light"
                        placement="bottom" :show-after="1000">
              <el-card shadow="hover" v-loading="pageCtl.loading || report1Ctl.loading" @dblclick="searchReport1Details" class="hand"
                       :style="{marginRight: index === report1Ctl.widgets.length - 1 ? '0 !important' : 'auto'}">
                <h4>{{ report1Ctl.loading ? '--' : $shortenNumber(report1Ctl.data[item.key]) }}<span
                    v-if="!!report1Ctl.data[item.key] && item.label.indexOf('%') !== -1">%</span>
                </h4>
                <h6>{{ item.label }}</h6>
              </el-card>
            </el-tooltip>
          </el-col>
        </el-row>

        <el-row style="margin-top: 10px">
          <el-col :span="10">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading || report5Ctl.loading" style="height: 400px">
              <scp-subscript id="IUSC" ref="report5SubscriptRef"/>
              <div class="front">
                <chart v-contextmenu:report5ContextmenuRef :height="400" :option="_report5Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." clearable filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." clearable filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display Value</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.report5Value" size="small" multiple filterable collapse-tags placeholder="Select...">
                        <el-option
                            v-for="item in pageCtl.report5TooltipsOpts"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report5Tooltips" style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report5TooltipsOpts"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></el-checkbox>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button @click="report5SubscriptRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="report5SubscriptRef.toggleView();searchReport5()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="14">
            <div class="subscript-container subscript-container-right" style="height: 400px">
              <scp-subscript id="UHSS" ref="uhssRef"/>
              <div class="front">
                <chart ref="report4ChartRef" :height="400" :option="_report4Opt"
                       v-loading="pageCtl.loading || report4Ctl.loading"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Report Settings</div>
                  <el-row>
                    <el-col :span="6">Date Range</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          size="small"
                          style="width: calc(100% - 35px)"
                          v-model="pageCtl.conditions.dateRange"
                          type="monthrange"
                          unlink-panels
                          range-separator="~"
                          format="YYYYMM"
                          value-format="YYYYMM"
                          start-placeholder="Start date"
                          end-placeholder="End date"
                          :picker-options="{
                        disabledDate(e) {
                          let time = e.getTime() - new Date().getTime()
                          return time > 63244800000 // 最多只能往前选2年，3600 * 24 * 1000 * (366 * 2) = 63244800000
                        }
                      }"
                          :clearable="false">
                      </el-date-picker>
                    </el-col>
                  </el-row>

                  <div class="box-footer">
                    <el-button @click="uhssRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="uhssRef.toggleView();searchReport4()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading">
              <scp-subscript id="IUST"/>
              <el-row style="margin-bottom: var(--scp-widget-margin)">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report2SelectedColumns" filterable multiple style="width: var(--scp-input-width)"
                             collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport2">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="report2TableRef"
                  :lazy="true"
                  url="/inventory/uhs_structure/query_report2"
                  :params="pageCtl.conditions"
                  :columns="report2Ctl.columns"
                  :after-select="afterReport2Select"
                  :context-menu-items="{
                                        view_details: {
                                          name: 'View details',
                                          callback: searchReport2Details
                                        },
                                        view_split0: { name: '---------' }
                                    }"
                  :last-row-bold="true"
                  :page-sizes="[20, 50, 100, 200, 500]"/>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="IUSB"/>
              <el-row style="margin-bottom: var(--scp-widget-margin)">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report3SplitBy" filterable style="width: var(--scp-input-width)">
                    <template #prefix>
                      Split By
                    </template>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report3yAxis" filterable multiple collapse-tags style="width: var(--scp-input-width)"
                             placeholder="yAxis">
                    <el-option
                        v-for="item in report3Ctl.yAxisOpts"
                        :key="item.key"
                        :label="item.label"
                        :value="item.key">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-date-picker
                      size="small"
                      style="width: calc(100% - 35px)"
                      v-model="pageCtl.conditions.dateRange"
                      type="monthrange"
                      unlink-panels
                      range-separator="~"
                      format="YYYYMM"
                      value-format="YYYYMM"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{
                                              disabledDate(e) {
                                                let time = e.getTime() - new Date().getTime()
                                                return time > 63244800000 // 最多只能往前选2年，3600 * 24 * 1000 * (366 * 2) = 63244800000
                                              }
                                            }"
                      :clearable="false">
                  </el-date-picker>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report3ChartRef" :height="400" :option="_report3Opt"
                     v-loading="pageCtl.loading || report3Ctl.loading"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="report5ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="report5Ctl.selectedParentLevel !== ''">
        View {{ report5Ctl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ report5Ctl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onBeforeMount, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import { Column } from '@/components/starter/components/Table.vue'

const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $px2Rem: any = inject('$px2Rem')
const $shortenNumber: any = inject('$shortenNumber')
const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report2TableRef = ref()
const report3ChartRef = ref()
const report4ChartRef = ref()
const report5ContextmenuRef = ref()
const report5SubscriptRef = ref()
const uhssRef = ref()

const pageCtl = reactive({
  loading: false,
  filterOpts: [],
  weekOpts: [],
  report5TooltipsOpts: ['THEO_PROV', 'THEO_PROV_SPECIAL_EVENT', 'EXCESS_STOCK', 'EXCESS_SPECIAL_EVENT', 'MISSING_STOCK', 'HEALTH_STOCK'],
  conditions: {
    dateRange: [] as Array<string>,
    report2SelectedColumns: ['BU'],
    report3yAxis: ['GROSS_UNHEALTHY_VALUE'],
    report3SplitBy: 'PLANT_CODE',
    currency: 'CNY',
    week: '',
    $scpFilter: {
      cascader: [],
      filter: []
    },
    detailIndex: '',
    report2SelectedValue: [],
    report3SelectedWeek: '',
    report4SelectedWeek: '',

    leafDepth: 1,
    level1: 'ENTITY',
    level2: 'PRODUCT_LINE',
    level3: 'MRP_CONTROLLER',
    level4: 'CLUSTER_NAME',
    level5: 'VENDOR_NAME',
    report5Tooltips: [],
    selectedTreePath: '',
    report5Value: ['THEO_PROV', 'EXCESS_STOCK', 'MISSING_STOCK']
  }
})

const report1Ctl = reactive({
  loading: false,
  data: {},
  widgets: [{
    key: 'GROSS_UNHEALTHY_VALUE',
    label: 'GROSS UHS'
  }, {
    key: 'GROSS_UNHEALTHY_PERCENT',
    label: 'GROSS UHS(%)'
  }, {
    key: 'NET_UNHEALTHY_PERCENT',
    label: 'NET UHS(%)'
  }, {
    key: 'SPECIAL_PERCENT',
    label: 'SPECIAL EVENTS(%)'
  }, {
    key: 'HS_EXCESS_VALUE',
    label: 'EXCESS'
  }, {
    key: 'HS_THEO_PROV_VALUE',
    label: 'THEO.PROV.'
  }, {
    key: 'PROPOSAL_FIN_PROV_VALUE',
    label: 'PFP VALUE'
  }, {
    key: 'FIN_VS_PFP',
    label: 'FIN VS PFP(%)'
  }]
})

const report2Ctl = reactive({
  columns: [] as Array<Column>
})

const report3Ctl = reactive({
  loading: false,
  seriesType: 'line',
  yAxisOpts: [
    { key: 'GROSS_UNHEALTHY_VALUE', label: 'GROSS_UNHEALTHY' },
    { key: 'NET_UNHEALTHY_VALUE', label: 'NET_UNHEALTHY' },
    { key: 'HS_MISSING_VALUE', label: 'MISSING' },
    { key: 'HS_EXCESS_VALUE', label: 'EXCESS' },
    { key: 'SPECIAL_VALUE', label: 'SPECIAL' }],
  data: {} as any
})

const report4Ctl = reactive({
  loading: false,
  data: {} as any
})

const report5Ctl = reactive({
  loading: false,
  data: [],
  selectedParentLevel: '',
  selectedCurrentLevel: ''
})

const reportDetails = reactive({
  title: 'View Details' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
})
const searchReport1Details = () => {
  reportDetails.title = 'View Details'
  pageCtl.conditions.detailIndex = 'report1'
  searchDetails()
}

const searchReport2Details = () => {
  pageCtl.conditions.detailIndex = 'report2'
  searchDetails()
}

const searchReport3Details = () => {
  reportDetails.title = 'View Details [' + pageCtl.conditions.report3SelectedWeek + ']'
  pageCtl.conditions.detailIndex = 'report3'
  searchDetails()
}

const searchReport4Details = () => {
  reportDetails.title = 'View Details [' + pageCtl.conditions.report4SelectedWeek + ']'
  pageCtl.conditions.detailIndex = 'report4'
  searchDetails()
}

const afterReport2Select = (r) => {
  const selectColumns = _report2SelectedColumns.value
  const selectedValue = [] as any
  for (let i = 0; i < selectColumns.length; i++) {
    selectedValue.push(r[selectColumns[i]])
  }
  pageCtl.conditions.report2SelectedValue = selectedValue

  let title = $join(...selectedValue)
  if (title) {
    title = ' [' + title + ']'
  }

  reportDetails.title = 'View Details' + title
}

const searchDetails = () => {
  $viewDetails({
    url: '/inventory/uhs_structure/query_report1_details',
    durl: '/inventory/uhs_structure/download_report1_details',
    params: pageCtl.conditions,
    title: reportDetails.title
  })
}

const initPage = () => {
  const start = new Date()
  const end = new Date()
  start.setMonth(start.getMonth() - 3)
  pageCtl.conditions.dateRange = [$dateFormatter(start, 'yyyyMM'), $dateFormatter(end, 'yyyyMM')]

  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/inventory/uhs_structure/init_page',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.weekOpts = body.week
    pageCtl.filterOpts = body.cascader
    pageCtl.conditions.week = pageCtl.weekOpts.length > 0 ? pageCtl.weekOpts[0] : ''
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
  searchReport5()
}

const searchReport1 = () => {
  report1Ctl.loading = true
  $axios({
    method: 'post',
    url: '/inventory/uhs_structure/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    report1Ctl.data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report1Ctl.loading = false
  })
}

const searchReport2 = () => {
  report2Ctl.columns = parseReport2Colums()
  report2TableRef.value.search()
}

const searchReport3 = () => {
  report3Ctl.loading = true
  $axios({
    method: 'post',
    url: '/inventory/uhs_structure/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    report3Ctl.data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report3Ctl.loading = false
  })
}

const searchReport4 = () => {
  report4Ctl.loading = true
  $axios({
    method: 'post',
    url: '/inventory/uhs_structure/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    report4Ctl.data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report4Ctl.loading = false
  })
}

const searchReport5 = () => {
  report5Ctl.loading = true
  $axios({
    method: 'post',
    url: '/inventory/uhs_structure/query_report5',
    data: pageCtl.conditions
  }).then((body) => {
    report5Ctl.data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report5Ctl.loading = false
  })
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = report5Ctl.selectedParentLevel
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = report5Ctl.selectedCurrentLevel
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const parseReport2Colums = () => {
  const columns = [] as Array<Column>
  for (let i = 0; i < _report2SelectedColumns.value.length; i++) {
    columns.push({
      data: _report2SelectedColumns.value[i]
    })
  }
  columns.push({
    data: 'STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT',
    title: 'Stock Value in Last HS Weekly',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_VALUE',
    title: 'Gross UHS',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_PERCENT',
    title: 'Gross UHS (%)',
    type: 'numeric',
    precision: 1
  }, {
    data: 'NET_UNHEALTHY_PERCENT',
    title: 'Net UHS (%)',
    type: 'numeric',
    precision: 1
  }, {
    data: 'SPECIAL_PERCENT',
    title: 'Special Events (%)',
    type: 'numeric',
    precision: 1
  }, {
    data: 'SPECIAL_VALUE',
    title: 'Special Value',
    type: 'numeric',
    precision: 1
  }, {
    data: 'HS_EXCESS_VALUE',
    title: 'HS Excess Value',
    type: 'numeric'
  }, {
    data: 'HS_THEO_PROV_VALUE',
    title: 'HS Theo.Prov. Value',
    type: 'numeric'
  }, {
    data: 'HS_MISSING_VALUE',
    title: 'HS Missing Value',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_VALUE_TOP20',
    title: 'Gross UHS Top 20',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_VALUE_EE_TOP1000',
    title: 'Gross UHS EE Top 1000',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_VALUE_NON_TOP20_EE_TOP1000',
    title: 'Gross UHS Non TOP 20 & EE Top 1000',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_VALUE_NON_TOP20_NON_EE_TOP1000',
    title: 'Gross UHS Non TOP 20 & Non EE Top 1000',
    type: 'numeric'
  }, {
    data: 'HS_EXCESS_VALUE_TOP20',
    title: 'HS Excess Value Top 20',
    type: 'numeric'
  }, {
    data: 'HS_THEO_PROV_VALUE_TOP20',
    title: 'HS Theo.Prov. Value Top 20',
    type: 'numeric'
  }, {
    data: 'HS_MISSING_VALUE_TOP20',
    title: 'HS Missing Value Top 20',
    type: 'numeric'
  }, {
    data: 'GROSS_UNHEALTHY_PERCENT_TOP20',
    title: 'Gross UHS Top 20 (%)',
    type: 'numeric',
    precision: 1
  }, {
    data: 'FIN_PROV_VALUE',
    title: 'Fin.Prov.',
    type: 'numeric'
  }, {
    data: 'PROPOSAL_FIN_PROV_VALUE',
    title: 'Pro.Fin.Prov. Value',
    type: 'numeric'
  }, {
    data: 'FIN_VS_PFP',
    title: 'Fin.Prov. vs Pro.Fin.Prov. (%)',
    type: 'numeric',
    precision: 1
  })
  return columns
}

const _report2SelectedColumns = computed(() => {
  if (pageCtl.conditions.report2SelectedColumns.length > 0) {
    return pageCtl.conditions.report2SelectedColumns
  } else {
    return ['BU', 'PRODUCT_LINE']
  }
})

const _report3Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const yAxisData = report3Ctl.data

  const keys = Object.keys(yAxisData)
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i]
    if (key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: report3Ctl.seriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        label: {
          show: report3Ctl.seriesType === 'bar' && keys.length < 10,
          position: 'top',
          valueAnimation: true,
          formatter: (obj) => {
            return $shortenNumber(obj.value)
          }
        },
        data: yAxisData[key] || []
      })
    }
  }
  return {
    title: {
      text: 'UHS by Consumption Buckets Trend' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: report3Ctl.seriesType === 'line' ? false : [0, '10%'],
      data: report3Ctl.data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const _report4Opt = computed(() => {
  const series = [] as any
  const legend = [{
    key: 'HS_THEO_PROV',
    label: 'Theo.Prov.',
    color: '#d66020'
  }, {
    key: 'HS_THEO_PROV_SPECIAL',
    label: 'Theo.Prov.Special Event',
    color: '#e19909'
  }, {
    key: 'HS_EXCESS',
    label: 'Excess Stock',
    color: '#b8b202'
  }, {
    key: 'HS_EXCESS_SPECIAL',
    label: 'Excess Special Event',
    color: '#6e9a11'
  }, {
    key: 'HS_MISSING',
    label: 'Missing Stock',
    color: '#458b17'
  }, {
    key: 'HEALTH_STOCK',
    label: 'Health Stock',
    color: '#2c821d'
  }, {
    key: 'NET_UNHEALTHY_PERCENT',
    label: 'NET UHS(%)',
    color: '#c12e34'
  }, {
    key: 'SPECIAL_PERCENT',
    label: 'SPEICAL EVENT(%)',
    color: '#5470c6'
  }]

  for (let i = 0; i < legend.length; i++) {
    const l = legend[i]
    if (l.key !== 'NET_UNHEALTHY_PERCENT' && l.key !== 'SPECIAL_PERCENT') {
      series.push({
        name: l.label,
        type: 'bar',
        stack: 'sum',
        areaStyle: {},
        itemStyle: {
          color: l.color
        },
        label: {
          show: false,
          position: 'top',
          valueAnimation: true,
          formatter: (obj) => {
            return $shortenNumber(obj.value)
          }
        },
        data: report4Ctl.data[l.key] || []
      })
    } else {
      series.push({
        name: l.label,
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          color: l.color
        },
        label: {
          show: true,
          position: 'top',
          valueAnimation: true,
          formatter: (obj) => {
            return $shortenNumber(obj.value) + '%'
          }
        },
        data: report4Ctl.data[l.key] || []
      })
    }
  }

  return {
    title: {
      text: 'UHS Structure' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend.map(e => e.label) }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: [0, '10%'],
      data: report4Ctl.data.xAxis || [],
      triggerEvent: true
    },
    yAxis: [{
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      scale: true,
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      },
      axisLabel: {
        formatter: (value) => {
          return value + '%'
        }
      }
    }],
    series
  }
})

const _report5Opt = computed(() => {
  return {
    title: {
      text: 'UHS Structure by Category' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treeAncestors as any
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)

          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)

            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === 'SO' ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        report5Ctl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          report5Ctl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: 'UHS',
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: report5Ctl.data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

onMounted(() => {
  initPage()

  report3ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      report3Ctl.seriesType = obj.currentType
    }
  })

  report3ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report3SelectedWeek = obj.name
    searchReport3Details()
  })

  report4ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report4SelectedWeek = obj.name
    searchReport4Details()
  })
})

onBeforeMount(() => {
  report2Ctl.columns.sort()
})
</script>

<style lang="scss">
#UHSStructure {
  .uhs-widget {
    .el-card {
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      border-radius: 0 !important;
      margin: 0 var(--scp-widget-margin) 0 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary) !important;
          margin: 0 !important;
        }

      }
    }
  }
}
</style>
