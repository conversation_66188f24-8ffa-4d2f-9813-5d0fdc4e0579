<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.rebalanceFilterOpts" :filter-base="['DC_STK_REBALANCE_V']"
                                    :after-apply="search"/>
          </el-col>
          <!--type-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Moving Average Price', 'Item']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!--selected coverage-->
          <el-col :span="2">
            <el-input-number v-model="pageCtl.conditions.target" size="small" :precision="1" :step="0.5" :max="12"
                             :min="2"
                             style="width: var(--scp-input-width) !important;" title="保存几个月的AMU用量"/>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"/>
          </el-col>
          <el-col :span="4">
            <el-button type="danger" icon="keys" size="small" @click="calcRebalance" style="display: none">
              重新计算
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-loading="pageCtl.loading.filter || pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 450px !important;">
              <scp-subscript id="IRPS"/>
              <el-row>
                <el-col :span="24">
                  <h3 style="text-align: left;margin-top: 10px">Stock Rebalance Possiblity Summary</h3>
                </el-col>
              </el-row>
              <scp-table2
                  :max-height="394"
                  ref="report1TableRef"
                  :lazy="true"
                  :on-search="searchReport1"
                  :columns="pageCtl.report1Columns"
                  :column-sorting="false"
                  :filters="false"
                  :data="pageCtl.report1Data"
                  :context-menu-items="pageCtl.report1ContextMenuItems"
                  :after-select="afterReport1Select"
                  :show-total="true"/>
            </div>
          </el-col>
          <el-col :span="12" v-loading="pageCtl.loading.filter || pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 450px !important;">
              <scp-subscript id="IRPL"/>
              <el-col :span="12">
                <h3 style="text-align: left;margin-top: 10px">Stock Rebalance Proposal List</h3>
              </el-col>
              <el-select v-model="pageCtl.conditions.report2Type" @change="searchReport2" style="width: 150px !important;position:absolute; top:10px; right: 10px">
                <el-option
                    v-for="item in _pivotColumns"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
              <scp-table
                  :max-height="394"
                  url="/inventory/rebalance/query_report2"
                  download-url="/inventory/rebalance/download_report2"
                  ref="report2TableRef"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :pagging="false"
                  :columns="_report2Columns"
                  :context-menu-items="pageCtl.report2ContextMenuItems"
                  :after-select="afterReport2Select"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- report1 details-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.report1DetailsVisible"
                             :title="pageCtl.report1DetailsTitle">
      <template v-slot="{ height }">
        <scp-table
            ref="report1DetailsTableRef"
            :columns="pageCtl.report1DetailsColumns"
            :max-height="height - 100"
            :lazy="true"
            url="/inventory/rebalance/query_report1_details"
            download-url="/inventory/rebalance/download_report1_details"
            :context-menu-items="pageCtl.report2DetailsContextMenuItems"
            :after-select="afterReport2DetailsSelect"
            :params="pageCtl.conditions"/>
      </template>
    </scp-draggable-resizable>

    <!-- report2 details-->
    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.report2DetailsVisible"
                             :title="pageCtl.report2DetailsTitle">
      <template v-slot="{ height }">
        <scp-table
            :max-height="height - 100"
            :lazy="true"
            ref="report2DetailsTableRef"
            url="/inventory/rebalance/query_report2_details"
            download-url="/inventory/rebalance/download_report2_details"
            :columns="pageCtl.report2DetailsColumns"
            :params="pageCtl.conditions"
            :context-menu-items="pageCtl.report2DetailsContextMenuItems"
            :after-select="afterReport2DetailsSelect"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="60vw" h="450px" v-model="pageCtl.report2DetailLogsVisible"
                             :title="pageCtl.report2DetailLogsTitle">
      <template v-slot="{ height }">
        <pre class="display" :style="{ height : height -150 + 'px'}">{{ pageCtl.report2DetailLogs }}</pre>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const searchRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()
const report1DetailsTableRef = ref()
const report2DetailsTableRef = ref()

const viewReport2TransferLogs = () => {
  $axios({
    method: 'post',
    url: '/inventory/rebalance/query_report2_details_logs',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2DetailLogs = body
    pageCtl.report2DetailLogsVisible = true
  }).catch((error) => {
    console.log(error)
  })
}

const viewReport2Details = () => {
  pageCtl.report2DetailsVisible = true
  report2DetailsTableRef.value.clearAndSearch()
}

const viewReport1Details = () => {
  pageCtl.report1DetailsVisible = true
  report1DetailsTableRef.value.clearAndSearch()
}

const renderReprotColumn = (hotInstance, td, row, column, prop, value) => {
  if (value !== '' && value !== null) {
    td.innerHTML = $thousandBitSeparator(value, 0)
  } else {
    td.innerHTML = value
  }
  const rd = hotInstance.getSourceDataAtRow(row)
  if (rd[pageCtl.conditions.report2Type] === 'Total') {
    td.style.fontWeight = 'bold'
  }
  td.style.textAlign = 'right'
}

const pageCtl = reactive({
  conditions: {
    type: 'Quantity',
    $scpFilter: {
      cascader: [],
      filter: []
    },
    target: 6,
    report1SelectedType: '',
    report1SelectedDC: '',
    report2Type: 'ENTITY',
    report2SelectedType: '',
    report2SelectedFrom: '',
    report2SelectedTo: '',
    report2DetailsSelectedType: ''
  },
  loading: {
    filter: false,
    report1: false,
    report2: false
  },
  rebalanceFilterOpts: [],
  report1Columns: [{
    title: 'Category',
    data: 'TRANSFER_PROPOSAL'
  }, {
    title: 'DCSH',
    data: 'O001',
    render: renderReprotColumn
  }, {
    title: 'DCBJ',
    data: 'N001',
    render: renderReprotColumn
  }, {
    title: 'DCWH',
    data: 'I003',
    render: renderReprotColumn
  }, {
    title: 'DCGZ',
    data: 'M001',
    render: renderReprotColumn
  }, {
    title: 'DCCD',
    data: 'I001',
    render: renderReprotColumn
  }, {
    title: 'Total',
    data: 'TOTAL',
    render: renderReprotColumn
  }],
  report1Data: [{}],
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport1Details
    },
    view_split0: { name: '---------' }
  },
  report1DetailsVisible: false,
  report1DetailsColumns: [
    { data: 'MATERIAL' },
    { data: 'O001_UU_STOCK', type: 'numeric' },
    { data: 'N001_UU_STOCK', type: 'numeric' },
    { data: 'M001_UU_STOCK', type: 'numeric' },
    { data: 'I001_UU_STOCK', type: 'numeric' },
    { data: 'I003_UU_STOCK', type: 'numeric' },
    { data: 'O001_OPEN_SO', type: 'numeric' },
    { data: 'N001_OPEN_SO', type: 'numeric' },
    { data: 'M001_OPEN_SO', type: 'numeric' },
    { data: 'I001_OPEN_SO', type: 'numeric' },
    { data: 'I003_OPEN_SO', type: 'numeric' },
    { data: 'O001_UD', type: 'numeric' },
    { data: 'N001_UD', type: 'numeric' },
    { data: 'M001_UD', type: 'numeric' },
    { data: 'I001_UD', type: 'numeric' },
    { data: 'I003_UD', type: 'numeric' },
    { data: 'O001_OPEN_TRANSFER', type: 'numeric' },
    { data: 'N001_OPEN_TRANSFER', type: 'numeric' },
    { data: 'M001_OPEN_TRANSFER', type: 'numeric' },
    { data: 'I001_OPEN_TRANSFER', type: 'numeric' },
    { data: 'I003_OPEN_TRANSFER', type: 'numeric' },
    { data: 'O001_DELIVERING_PLANT' },
    { data: 'N001_DELIVERING_PLANT' },
    { data: 'M001_DELIVERING_PLANT' },
    { data: 'I001_DELIVERING_PLANT' },
    { data: 'I003_DELIVERING_PLANT' },
    { data: 'O001_ONE_MM_AMU', type: 'numeric' },
    { data: 'N001_ONE_MM_AMU', type: 'numeric' },
    { data: 'M001_ONE_MM_AMU', type: 'numeric' },
    { data: 'I001_ONE_MM_AMU', type: 'numeric' },
    { data: 'I003_ONE_MM_AMU', type: 'numeric' },
    { data: 'O001_UU_STOCK_NEW', type: 'numeric' },
    { data: 'N001_UU_STOCK_NEW', type: 'numeric' },
    { data: 'M001_UU_STOCK_NEW', type: 'numeric' },
    { data: 'I001_UU_STOCK_NEW', type: 'numeric' },
    { data: 'I003_UU_STOCK_NEW', type: 'numeric' },
    { data: 'ACTIVENESS' },
    { data: 'DELETION' },
    { data: 'STOCKING_POLICY' },
    { data: 'O001_MVP', type: 'numeric' },
    { data: 'N001_MVP', type: 'numeric' },
    { data: 'M001_MVP', type: 'numeric' },
    { data: 'I001_MVP', type: 'numeric' },
    { data: 'I003_MVP', type: 'numeric' },
    { data: 'O001_UNIT_COST', type: 'numeric' },
    { data: 'N001_UNIT_COST', type: 'numeric' },
    { data: 'M001_UNIT_COST', type: 'numeric' },
    { data: 'I001_UNIT_COST', type: 'numeric' },
    { data: 'I003_UNIT_COST', type: 'numeric' },
    { data: 'CLUSTER_NAME' },
    { data: 'ENTITY' },
    { data: 'BU' },
    { data: 'PRODUCT_LINE' },
    { data: 'PRODUCTION_LINE' },
    { data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'VENDOR_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MRP_CONTROLLER' },
    { data: 'O001_ROUNDING_VALUE', type: 'numeric' },
    { data: 'N001_ROUNDING_VALUE', type: 'numeric' },
    { data: 'M001_ROUNDING_VALUE', type: 'numeric' },
    { data: 'I001_ROUNDING_VALUE', type: 'numeric' },
    { data: 'I003_ROUNDING_VALUE', type: 'numeric' },
    { data: 'TRANSFER_FROM' },
    { data: 'TRANSFER_TO' },
    { data: 'TRANSFER_QTY' },
    { data: 'TRANSFER_PROPOSAL' }
  ],
  report1DetailsTitle: '',
  report2ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport2Details
    },
    view_split0: { name: '---------' }
  },
  report2DetailsContextMenuItems: {
    view_transfer_logs: {
      name: 'View transfer logs',
      callback: viewReport2TransferLogs
    },
    view_split0: { name: '---------' }
  },
  report2DetailsVisible: false,
  report2DetailsColumns: [
    { data: 'TARGET' },
    { data: 'MATERIAL' },
    { data: 'TRANSFER_FROM' },
    { data: 'TRANSFER_TO' },
    { data: 'TRANSFER_QTY', type: 'numeric' },
    { data: 'TRANSFER_VALUE', type: 'numeric' },
    { data: 'CLUSTER_NAME' },
    { data: 'ENTITY' },
    { data: 'BU' },
    { data: 'PRODUCT_LINE' },
    { data: 'PRODUCTION_LINE' },
    { data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'VENDOR_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'MRP_CONTROLLER' },
    { data: 'TRANSFER_PROPOSAL' }
  ],
  report2DetailsTitle: '',
  report2DetailLogsVisible: false,
  report2DetailLogsTitle: '',
  report2DetailLogs: ''
})

onMounted(() => {
  initPage()
})

const calcRebalance = () => {
  if (confirm('确定要重新计算吗? 这会需要一些时间')) {
    $axios({
      method: 'post',
      url: '/inventory/rebalance/calc'
    }).then((body) => {
      console.log(body)
    }).catch((error) => {
      console.log(error)
    })
  }
}
const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/rebalance/init_page'
  }).then((body) => {
    pageCtl.rebalanceFilterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  searchReport1()
  searchReport2()
}
const searchReport1 = () => {
  report1TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/rebalance/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report1TableRef.value.setLoading(false)
  })
}
const searchReport2 = () => {
  report2TableRef.value.search()
}
const afterReport1Select = (r, c, a, column) => {
  pageCtl.conditions.report1SelectedType = (r.TRANSFER_PROPOSAL === 'Total' ? '' : r.TRANSFER_PROPOSAL)
  pageCtl.conditions.report1SelectedDC = ((column === 0 || column === 6) ? '' : a)

  const title = $join(pageCtl.conditions.report1SelectedType, pageCtl.conditions.report1SelectedDC)

  pageCtl.report1DetailsTitle = 'View Details ' + (title ? '[' + title + ']' : '')
}
const afterReport2Select = (r) => {
  pageCtl.conditions.report2SelectedType = r[pageCtl.conditions.report2Type]
  pageCtl.conditions.report2SelectedFrom = r.TRANSFER_FROM
  pageCtl.conditions.report2SelectedTo = r.TRANSFER_TO
  pageCtl.report2DetailsTitle = 'View Details [' + pageCtl.conditions.report2SelectedType + ']'
}
const afterReport2DetailsSelect = (r) => {
  pageCtl.conditions.report2DetailsSelectedType = r.MATERIAL
  pageCtl.report2DetailLogsTitle = 'Transfer Logs [' + r.MATERIAL + ']'
}

const _report2Columns = computed(() => {
  return [{
    data: pageCtl.conditions.report2Type,
    title: pageCtl.conditions.report2Type,
    render: (hotInstance, td, row, column, prop, value) => {
      if (value === 'Total') {
        td.style.fontWeight = 'bold'
      }
      td.innerHTML = value
    }
  }, {
    data: 'TRANSFER_FROM',
    title: 'From'
  }, {
    data: 'TRANSFER_TO',
    title: 'To'
  }, {
    data: 'TRANSFER_QTY',
    title: 'Qty',
    render: renderReprotColumn
  }, {
    data: 'TRANSFER_VALUE',
    title: 'Value',
    render: renderReprotColumn
  }, {
    data: 'ITEM',
    render: renderReprotColumn
  }]
})
const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.rebalanceFilterOpts)
})

</script>
