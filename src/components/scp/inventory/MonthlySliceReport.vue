<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.version" size="small" placeholder="Version" @change="search">
              <el-option
                  v-for="item in pageCtl.versionsOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.filterOpts"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.currency" size="small" placeholder="Currency">
              <el-option
                  v-for="item in ['CNY', 'EUR', 'HKD']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small" placeholder="Currency" @change="search">
              <el-option
                  v-for="item in ['Projection', 'Target']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['version']"/>
            &nbsp;
            <el-button @click="saveReport1Calc" :loading="pageCtl.loading.saveReport1" style="display: none">Calc</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report1">
            <div class="subscript-container">
              <scp-subscript id="IMSR"/>
              <scp-table2
                  :max-height="500"
                  ref="report1TableRef"
                  :data="pageCtl.report1Data"
                  :context-menu-items="pageCtl.report1MenuItems"
                  :context-menu-items-reverse="false"
                  :column-sorting="false"
                  :params="pageCtl.conditions"
                  :nested-headers="_report1Columns['headers']"
                  :columns="_report1Columns['columns']"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <h3 style="float: left">Projection</h3>
            <el-popconfirm title="确定使用上个月的Projection来替换本月数据(系统每月1日会自动进行此操作)? 此操作会导致本月数据丢失"
                           iconColor="var(--scp-text-color-error)"
                           @confirm="sycnProjection"
                           confirmButtonType="warning"
                           confirmButtonText='确定'
                           cancelButtonText='取消'>
              <template #reference>
                <el-button type="danger" size="small" style="float: right; margin-top: 10px;" :loading="pageCtl.loading.syncProjection"
                           v-show="pageCtl.isAdmin">
                  <font-awesome-icon icon="fa-arrows-rotate" style="font-size: 80%"/>&nbsp;
                  Sync Projection
                </el-button>
              </template>
            </el-popconfirm>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report1">
            <div class="subscript-container">
              <scp-subscript id="IMSP"/>
              <scp-table
                  ref="report2TableRef"
                  :lazy="true"
                  url="/inventory/monthly_slice_report/query_report2"
                  :save-url="(pageCtl.conditions.type === 'Target' && pageCtl.isAdmin) || pageCtl.conditions.type === 'Projection' ? '/inventory/monthly_slice_report/save_report2': ''"
                  :primary-key-id="['ROW_ID']"
                  :params="pageCtl.conditions"
                  :before-save="beforeReport2Save"
                  :after-save="searchReport1"
                  :columns="_report2Columns"
                  :hidden-columns="{columns: [0]}"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $axios: any = inject('$axios')
const $getNext25Months: any = inject('$getNext25Months')
const $renderTdTitle: any = inject('$renderTdTitle')
const $message: any = inject('$message')
const $isEmpty: any = inject('$isEmpty')
const $downloadFile: any = inject('$downloadFile')

const searchRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()

const downloadDinReport = () => {
  $downloadFile('/inventory/monthly_slice_report/download_din_report', { version: pageCtl.conditions.version })
}

const pageCtl = reactive({
  isAdmin: false,
  report1Column: [],
  report1ColumnActual: [],
  report1ColumnDemand: [],
  versionsOpts: [],
  rcaOpts: [],
  filterOpts: [],
  entityOpts: [],
  conditions: {
    type: 'Projection',
    version: '',
    currency: 'CNY',
    filterList: [
      ['CLASS', 'COGS'],
      ['CLASS', 'Gross Inventory'],
      ['CLASS', 'Net Inv'],
      ['CLASS', 'Provision'],
      ['CLASS', 'Spot DIN_Net Inv'],
      ['CLASS', 'Yearly DIN_Net Inv'],
      ['CLUSTER_NAME', 'LV'],
      ['CLUSTER_NAME', 'China_Total'],
      ['CLUSTER_NAME', 'MV'],
      ['CLUSTER_NAME', 'EE'],
      ['CLUSTER_NAME', 'H&D'],
      ['CLUSTER_NAME', 'Trading'],
      ['CLUSTER_NAME', 'China_DC'],
      ['CLUSTER_NAME', 'CHINA_REST'],
      ['CLUSTER_NAME', 'CNDC_REST'],
      ['CLUSTER_NAME', 'SPC']
    ],
    specialType: 'MATERIAL',
    specialContent: ''
  },
  loading: {
    filter: false,
    saveReport1: false,
    report1: false,
    syncProjection: false
  },
  report1Data: [],
  report1MergeCell: [],
  rcaMap: {},
  report1MenuItems: {
    view_split0: { name: '---------' },
    downloadDIN: {
      name: 'Download DIN Report',
      callback: downloadDinReport
    }
  }
})

const _report1Columns = computed(() => {
  const columns = [
    { data: 'clusterName', title: 'Cluster Name', render: mergeCellStyle },
    { data: 'entityName', title: 'Entity Name', render: mergeCellStyle },
    { data: 'plantType', title: 'Plant Type', render: mergeCellStyle },
    { data: 'clazz', title: 'Class' },
    { data: 'unitOfMeasure', title: 'Unit of Measure' },
    { data: 'currentMonth', title: 'Cur.Month', type: 'numeric', render: renderReport1Column }
  ]

  if (pageCtl.report1Column) {
    for (let i = 0; i < pageCtl.report1Column.length; i++) {
      const c: any = pageCtl.report1Column[i]
      c.data = c.data.toLowerCase()
      c.type = 'numeric'
      c.render = renderReport1Column
      columns.push(c)
    }
  }

  const headers: any = [
    [
      {
        label: '',
        colspan: 6
      }
    ]
  ]

  if (pageCtl.report1ColumnActual) {
    headers[0].push({
      label: 'Actual(Y-1)',
      colspan: 12
    })
    headers[0].push({
      label: 'Actual',
      colspan: (pageCtl.report1ColumnActual - 12 || 0)
    })
  }

  if (pageCtl.report1ColumnDemand) {
    headers[0].push({
      label: 'Projection',
      colspan: pageCtl.report1ColumnDemand || 0
    })
  }
  return {
    columns,
    headers
  }
})

const _report2Columns = computed(() => {
  const columns: any =
      [
        { data: 'ROW_ID' },
        { data: 'ENTITY', editor: 'select', selectOptions: pageCtl.entityOpts },
        {
          data: 'PLANT_TYPE',
          editor: 'select',
          selectOptions: ['Plant', 'DC']
        },
        {
          data: 'CLASS',
          editor: 'select',
          selectOptions: pageCtl.isAdmin ? ['Provision', 'Gross Inventory', 'COGS', 'Spot DIN_Net Inv', 'Spot DIN_Gross Inv', 'Yearly DIN_Gross Inv'] : ['Provision', 'Gross Inventory', 'COGS']
        },
        {
          data: 'CURRENCY',
          editor: 'select',
          selectOptions: ['CNY', 'HKD', 'EUR']
        },
        {
          data: 'RCA_CATEGORY',
          editor: 'select',
          selectOptions: pageCtl.isAdmin ? ['', 'Positive', 'Negative', 'Target'] : ['', 'Positive', 'Negative']
        },
        {
          data: 'RCA_CODE',
          editor: 'select',
          selectOptions: pageCtl.rcaOpts,
          render: (hotInstance, td, row, column, prop, value) => $renderTdTitle(hotInstance, td, row, column, prop, value, pageCtl.rcaOpts, pageCtl.rcaMap)
        }
      ]

  const months = $getNext25Months(pageCtl.conditions.version)

  for (let i = 1; i <= months.length; i++) {
    const key = 'MONTH' + (i < 10 ? '0' + i : i)
    columns.push({
      data: key,
      title: months[i - 1],
      type: 'strict-numeric',
      precision: 9
    })
  }

  columns.push({
    data: 'CREATE_BY'
  }, {
    data: 'UPDATE_TIME'
  })

  return columns
})

onMounted(() => {
  initPage()
})

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/monthly_slice_report/init_page'
  }).then((body) => {
    pageCtl.versionsOpts = body.version
    pageCtl.filterOpts = body.cascader
    pageCtl.entityOpts = body.entity
    pageCtl.rcaOpts = body.rca
    pageCtl.isAdmin = body.isAdmin
    pageCtl.rcaMap = body.rcaMap || {}
    if (pageCtl.versionsOpts.length) {
      pageCtl.conditions.version = pageCtl.versionsOpts[0]
    }
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  searchReport1()
  searchReport2()
}

const saveReport1Calc = () => {
  pageCtl.loading.saveReport1 = true
  $axios({
    method: 'post',
    url: '/inventory/monthly_slice_report/save_report1_calculated',
    data: pageCtl.conditions
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success('计算成功')
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.saveReport1 = false
  })
}

const searchReport1 = () => {
  report1TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/monthly_slice_report/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Column = body.columns
    pageCtl.report1ColumnActual = body.columnActual
    pageCtl.report1ColumnDemand = body.columnDemand

    const data = body.data
    pageCtl.report1Data = data
    parseReport1MergeCell(data)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report1TableRef.value.setLoading(false)
  })
}
const searchReport2 = () => {
  report2TableRef.value.search()
}
const beforeReport2Save = (e, changes) => {
  let valid = true
  for (let i = 0; i < changes.length; i++) {
    const row = changes[i]
    if (row.CLASS !== 'Gross Inventory' && row.RCA_CATEGORY) {
      $message.error('仅在Class为Gross Inventory时，RCA Category不能为空，其他情况RCA Category均需要为空')
      valid = false
    }
    if (row.CLASS === 'Gross Inventory' && (!row.RCA_CATEGORY)) {
      $message.error('当Class为Gross Inventory时，RCA Category为必填项')
      valid = false
    }

    const months = $getNext25Months(pageCtl.conditions.version)
    for (let j = 1; j <= months.length; j++) {
      const key = 'MONTH' + (j < 10 ? '0' + j : j)
      if ($isEmpty(row[key]) === false) {
        if (isNaN(row[key])) {
          $message.error(months[j - 1] + '需要填充数字')
          valid = false
        }
        if (parseFloat(row[key]) < 0 || parseFloat(row[key]) > 1000) {
          $message.error(months[j - 1] + '应为 [0,1000] 之间的数字')
          valid = false
        }
      }
    }
  }
  return valid
}
const mergeCellStyle = (hotInstance, td, row, column, prop, value) => {
  td.style.verticalAlign = 'middle'
  if (value === 'Total') {
    td.style.fontWeight = 'bold'
    td.style.textAlign = 'center'
    td.innerHTML = value
  } else {
    td.innerHTML = value
  }
}
const parseReport1MergeCell = (data) => {
  const result = [] as any
  const megerList = [] as any
  let key = ''
  let step = 1
  for (let i = 0; i < data.length; i++) {
    const key0 = data[i].entityName + '.' + data[i].plantType
    if (key !== key0) {
      key = key0
      megerList.push(step)
      step = 1
    } else {
      step++
    }
  }
  megerList.push(step)

  let start = 0
  for (let i = 1; i < megerList.length - 1; i++) {
    if (megerList[i] !== 1) {
      result.push({
        row: start,
        col: 0,
        rowspan: megerList[i],
        colspan: 1
      })
      result.push({
        row: start,
        col: 1,
        rowspan: megerList[i],
        colspan: 1
      })
      result.push({
        row: start,
        col: 2,
        rowspan: megerList[i],
        colspan: 1
      })
    }
    start += megerList[i]
  }

  result.push({
    row: start,
    col: 0,
    rowspan: 1,
    colspan: 5
  })

  pageCtl.report1MergeCell = result
}
const renderReport1Column = (hotInstance, td, row, column, prop, value) => {
  td.style.textAlign = 'right'
  const rowData = hotInstance.getSourceDataAtRow(row)
  if (value && rowData.clazz && rowData.clazz !== 'COGS convert%' && rowData.clazz.indexOf('DIN') === -1) {
    td.innerHTML = $thousandBitSeparator(value, 1)
  } else if (rowData.clazz === 'COGS convert%') {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 1) + '%'
    } else {
      td.innerHTML = value
    }
  } else if (rowData.clusterName === 'Total') {
    if (value) {
      td.style.fontWeight = 'bold'
      td.innerHTML = $thousandBitSeparator(value, 1)
    } else {
      td.innerHTML = value
    }
  } else {
    if (value) {
      td.innerHTML = $thousandBitSeparator(value, 1)
    } else {
      td.innerHTML = value
    }
  }
}
const sycnProjection = () => {
  pageCtl.loading.syncProjection = true
  $axios({
    method: 'post',
    url: '/inventory/monthly_slice_report/sync_projection'
  }).then(() => {
    $message.success('Projection Synchronized')
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.syncProjection = false
  })
}

</script>
