<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.inventoryFilterOpts" :loading="pageCtl.loading.filter"
                        :filter-base="['INVENTORY_STRUCTURE_V']" :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="pageCtl.pickerOptions">
            </el-date-picker>
          </el-col>
          <!--type-->
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.stock" size="small" placeholder="Stock" multiple :collapse-tags="true">
              <el-option
                  v-for="item in ['BLOCKED_STOCK', 'GIT', 'INTER_STK_TRANSFER', 'RESTRICTED_STOCK', 'RETURNS_STOCK', 'STOCK_IN_QI', 'UU_STOCK', 'WIP']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Moving Average Price', 'Pallet', 'Accounting View', 'Avg Selling Price']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['dateRange', 'report3SelectedDate','report4SelectedDate','targetMonth','projectionVersion']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report1 || pageCtl.loading.filter" style="height: 350px">
              <scp-subscript id="INTM" ref="intmRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable
                                 style="width:var(--scp-input-width)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable
                                 style="width:var(--scp-input-width)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable
                                 style="width:var(--scp-input-width)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." clearable filterable
                                 style="width:var(--scp-input-width)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." clearable filterable
                                 style="width:var(--scp-input-width)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" style="width:var(--scp-input-width)" size="small" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Color</el-col>
                    <el-col :span="18">
                      <el-row>
                        <el-col :span="6">
                          <font-awesome-icon icon="angle-double-up" style="font-size: 0.4rem"/>&nbsp;
                          <el-color-picker v-model="pageCtl.conditions.positiveColor"/>
                        </el-col>
                        <el-col :span="6">
                          <font-awesome-icon icon="minus" style="font-size: 0.4rem"/>&nbsp;
                          <el-color-picker v-model="pageCtl.conditions.middleColor"/>
                        </el-col>
                        <el-col :span="6">
                          <font-awesome-icon icon="angle-double-down" style="font-size: 0.4rem"/>&nbsp;
                          <el-color-picker v-model="pageCtl.conditions.nagitiveColor"/>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.tooltips" style="height:260px; overflow: auto">
                    <el-checkbox v-for="item in pageCtl.tooltipsOpts"
                                 :key="item"
                                 :value="item"
                                 :label="item"></el-checkbox>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button @click="intmRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="intmRef.toggleView();searchReport1()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="14">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report2 || pageCtl.loading.filter" style="height: 350px">
              <scp-subscript id="INTG" ref="intgRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Report Settings</div>
                  <el-row>
                    <el-col :span="6">Price Reference</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.priceReference" size="small" filterable clearable style="width: 100%"
                                 placeholder="Price Reference">
                        <el-option
                            v-for="item in pageCtl.priceReferenceOpts"
                            :key="item"
                            :label="item"
                            :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="6">Projection Version</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.projectionVersion" size="small" placeholder="Projection Version" filterable
                                 style="width:100%">
                        <el-option
                            v-for="item in pageCtl.projectionVersionOpts"
                            :key="item"
                            :label="item"
                            :value="item"/>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title" style="display: block;margin-top:10px;">Stacked Variables</div>
                  <el-checkbox-group v-model="pageCtl.conditions.stackedVariables" style="height:260px;overflow: auto">
                    <el-checkbox v-for="item in pageCtl.tooltipsOpts"
                                 :key="item"
                                 :value="item"
                                 :label="item"></el-checkbox>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button @click="intgRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="intgRef.toggleView();searchReport1();searchReport2()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.report3 || pageCtl.loading.filter">
              <scp-subscript id="INTT" ref="inttRef"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report3SelectedColumn" size="small" placeholder="Columns" multiple collapse-tags
                             filterable clearable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  url="/inventory/structure/query_report3"
                  download-url="/inventory/structure/download_report3"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :pagging="false"
                  :fixed-columns-left="_report3SelectedColumn.length"
                  :max-height="375"
                  :context-menu-items="pageCtl.report3ContextItems"
                  :after-select="afterReport3Select"
                  :last-row-bold="true"
                  ref="report3TableRef"
                  :columns="pageCtl.report3Columns"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.report4 || pageCtl.loading.filter">
              <scp-subscript id="INMA"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-date-picker
                      style="width: var(--scp-input-width)"
                      v-model="pageCtl.conditions.report4Date"
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      type="date"
                      :clearable="false"
                      placeholder="Date">
                  </el-date-picker>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report4SelectedColumn" size="small" placeholder="Columns" multiple collapse-tags
                             filterable clearable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report4ConvType" size="small">
                    <el-option
                        v-for="item in ['AMU', 'AMF', 'MAX(AMU, AMF)']"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport4">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table2 ref="report4TableRef"
                          :pagging="false"
                          :context-menu-items="pageCtl.report4ContextItems"
                          :column-sorting="false"
                          :filters="false"
                          :fixed-columns-left="_report4SelectedColumn.length"
                          :paggingSettingEnable="false"
                          :showContextMenu="true"
                          :max-height="600"
                          :columns="pageCtl.report4TableSettings['header']"
                          :nested-headers="pageCtl.report4TableSettings['nestedHeader']"
                          :after-select="afterReport4Select"
                          :on-search="searchReport4"
                          :data="pageCtl.report4Data"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- report1 contextmenu-->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import ecStat from 'echarts-stat'
import { computed, inject, onMounted, reactive, ref, nextTick } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')
const $join: any = inject('$join')
const $isEmpty: any = inject('$isEmpty')
const $shortenNumber: any = inject('$shortenNumber')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const searchRef = ref()
const intmRef = ref()
const intgRef = ref()
const report2ChartRef = ref()
const inttRef = ref()
const report3TableRef = ref()
const report4TableRef = ref()
const report1ContextmenuRef = ref()

const viewReport4Details = () => {
  $viewDetails({
    url: '/inventory/structure/query_report4_details',
    durl: '/inventory/structure/download_report4_details',
    params: pageCtl.conditions,
    columns: pageCtl.report4DetailsColumns,
    title: pageCtl.report4DetailsTitle
  })
}

const viewReport3Details = () => {
  $viewDetails({
    url: '/inventory/structure/query_report3_details',
    durl: '/inventory/structure/download_report3_details',
    params: pageCtl.conditions,
    columns: pageCtl.report3DetailsColumns,
    title: pageCtl.report3DetailsTitle
  })
}

const pageCtl = reactive({
  priceReferenceOpts: [],
  inventoryFilterOpts: [],
  top20FilterOpts: [],
  loading: {
    filter: false,
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    target: false
  },
  conditions: {
    $scpFilter: {
      cascader: [['MATERIAL_TYPE', 'DIEN-SERVICE'], ['MATERIAL_TYPE', 'Others'], ['MATERIAL_TYPE', 'ERSA-SPARE'], ['MATERIAL_TYPE', 'HALB-SEMI_FG'],
        ['MATERIAL_TYPE', 'HAWA-FG'], ['MATERIAL_TYPE', 'ROH-RM'], ['MATERIAL_TYPE', 'VERP-PACKAGING'], ['MATERIAL_TYPE', 'WIP_BLANK_MATERIAL'],
        ['SPECIAL_ST', 'W'], ['SPECIAL_ST', '#'], ['SPECIAL_ST', 'E'], ['SPECIAL_ST', 'O'], ['SPECIAL_ST', 'P'], ['SPECIAL_ST', 'Q'], ['SPECIAL_ST', 'V'],
        ['SPECIAL_ST', 'M'], ['SPECIAL_ST', 'Y'], ['SPECIAL_ST', 'Others']],
      filter: []
    },
    dateRange: [] as any,
    priceReference: '',
    stock: ['UU_STOCK', 'STOCK_IN_QI', 'RESTRICTED_STOCK', 'BLOCKED_STOCK', 'RETURNS_STOCK', 'WIP', 'GIT'],
    resultType: 'Moving Average Price',
    level1: 'ENTITY',
    level2: 'PLANT_TYPE',
    level3: 'MRP_CONTROLLER',
    level4: 'PURCHASING_GROUP',
    level5: 'VENDOR_NAME',
    positiveColor: '#A91500',
    middleColor: '#D7B600',
    nagitiveColor: '#097700',
    leafDepth: 1,
    tooltips: [],
    stackedVariables: [],
    selectedTreePath: '',
    report3SelectedColumn: ['CLUSTER_NAME', 'ENTITY'],
    report3ColumnNames: [],
    report3SelectedValue: [],
    report3SelectedDate: '',
    report4Date: '',
    report4SelectedDate: '',
    report4SelectedValue: [],
    report4ConvType: 'AMU',
    projectionVersion: '',
    report4SelectedColumn: ['LT_GROUP']
  },
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  tooltipsOpts: [
    'AVAILABLE_STOCK',
    'AMF_ONE_MM',
    'AMU_ONE_MM',
    'BLOCKED_STOCK',
    'EXCESS_ONE_MM',
    'GIT_QTY',
    'GIT_VALUE',
    'GR_LAST_WD',
    'INTER_STK_TRANSFER',
    'LA_LAST_WD',
    'MINIMUM_LOT_SIZE',
    'MISSING_ONE_MM',
    'MO_RESERVATION',
    'OPEN_AB',
    'OPEN_LA',
    'OPEN_NON_AB_LA',
    'OPEN_PO',
    'OPEN_SO',
    'PO_CREATION_LAST_WD',
    'PROPOSED_FIN_PROV',
    'REAL_PROV_ONE_MM',
    'REORDER_POINT',
    'RESTRICTED_STOCK',
    'RETURNS_STOCK',
    'SAFETY_STOCK',
    'SS2',
    'SS3',
    'STOCK_IN_QI',
    'THEO_PROV_ONE_MM',
    'UD_<30CD',
    'UD_<7CD',
    'UD_>30CD',
    'UU_STOCK',
    'WIP_QTY',
    'WIP_VALUE'
  ],
  projectionVersionOpts: [],
  pickerOptions: {
    shortcuts: [{
      text: 'Next 6 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 6)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Next 1 year',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 12)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  report1Data: [],
  report2Data: { xAxis: [], yAxis: [], legends: [], prAxis: [], pAxis: [], cAxis: [], aAxis: [] } as any,
  report3ContextItems: {
    view_details: {
      name: 'View details',
      disabled () {
        return pageCtl.conditions.report3SelectedDate === ''
      },
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3Columns: [],
  report3DetailsTitle: '',
  report3DetailsVisible: false,
  report3DetailsColumns: [
    { title: 'Date', data: 'DATE_STR' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'SPECIAL_ST' },
    { data: 'SPECIAL_ST_NO' },
    { data: 'CURRENCY' },
    { data: 'UU_STOCK', type: 'numeric' },
    { title: 'Stock IN QI', data: 'STOCK_IN_QI', type: 'numeric' },
    { data: 'RESTRICTED_STOCK', type: 'numeric' },
    { data: 'BLOCKED_STOCK', type: 'numeric' },
    { data: 'RETURNS_STOCK', type: 'numeric' },
    { data: 'INTER_STK_TRANSFER', type: 'numeric' },
    { data: 'MOVING_AVERAGE_P', type: 'numeric' },
    { data: 'MOVING_AVERAGE_P_ETO', type: 'numeric' },
    { data: 'PRICE_UNIT', type: 'numeric' },
    { data: 'UNIT_COST', type: 'numeric' },
    { data: 'EXCHANGE_RATE', type: 'numeric' },
    { data: 'PALLETIZATION_QTY', type: 'numeric' },
    { data: 'WIP_QTY', type: 'numeric' },
    { data: 'WIP_VALUE', type: 'numeric' },
    { data: 'GIT_QTY', type: 'numeric' },
    { data: 'GIT_VALUE', type: 'numeric' },
    { data: 'PROPOSED_FIN_PROV_QTY', type: 'numeric' },
    { data: 'PROPOSED_FIN_PROV_VALUE', type: 'numeric' },
    { title: 'UD<7CD', data: 'UD_LT_7_CD', type: 'numeric' },
    { title: 'UD<30CD', data: 'UD_LT_30_CD', type: 'numeric' },
    { title: 'UD>30CD', data: 'UD_GT_30_CD', type: 'numeric' },
    { title: 'Open PO', data: 'OPEN_PO', type: 'numeric' },
    { title: 'Open SO', data: 'OPEN_SO', type: 'numeric' },
    { title: 'MO Reservation', data: 'MO_RESERVATION', type: 'numeric' },
    { title: 'Excess OneMM', data: 'EXCESS_ONE_MM', type: 'numeric' },
    { title: 'Missing OneMM', data: 'MISSING_ONE_MM', type: 'numeric' },
    { title: 'Theo Prov OneMM', data: 'THEO_PROV_ONE_MM', type: 'numeric' },
    { title: 'AMU OneMM', data: 'AMU_ONE_MM', type: 'numeric' },
    { title: 'AMF OneMM', data: 'AMF_ONE_MM', type: 'numeric' },
    { title: 'GR Last WD', data: 'GR_LAST_WD', type: 'numeric' },
    { title: 'LA Last WD', data: 'LA_LAST_WD', type: 'numeric' },
    { title: 'PO Creation Last WD', data: 'PO_CREATION_LAST_WD', type: 'numeric' },
    { data: 'MINIMUM_LOT_SIZE', type: 'numeric' },
    { data: 'REORDER_POINT', type: 'numeric' },
    { data: 'SAFETY_STOCK', type: 'numeric' },
    { title: 'SS2', data: 'SS2', type: 'numeric' },
    { title: 'SS3', data: 'SS3', type: 'numeric' },
    { data: 'PLANT_TYPE' },
    { data: 'CLUSTER_NAME' },
    { data: 'ENTITY' },
    { title: 'BU', data: 'BU' },
    { data: 'PRODUCT_LINE' },
    { data: 'PRODUCTION_LINE' },
    { title: 'Local BU', data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { title: 'MRP Controller', data: 'MRP_CONTROLLER' },
    { data: 'STOCKING_POLICY' },
    { data: 'VENDOR_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'MOQ_COV_RANGE' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MATERIAL_CRITICALITY' },
    { data: 'SSS_COVERAGE' },
    { data: 'XBOARD' },
    { data: 'XBOARD_CONTROLLER' },
    { data: 'MATERIAL_TYPE' },
    { data: 'ACTIVENESS' },
    { title: 'LT Range', data: 'LT_RANGE' },
    { title: 'Calculated ABC', data: 'CALCULATED_ABC' },
    { title: 'Calculated FMR', data: 'CALCULATED_FMR' },
    { data: 'NEW_PRODUCTS' },
    { data: 'INDUSTRY_CODE' },
    { title: 'MRP Type', data: 'MRP_TYPE' },
    { data: 'AVAILABILITY_CHECK' },
    { title: 'ABC', data: 'ABC' },
    { data: 'PURCHASING_GROUP' },
    { data: 'PROCUREMENT_TYPE' },
    { data: 'SPECIAL_PROC_CODE' },
    { data: 'INDIVIDUAL_COLLECT' },
    { data: 'FOLLOW_UP_MATERIAL' },
    { data: 'PRODN_SUPERVISOR' },
    { title: 'MRP Group', data: 'MRP_GROUP' },
    { title: 'MAT Pricing Group', data: 'MAT_PRICING_GROUP' },
    { data: 'MATERIAL_GROUP_1' },
    { data: 'MATERIAL_GROUP_2' },
    { data: 'MATERIAL_GROUP_3' },
    { data: 'MATERIAL_GROUP_4' },
    { data: 'MATERIAL_GROUP_5' },
    { data: 'MATERIAL_ST_PLANT' },
    { data: 'DIST_CHANNEL_SP_ST' },
    { data: 'ACCOUNT_GROUP' },
    { data: 'AVAILABLE_STOCK' },
    { data: 'AVAILABLE_STOCK_VALUE' }
  ],
  report4Data: [{}],
  report4DetailsTitle: '',
  report4DetailsColumns: [] as any,
  report4ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport4Details
    },
    view_split0: { name: '---------' }
  },
  report4TableSettings: {
    header: [{
      title: 'Total',
      data: 'Total'
    }],
    nestedHeader: [[{
      colspan: 1
    }]]
  }
})

onMounted(() => {
  initFilter()
})

const _report3SelectedColumn = computed(() => {
  if (pageCtl.conditions.report3SelectedColumn.length === 0) {
    return ['CLUSTER_NAME', 'ENTITY']
  } else {
    return pageCtl.conditions.report3SelectedColumn
  }
}
)
const _report4SelectedColumn = computed(() => {
  if (pageCtl.conditions.report4SelectedColumn.length === 0) {
    return ['LT_GROUP']
  } else {
    return pageCtl.conditions.report4SelectedColumn
  }
}
)
const _report1Opt = computed(() => {
  const rootName = 'Inventory Structure'
  return {
    title: {
      text: 'Inventory Treemap'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:14rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)

          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)

            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
}
)
const _report2Opt = computed(() => {
  // 计算趋势线
  const x = pageCtl.report2Data.xAxis
  const ecs = [] as any
  for (let i = 0; i < pageCtl.report2Data.yAxis.length; i++) {
    const a = pageCtl.report2Data.yAxis[i]
    ecs.push([i * 2 + 1, a / 10000])
  }

  // 线性回归: 'linear'; 指数回归:  'exponential';对数回归: 'logarithmic';多项式回归: 'polynomial'
  const myRegression = ecStat.regression('linear', ecs, -1)

  for (let i = 0; i < myRegression.points.length; i++) {
    const p = myRegression.points[i]
    myRegression.points[i] = [x[i], p[1]]
  }

  const color = ['#5470c6']
  const legends = ['Stock']
  const series = [{
    name: 'Stock',
    type: 'line',
    smooth: false,
    symbol: 'none',
    data: pageCtl.report2Data.yAxis
  }] as any

  if (pageCtl.report2Data.pAxis && pageCtl.report2Data.pAxis.length > 0) {
    legends.push('Projection')
    color.push('#91cc75')
    series.push({
      name: 'Projection',
      type: 'line',
      smooth: false,
      symbol: 'none',
      data: pageCtl.report2Data.pAxis,
      lineStyle: {
        width: 2,
        type: 'dashed'
      }
    })
  }

  color.push('#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc')

  legends.push('AMU', 'SOH Covr.Day', '[Linear]')
  series.push({
    name: 'SOH Covr.Day',
    type: 'line',
    smooth: false,
    symbol: 'none',
    yAxisIndex: 1,
    data: pageCtl.report2Data.cAxis
  }, {
    name: 'AMU',
    type: 'line',
    smooth: false,
    symbol: 'none',
    data: pageCtl.report2Data.aAxis
  }, {
    color: 'gray',
    name: '[Linear]',
    type: 'line',
    smooth: true,
    symbol: 'none',
    yAxisIndex: 1,
    data: myRegression.points,
    lineStyle: {
      width: 1,
      type: 'dashed',
      color: 'gray'
    },
    markPoint: {
      itemStyle: {
        color: 'transparent'
      },
      label: {
        show: true,
        position: 'left',
        formatter: myRegression.expression,
        color: 'gray',
        fontSize: 14
      },
      data: [{
        coord: myRegression.points[myRegression.points.length - 1]
      }]
    }
  })

  if (pageCtl.conditions.priceReference) {
    const prName = 'Stock(Price:' + pageCtl.conditions.priceReference + ')'
    legends.push(prName)
    color.push('#e7dd6f')
    series.push({
      name: prName,
      type: 'line',
      smooth: false,
      symbol: 'none',
      data: pageCtl.report2Data.prAxis,
      lineStyle: {
        width: 2,
        type: 'dashed'
      }
    })
  }

  if (pageCtl.report2Data.legends) {
    for (let i = 0; i < pageCtl.report2Data.legends.length; i++) {
      const key = pageCtl.report2Data.legends[i]
      legends.push(key)

      series.push({
        name: key,
        type: 'line',
        smooth: false,
        stack: 'tooltips',
        symbol: 'none',
        areaStyle: {},
        data: pageCtl.report2Data['yAxis' + (i + 2)]
      })
    }
  }

  return {
    color,
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          if (params[i].seriesName === 'Projection' && (params[i].value === null || typeof params[i].value === 'undefined')) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:11.5rem;">')
          const marker = params[i].marker

          tip.push(marker)
          if (params[i].seriesName.indexOf('Price:') !== -1) {
            tip.push(params[i].seriesName)
          } else {
            tip.push(params[i].seriesName.replace(/1/g, ''))
          }
          tip.push('<span style="float: right">')
          if (typeof value === 'object') {
            const tv = value[2] - value[1]
            tip.push((tv > 0 ? '+' : '') + $shortenNumber(tv))
          } else {
            tip.push($shortenNumber(value))
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: legends,
      selected: {
        AMU: false,
        'SOH Covr.Day': false,
        '[Linear]': false
      }
    }),
    title: {
      text: 'Inventory Trend Graph' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: pageCtl.report2Data.xAxis
    },
    yAxis: [{
      type: 'value',
      scale: (pageCtl.report2Data.legends || []).length < 2,
      axisLabel: {
        margin: 10,
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    }, {
      type: 'value',
      scale: true,
      axisLabel: { // y轴刻度
        show: false
      },
      axisLine: { // y轴
        show: false
      },
      axisTick: { // y轴刻度线
        show: false
      },
      splitLine: { // 网格线
        show: false
      }
    }],
    series
  }
}
)

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.inventoryFilterOpts, 'VENDOR_CODE', 'STARS_DESCRIPTION', 'MATERIAL')
})

const initFilter = () => {
  const now = new Date()
  const start = new Date(now.getFullYear(), now.getMonth() - 2, 1)
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.dateRange = [$dateFormatter(start, 'yyyy/MM/dd'), $dateFormatter(end, 'yyyy/MM/dd')]
  pageCtl.conditions.report4Date = $dateFormatter(end, 'yyyy/MM/dd')

  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/structure/query_filters'
  }).then((body) => {
    pageCtl.inventoryFilterOpts = body.cascader
    pageCtl.priceReferenceOpts = body.priceReferenceOpts
    pageCtl.projectionVersionOpts = body.projectionVersionOpts
    pageCtl.top20FilterOpts = body.top20FilterOpts
    if (pageCtl.projectionVersionOpts.length > 0) {
      pageCtl.conditions.projectionVersion = pageCtl.projectionVersionOpts[0]
    }
    // this.$setDefaultMaterialOwner(this.inventoryFilterOpts, this.conditions.$scpFilter.cascader)
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}
const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/inventory/structure/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/inventory/structure/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  report3TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/structure/query_report3_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3ColumnNames = body
    pageCtl.report3Columns = parseReport3Columns()
    report3TableRef.value.clearAndSearch()
  }).catch((error) => {
    console.log(error)
  })
}
const searchReport4 = () => {
  report4TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/structure/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4TableSettings = parseReport4Columns()
    pageCtl.report4DetailsColumns = parseReport4DetailColumns()
    nextTick(() => {
      pageCtl.report4Data = body
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    report4TableRef.value.setLoading(false)
  })
}

const parseReport3Columns = () => {
  const result = [] as any
  const selectColumns = _report3SelectedColumn.value
  if (selectColumns.length === 0) {
    result.push({
      data: 'ENTITY',
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    }, {
      data: 'PLANT_TYPE'
    })
  } else {
    for (let i = 0; i < selectColumns.length; i++) {
      if (i === 0) {
        result.push({
          data: selectColumns[i],
          render: (hotInstance, td, row, column, prop, value) => {
            if (value === 'Total') {
              td.style.fontWeight = 'bold'
            }
            td.innerHTML = value
          }
        })
      } else {
        result.push({
          data: selectColumns[i]
        })
      }
    }
  }

  for (let i = 0; i < pageCtl.conditions.report3ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report3ColumnNames[i],
      data: '\'' + pageCtl.conditions.report3ColumnNames[i] + '\'_TOTAL',
      type: 'numeric',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (value) {
          td.innerHTML = $thousandBitSeparator(value, 0)
        } else {
          td.innerHTML = value
        }
      }
    })
  }
  return result
}
const parseReport4Columns = () => {
  const columns = ['<1W', '1W-2W', '2W-3W', '3W-4W', '4W-2M', '2M-3M', '3M-6M', '6M-1Y', '1Y-2Y', '>2Y', 'No Demand']
  const header = [] as any
  const nestedHeader = [] as any

  for (let i = 0; i < _report4SelectedColumn.value.length; i++) {
    header.push({
      data: _report4SelectedColumn.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  nestedHeader.push({
    colspan: _report4SelectedColumn.value.length
  })
  for (let i = 0; i < columns.length; i++) {
    const c = columns[i]
    header.push({
      title: 'Value',
      data: '\'' + c + '\'_TOTAL',
      render: renderNumber
    }, {
      title: '%',
      data: '\'' + c + '\'_TOTAL_P',
      render: renderPercent
    })
    nestedHeader.push({
      label: c,
      colspan: 2
    })
  }
  header.push({
    title: 'Value',
    data: 'Total',
    render: renderNumber
  })
  header.push({
    title: '%',
    data: 'Total_P',
    render: renderNumber
  })
  nestedHeader.push({
    label: 'Total',
    colspan: 2
  })

  nestedHeader.push({
    colspan: 1
  })

  return {
    header,
    nestedHeader: [nestedHeader]
  }
}
const parseReport4DetailColumns = () => {
  return [
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'AMU_AMF_ONE_MM', title: (pageCtl.conditions.report4ConvType + ' ONE MM'), type: 'numeric' },
    { data: 'UU_STOCK', type: 'numeric' },
    { data: 'STOCK_IN_QI', title: 'Stock IN QI', type: 'numeric' },
    { data: 'RESTRICTED_STOCK', type: 'numeric' },
    { data: 'BLOCKED_STOCK', type: 'numeric' },
    { data: 'RETURNS_STOCK', type: 'numeric' },
    { data: 'INTER_STK_TRANSFER', type: 'numeric' },
    { data: 'WIP_QTY', title: 'WIP Qty', type: 'numeric' },
    { data: 'WIP_VALUE', title: 'WIP Value', type: 'numeric' },
    { data: 'GIT_QTY', type: 'numeric' },
    { data: 'GIT_VALUE', type: 'numeric' },
    { data: 'PROPOSED_FIN_PROV_QTY', type: 'numeric' },
    { data: 'PROPOSED_FIN_PROV_VALUE', type: 'numeric' },
    { data: 'OPEN_PO', title: 'Open PO', type: 'numeric' },
    { data: 'UD_<7CD', title: 'UD_<7CD', type: 'numeric' },
    { data: 'UD_<30CD', title: 'UD_<30CD', type: 'numeric' },
    { data: 'UD_>30CD', title: 'UD_>30CD', type: 'numeric' },
    { data: 'SELECTED_SUMMARY', title: '[Selected Summary]' },
    { data: 'STOCK_AVAILABLE_DAY' },
    { data: 'PLANNED_DELIV_TIME' },
    { data: 'PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'LOCAL_BU', title: 'Local BU' },
    { data: 'BU', title: 'BU' },
    { data: 'CLUSTER_NAME' },
    { data: 'ENTITY' },
    { data: 'MRP_CONTROLLER', title: 'MRP Controller' },
    { data: 'VENDOR_CODE' },
    { data: 'MATERIAL_TYPE' },
    { data: 'MAT_PRICING_GROUP' },
    { data: 'MOQ_COV_RANGE' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MATERIAL_CRITICALITY' },
    { data: 'SSS_COVERAGE' },
    { data: 'XBOARD' },
    { data: 'XBOARD_CONTROLLER' },
    { data: 'PLANT_TYPE' },
    { data: 'MOVING_AVERAGE_P' },
    { data: 'PRICE_UNIT' },
    { data: 'STANDARD_PRICE' }
  ]
}
const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}
const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}

const afterReport3Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report3SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report3SelectedDate = ''
  }

  const selectColumns = _report3SelectedColumn.value

  const selectedValue = [] as any
  for (let i = 0; i < selectColumns.length; i++) {
    selectedValue.push(r[selectColumns[i]])
  }
  pageCtl.conditions.report3SelectedValue = selectedValue

  let title = $join(pageCtl.conditions.report3SelectedDate, ...selectedValue)
  if (title) {
    title = ' [' + title + ']'
  }

  pageCtl.report3DetailsTitle = 'View Details' + title
}

const afterReport4Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report4SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report4SelectedDate = ''
  }

  const selectColumns = _report4SelectedColumn.value

  const selectedValue = [] as any
  for (let i = 0; i < selectColumns.length; i++) {
    selectedValue.push(r[selectColumns[i]])
  }
  pageCtl.conditions.report4SelectedValue = selectedValue

  let title = $join(pageCtl.conditions.report4SelectedDate, ...selectedValue)
  if (title) {
    title = ' [' + title + ']'
  }

  pageCtl.report4DetailsTitle = 'View Details' + title
}
const renderNumber = (hotInstance, td, row, column, prop, value) => {
  if ($isEmpty(value) === false) {
    td.innerHTML = $thousandBitSeparator(value, 0)
    td.style.textAlign = 'right'
    const rd = hotInstance.getSourceDataAtRow(row)
    if (rd[_report4SelectedColumn.value[0]] === 'Total') {
      td.style.fontWeight = 'bold'
    }
  } else {
    td.innerHTML = value
  }
}
const renderPercent = (hotInstance, td, row, column, prop, value) => {
  td.innerHTML = value
  td.style.textAlign = 'right'
  const rd = hotInstance.getSourceDataAtRow(row)
  if (rd[_report4SelectedColumn.value[0]] === 'Total') {
    td.style.fontWeight = 'bold'
  }
}
</script>
