<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="6">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.deliveryFilterOpts" :filter-base="['UD_HIST']"
                        :after-apply="search"/>
          </el-col>
          <!--type-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Moving Average Price', 'Moving Average Price HKD', 'Net Net Price', 'Net Net Price HKD', 'Net Price', 'Line']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['dateRange','selectedReport2Date','selectedReport3Date']"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report4" style="height: 350px">
              <scp-subscript id="IUDS" ref="IUDSRef"/>
              <div class="front">
                <chart v-contextmenu:report4ContextmenuRef :height="350" :option="_report4Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable
                                 style="width:calc(100% - 100px)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable
                                 style="width:calc(100% - 100px)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable
                                 style="width:calc(100% - 100px)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." clearable filterable
                                 style="width:calc(100% - 100px)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." clearable filterable
                                 style="width:calc(100% - 100px)">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select..."
                                 style="width:calc(100% - 100px)">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Date</el-col>
                    <el-col :span="18">
                      <el-date-picker v-model="pageCtl.conditions.report4DateRange"
                                      type="date"
                                      size="small" style="width:calc(100% - 100px)" :clearable="false" format="YYYY/MM/DD"
                                      value-format="YYYY/MM/DD">
                      </el-date-picker>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report4Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report4TooltipsOpts"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></el-checkbox>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button @click="IUDSRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="IUDSRef.toggleView();searchReport4()">Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report1" style="height: 350px">
              <scp-subscript id="IUDP" ref="IUDPRef"/>
              <div class="front">
                <chart ref="report1ChartRef" :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Options</div>
                  <el-radio-group v-model="pageCtl.conditions.selectedOptions" style="height: 280px;overflow: auto">
                    <el-radio v-for="item in _pivotColumns" :key="item"
                              :value="item">{{ item }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.tooltips">
                    <el-checkbox v-for="item in pageCtl.report1TooltipsOpts" :key="item" :label="item"
                                 :value="item"></el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="box">
                  <div class="box-title">Date</div>
                  <el-date-picker v-model="pageCtl.conditions.report1DateRange"
                                  type="date"
                                  size="small" style="width:calc(100% - 100px)" :clearable="false" format="YYYY/MM/DD"
                                  value-format="YYYY/MM/DD">
                  </el-date-picker>

                  <div class="box-footer">
                    <el-button @click="IUDPRef.toggleView()">Back</el-button>
                    <el-button type="primary" @click="IUDPRef.toggleView();searchReport1()">Search</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.report2">
              <scp-subscript id="IUDT"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report2Type" size="small">
                    <el-option
                        v-for="item in pageCtl.conditions.report2TypeOpts"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.dateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="pageCtl.pickerOptions"
                      style="width: calc(100% - 25px)">
                  </el-date-picker>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport2">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report2ChartRef" :height="320" :option="_report2Opt"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="IUDM" ref="IUDMRef"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-date-picker
                      style="width: var(--scp-input-width)"
                      v-model="pageCtl.conditions.report3Date"
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      type="date"
                      :clearable="false"
                      placeholder="Date">
                  </el-date-picker>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3Type" size="small" placeholder="Columns" clearable filterable multiple
                             collapse-tags>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-input-number v-model="pageCtl.conditions.report3Length" size="small" :precision="0" :step="5" :max="1000" :min="0"
                                   style="width: var(--scp-input-width) !important;"/>
                </el-col>
                <el-col :span="3">
                  <el-tooltip class="item" effect="light" content="Order By" placement="bottom-end">
                    <el-select v-model="pageCtl.conditions.report3OrderType" size="small" placeholder="Order by" multiple clearable
                               collapse-tags>
                      <el-option
                          v-for="item in ['0-1D', '1-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M', '3-6M', '>6M']"
                          :key="item"
                          :label="item"
                          :value="item"/>
                    </el-select>
                  </el-tooltip>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  :params="pageCtl.conditions"
                  :fixed-columns-left="1"
                  :max-height="400"
                  ref="report3TableRef"
                  :columns="pageCtl.report3Columns"
                  :pagging="false"
                  :filters="false"
                  :column-sorting="false"
                  :dropdown-menu="false"
                  :context-menu-items="pageCtl.report3ContextItems"
                  :after-select="afterReport3Select"
                  url="/inventory/delivery/query_report3"
                  download-url="/inventory/delivery/download_report3"/>
              <div class="clearfix"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- report1 chart / table-->
    <scp-draggable-resizable v-model="pageCtl.report1DetailsVisible" h="650px" w="60vw" :title="pageCtl.report1DetailsTitle">
      <template v-slot="{ height }">
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report1Details">
            <chart ref="report1DetailsChartRef" :style="{width: pageCtl.pageWidth, height:'250px'}" :option="_report1DetailsOpt"/>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <scp-table ref="report1DetailsTableRef"
                       :lazy="true"
                       url="/inventory/delivery/query_report1_details_table"
                       download-url="/inventory/delivery/download_report1_details_table"
                       :max-height="height - 350"
                       :params="pageCtl.conditions"/>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>

    <!-- report4 table -->
    <v-contextmenu ref="report4ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const IUDSRef = ref()
const IUDPRef = ref()
const report1ChartRef = ref()
const report2ChartRef = ref()
const IUDMRef = ref()
const report3TableRef = ref()
const report1DetailsChartRef = ref()
const report1DetailsTableRef = ref()
const report4ContextmenuRef = ref()

const viewReport3Details = () => {
  $viewDetails({
    url: '/inventory/delivery/query_report3_details',
    durl: '/inventory/delivery/download_report3_details',
    params: pageCtl.conditions,
    title: pageCtl.report3DetailsTitle
  })
}

const pageCtl = reactive({
  pageWidth: document.body.clientWidth * 0.6 - 20 + 'px',
  loading: {
    filter: false,
    report1: false,
    report1Details: false,
    report2: false,
    report3: false,
    report4: false
  },
  report4TooltipsOpts: ['DELIVERY_QUANTITY', 'DELIVERY_VALUE', 'DELIVERY_NET_VALUE', 'DELIVERY_COST_VALUE', 'UU_STOCK_QTY', 'UU_STOCK_VALUE', 'UU_STOCK_SALES_VALUE', 'OPEN_SO_QTY', 'OPEN_SO_COST_VALUE', 'OPEN_SO_SALES_VALUE'],
  conditions: {
    dateRange: [$dateFormatter(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), 'yyyy/MM/dd'), $dateFormatter(new Date(), 'yyyy/MM/dd')],
    selectedTreePath: '',
    $scpFilter: {
      cascader: [],
      filter: []
    },
    report4DateRange: '',
    report1DateRange: '',
    report4Tooltips: [],
    type: 'Net Net Price',
    selectedOptions: 'PLANT_CODE',
    tooltips: [],
    selectedReport1Type: '' as any,
    selectedReport2Date: '',
    selectedReport2Type: '',
    report2Type: 'UD_AGING_RANGE',
    report2SeriesType: 'line',
    report2TypeOpts: ['UD_AGING_RANGE', 'PLANT_TYPE'],
    report3Date: '',
    report3Length: 10,
    report3Type: ['SOLD_TO_SHORT_NAME'],
    report3OrderType: ['0-1D', '1-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M', '3-6M', '>6M'],
    report3Columns: ['SOLD TO NAME'],
    selectedReport3Date: '',
    selectedReport3Type: '',
    report3DateColumns: [],
    leafDepth: 1,
    level1: 'ENTITY',
    level2: 'PRODUCT_LINE',
    level3: 'MRP_CONTROLLER',
    level4: 'SOLD_TO_SHORT_NAME',
    level5: 'SHIP_TO_SHORT_NAME',
    selectedLegend: {
      '0-1D': true,
      '1-2M': true,
      '1-3D': true,
      '2-3M': true,
      '3-6M': true,
      '3-7D': true,
      '7-14D': true,
      '14-30D': true,
      '>6M': true
    }
  },
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  deliveryFilterOpts: [],
  report1Data: [],
  report1DetailsVisible: false,
  report1DetailsData: [],
  report1TooltipsMap: {},
  report1DetailsTooltipsMap: {},
  report1DetailsTitle: '',
  report1TooltipsOpts: ['DELIVERY_QUANTITY', 'DELIVERY_VALUE', 'DELIVERY_COST_VALUE', 'UU_STOCK_QTY', 'UU_STOCK_VALUE', 'OPEN_SO_QTY', 'OPEN_SO_COST_VALUE', 'OPEN_SO_SALES_VALUE'],
  report2Data: {} as any,
  report3DetailsTitle: '',
  report3ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport3Details
    },
    view_split0: { name: '---------' }
  },
  report3Columns: [],
  pickerOptions: {
    shortcuts: [{
      text: 'Last 3 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        start.setMonth(start.getMonth() - 3)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Last 6 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        start.setMonth(start.getMonth() - 6)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  report4Data: []
})

onMounted(() => {
  initPage()
  report1ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.selectedReport1Type = [params.name]
    pageCtl.report1DetailsTitle = 'View Details [' + params.name + ']'
    pageCtl.report1DetailsVisible = true
    report1DetailsTableRef.value.search()
    searchReport1DetailsChart()
  })
  report1ChartRef.value.chart().on('legendselectchanged', (params) => {
    pageCtl.conditions.selectedLegend = params.selected
  })
  report2ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.selectedReport2Date = params.name
    pageCtl.conditions.selectedReport2Type = params.seriesName

    $viewDetails({
      url: '/inventory/delivery/query_report2_details',
      durl: '/inventory/delivery/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + params.name + ', ' + params.seriesName + ']'
    })
  })
  report2ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report2SeriesType = obj.currentType
    }
  })
})

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.report4DateRange = $dateFormatter(end, 'yyyy/MM/dd')
  pageCtl.conditions.report1DateRange = $dateFormatter(end, 'yyyy/MM/dd')
  pageCtl.conditions.report3Date = $dateFormatter(end, 'yyyy/MM/dd')
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/inventory/delivery/init_page'
  }).then((body) => {
    pageCtl.deliveryFilterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}
const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport1Overall()
}
const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport1Overall()
}
const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/inventory/delivery/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
    const map = {}
    for (let i = 0; i < pageCtl.report1Data.length; i++) {
      const e: any = pageCtl.report1Data[i]
      map[e.name] = e.tooltips
    }
    pageCtl.report1TooltipsMap = map
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
const searchReport1DetailsChart = () => {
  pageCtl.loading.report1Details = true
  $axios({
    method: 'post',
    url: '/inventory/delivery/query_report1_details_chart',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1DetailsData = body
    const map = {}
    for (let i = 0; i < pageCtl.report1DetailsData.length; i++) {
      const e: any = pageCtl.report1DetailsData[i]
      map[e.name] = e.tooltips
    }
    pageCtl.report1DetailsTooltipsMap = map
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1Details = false
  })
}
const searchReport1Overall = () => {
  $viewDetails({
    url: '/inventory/delivery/query_report1_overall',
    durl: '/inventory/delivery/download_report1_overall',
    params: pageCtl.conditions,
    title: pageCtl.report1DetailsTitle
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/inventory/delivery/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  report3TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/inventory/delivery/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report3DateColumns = body
    pageCtl.report3Columns = parseReport3Columns()
    nextTick(() => {
      report3TableRef.value.search()
    })
  }).catch((error) => {
    console.log(error)
  })
}
const parseReport3Columns = () => {
  const c = ['>6M', '3-6M', '2-3M', '1-2M', '14-30D', '7-14D', '3-7D', '1-3D', '0-1D']
  const columns = [] as any
  let columnName
  if (pageCtl.conditions.report3Type.length === 0) {
    columnName = ['SOLD_TO_SHORT_NAME']
  } else {
    columnName = pageCtl.conditions.report3Type
  }
  if (columnName.length > 1) {
    for (let i = 0; i < (columnName.length - 1); i++) {
      columns.push({
        data: columnName[i],
        render: (hotInstance, td, row, column, prop, value) => {
          td.style.fontWeight = 'bold'
          td.innerHTML = value
        }
      })
    }
    columns.push({
      data: 'KEY',
      title: columnName[columnName.length - 1],
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.fontWeight = 'bold'
        td.innerHTML = value
      }
    })
  } else {
    columns.push({
      data: 'KEY',
      title: columnName[0],
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.fontWeight = 'bold'
        td.innerHTML = value
      }
    })
  }
  for (let i = 0; i < c.length; i++) {
    columns.push({
      data: '\'' + c[i] + '\'_TOTAL',
      title: c[i],
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        if (value) {
          const rd = hotInstance.getSourceDataAtRow(row)
          if (rd.KEY !== null) {
            if (rd.KEY.indexOf('Total') !== -1) {
              td.style.fontWeight = 'bold'
            }
          }
          if (rd.KEY === '%') {
            td.style.fontWeight = 'bold'
            td.innerHTML = $thousandBitSeparator(value, 1)
          } else {
            td.innerHTML = $thousandBitSeparator(value, 0)
          }
        } else {
          td.innerHTML = value
        }
      }
    })
  }
  columns.push({
    data: 'TOTAL_UD',
    title: 'TOTAL_UD',
    render: (hotInstance, td, row, column, prop, value) => {
      td.style.textAlign = 'right'
      if (value) {
        const rd = hotInstance.getSourceDataAtRow(row)
        if (rd.KEY !== null) {
          if (rd.KEY.indexOf('Total') !== -1) {
            td.style.fontWeight = 'bold'
          }
        }
        if (rd.KEY === '%') {
          td.style.fontWeight = 'bold'
          td.innerHTML = $thousandBitSeparator(value, 1)
        } else {
          td.innerHTML = $thousandBitSeparator(value, 0)
        }
      } else {
        td.innerHTML = value
      }
    }
  })
  return columns
}
const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/inventory/delivery/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}
const afterReport3Select = (r, rc, c) => {
  if (c !== null) {
    if (c.indexOf('\'') !== -1) {
      pageCtl.conditions.selectedReport3Date = c.split('\'')[1]
    } else {
      pageCtl.conditions.selectedReport3Date = ''
    }
  }
  let value = r.KEY
  if (value !== null) {
    if (value.indexOf('-Total') !== -1 || value === '%') {
      value = ''
    }
  }
  pageCtl.conditions.selectedReport3Type = value
  let a = ''
  let title = ''
  for (let i = 0; i < pageCtl.conditions.report3Type.length - 1; i++) {
    a = a + r[pageCtl.conditions.report3Type[i].toString()] + ','
  }
  if (pageCtl.conditions.selectedReport3Type || pageCtl.conditions.selectedReport3Date) {
    title = '[' + a + pageCtl.conditions.selectedReport3Type + (pageCtl.conditions.selectedReport3Type && pageCtl.conditions.selectedReport3Date ? ' ,' : '') + pageCtl.conditions.selectedReport3Date + ']'
  }
  pageCtl.report3DetailsTitle = 'View Details ' + title
}

const _report1Opt = computed(() => {
  const colorSettings = {
    '0-1D': '#2c821d',
    '1-3D': '#418919',
    '3-7D': '#639613',
    '7-14D': '#8aa40b',
    '14-30D': '#e2b600',
    '1-2M': '#dd7619',
    '2-3M': '#d25924',
    '3-6M': '#c7402d',
    '>6M': '#c12e34'
  }
  const colors = [] as any
  const data = $deepClone(pageCtl.report1Data)
  const legend = data.length > 0 ? ['0-1D', '1-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M', '3-6M', '>6M'] : []
  for (let i = 0; i < data.length; i++) {
    const name = data[i].name
    colors.push(colorSettings[name])
    data[i].label = { color: colorSettings[name] }
  }
  return {
    color: colors,
    title: {
      text: 'Unissue Delivery Structure by Delivery Range' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    toolbox: $toolbox({
      opts: [],
      view: {
        url: '/inventory/delivery/query_report1_overall',
        durl: '/inventory/delivery/download_report1_overall',
        params: pageCtl.conditions,
        title: () => {
          const selectResult = [] as any
          let item = ''
          for (item in pageCtl.conditions.selectedLegend) {
            if (pageCtl.conditions.selectedLegend[item].toString() === 'true'.toString()) {
              selectResult.push(item)
            }
          }
          if (selectResult.length === 0) {
            pageCtl.conditions.selectedReport1Type = ''
            selectResult.push('Total')
          } else {
            pageCtl.conditions.selectedReport1Type = selectResult
          }
          pageCtl.report1DetailsTitle = 'View Selected Details [' + selectResult + ']'
          return pageCtl.report1DetailsTitle
        }
      }
    }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width:9.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')

        const tooltips = pageCtl.report1TooltipsMap[params.name]
        const ks = [] as any
        for (const k in tooltips) {
          if (tooltips.hasOwnProperty(k)) {
            ks.push(k)
          }
        }
        ks.sort((e1, e2) => e1 > e2 ? 1 : -1)
        let first = true
        for (let i = 0; i < ks.length; i++) {
          const k = ks[i]
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ type: 'pie', data: legend }),
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data
      }
    ]
  }
})
const _report1DetailsOpt = computed(() => {
  return {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:9.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')

        const tooltips = pageCtl.report1TooltipsMap[params.name]
        const ks = [] as any
        for (const k in tooltips) {
          if (tooltips.hasOwnProperty(k)) {
            ks.push(k)
          }
        }
        ks.sort((e1, e2) => e1 > e2 ? 1 : -1)
        let first = true
        for (let i = 0; i < ks.length; i++) {
          const k = ks[i]
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: pageCtl.report1DetailsData.map((e: any) => e.name) }),
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['50%', '50%'],
        data: pageCtl.report1DetailsData
      }
    ]
  }
})

const decodeOrder = {
  '0-1D': 0,
  '1-3D': 1,
  '3-7D': 2,
  '7-14D': 3,
  '14-30D': 4,
  '1-2M': 5,
  '2-3M': 6,
  '3-6M': 7,
  '>6M': 8
}

const _report2Opt = computed(() => {
  const legend = [] as any

  const colorSettings = {
    '0-1D': '#2c821d',
    '1-3D': '#418919',
    '3-7D': '#639613',
    '7-14D': '#8aa40b',
    '14-30D': '#e2b600',
    '1-2M': '#dd7619',
    '2-3M': '#d25924',
    '3-6M': '#c7402d',
    '>6M': '#c12e34'
  }

  const sortedKeys = Object.keys(pageCtl.report2Data).sort((a, b) => {
    return (decodeOrder[a] ?? 9) - (decodeOrder[b] ?? 9)
  })

  const series = [] as any
  let i = 0
  for (const key of sortedKeys) {
    if (pageCtl.report2Data.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report2SeriesType,
        smooth: false,
        itemStyle: {
          color: colorSettings[key]
        },
        stack: 'sum',
        areaStyle: {},
        data: pageCtl.report2Data[key] || []
      })
      i = i + 1
    }
  }
  return {
    title: {
      text: 'Evolution of Unissue Delivery Structure by Date' +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : ''),
      triggerEvent: true
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        const p = params.reverse()
        let total = 0
        let marker = ''
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          tip.push('<div style="width:9.5rem;">')
          marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span></div>')

          total += value
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push(marker)
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report2SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report2Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '40%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series
  }
})
const _report4Opt = computed(() => {
  const rootName = 'UD'
  return {
    title: {
      text: 'Unissue Delivery Structure by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)

          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)

            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted green;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report4Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})
const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.deliveryFilterOpts)
})

</script>
