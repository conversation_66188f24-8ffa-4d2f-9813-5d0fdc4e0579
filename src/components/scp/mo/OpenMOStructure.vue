<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="3">
            <el-select
                v-model="pageCtl.conditions.dateType" size="small">
              <el-option label="By Snapshot Date" value="BY_SNAPSHOT_DATE"/>
              <el-option label="By Req Date" value="BY_REQ_DATE"/>
              <el-option label="By Reschedule Date" value="BY_RESCHEDULE_DATE"/>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-tooltip effect="light" placement="bottom">
              <div>
                <el-date-picker
                    style="width: calc(100% - 32px)"
                    v-model="pageCtl.conditions.dateRange"
                    type="daterange"
                    unlink-panels
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    range-separator="to"
                    :clearable="false"
                    :start-placeholder="camelCaseStartPlaceholder(pageCtl.conditions.dateType) + ' Start'"
                    :end-placeholder="camelCaseStartPlaceholder(pageCtl.conditions.dateType) + ' End'"/>
              </div>
              <template #content>
                {{ camelCaseStartPlaceholder(pageCtl.conditions.dateType) + ' Range' }}
              </template>
            </el-tooltip>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" cascader-base-url="/mo/open_mo_structure/query_filters" :filter-base="['OPEN_MO_STRUCTURE_V']"
                        :after-apply="search" :after-cascader-loaded="afterFilterLoaded"/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.resultType" size="small">
              <el-option
                  v-for="item in ['Quantity', 'Value RMB', 'Line']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['specificDate', 'specialLeftDateRange', 'specialRightDateRange', 'dateRange', 'report1SpecificDate', 'report2SpecificDate']"
                        :afterExpand="moreConditions"/>
          </el-col>
        </el-row>
        <el-row class="search-box" v-show="pageCtl.conditionsExpanded">
          <el-col :span="3" v-show="pageCtl.conditions.dateType !== 'BY_SNAPSHOT_DATE'">
            <el-tooltip effect="light" placement="bottom" content="Snapshot Date">
              <div>
                <el-date-picker
                    style="width: calc(100% - 0.5rem)"
                    v-model="pageCtl.conditions.specificDate"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    type="date"
                    :clearable="false"
                    size="small"
                    placeholder="Snapshot Date"/>
              </div>
            </el-tooltip>
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="pageCtl.conditions.specialLeftDateType"
                size="small"
                style="width: 100% !important;"
                class="search-group-left">
              <el-option v-for="item in pageCtl.conditions.specialLeftDateTypeOpts.filter(option => option !== pageCtl.conditions.specialRightDateType)"
                         :value = "item"
                         :label="item"
                         :key="item"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-tooltip effect="light" placement="bottom">
              <div>
                <el-date-picker
                    style="width: calc(100% - 32px)"
                    v-model="pageCtl.conditions.specialLeftDateRange"
                    type="daterange"
                    unlink-panels
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    range-separator="to"
                    clearable
                    :start-placeholder="camelCaseStartPlaceholder(pageCtl.conditions.specialLeftDateType) + ' Start'"
                    :end-placeholder="camelCaseStartPlaceholder(pageCtl.conditions.specialLeftDateType) + ' End'"/>
              </div>
              <template #content>
                {{ camelCaseStartPlaceholder(pageCtl.conditions.specialLeftDateType) + ' Range' }}
              </template>
            </el-tooltip>
          </el-col>
          <el-col :span="3">
            <el-select
                v-model="pageCtl.conditions.specialRightDateType"
                size="small"
                style="width: 100% !important;"
                class="search-group-left">
              <el-option v-for="item in pageCtl.conditions.specialRightDateTypeOpts.filter(option => option !== pageCtl.conditions.specialLeftDateType)"
                         :value = "item"
                         :label="item"
                         :key="item"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-tooltip class="item" effect="light" placement="bottom">
              <div>
                <el-date-picker
                    style="width: calc(100% - 32px)"
                    v-model="pageCtl.conditions.specialRightDateRange"
                    type="daterange"
                    unlink-panels
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    range-separator="to"
                    clearable
                    :start-placeholder="camelCaseStartPlaceholder(pageCtl.conditions.specialRightDateType) + ' Start'"
                    :end-placeholder="camelCaseStartPlaceholder(pageCtl.conditions.specialRightDateType) + ' End'"
                />
              </div>
              <template #content>
                {{ camelCaseStartPlaceholder(pageCtl.conditions.specialRightDateType) + ' Range' }}
              </template>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14" v-loading="pageCtl.loading.report1">
            <div class="subscript-container subscript-container-left" style="height: 350px">
              <scp-subscript id="MOSC" ref="report1SubRef"/>
              <div class="front">
                <chart v-contextmenu:report1ContextmenuRef :height="350" :option="_report1Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level1" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level2" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level3" size="small" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level4" size="small" placeholder="Select..." clearable
                                 filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.level5" size="small" placeholder="Select..." clearable
                                 filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select style="width: calc(100% - 5rem);" v-model="pageCtl.conditions.leafDepth" size="small" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Display Date</el-col>
                    <el-col :span="18">
                      <el-date-picker
                          style="width: calc(100% - 5rem);"
                          v-model="pageCtl.conditions.report1SpecificDate"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          type="date"
                          :clearable="false"
                          :disabled-date="disabledDate"
                          size="small"
                          placeholder="Date"/>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report1Tooltips"
                                     style="height:calc(100% - 70px);overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report1TooltipsOpts"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></el-checkbox>
                  </el-checkbox-group>
                  <div class="box-footer">
                    <el-button
                        @click="report1SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report1SubRef.toggleView();searchReport1()">
                      Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="10" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="MOSD" ref="report2SubRef"/>
              <div class="front">
                <chart ref="report2ChartRef" :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Pie Options</div>
                  <el-row>
                    <el-col :span="10">Display Date</el-col>
                    <el-col :span="14">
                      <el-date-picker
                          v-model="pageCtl.conditions.report2SpecificDate"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          type="date"
                          :clearable="false"
                          :disabled-date="disabledDate"
                          size="small"
                          placeholder="Date"/>
                    </el-col>
                  </el-row>
                  <div class="box-footer">
                    <el-button
                        @click="report2SubRef.toggleView()">
                      Back
                    </el-button>
                    <el-button
                        type="primary"
                        @click="report2SubRef.toggleView();searchReport2()">
                      Search
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="MOTD" ref="report3Sub"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select
                      v-model="pageCtl.conditions.report3SelectedColumn" size="small"
                      style="width: var(--scp-input-width) !important;" filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select
                      v-model="pageCtl.conditions.report3SelectedType"
                      size="small">
                    <el-option label="View by day" value="VIEW_BY_DAY"/>
                    <el-option label="View by week" value="VIEW_BY_WEEK"/>
                    <el-option label="View by month" value="VIEW_BY_MONTH"/>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select
                      v-model="pageCtl.conditions.report3ShowType"
                      size="small">
                    <el-option label="View by value" value="VIEW_BY_VALUE"/>
                    <el-option label="View by percentage" value="VIEW_BY_PERCENT"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report3ChartRef" height="400" :option="_report3Opt"/>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report4">
            <div class="subscript-container">
              <scp-subscript id="MOTB" ref="report4Sub"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report4SelectedColumns" size="small" placeholder="Columns"
                             multiple collapse-tags clearable filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select
                      v-model="pageCtl.conditions.report4ViewType"
                      size="small">
                    <el-option label="View by day" value="VIEW_BY_DAY"/>
                    <el-option label="View by week" value="VIEW_BY_WEEK"/>
                    <el-option label="View by month" value="VIEW_BY_MONTH"/>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button size="small" @click="searchReport4">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="report4TableRef"
                  url="/mo/open_mo_structure/query_report4"
                  download-url="/mo/open_mo_structure/download_report4"
                  :params="pageCtl.conditions"
                  :lazy="true"
                  :show-total="true"
                  :pagging="false"
                  :fixed-columns-left="_report4SelectedColumns.length"
                  :max-height="375"
                  :context-menu-items="pageCtl.report4ContextItems"
                  :after-select="afterReport4Select"
                  :columns="pageCtl.report4Columns"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- report1 contextmenu-->
    <v-contextmenu ref="report1ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>

  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $toFixed: any = inject('$toFixed')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $deepClone: any = inject('$deepClone')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $tooltip: any = inject('$echarts.tooltip')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1SubRef = ref()
const report2SubRef = ref()
const report1ContextmenuRef = ref()
const report2ChartRef = ref()
const report3ChartRef = ref()
const report4TableRef = ref()

const viewReport4Details = () => {
  let title = $join(
    pageCtl.conditions.report4SelectedDate,
    ...pageCtl.conditions.report4SelectedValues
  )
  if (title) {
    title = ' [' + title + ']'
  }

  $viewDetails({
    url: '/mo/open_mo_structure/query_report4_details',
    durl: '/mo/open_mo_structure/download_report4_details',
    params: pageCtl.conditions,
    title: 'View Details' + title
  })
}

const pageCtl = reactive({
  loading: {
    report1: false,
    report2: false,
    report2Overall: false,
    report3: false,
    report4: false
  },
  conditions: {
    specificDate: '',
    specialLeftDateType: 'RESCHEDULING_DATE',
    specialRightDateType: 'RELEASE_DATE',
    specialLeftDateRange: [],
    specialRightDateRange: [],
    specialLeftDateTypeOpts: ['MO_REQ_DATE', 'RELEASE_DATE', 'RESCHEDULING_DATE'] as any,
    specialRightDateTypeOpts: ['MO_REQ_DATE', 'RELEASE_DATE', 'RESCHEDULING_DATE'] as any,
    dateType: 'BY_SNAPSHOT_DATE',
    dateRange: [] as any,
    $scpFilter: {
      cascader: [
        ['PAST_DUE_INDICATOR', 'Y'],
        ['PLANT_CODE', 'A001'],
        ['PLANT_CODE', 'AX01'],
        ['PLANT_CODE', 'AX02'],
        ['PLANT_CODE', 'B001'],
        ['PLANT_CODE', 'B002'],
        ['PLANT_CODE', 'BG02'],
        ['PLANT_CODE', 'C001'],
        ['PLANT_CODE', 'E001'],
        ['PLANT_CODE', 'F001'],
        ['PLANT_CODE', 'G001'],
        ['PLANT_CODE', 'I001'],
        ['PLANT_CODE', 'I003'],
        ['PLANT_CODE', 'J001'],
        ['PLANT_CODE', 'K001'],
        ['PLANT_CODE', 'K003'],
        ['PLANT_CODE', 'L001'],
        ['PLANT_CODE', 'LH01'],
        ['PLANT_CODE', 'M001'],
        ['PLANT_CODE', 'MC01'],
        ['PLANT_CODE', 'N001'],
        ['PLANT_CODE', 'N002'],
        ['PLANT_CODE', 'O001'],
        ['PLANT_CODE', 'P001'],
        ['PLANT_CODE', 'PG01'],
        ['PLANT_CODE', 'R001'],
        ['PLANT_CODE', 'SA01'],
        ['PLANT_CODE', 'SP01'],
        ['PLANT_CODE', 'SP02'],
        ['PLANT_CODE', 'U001'],
        ['PLANT_CODE', 'WG01'],
        ['PLANT_CODE', 'WH01'],
        ['PLANT_CODE', 'X001'],
        ['PLANT_CODE', 'XA01'],
        ['PLANT_CODE', 'XA03'],
        ['PLANT_CODE', 'FS01'],
        ['PLANT_CODE', 'FS02']
      ],
      filter: []
    },
    resultType: 'Value RMB',
    level1: 'ENTITY',
    level2: 'BU',
    level3: 'MRP_CONTROLLER',
    level4: 'MRP_CONTROLLER',
    level5: 'VENDOR_NAME',
    leafDepth: 1,
    selectedTreePath: '',
    selectedLegend: {},
    report1Tooltips: [],
    report1SpecificDate: '',
    report2SpecificDate: '',
    report2SelectedValue: '' as any,
    report3SeriesType: 'line',
    report3ShowType: 'VIEW_BY_VALUE',
    report3SelectedColumn: 'DELIVERY_RANGE',
    report3SelectedType: 'VIEW_BY_DAY',
    report3SelectedValue: '',
    report3SelectedAxis: '',
    report4ColumnNames: [],
    report4SelectedColumns: ['BU', 'ENTITY'],
    report4SelectedValues: [],
    report4SelectedDate: '',
    report4ViewType: 'VIEW_BY_DAY',
    legendWidth: [] as any
  },
  conditionsExpanded: false,
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  report1Data: [],
  report1TooltipsOpts: ['DELIVERY_DEPTH', 'OPEN_MO_QTY', 'OPEN_MO_VALUE', 'CONFIRM_QTY', 'GR_QTY', 'GR_VALUE', 'UNIT_COST', 'REORDER_POINT', 'SAFETY_STOCK', 'AMU', 'AMF'],
  report2Data: [],
  selectLength: [0],
  report2TooltipsMap: {},
  report3Data: [] as any,
  report4Columns: [],
  report4ContextItems: {
    view_details: {
      name: 'View details',
      callback: viewReport4Details
    },
    view_split0: { name: '---------' }
  },
  filterOpts: []
})

onMounted(() => {
  initFilter()
  report2ChartRef.value.chart().on('legendselectchanged', (params) => {
    pageCtl.conditions.selectedLegend = params.selected
  })
  report2ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.report2SelectedValue = [params.name]

    $viewDetails({
      url: '/mo/open_mo_structure/query_report2_details',
      durl: '/mo/open_mo_structure/download_report2_details',
      params: pageCtl.conditions,
      title: 'View Details [' + params.name + ']'
    })
  })
  report3ChartRef.value.chart().on('dblclick', (obj) => {
    if ('series'.indexOf(obj.componentType) !== -1) {
      pageCtl.conditions.report3SelectedAxis = obj.name
      pageCtl.conditions.report3SelectedValue = obj.seriesName
    } else {
      pageCtl.conditions.report3SelectedAxis = obj.value
      pageCtl.conditions.report3SelectedValue = ''
    }
    $viewDetails({
      url: '/mo/open_mo_structure/query_report3_details',
      durl: '/mo/open_mo_structure/download_report3_details',
      params: pageCtl.conditions,
      title: 'View Details [' + [pageCtl.conditions.dateType, pageCtl.conditions.report3SelectedAxis, pageCtl.conditions.report3SelectedValue].join(', ') + ']'
    })
  })
  report3ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report3SeriesType = obj.currentType
    }
  })
})

const disabledDate = (time: Date) => {
  if (pageCtl.conditions.dateType === 'BY_SNAPSHOT_DATE') {
    return (time < new Date(pageCtl.conditions.dateRange[0])) || (time > new Date(pageCtl.conditions.dateRange[1]))
  } else {
    return null
  }
}

const camelCaseStartPlaceholder = (word : string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const initFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 7 * 9)
  pageCtl.conditions.specialLeftDateRange = []
  pageCtl.conditions.specialRightDateRange = []
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.specificDate = $dateFormatter(now, 'yyyy/MM/dd')
  pageCtl.conditions.report1SpecificDate = $dateFormatter(now, 'yyyy/MM/dd')
  pageCtl.conditions.report2SpecificDate = $dateFormatter(now, 'yyyy/MM/dd')
}

const afterFilterLoaded = (opts) => {
  pageCtl.filterOpts = opts
  searchRef.value.loadAndClick()
}

const initSelectedLegend = () => {
  pageCtl.conditions.selectedLegend = {
    'P 0-3D': false,
    'P 3-7D': false,
    'P 7-14D': false,
    'P 14-30D': false,
    'P 1-2M': false,
    'P 2-3M': false,
    'P 3-6M': false,
    'P >6M': false,
    'F 0-3D': false,
    'F 3-7D': false,
    'F 7-14D': false,
    'F 14-30D': false,
    'F 1-2M': false,
    'F 2-3M': false,
    'F 3-6M': false,
    'F >6M': false
  }
  for (const item in pageCtl.conditions.$scpFilter.cascader) {
    if (pageCtl.conditions.$scpFilter.cascader[item].includes('PAST_DUE_INDICATOR')) {
      if (pageCtl.conditions.$scpFilter.cascader[item].includes('N')) {
        pageCtl.conditions.selectedLegend['F 0-3D'] = true
        pageCtl.conditions.selectedLegend['F 3-7D'] = true
        pageCtl.conditions.selectedLegend['F 7-14D'] = true
        pageCtl.conditions.selectedLegend['F 14-30D'] = true
        pageCtl.conditions.selectedLegend['F 1-2M'] = true
        pageCtl.conditions.selectedLegend['F 2-3M'] = true
        pageCtl.conditions.selectedLegend['F 3-6M'] = true
        pageCtl.conditions.selectedLegend['F >6M'] = true
      } else if (pageCtl.conditions.$scpFilter.cascader[item].includes('Y')) {
        pageCtl.conditions.selectedLegend['P 0-3D'] = true
        pageCtl.conditions.selectedLegend['P 3-7D'] = true
        pageCtl.conditions.selectedLegend['P 7-14D'] = true
        pageCtl.conditions.selectedLegend['P 14-30D'] = true
        pageCtl.conditions.selectedLegend['P 1-2M'] = true
        pageCtl.conditions.selectedLegend['P 2-3M'] = true
        pageCtl.conditions.selectedLegend['P 3-6M'] = true
        pageCtl.conditions.selectedLegend['P >6M'] = true
      }
    }
  }
}

const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  initSelectedLegend()
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const sumValues = (node) => {
  // 如果有子节点，则递归计算子节点的 value 之和
  if (node.children && node.children.length > 0) {
    let sum = 0
    node.children.forEach(child => {
      sum += sumValues(child)
    })
    node.value = sum // 将子节点的和赋值给当前节点
    node.children.sort((a, b) => b.value - a.value)
  }
  return node.value || 0
}

const calculateValues = (tree) => {
  tree.forEach(node => sumValues(node))
  tree.sort((a, b) => b.value - a.value)
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/mo/open_mo_structure/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    calculateValues(body)
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/mo/open_mo_structure/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
    const map = {}
    for (let i = 0; i < pageCtl.report2Data.length; i++) {
      const e: any = pageCtl.report2Data[i]
      map[e.name] = e.tooltips
    }
    pageCtl.report2TooltipsMap = map
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/mo/open_mo_structure/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/mo/open_mo_structure/query_report4_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.report4ColumnNames = body
    pageCtl.report4Columns = parseReport4Columns()
    report4TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const parseReport4Columns = () => {
  const result = [] as any

  for (let i = 0; i < _report4SelectedColumns.value.length; i++) {
    result.push({
      data: _report4SelectedColumns.value[i],
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Total') {
          td.style.fontWeight = 'bold'
        }
        td.innerHTML = value
      }
    })
  }

  for (let i = 0; i < pageCtl.conditions.report4ColumnNames.length; i++) {
    result.push({
      title: pageCtl.conditions.report4ColumnNames[i],
      data: '\'' + pageCtl.conditions.report4ColumnNames[i] + '\'_TOTAL',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value ? $thousandBitSeparator(value, 0) : '0'
      }
    })
  }
  return result
}

const afterReport4Select = (r, rc, c) => {
  if (c.indexOf('\'') !== -1) {
    pageCtl.conditions.report4SelectedDate = c.split('\'')[1]
  } else {
    pageCtl.conditions.report4SelectedDate = ''
  }

  const selected = [] as any
  for (let i = 0; i < _report4SelectedColumns.value.length; i++) {
    let v = r[_report4SelectedColumns.value[i]]
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }
  pageCtl.conditions.report4SelectedValues = selected
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport2()
  searchReport3()
  searchReport4()
}

const _report1Opt = computed(() => {
  const rootName = 'MO'
  return {
    title: {
      text: 'Open MO Structure by Category'
    },
    toolbox: $toolbox({ opts: [] }),
    tooltip: $tooltip({
      callback: (e) => {
        pageCtl.selectedCurrentLevel = e.selectedCurrentLevel
        pageCtl.selectedParentLevel = e.selectedParentLevel
      }
    }, pageCtl.report1Data),
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report1Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report2Opt = computed(() => {
  const colorSettings = {
    'P >6M': '#c13033',
    'P 3-6M': '#c43730',
    'P 2-3M': '#c8412d',
    'P 1-2M': '#cc4c27',
    'P 14-30D': '#d25924',
    'P 7-14D': '#d7671f',
    'P 3-7D': '#e1990a',
    'P 0-3D': '#e1a506',
    'F 0-3D': '#c6b601',
    'F 3-7D': '#87a40c',
    'F 7-14D': '#749d10',
    'F 14-30D': '#629512',
    'F 1-2M': '#539017',
    'F 2-3M': '#448a1a',
    'F 3-6M': '#36851a',
    'F >6M': '#2c821d'
  }
  const colors = [] as any
  const data = $deepClone(pageCtl.report2Data)
  for (let i = 0; i < data.length; i++) {
    colors.push(colorSettings[data[i].name])
    data[i].label = { color: colorSettings[data[i].name] }
  }
  return {
    color: colors,
    title: {
      text: 'Open MO Structure by Delivery Range' + (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    toolbox: $toolbox({
      opts: [],
      view: {
        url: '/mo/open_mo_structure/query_report2_overall',
        durl: '/mo/open_mo_structure/download_report2_overall',
        params: pageCtl.conditions,
        title: () => {
          const selectResult = [] as any
          let item = ''
          for (item in pageCtl.conditions.selectedLegend) {
            if (pageCtl.conditions.selectedLegend[item].toString() === 'true'.toString()) {
              selectResult.push(item)
            }
          }
          if (selectResult.length === 0) {
            pageCtl.conditions.report2SelectedValue = ''
            selectResult.push('Total')
          } else {
            pageCtl.conditions.report2SelectedValue = selectResult
          }

          return 'View Details [' + selectResult + ']'
        }
      }
    }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:9.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')

        const tooltips = pageCtl.report2TooltipsMap[params.name]
        const ks = [] as any
        for (const k in tooltips) {
          if (tooltips.hasOwnProperty(k)) {
            ks.push(k)
          }
        }
        ks.sort((e1, e2) => e1 > e2 ? 1 : -1)
        let first = true
        for (let i = 0; i < ks.length; i++) {
          const k = ks[i]
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: {
      top: pageCtl.conditions.legendWidth[0],
      left: pageCtl.conditions.legendWidth[1],
      width: pageCtl.conditions.legendWidth[2]
    },
    series: [
      {
        type: 'pie',
        radius: pageCtl.conditions.legendWidth[3],
        center: [pageCtl.conditions.legendWidth[4], pageCtl.conditions.legendWidth[5]],
        data
      }
    ]
  }
})

watch(() => pageCtl.report2Data, () => {
  if (pageCtl.report2Data.length !== pageCtl.selectLength[pageCtl.selectLength.length - 1]) {
    if (pageCtl.report2Data.length > 8 || pageCtl.report2Data.length === 0) {
      pageCtl.conditions.legendWidth = ['25%', '70%', '30%', '60%', '38%', '50%']
    } else {
      pageCtl.conditions.legendWidth = ['25%', '72%', '15%', '60%', '38%', '50%']
    }
    pageCtl.selectLength = [16, pageCtl.report2Data.length]
  }
})

watch(() => pageCtl.conditions.dateRange, () => {
  if (pageCtl.conditions.dateType === 'BY_SNAPSHOT_DATE') {
    if (new Date(pageCtl.conditions.report1SpecificDate) < new Date(pageCtl.conditions.dateRange[0]) || new Date(pageCtl.conditions.report1SpecificDate) > new Date(pageCtl.conditions.dateRange[1])) {
      pageCtl.conditions.report1SpecificDate = pageCtl.conditions.dateRange[1]
      pageCtl.conditions.report2SpecificDate = pageCtl.conditions.dateRange[1]
    }
  }
})

watch(() => pageCtl.conditions.dateType, () => {
  pageCtl.conditions.specialLeftDateType = 'RELEASE_DATE'
  pageCtl.conditions.specialRightDateType = 'RESCHEDULING_DATE'
  pageCtl.conditions.specialLeftDateRange = []
  pageCtl.conditions.specialRightDateRange = []
  if (pageCtl.conditions.dateType === 'BY_SNAPSHOT_DATE') {
    pageCtl.conditions.specialLeftDateTypeOpts = ['MO_REQ_DATE', 'RELEASE_DATE', 'RESCHEDULING_DATE']
    pageCtl.conditions.specialRightDateTypeOpts = ['MO_REQ_DATE', 'RELEASE_DATE', 'RESCHEDULING_DATE']
  } else if (pageCtl.conditions.dateType === 'BY_RESCHEDULING_DATE') {
    pageCtl.conditions.specialLeftDateTypeOpts = ['MO_REQ_DATE', 'RELEASE_DATE']
    pageCtl.conditions.specialRightDateTypeOpts = ['MO_REQ_DATE', 'RELEASE_DATE']
  } else {
    pageCtl.conditions.specialLeftDateTypeOpts = ['RESCHEDULING_DATE', 'RELEASE_DATE']
    pageCtl.conditions.specialRightDateTypeOpts = ['RESCHEDULING_DATE', 'RELEASE_DATE']
  }
})

const _report3Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const colorMap = {
    'P >6M': '#c13033',
    'P 3-6M': '#c43730',
    'P 2-3M': '#c8412d',
    'P 1-2M': '#cc4c27',
    'P 14-30D': '#d25924',
    'P 7-14D': '#d7671f',
    'P 3-7D': '#e1990a',
    'P 0-3D': '#e1a506',
    'F 0-3D': '#c6b601',
    'F 3-7D': '#87a40c',
    'F 7-14D': '#749d10',
    'F 14-30D': '#629512',
    'F 1-2M': '#539017',
    'F 2-3M': '#448a1a',
    'F 3-6M': '#36851a',
    'F >6M': '#2c821d'
  }

  let yAxisData = pageCtl.report3Data

  // 转换数字为百分比
  if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {} as any
    const stackTotal = {}
    const xAxis = pageCtl.report3Data.xAxis

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report3Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report3Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report3SeriesType,
        smooth: false,
        stack: 'sum',
        itemStyle: {
          color: colorMap[key]
        },
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }

  return {
    title: {
      text: 'Evolution of Open MO Structure by ' + camelCaseStartPlaceholder(pageCtl.conditions.dateType) +
          (pageCtl.conditions.selectedTreePath ? ' [' + pageCtl.conditions.selectedTreePath + ']' : '')
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:9.5rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')

        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report3SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series
  }
})

const _report4SelectedColumns = computed(() => {
  if (pageCtl.conditions.report4SelectedColumns.length > 0) {
    return pageCtl.conditions.report4SelectedColumns
  } else {
    return ['BU', 'ENTITY']
  }
})

const _pivotColumns = computed(() => {
  const filterTemp = $getPivotColumnByFilter(pageCtl.filterOpts)
  const index = filterTemp.indexOf('MO_MSG')
  if (index !== -1) {
    filterTemp.splice(index, 1)
  }
  return filterTemp.concat(['MO_MSG_FINISH_DATE_IN_THE_PAST', 'MO_MSG_RESCHEDULE_IN', 'MO_MSG_RESCHEDULE_OUT', 'MO_MSG_CANCEL_PROCESS', 'MO_MSG_EXCESS_STOCK_IN_INDIVIDUAL_SEGMENT', 'MO_MSG_RECEIPT_AFTER_EFFECTIVE_OUT_DATE', 'RESCHEDULE_IN_DEL_RANGE', 'RESCHEDULE_OUT_DEL_RANGE']).sort()
})

</script>
