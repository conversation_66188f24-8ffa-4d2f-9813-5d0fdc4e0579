<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.category"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.filterOpts"/>
          </el-col>
          <!-- material -->
          <el-col :span="4">
            <el-input v-model="pageCtl.conditions.material" placeholder="Material" size="small" class="el-input"
                      style="width: var(--scp-input-width) !important;" clearable></el-input>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small">
              <el-option
                  v-for="item in ['Net Net Price','Moving Average Price','Quantity','Line']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- sales type -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.salesType" size="small" placeholder="Scope">
              <el-option
                  v-for="item in [{
                  name: 'Order Intake',
                  value: 'ORDER_INTAKE'
                },{
                  name: 'Sales',
                  value: 'SALES'
                },{
                  name: 'CRD',
                  value: 'CRD'
                }]"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <!-- fcst version -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.fcstVersion" size="small" :loading="pageCtl.loading.filter">
              <el-option
                  v-for="item in pageCtl.fcstVersion"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-input-number v-model="pageCtl.conditions.warningValue" size="small" :precision="0" :step="5" :max="300"
                             :min="50"
                             style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['fcstVersion']" :expand="true" :after-expand="(expand)=>{pageCtl.moreConditions = expand}"/>
          </el-col>
        </el-row>
        <el-row class="search-box" v-show="pageCtl.moreConditions">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.selectedReportColumns"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Columns'"
                :options="_reportFilterOpts"/>
          </el-col>
          <!-- scope -->
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.scope" size="small">
              <el-option
                  v-for="item in ['SECI', 'PLANT']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- scope -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.subScope" size="small" collapse-tags multiple placeholder="Sub-Scope">
              <el-option
                  v-for="item in _subScopeOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
        </el-row>

        <div class="subscript-container">
          <scp-subscript id="DADA"/>
          <scp-table
              ref="trackingTableRef"
              :columns="_reportColumns"
              :params="pageCtl.conditions"
              :lazy="true"
              :fixedColumnsLeft="3"
              url="/demand/abnormal_demand/query_report"
              download-url="/demand/abnormal_demand/download_report_data"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch, nextTick } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $calcMonthGap: any = inject('$calcMonthGap')

const searchRef = ref()
const trackingTableRef = ref()

const pageCtl = reactive({
  loading: {
    filter: false
  },
  moreConditions: true,
  conditions: {
    type: 'Net Net Price',
    category: [],
    salesType: 'ORDER_INTAKE',
    scope: 'SECI',
    subScope: ['SECI_OG'],
    fcstVersion: '' as any,
    material: '',
    warningValue: 130,
    category8Value: 'SECI_CSF_DEMAND'
  },
  fcstVersion: [],
  filterOpts: [],
  reportRawColumns: [
    {
      title: 'Material',
      data: 'MATERIAL'
    }
  ],
  warningValue: 130,
  selectedReportColumns: [ // 选中列表
    ['MASTER DATA', 'MATERIAL'],
    ['MASTER DATA', 'PLANT_CODE'],
    ['MASTER DATA', 'ENTITY'],
    ['MASTER DATA', 'PRODUCT_LINE'],
    ['MASTER DATA', 'MRP_CONTROLLER'],
    ['MASTER DATA', 'STOCKING_POLICY'],
    ['MASTER DATA', 'VENDOR_NAME'],
    ['MTD', 'FULFILL_RATIO'],
    ['MTD', 'TOP1_CUSTOMER_NAME'],
    ['MTD', 'DIFFERENCE_BY_TOP1_CUSTOMER'],
    ['MTD', 'TOP2_CUSTOMER_NAME'],
    ['MTD', 'DIFFERENCE_BY_TOP2_CUSTOMER'],
    ['MTD', 'TOP3_CUSTOMER_NAME'],
    ['MTD', 'DIFFERENCE_BY_TOP3_CUSTOMER']
  ]
})

watch(() => pageCtl.conditions.scope, () => {
  pageCtl.conditions.subScope = []
  nextTick(() => {
    if (_category8ValueOpts.value.length > 0) {
      pageCtl.conditions.category8Value = _category8ValueOpts.value[0].value
    }
  })
})

onMounted(() => {
  filterInit()
})

const search = () => {
  $axios({
    method: 'post',
    url: '/demand/abnormal_demand/query_report_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.reportRawColumns = parseReport6Columns(body)
    trackingTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const filterInit = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/demand/abnormal_demand/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader

    const fcstVersion = body.fcstVersion
    const version = [] as any
    const currentMonth = $dateFormatter(new Date(), 'yyyyMM')
    for (let i = 0; i < fcstVersion.length; i++) {
      version.push({
        label: 'M-' + $calcMonthGap(fcstVersion[i], currentMonth) + ' [' + fcstVersion[i] + ']',
        value: fcstVersion[i]
      })
    }
    pageCtl.fcstVersion = version
    pageCtl.conditions.fcstVersion = version[0].value
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const parseReport6Columns = (actualColumns) => {
  const columns = [
    {
      title: 'Material',
      data: 'MATERIAL'
    }, {
      title: 'Plant',
      data: 'PLANT_CODE'
    }, {
      title: 'Fulfill Ratio',
      data: 'FULFILL_RATIO',
      type: 'numeric'
    }, {
      title: 'Cluster',
      data: 'CLUSTER_NAME'
    }, {
      title: 'Entity',
      data: 'ENTITY'
    }, {
      title: 'Business Unit',
      data: 'BU'
    }, {
      title: 'Product Line',
      data: 'PRODUCT_LINE'
    }, {
      title: 'Local BU',
      data: 'LOCAL_BU'
    }, {
      title: 'Local Product Line',
      data: 'LOCAL_PRODUCT_LINE'
    }, {
      title: 'Local Product Family',
      data: 'LOCAL_PRODUCT_FAMILY'
    }, {
      title: 'Local Product SubFamily',
      data: 'LOCAL_PRODUCT_SUBFAMILY'
    }, {
      title: 'MRP Controller',
      data: 'MRP_CONTROLLER'
    }, {
      title: 'Material Owner Name',
      data: 'MATERIAL_OWNER_NAME'
    }, {
      title: 'Material Owner Sesa',
      data: 'MATERIAL_OWNER_SESA'
    }, {
      title: 'Stocking Policy',
      data: 'STOCKING_POLICY'
    }, {
      title: 'Vendor Code',
      data: 'VENDOR_CODE'
    }, {
      title: 'Vendor Name',
      data: 'VENDOR_NAME'
    }, {
      title: 'Monthly Order Line',
      data: 'ORDER_LINE',
      type: 'numeric'
    }, {
      title: 'AMU',
      data: 'AMU',
      type: 'numeric'
    }, {
      title: 'COV',
      data: 'COV',
      type: 'numeric'
    },
    {
      title: 'Backlog',
      data: 'BACKLOG',
      type: 'numeric'
    }, {
      title: 'Delivery',
      data: 'DELIVERY',
      type: 'numeric'
    }, {
      title: 'Stock On Hand',
      data: 'STOCK_ON_HAND',
      type: 'numeric'
    }, {
      title: 'SO Stock',
      data: 'SO_STOCK',
      type: 'numeric'
    }
  ]

  for (const k in actualColumns.Actual) {
    if (actualColumns.Actual.hasOwnProperty(k)) {
      columns.push({
        title: actualColumns.Actual[k],
        data: actualColumns.Actual[k],
        type: 'numeric'
      })
    }
  }

  for (const k in actualColumns.MTD) {
    if (actualColumns.MTD.hasOwnProperty(k)) {
      columns.push({
        title: actualColumns.MTD[k],
        data: actualColumns.MTD[k],
        type: 'numeric'
      })
    }
  }

  // (当月实际,受ORDERINTAKE,SALES,CRD影响/预测截止到当天)
  columns.push({
    title: 'Top1 Customer Code',
    data: 'TOP1_CUSTOMER_CODE'
  }, {
    title: 'Top1 Customer Name',
    data: 'TOP1_CUSTOMER_NAME'
  }, {
    title: 'MTD by Top1 Customer',
    data: 'TOP1_MTD',
    type: 'numeric'
  }, {
    title: 'AMU by Top1 Customer',
    data: 'TOP1_AMU',
    type: 'numeric'
  }, {
    // MTD-AMU
    title: 'Difference by Top1 Customer',
    data: 'TOP1_DIFF',
    type: 'numeric'
  }, {
    title: 'Top2 Customer Code',
    data: 'TOP2_CUSTOMER_CODE'
  }, {
    title: 'Top2 Customer Name',
    data: 'TOP2_CUSTOMER_NAME'
  }, {
    title: 'MTD by Top2 Customer',
    data: 'TOP2_MTD',
    type: 'numeric'
  }, {
    title: 'AMU by Top2 Customer',
    data: 'TOP2_AMU',
    type: 'numeric'
  }, {
    title: 'Difference by Top2 Customer',
    data: 'TOP2_DIFF',
    type: 'numeric'
  }, {
    title: 'Top3 Customer Code',
    data: 'TOP3_CUSTOMER_CODE'
  }, {
    title: 'Top3 Customer Name',
    data: 'TOP3_CUSTOMER_NAME'
  }, {
    title: 'MTD by Top3 Customer',
    data: 'TOP2_MTD',
    type: 'numeric'
  }, {
    title: 'AMU by Top3 Customer',
    data: 'TOP3_AMU',
    type: 'numeric'
  }, {
    title: 'Difference by Top3 Customer',
    data: 'TOP3_DIFF',
    type: 'numeric'
  })

  for (const k in actualColumns.FCST) {
    if (actualColumns.FCST.hasOwnProperty(k)) {
      columns.push({
        title: actualColumns.FCST[k].replace(/_/g, ' '),
        data: actualColumns.FCST[k],
        type: 'numeric'
      })
    }
  }

  return columns
}

const _reportColumns = computed(() => {
  const columns = [] as any
  const array = pageCtl.selectedReportColumns.map(e => e[1])
  for (let i = 0; i < pageCtl.reportRawColumns.length; i++) {
    const column = pageCtl.reportRawColumns[i]
    // 是否在选中列中
    if (array.includes(column.data) || column.data === 'MATERIAL' || column.data === 'PLANT_CODE' || column.data === 'FULFILL_RATIO') {
      columns.push(column)
    }
  }
  return columns
})

const _reportFilterOpts = computed(() => {
  const result: any = [
    {
      label: 'Master Data',
      value: 'MASTER DATA',
      children: []
    }, {
      label: 'Calculations',
      value: 'CALCULATIONS',
      children: []
    }, {
      label: 'Actual',
      value: 'ACTUAL',
      children: []
    }, {
      label: 'MTD',
      value: 'MTD',
      children: []
    }, {
      label: 'FCST',
      value: 'FCST',
      children: []
    }
  ]

  const arr = pageCtl.reportRawColumns.map(x => {
    return x.data
  })
  if ((!arr) || arr.length < 16) {
    return [[]]
  }

  let actualCheck = false
  for (let i = 3; i < arr.length; i++) {
    const data = arr[i]
    if (i < 16) {
      result[0].children.push({
        value: arr[i],
        label: arr[i].replace(/_/gi, ' ')
      })
    } else if (i < 23) {
      result[1].children.push({
        value: arr[i],
        label: arr[i].replace(/_/gi, ' ')
      })
    } else if (data.length === 6 && !actualCheck) {
      result[2].children.push({
        value: arr[i],
        label: arr[i].replace(/_/gi, ' ')
      })
    } else if (data.indexOf('_FCST') === -1) {
      actualCheck = true
      result[3].children.push({
        value: arr[i],
        label: arr[i].replace(/_/gi, ' ')
      })
    } else {
      result[4].children.push({
        value: arr[i],
        label: arr[i].replace(/_/gi, ' ')
      })
    }
  }
  result[0].children.sort((e1, e2) => e1.value.localeCompare(e2.value))
  result[1].children.sort((e1, e2) => e1.value.localeCompare(e2.value))
  result.sort((e1, e2) => e1.value.localeCompare(e2.value))

  return result
})

const _subScopeOpts = computed(() => {
  if (pageCtl.conditions.scope === 'SECI') {
    return ['SECI_IG', 'SECI_OG', 'SEHK_IG', 'SEHK_OG']
  } else if (pageCtl.conditions.scope === 'PLANT') {
    return ['PLANT_IG_DOMESTIC', 'PLANT_IG_EXPORT', 'PLANT_OG_DOMESTIC', 'PLANT_OG_EXPORT']
  } else {
    return []
  }
})

const _category8ValueOpts = computed(() => {
  if (pageCtl.conditions.scope === 'SECI') {
    return [{
      label: 'FCST',
      value: 'SECI_CSF_DEMAND'
    }]
  } else if (pageCtl.conditions.scope === 'PLANT') {
    return [{
      label: 'MPP Dem. FCST',
      value: 'PLANT_MPP_DEMAND'
    }, {
      label: 'MPP Com. FCST',
      value: 'PLANT_MPP_COMMIT'
    }]
  }
  return []
})

</script>
