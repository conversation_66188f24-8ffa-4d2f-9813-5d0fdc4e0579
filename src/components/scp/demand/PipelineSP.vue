<template>
  <div class="left-sidebar" id="piplineSP">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.displayTab">
          <el-tab-pane label="Pipeline Charts" name="charts">
            <el-row class="search-box">
              <el-col :span="3">
                <el-select v-model="pageCtl.submissionDate" size="small" filterable placeholder="Submission Date" :loading="pageCtl.submissionLoading">
                  <el-option
                      v-for="item in pageCtl.submissionOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.reportType" size="small" filterable placeholder="Report Type">
                  <el-option
                      v-for="item in pageCtl.reportTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.updateBy" size="small" clearable filterable placeholder="Uploaded By" :loading="pageCtl.updateByLoading">
                  <el-option
                      v-for="item in pageCtl.updateByOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.productLine" size="small" multiple filterable clearable placeholder="Product Family" :loading="pageCtl.productLineLoading">
                  <el-option
                      v-for="item in pageCtl.productLineOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.projectStatus" size="small" multiple filterable clearable placeholder="Project Status"
                           :loading="pageCtl.projectStatusLoading">
                  <el-option
                      v-for="item in pageCtl.projectStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-date-picker
                    size="small"
                    v-model="pageCtl.deliveryDate"
                    type="monthrange"
                    unlink-panels
                    range-separator="to"
                    format="YYYY/MM"
                    value-format="YYYY/MM"
                    start-placeholder="Start month"
                    end-placeholder="End month"
                    :clearable="false"
                    :picker-options="pageCtl.pickerOptions">
                </el-date-picker>
              </el-col>
              <el-col :span="3">
                <el-button-group>
                  <el-button size="small" @click="search">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                  <el-button size="small" @click="pageCtl.isFold = !pageCtl.isFold" style="padding: 0.25rem 0.35rem !important;">
                    <font-awesome-icon :icon="(!pageCtl.isFold) ? 'angle-up': 'angle-down'"/>
                  </el-button>
                </el-button-group>
              </el-col>
              <el-col :span="2">
                <el-button size="small" title="Meeting memo" @click="pageCtl.tipVisible = true" style="float: right;">
                  <font-awesome-icon icon="info"/>
                </el-button>
              </el-col>
            </el-row>
            <el-row class="search-box" v-show="!pageCtl.isFold">
              <el-col :span="3">
                <el-select v-model="pageCtl.salesTeam" size="small" multiple filterable placeholder="Sales Team" :loading="pageCtl.salesTeamLoading">
                  <el-option
                      v-for="item in pageCtl.salesTeamOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.salesPerson" size="small" multiple filterable placeholder="Sales Person" :loading="pageCtl.salesPersonLoading">
                  <el-option
                      v-for="item in pageCtl.salesPersonOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>

            <!--  page 1 -->
            <div class="page" style="height: auto;margin-bottom: var(--scp-widget-margin)">
              <div class="subscript-container">
                <scp-subscript id="DSEF"/>
                <scp-table
                    ref="report1TableRef"
                    :columns="pageCtl.report1Column"
                    :lazy="true"
                    :max-height="310"
                    :pagging="false"
                    :filters="false"
                    :columnSorting="false"
                    :params="_report1Param"
                    :contextMenuItems="pageCtl.report1ContextMenuItems"
                    url="/demand/pipeline/query_report1"
                    :afterSelect="afterReport1Select"
                />
              </div>
            </div>
            <!--  page 2 -->
            <el-row style="margin-bottom: var(--scp-widget-margin)">
              <el-col :span="14">
                <div class="subscript-container" style="margin-right:var(--scp-widget-margin);" v-loading="pageCtl.report2Loading">
                  <scp-subscript id="DSTM"/>
                  <el-row style="margin-bottom: 10px">
                    <el-col :span="6">
                      <el-date-picker
                          size="small"
                          v-model="pageCtl.submissionDateRange"
                          type="monthrange"
                          unlink-panels
                          style="width: 100%"
                          range-separator="to"
                          format="YYYY/MM"
                          value-format="YYYY/MM"
                          start-placeholder="Start month"
                          end-placeholder="End month"
                          :clearable="false"
                          :picker-options="pageCtl.pickerOptions2">
                      </el-date-picker>
                    </el-col>
                    <el-col :span="12" style="padding:5px 0 0 40px">
                      <el-radio-group v-model="pageCtl.report2Type">
                        <el-radio value="by_row">By Row</el-radio>
                        <el-radio value="by_value">By Value</el-radio>
                      </el-radio-group>
                    </el-col>
                  </el-row>
                  <chart ref="report2Ref" :height="365" :option="_report2Opt"/>
                </div>
              </el-col>
              <el-col :span="10">
                <div class="subscript-container" v-loading="pageCtl.report3Loading">
                  <scp-subscript id="DSDB"/>
                  <el-radio-group v-model="pageCtl.report3Type" style="margin-bottom: 15px">
                    <el-radio value="by_qty">By Qty</el-radio>
                    <el-radio value="by_value">By Value</el-radio>
                  </el-radio-group>
                  <chart ref="report3Ref" :height="365" :option="_report3Opt"/>
                </div>
              </el-col>
            </el-row>
            <!--  page 3 -->
            <el-row style="margin-bottom: var(--scp-widget-margin)">
              <el-col :span="8">
                <div class="subscript-container" style="margin-right:var(--scp-widget-margin);" v-loading="pageCtl.report4Loading">
                  <scp-subscript id="DSLP"/>
                  <chart :height="450" :option="_report4Opt"/>
                </div>
              </el-col>
              <el-col :span="16">
                <div class="subscript-container" v-loading="pageCtl.report5Loading">
                  <scp-subscript id="DSLT"/>
                  <chart ref="report5Ref" :height="450" :option="_report5Opt"/>
                </div>
              </el-col>
            </el-row>
            <!--  page 4 -->
            <el-row style="margin-bottom: var(--scp-widget-margin)">
              <el-col :span="24">
                <div class="subscript-container" v-loading="pageCtl.report6Loading">
                  <scp-subscript id="DSUA"/>
                  <chart ref="report6Ref" :height="350" :option="_report6Opt"/>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="Pipeline Raw Data" name="rawdata">
            <div class="subscript-container">
              <scp-subscript id="DSRD"/>
              <scp-datagrid bindTo="MR3_DP_PROJECT_PIPELINE_MANAGEMENT"
                            cache-keys="1d::c.s.d.s.i.PipelineServiceImpl"
                            :after-changed="afterRowdataChanged"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Material Standard Delivery Days" name="stddata">
            <div class="subscript-container">
              <scp-subscript id="DSDT"/>
              <scp-datagrid bindTo="MR3_DP_PROJECT_PIPELINE_STD"
                            cache-keys="1d::c.s.d.s.i.PipelineServiceImpl"
                            :after-changed="afterStddataChanged"
              />
            </div>
            <hr/>
            <div class="subscript-container">
              <scp-subscript id="DSAD"/>
              <scp-table
                  ref="invalidTableRef"
                  :columns="pageCtl.invalidTableColumn"
                  :filters="false"
                  :columnSorting="false"
                  url="/demand/pipeline/query_invalid_pipeline_data"/>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <scp-draggable-resizable v-model="pageCtl.tipVisible" h="450px" w="60vw" title="Meeting Memo" :save="saveReport1Comments">
      <template v-slot="{ height }">
        <scp-ck-editor
            v-model="pageCtl.report1Comments"
            :style="{ height : height - 150 + 'px' }"
        ></scp-ck-editor>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" title="Details" v-model="pageCtl.report1DetailsVisible">
      <template v-slot="{ height }">
        <scp-table
            ref="report1DetailsTableRef"
            :columns="pageCtl.report1DetailsColumn"
            :max-height="height - 120"
            :lazy="true"
            :pagging="false"
            :filters="false"
            :columnSorting="false"
            :params="pageCtl.report1DetailsParam"
            url="/demand/pipeline/query_report1_details"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" title="Source" v-model="pageCtl.report1SourceVisible" :save="saveReport1Source">
      <template v-slot="{ height }">
        <scp-table
            ref="report1SourceTableRef"
            :max-height="height - 120"
            :columns="pageCtl.report1SourceColumn"
            :hiddenColumns="pageCtl.report1SourceHiddenColumn"
            :lazy="true"
            :pagging="false"
            :filters="false"
            :columnSorting="false"
            :params="pageCtl.report1SourceParam"
            :afterChange="report1SourceAfterChange"
            url="/demand/pipeline/query_report1_source"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" :title="pageCtl.report2DetialsTitle" v-model="pageCtl.report2DetailsVisible"
                             v-loading="pageCtl.report2DetailsLoading">
      <template v-slot="{ height }">
        <chart ref="report2DetailsChartRef" :style="{ height : height - 120 + 'px', width : pageCtl.pageWidth + 'px' }" :option="_report2DetailsOpt"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" :title="pageCtl.report2SourceTitle" v-model="pageCtl.report2SourceVisible">
      <template v-slot="{ height }">
        <scp-table
            ref="report2SourceTableRef"
            :columns="_report2SourceColumn"
            :lazy="true"
            :pagging="false"
            :filters="false"
            :max-height="height - 120"
            :columnSorting="false"
            :params="pageCtl.report2SourceParam"
            url="/demand/pipeline/query_report2_source"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" :title="pageCtl.report3DetailsTitle" v-model="pageCtl.report3DetailsVisible">
      <template v-slot="{ height }">
        <scp-table
            ref="report3DetailsTableRef"
            :columns="pageCtl.report3DetailsColumn"
            :max-height="height - 120"
            :lazy="true"
            :pagging="false"
            :filters="false"
            :columnSorting="false"
            :params="pageCtl.report3DetailsParam"
            url="/demand/pipeline/query_report3_details"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" :title="pageCtl.report5DetailsTitle" v-model="pageCtl.report5DetailsVisible">
      <template v-slot="{ height }">
        <scp-table
            ref="report5DetailsTableRef"
            :columns="pageCtl.report5DetailsColumn"
            :max-height="height - 120"
            :lazy="true"
            :pagging="false"
            :filters="false"
            :columnSorting="false"
            :params="pageCtl.report5DetailsParam"
            url="/demand/pipeline/query_report5_details"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable h="450px" w="60vw" :title="pageCtl.report6DetailsTitle" v-model="pageCtl.report6DetailsVisible">
      <template v-slot="{ height }">
        <scp-table
            ref="report6DetailsTableRef"
            :columns="pageCtl.report6DetailsColumn"
            :max-height="height - 120"
            :lazy="true"
            :pagging="false"
            :filters="false"
            :columnSorting="false"
            :params="pageCtl.report6DetailsParam"
            url="/demand/pipeline/query_report6_details"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const report1TableRef = ref()
const report2Ref = ref()
const report3Ref = ref()
const report5Ref = ref()
const report6Ref = ref()
const invalidTableRef = ref()
const report1DetailsTableRef = ref()
const report1SourceTableRef = ref()
const report2DetailsChartRef = ref()
const report2SourceTableRef = ref()
const report3DetailsTableRef = ref()
const report5DetailsTableRef = ref()
const report6DetailsTableRef = ref()

const viewReport1Source = () => {
  let material = pageCtl.selectedRow.MATERIAL
  if (!material || material === 'Grand Total') {
    material = ''
  }

  let selectedDate = pageCtl.selectedColumnName
  if (selectedDate === 'Grand Total' || selectedDate.length !== 6 || selectedDate.indexOf('-') !== 3) {
    selectedDate = ''
  }

  pageCtl.report1SourceParam.material = material
  pageCtl.report1SourceParam.selectedDate = selectedDate
  pageCtl.report1SourceParam.deliveryDate = pageCtl.deliveryDate
  pageCtl.report1SourceParam.submissionDate = pageCtl.submissionDate
  pageCtl.report1SourceParam.updateBy = pageCtl.updateBy
  pageCtl.report1SourceParam.productLine = pageCtl.productLine
  pageCtl.report1SourceParam.projectStatus = pageCtl.projectStatus
  pageCtl.report1SourceParam.salesTeam = pageCtl.salesTeam
  pageCtl.report1SourceParam.salesPerson = pageCtl.salesPerson

  pageCtl.report1SourceVisible = true
  report1SourceTableRef.value.search()
}

const viewReport1Details = () => {
  let material = pageCtl.selectedRow.MATERIAL
  if (!material || material === 'Grand Total') {
    material = ''
  }

  let selectedDate = pageCtl.selectedColumnName
  if (selectedDate === 'Grand Total' || selectedDate.length !== 6 || selectedDate.indexOf('-') !== 3) {
    selectedDate = ''
  }

  pageCtl.report1DetailsParam.material = material
  pageCtl.report1DetailsParam.selectedDate = selectedDate
  pageCtl.report1DetailsParam.deliveryDate = pageCtl.deliveryDate
  pageCtl.report1DetailsParam.submissionDate = pageCtl.submissionDate
  pageCtl.report1DetailsParam.updateBy = pageCtl.updateBy
  pageCtl.report1DetailsParam.productLine = pageCtl.productLine
  pageCtl.report1DetailsParam.projectStatus = pageCtl.projectStatus
  pageCtl.report1DetailsParam.salesTeam = pageCtl.salesTeam
  pageCtl.report1DetailsParam.salesPerson = pageCtl.salesPerson

  pageCtl.report1DetailsVisible = true
  report1DetailsTableRef.value.search()
}

const renderGrandTotal = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (r.MATERIAL === 'Grand Total') {
    td.innerHTML = '<b>' + ($thousandBitSeparator(value) || '') + '</b>'
  } else {
    td.innerHTML = $thousandBitSeparator(value)
  }
}

const pageCtl = reactive({
  pageWidth: document.body.clientWidth * 0.6,
  displayTab: 'charts',
  isFold: false,
  isThumbnailVisible: false,
  reportType: 'quantity',
  reportTypeOptions: [{
    value: 'quantity',
    label: 'By Quantity'
  }, {
    value: 'order_value',
    label: 'By Order Value'
  }],
  submissionDate: '',
  submissionDateRange: [] as any,
  submissionOptions: [] as any,
  submissionLoading: false,
  updateBy: '',
  updateByOptions: [],
  updateByLoading: false,
  productLine: [],
  productLineOptions: [],
  productLineLoading: false,
  projectStatus: [],
  projectStatusOptions: [],
  projectStatusLoading: false,
  deliveryDate: [] as any,
  salesPerson: [],
  salesPersonOptions: [],
  salesPersonLoading: false,
  salesTeam: [],
  salesTeamOptions: [],
  salesTeamLoading: false,
  report1Column: [],
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      disabled: () => {
        return !pageCtl.selectedValue
      },
      callback: viewReport1Details
    },
    view_source: {
      name: 'View source',
      disabled: () => {
        return !pageCtl.selectedValue
      },
      callback: viewReport1Source
    }
  },
  selectedRow: '' as any,
  selectedValue: '',
  selectedColumnName: '',
  report1Comments: '',
  report1EditorOption: {
    modules: {
      toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{ header: 1 }, { header: 2 }], [{ list: 'ordered' }, { list: 'bullet' }], [{ script: 'sub' }, { script: 'super' }], [{ indent: '-1' }, { indent: '+1' }], [{ direction: 'rtl' }], [{ size: ['small', false, 'large', 'huge'] }], [{ header: [1, 2, 3, 4, 5, 6, false] }], [{ color: [] }, { background: [] }], [{ font: [] }], [{ align: [] }], ['clean']]
    },
    theme: 'bubble',
    placeholder: 'Input your comments'
  },
  tipVisible: false,
  report1DetailsVisible: false,
  report1DetailsColumn: [{
    data: 'PROJECT_NAME',
    title: 'Project Name'
  }, {
    data: 'QTY',
    title: 'Quantity',
    render: renderGrandTotal
  }, {
    data: 'FORECAST_QUANTITY',
    title: 'Forecast Quantity',
    render: renderGrandTotal
  }, {
    data: 'ORDER_VALUE',
    title: 'Order Value',
    render: renderGrandTotal
  }, {
    data: 'SALES_PERSON',
    title: 'Sales Person'
  }, {
    data: 'PROJECT_STATUS',
    title: 'Project Status'
  }],
  report1DetailsParam: {} as any,
  report1SourceVisible: false,
  report1SourceColumn: [{
    data: 'ROW_ID'
  }, {
    data: 'PROJECT_NAME',
    title: 'Project Name'
  }, {
    data: 'MATERIAL',
    title: 'Material'
  }, {
    data: 'QUANTITY',
    title: 'Quantity',
    render: renderGrandTotal
  }, {
    data: 'FORECAST_QUANTITY',
    title: 'Forecast Quantity',
    render: renderGrandTotal
  }, {
    data: 'ORDER_VALUE',
    title: 'Order Value',
    render: renderGrandTotal
  }, {
    data: 'SALES_PERSON',
    title: 'Sales Person'
  }, {
    data: 'DP_COMMENTS',
    title: 'DP Comments'
  }],
  report1SourceParam: {} as any,
  report1SourceHiddenColumn: {
    columns: [0]
  },
  report1SourceUpdated: {},
  pickerOptions: {
    shortcuts: [{
      text: 'Next 6 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 6)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Next 1 year',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        end.setMonth(end.getMonth() + 12)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  pickerOptions2: {
    shortcuts: [{
      text: 'Last 6 months',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        start.setMonth(start.getMonth() - 6)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Last 1 year',
      onClick (picker) {
        const start = new Date()
        const end = new Date()
        start.setMonth(start.getMonth() - 12)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  report2Data: {} as any,
  report2Type: 'by_row',
  report2Loading: false,
  report2DetailsData: {} as any,
  report2DetailsVisible: false,
  report2DetialsTitle: '',
  report2DetailsLoading: false,
  report2SourceVisible: false,
  report2SourceParam: {} as any,
  report2SourceTitle: '',
  report3Loading: false,
  report3Type: 'by_qty',
  report3Data: {} as any,
  report3DetailsTitle: '',
  report3DetailsVisible: false,
  report3DetailsParam: {} as any,
  report3DetailsColumn: [{
    data: 'SUBMISSION_DATE',
    title: 'Submission Date'
  }, {
    data: 'PROJECT_NAME',
    title: 'Project Name'
  }, {
    data: 'MATERIAL',
    title: 'Material'
  }, {
    data: 'EST_DELIVERY_DATE',
    title: 'EST Delivery Date'
  }, {
    data: 'QUANTITY',
    title: 'Quantity',
    render: renderGrandTotal
  }, {
    data: 'FORECAST_QUANTITY',
    title: 'Forecast Quantity',
    render: renderGrandTotal
  }, {
    data: 'ORDER_VALUE',
    title: 'Order Value',
    render: renderGrandTotal
  }, {
    data: 'SALES_PERSON',
    title: 'Sales Person'
  }, {
    data: 'DP_COMMENTS',
    title: 'DP Comments'
  }],
  report4Loading: false,
  report4Data: [],
  report5Loading: false,
  report5Data: {} as any,
  report5DetailsParam: {} as any,
  report5DetailsVisible: false,
  report5DetailsTitle: '',
  report5DetailsColumn: [{
    data: 'SUBMISSION_DATE',
    title: 'Submission Date'
  }, {
    data: 'PROJECT_NAME',
    title: 'Project Name'
  }, {
    data: 'MATERIAL',
    title: 'Material'
  }, {
    data: 'EST_DELIVERY_DATE',
    title: 'EST Delivery Date'
  }, {
    data: 'EST_DATE_TRACKING',
    title: 'EST Delivery Date Tracking'
  }, {
    data: 'DF',
    title: 'DF'
  }, {
    data: 'DD',
    title: 'DD'
  }, {
    data: 'QUANTITY',
    title: 'Quantity',
    render: renderGrandTotal
  }, {
    data: 'FORECAST_QUANTITY',
    title: 'Forecast Quantity',
    render: renderGrandTotal
  }, {
    data: 'ORDER_VALUE',
    title: 'Order Value',
    render: renderGrandTotal
  }, {
    data: 'SALES_PERSON',
    title: 'Sales Person'
  }, {
    data: 'DP_COMMENTS',
    title: 'DP Comments'
  }],
  invalidTableColumn: [{
    data: 'CNT',
    title: 'Repeats',
    render: (hotInstance, td, row, column, prop, value) => {
      td.className = 'warning-tr'
      td.innerHTML = value
    }
  }, {
    data: 'SUBMISSION_DATE',
    title: 'Submission Date'
  }, {
    data: 'PROJECT_NAME',
    title: 'Project Name'
  }, {
    data: 'MATERIAL',
    title: 'Material'
  }, {
    data: 'EST_DELIVERY_DATE',
    title: 'EST Delivery Date'
  }, {
    data: 'QUANTITY',
    title: 'Quantity',
    render: renderGrandTotal
  }, {
    data: 'FORECAST_QUANTITY',
    title: 'Forecast Quantity',
    render: renderGrandTotal
  }, {
    data: 'ORDER_VALUE',
    title: 'Order Value',
    render: renderGrandTotal
  }, {
    data: 'SALES_PERSON',
    title: 'Sales Person'
  }, {
    data: 'DP_COMMENTS',
    title: 'DP Comments'
  }],
  report6Loading: false,
  report6Data: {} as any,
  report6DetailsParam: {} as any,
  report6DetailsVisible: false,
  report6DetailsTitle: '',
  report6DetailsColumn: [{
    data: 'SUBMISSION_DATE',
    title: 'Submission Date'
  }, {
    data: 'PROJECT_NAME',
    title: 'Project Name'
  }, {
    data: 'MATERIAL',
    title: 'Material'
  }, {
    data: 'EST_DELIVERY_DATE',
    title: 'EST Delivery Date'
  }, {
    data: 'ACTUAL_DELIVERY_DAYS',
    title: 'Actual Delivery Days'
  }, {
    data: 'STD_DELIVERY_DAYS',
    title: 'STD Delivery Days'
  }, {
    data: 'QUANTITY',
    title: 'Quantity',
    render: renderGrandTotal
  }, {
    data: 'FORECAST_QUANTITY',
    title: 'Forecast Quantity',
    render: renderGrandTotal
  }, {
    data: 'ORDER_VALUE',
    title: 'Order Value',
    render: renderGrandTotal
  }, {
    data: 'SALES_PERSON',
    title: 'Sales Person'
  }, {
    data: 'DP_COMMENTS',
    title: 'DP Comments'
  }]
})

watch(() => pageCtl.submissionDate, () => {
  queryUpdateBy()
})

watch(() => pageCtl.updateBy, () => {
  queryProductLine()
})

watch(() => pageCtl.productLine, () => {
  queryProjectStatus()
  querySalesTeam()
})

watch(() => pageCtl.report2Type, () => {
  searchReport2()
})

watch(() => pageCtl.report3Type, () => {
  searchReport3()
})

watch(() => pageCtl.submissionDateRange, (newVal, oldVal) => {
  if (oldVal.length > 0) {
    searchReport2()
  }
})

watch(() => pageCtl.salesTeam, () => {
  querySalesPerson()
})

const querySubmissionOpt = () => {
  pageCtl.submissionLoading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_submission_date'
  }).then((body) => {
    pageCtl.submissionOptions = body
    if (pageCtl.submissionOptions.length > 0) {
      pageCtl.submissionDate = pageCtl.submissionOptions[0].value
      search()
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.submissionLoading = false
  })
}
const queryUpdateBy = () => {
  if (pageCtl.submissionDate) {
    pageCtl.updateByLoading = true
    $axios({
      method: 'post',
      url: '/demand/pipeline/query_update_by',
      data: {
        submissionDate: pageCtl.submissionDate
      }
    }).then((body) => {
      if (pageCtl.updateBy === '') {
        queryProductLine()
      } else {
        pageCtl.updateBy = ''
      }
      pageCtl.updateByOptions = body
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.updateByLoading = false
    })
  }
}
const queryProductLine = () => {
  pageCtl.productLineLoading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_product_line',
    data: {
      submissionDate: pageCtl.submissionDate,
      updateBy: pageCtl.updateBy
    }
  }).then((body) => {
    pageCtl.productLine = []
    body.sort((e1, e2) => e1.value.localeCompare(e2.value))
    pageCtl.productLineOptions = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.productLineLoading = false
  })
}
const queryProjectStatus = () => {
  pageCtl.projectStatusLoading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_project_status',
    data: {
      submissionDate: pageCtl.submissionDate,
      updateBy: pageCtl.updateBy,
      productLine: pageCtl.productLine
    }
  }).then((body) => {
    pageCtl.projectStatus = []
    pageCtl.projectStatusOptions = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.projectStatusLoading = false
  })
}
const querySalesTeam = () => {
  pageCtl.salesTeamLoading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_sales_team',
    data: {
      submissionDate: pageCtl.submissionDate,
      productLine: pageCtl.productLine
    }
  }).then((body) => {
    pageCtl.salesTeam = []
    pageCtl.salesTeamOptions = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.salesTeamLoading = false
  })
}
const querySalesPerson = () => {
  pageCtl.salesPersonLoading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_sales_person',
    data: {
      submissionDate: pageCtl.submissionDate,
      productLine: pageCtl.productLine,
      salesTeam: pageCtl.salesTeam
    }
  }).then((body) => {
    pageCtl.salesPerson = []
    pageCtl.salesPersonOptions = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.salesPersonLoading = false
  })
}
const initDatePicker = () => {
  const end = new Date()
  const start = new Date()
  end.setMonth(end.getMonth() + 6)
  let startMonth: any = start.getMonth() + 1
  if (startMonth < 10) {
    startMonth = '0' + startMonth
  }

  let endMonth: any = end.getMonth() + 1
  if (endMonth < 10) {
    endMonth = '0' + endMonth
  }
  pageCtl.deliveryDate = [start.getFullYear() + '/' + startMonth, end.getFullYear() + '/' + endMonth]

  const end2 = new Date()
  const start2 = new Date()
  start2.setMonth(start2.getMonth() - 6)
  let startMonth2: any = start2.getMonth() + 1
  if (startMonth2 < 10) {
    startMonth2 = '0' + startMonth2
  }

  let endMonth2: any = end2.getMonth() + 1
  if (endMonth2 < 10) {
    endMonth2 = '0' + endMonth2
  }

  pageCtl.submissionDateRange = [start2.getFullYear() + '/' + startMonth2, end2.getFullYear() + '/' + endMonth2]
}
const search = () => {
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
  searchReport5()
  searchReport6()
}
const searchReport1 = () => {
  pageCtl.report2DetailsVisible = false
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_report1_columns_comments',
    data: {
      deliveryDate: pageCtl.deliveryDate,
      submissionDate: pageCtl.submissionDate
    }
  }).then((body) => {
    const c = [{
      data: 'MATERIAL',
      title: 'Material',
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === 'Grand Total') {
          td.innerHTML = '<b>' + value + '</b>'
        } else {
          td.innerHTML = value
        }
      }
    }] as any
    for (const i in body.columns) {
      if (!body.columns.hasOwnProperty(i)) {
        continue
      }
      c.push({
        data: body.columns[i],
        render: renderGrandTotal
      })
    }

    c.push({
      data: 'grandTotal',
      title: 'Grand Total',
      render: renderGrandTotal
    })
    pageCtl.report1Column = c
    report1TableRef.value.search()
    pageCtl.report1Comments = body.comments || ''
  }).catch((error) => {
    console.log(error)
  })
}
const saveReport1Comments = () => {
  $axios({
    method: 'post',
    url: '/demand/pipeline/save_report1_comments',
    data: {
      submissionDate: pageCtl.submissionDate,
      report1Comments: pageCtl.report1Comments
    }
  }).then(() => {
    $message.success('Meeting memo saved')
  }).catch((error) => {
    console.log(error)
  })
}
const report1SourceAfterChange = (changes) => {
  if (changes) {
    const ht = report1SourceTableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      if (oldValue !== newValue) {
        const rowid = ht.getDataAtRow(row)[0]
        if (rowid) {
          let e = pageCtl.report1SourceUpdated[rowid]
          if (typeof (e) === 'undefined') {
            e = {}
          }
          e[prop] = newValue
          pageCtl.report1SourceUpdated[rowid] = e
        }
      }
    })
  }
}
const saveReport1Source = () => {
  $axios({
    method: 'post',
    url: '/demand/pipeline/save_report1_source',
    data: {
      report1SourceUpdated: pageCtl.report1SourceUpdated
    }
  }).then(() => {
    $message.success('Changes Saved')
    setTimeout(() => {
      search()
    }, 300)
  }).catch((error) => {
    console.log(error)
  })
}
const afterReport1Select = (r, value, column) => {
  pageCtl.selectedRow = r
  pageCtl.selectedValue = value
  pageCtl.selectedColumnName = column
}

const afterRowdataChanged = () => {
  invalidTableRef.value.search()
}

const afterStddataChanged = () => {
  searchReport6()
}

const searchReport2 = () => {
  pageCtl.report2DetailsVisible = false
  pageCtl.report2Loading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_report2',
    data: {
      updateBy: pageCtl.updateBy,
      productLine: pageCtl.productLine,
      submissionDate: pageCtl.submissionDate,
      deliveryDate: pageCtl.deliveryDate,
      report2Type: pageCtl.report2Type,
      salesTeam: pageCtl.salesTeam,
      salesPerson: pageCtl.salesPerson
    }
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report2Loading = false
  })
}
const searchReport3 = () => {
  pageCtl.report3Loading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_report3',
    data: {
      updateBy: pageCtl.updateBy,
      productLine: pageCtl.productLine,
      submissionDate: pageCtl.submissionDate,
      deliveryDate: pageCtl.deliveryDate,
      report3Type: pageCtl.report3Type,
      salesTeam: pageCtl.salesTeam,
      salesPerson: pageCtl.salesPerson
    }
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report3Loading = false
  })
}
const searchReport4 = () => {
  pageCtl.report4Loading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_report4',
    data: {
      updateBy: pageCtl.updateBy,
      productLine: pageCtl.productLine,
      submissionDate: pageCtl.submissionDate,
      deliveryDate: pageCtl.deliveryDate,
      salesTeam: pageCtl.salesTeam,
      salesPerson: pageCtl.salesPerson
    }
  }).then((body) => {
    pageCtl.report4Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report4Loading = false
  })
}
const searchReport5 = () => {
  pageCtl.report5Loading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_report5',
    data: {
      updateBy: pageCtl.updateBy,
      productLine: pageCtl.productLine,
      submissionDate: pageCtl.submissionDate,
      deliveryDate: pageCtl.deliveryDate,
      salesTeam: pageCtl.salesTeam,
      salesPerson: pageCtl.salesPerson
    }
  }).then((body) => {
    pageCtl.report5Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report5Loading = false
  })
}
const searchReport6 = () => {
  pageCtl.report6Loading = true
  $axios({
    method: 'post',
    url: '/demand/pipeline/query_report6',
    data: {
      updateBy: pageCtl.updateBy,
      productLine: pageCtl.productLine,
      submissionDate: pageCtl.submissionDate,
      deliveryDate: pageCtl.deliveryDate,
      salesTeam: pageCtl.salesTeam,
      salesPerson: pageCtl.salesPerson
    }
  }).then((body) => {
    pageCtl.report6Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report6Loading = false
  })
}

onMounted(() => {
  querySubmissionOpt()
  initDatePicker()
  pageCtl.isFold = true // el-input在渲染隐藏控件的时候有bug, 需要先显示出来, 在mounted的时候再隐藏起来
  report2Ref.value.chart().on('dblclick', (param) => {
    pageCtl.report2DetailsLoading = true
    $axios({
      method: 'post',
      url: '/demand/pipeline/query_report2_details',
      data: {
        reportName: param.name,
        report2Type: pageCtl.report2Type,
        submissionDateRange: pageCtl.submissionDateRange,
        submissionDate: pageCtl.submissionDate,
        updateBy: pageCtl.updateBy,
        productLine: pageCtl.productLine,
        projectStatus: pageCtl.projectStatus,
        salesTeam: pageCtl.salesTeam,
        salesPerson: pageCtl.salesPerson
      }
    }).then((body) => {
      pageCtl.report2DetialsTitle = param.name
      pageCtl.report2DetailsVisible = true
      pageCtl.report2DetailsData = body
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.report2DetailsLoading = false
    })
  })
  report2DetailsChartRef.value.chart().on('dblclick', (param) => {
    if (param.value) {
      pageCtl.report2SourceTitle = pageCtl.report2DetialsTitle + ' (' + param.value + ') in ' + param.name
      pageCtl.report2SourceParam.reportName = pageCtl.report2DetialsTitle
      pageCtl.report2SourceParam.submissionMonth = param.name
      pageCtl.report2SourceParam.updateBy = pageCtl.updateBy
      pageCtl.report2SourceParam.productLine = pageCtl.productLine
      pageCtl.report2SourceParam.projectStatus = pageCtl.projectStatus
      pageCtl.report2SourceParam.salesTeam = pageCtl.salesTeam
      pageCtl.report2SourceParam.salesPerson = pageCtl.salesPerson
      pageCtl.report2SourceVisible = true
      report2SourceTableRef.value.search()
    }
  })
  report3Ref.value.chart().on('dblclick', (param) => {
    pageCtl.report3DetailsParam.reportName = param.seriesName
    pageCtl.report3DetailsParam.deliveryDate = param.name
    pageCtl.report3DetailsParam.submissionDate = pageCtl.submissionDate
    pageCtl.report3DetailsParam.updateBy = pageCtl.updateBy
    pageCtl.report3DetailsParam.productLine = pageCtl.productLine
    pageCtl.report3DetailsParam.projectStatus = pageCtl.projectStatus
    pageCtl.report3DetailsParam.salesTeam = pageCtl.salesTeam
    pageCtl.report3DetailsParam.salesPerson = pageCtl.salesPerson
    pageCtl.report3DetailsVisible = true
    pageCtl.report3DetailsTitle = param.seriesName + '(' + $thousandBitSeparator(param.value) + ') in ' + param.name
    report3DetailsTableRef.value.search()
  })
  report5Ref.value.chart().on('dblclick', (param) => {
    pageCtl.report5DetailsParam.reportName = param.seriesName
    pageCtl.report5DetailsParam.submissionDate = param.name
    pageCtl.report5DetailsParam.updateBy = pageCtl.updateBy
    pageCtl.report5DetailsParam.productLine = pageCtl.productLine
    pageCtl.report5DetailsParam.deliveryDate = pageCtl.deliveryDate
    pageCtl.report5DetailsParam.salesTeam = pageCtl.salesTeam
    pageCtl.report5DetailsParam.salesPerson = pageCtl.salesPerson
    pageCtl.report5DetailsVisible = true
    pageCtl.report5DetailsTitle = param.seriesName + ' (' + $thousandBitSeparator(param.value) + ') in ' + param.name
    report5DetailsTableRef.value.search()
  })
  report6Ref.value.chart().on('dblclick', (param) => {
    pageCtl.report6DetailsParam.reportName = param.seriesName
    pageCtl.report6DetailsParam.submissionDate = param.name
    pageCtl.report6DetailsParam.updateBy = pageCtl.updateBy
    pageCtl.report6DetailsParam.productLine = pageCtl.productLine
    pageCtl.report6DetailsParam.deliveryDate = pageCtl.deliveryDate
    pageCtl.report6DetailsParam.salesTeam = pageCtl.salesTeam
    pageCtl.report6DetailsParam.salesPerson = pageCtl.salesPerson
    pageCtl.report6DetailsVisible = true
    pageCtl.report6DetailsTitle = param.seriesName + ' (' + $thousandBitSeparator(param.value) + ') in ' + param.name
    report6DetailsTableRef.value.search()
  })
})

const _report1Param = computed(() => {
  return {
    reportType: pageCtl.reportType,
    deliveryDate: pageCtl.deliveryDate,
    submissionDate: pageCtl.submissionDate,
    updateBy: pageCtl.updateBy,
    productLine: pageCtl.productLine,
    projectStatus: pageCtl.projectStatus,
    salesTeam: pageCtl.salesTeam,
    salesPerson: pageCtl.salesPerson
  }
})
const _report2Opt = computed(() => {
  const title = pageCtl.report2Type === 'by_row' ? 'Row' : 'Value'
  const total = pageCtl.report2Type === 'by_row' ? pageCtl.report2Data.ROW_CNT : pageCtl.report2Data.VALUE_SUM
  return {
    title: {
      text: 'Pipeline Outlook'
    },
    tooltip: {
      formatter: (info) => {
        const value = info.value
        const treePathInfo = info.treePathInfo
        const treePath = [] as any
        for (let i = 1; i < treePathInfo.length; i++) {
          treePath.push(treePathInfo[i].name)
        }

        const tip = [] as any
        tip.push('<div>')
        tip.push(treePath.join(' / '))
        tip.push('<br>')
        tip.push('<div>')
        tip.push(info.marker)
        tip.push(title)
        tip.push(' : ')
        tip.push($thousandBitSeparator(value))
        tip.push('<span style="float: right">')
        tip.push((total === 0 ? 0 : value * 100 / total).toFixed(1))
        tip.push('%</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: $grid(),
    series: [
      {
        name: title,
        roam: false,
        nodeClick: false,
        breadcrumb: { show: false },
        type: 'treemap',
        ...$grid({ type: 'treemap' }),
        visibleMin: 300,
        label: {
          show: true,
          formatter: '{b}'
        },
        upperLabel: {
          show: true,
          height: 30
        },
        levels: [
          {
            itemStyle: {
              borderColor: '#fafafa', // background color
              borderWidth: 0,
              gapWidth: 1
            },
            upperLabel: {
              show: false
            }
          },
          {
            itemStyle: {
              borderColor: '#555555', // level 1 background color
              borderWidth: 0,
              gapWidth: 1
            },
            upperLabel: {
              show: true,
              position: 'insideLeft'
            },
            emphasis: {
              color: 'red',
              itemStyle: {
                borderColor: '#7b7b7b' // hover color
              }
            }
          },
          {
            itemStyle: {
              borderColor: '#555', // level 2 background color
              borderWidth: 0,
              gapWidth: 0
            },
            upperLabel: {
              show: true,
              position: 'insideLeft'
            },
            emphasis: {
              color: 'red',
              itemStyle: {
                borderColor: '#7b7b7b' // hover color
              }
            }
          },
          {
            colorSaturation: [0.35, 0.5],
            itemStyle: {
              borderWidth: 1,
              gapWidth: 1,
              borderColorSaturation: 0.6
            }
          }
        ],
        data: pageCtl.report2Data.data
      }
    ]
  }
})
const _report2DetailsOpt = computed(() => {
  return {
    color: '#4472c4',
    grid: $grid(),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return pageCtl.report2DetialsTitle + '<br/>' + param.name + ' : ' + (param.value || '0')
      },
      axisPointer: {
        animation: false
      }
    },
    xAxis: {
      type: 'category',
      data: pageCtl.report2DetailsData.xAxis
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: pageCtl.report2DetailsData.yAxis,
      type: 'line',
      smooth: true
    }]
  }
})
const _report2SourceColumn = computed(() => {
  if (pageCtl.report2DetialsTitle === 'RSD Change' || pageCtl.report2DetialsTitle === 'QTY Change') {
    return [{
      data: 'SUBMISSION_DATE',
      title: 'Submission Date'
    }, {
      data: 'PROJECT_NAME',
      title: 'Project Name'
    }, {
      data: 'MATERIAL',
      title: 'Material'
    }, {
      data: 'EST_DELIVERY_DATE',
      title: 'EST Delivery Date'
    }, {
      data: 'QUANTITY',
      title: 'Quantity',
      render: renderGrandTotal
    }, {
      data: 'FORECAST_QUANTITY',
      title: 'Forecast Quantity',
      render: renderGrandTotal
    }, {
      data: 'ORDER_VALUE',
      title: 'Order Value',
      render: renderGrandTotal
    }, {
      data: 'SALES_PERSON',
      title: 'Sales Person'
    }, {
      data: 'DP_COMMENTS',
      title: 'DP Comments'
    }, {
      data: 'RSD_CHANGE',
      title: 'RSD Change',
      render: (hotInstance, td, row, column, prop, value) => {
        if (value) {
          const s = value.split(' → ')
          if (s.length === 2) {
            if (s[0] < s[1]) {
              td.className = 'warning-tr'
            } else {
              td.className = 'info-tr'
            }
          }
        }
        td.innerHTML = value || ''
      }
    }, {
      data: 'QUANTITY_CHANGE',
      title: 'Quantity Change'
    }]
  } else {
    return [{
      data: 'SUBMISSION_DATE',
      title: 'Submission Date'
    }, {
      data: 'PROJECT_NAME',
      title: 'Project Name'
    }, {
      data: 'MATERIAL',
      title: 'Material'
    }, {
      data: 'EST_DELIVERY_DATE',
      title: 'EST Delivery Date'
    }, {
      data: 'FORECAST_QUANTITY',
      title: 'Forecast Quantity',
      render: renderGrandTotal
    }, {
      data: 'QUANTITY',
      title: 'Quantity',
      render: renderGrandTotal
    }, {
      data: 'ORDER_VALUE',
      title: 'Order Value',
      render: renderGrandTotal
    }, {
      data: 'SALES_PERSON',
      title: 'Sales Person'
    }, {
      data: 'DP_COMMENTS',
      title: 'DP Comments'
    }]
  }
})
const _report3Opt = computed(() => {
  const total = [] as any
  const newAdd = pageCtl.report3Data['New Added']
  const inherited = pageCtl.report3Data.Inherited
  if (newAdd && inherited) {
    for (let i = 0; i < newAdd.length; i++) {
      total.push((newAdd[i] || 0) + (inherited[i] || 0))
    }
  }

  return {
    color: ['#1aa3ff', '#5cd65c', '#5cd65c'],
    animation: true,
    title: {
      show: true,
      text: 'Pipeline Distribution by Month'
    },
    grid: $grid(),
    legend: $legend({ data: ['New Added', 'Inherited'] }),
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        let total = 0
        const p = params.reverse()
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          total += p[i].data
        }

        const tip = [] as any
        tip.push('<div>')
        tip.push(p[0].name)
        tip.push('<br>')
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0

          tip.push('<div style="width:7.5rem;">')
          tip.push(p[i].marker)
          tip.push(p[i].seriesName)
          tip.push(' : ')
          tip.push($thousandBitSeparator(value))
          tip.push('<span style="float: right">')
          tip.push((total === 0 ? 0 : value * 100 / total).toFixed(1))
          tip.push('%</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      data: pageCtl.report3Data.xAxis || []
    },
    yAxis: {
      splitLine: {
        show: false
      }
    },
    series: [{
      name: 'Inherited',
      type: 'bar',
      stack: 'total',
      itemStyle: {},
      data: pageCtl.report3Data.Inherited
    }, {
      name: 'New Added',
      type: 'bar',
      stack: 'total',
      itemStyle: {
        borderRadius: [2, 2, 0, 0]
      },
      data: pageCtl.report3Data['New Added']
    }, {
      name: 'Summary',
      type: 'line',
      showSymbol: true,
      symbol: 'circle',
      symbolSize: 1,
      lineStyle: {
        color: 'rgba(255,0,0,.0)'
      },
      label: {
        show: true,
        color: '#5cd65c'
      },
      data: total
    }]
  }
})
const _report4Opt = computed(() => {
  return {
    title: {
      show: true,
      text: 'Pipeline Life Cycle Outlook'
    },
    color: ['#1aa3ff', '#5c6573', '#ff5c33'],
    tooltip: {
      formatter: (params) => {
        const pdata = params.data
        const tip = [] as any
        tip.push('<div>')
        tip.push('<div style="width:5rem;">')
        tip.push(params.marker)
        tip.push(pdata.name)
        tip.push('<span style="float: right">')
        tip.push($thousandBitSeparator(pdata.value))
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: {
      type: 'sunburst',
      data: pageCtl.report4Data,
      radius: [0, '80%'],
      center: ['50%', '60%'],
      itemStyle: {
        color: '#1aa3ff'
      },
      label: {
        rotate: 'radial',
        color: 'white'
      }
    }
  }
})
const _report5Opt = computed(() => {
  const colors = ['#1aa3ff', '#ffad33', '#ff5c33', '#1aa3ff', '#ffad33', '#ff5c33']
  return {
    color: colors,
    title: {
      text: 'Pipeline Life Cycle Trend'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          const seriesName = params[i].seriesName

          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push('<span style="float: right">')
          tip.push($thousandBitSeparator(value))
          if (seriesName.indexOf('DD') !== -1) {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: ['DF < 3', 'DF < 5', 'DF >= 5', 'DD < 3', 'DD < 5', 'DD >= 5'], selected: { 'DD < 3': false } }),
    grid: $grid({ top: 50 }),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report5Data.xAxis,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: 'Delay Frequency',
        splitLine: {
          show: false
        }
      },
      {
        type: 'value',
        name: 'Delay Depth（%）',
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: 'DF < 3',
        type: 'bar',
        data: pageCtl.report5Data.yAxis1,
        label: {
          show: true,
          position: 'top',
          color: colors[0]
        }
      },
      {
        name: 'DF < 5',
        type: 'bar',
        data: pageCtl.report5Data.yAxis2,
        label: {
          show: true,
          position: 'top',
          color: colors[1]
        }
      },
      {
        name: 'DF >= 5',
        type: 'bar',
        data: pageCtl.report5Data.yAxis3,
        label: {
          show: true,
          position: 'top',
          color: colors[2]
        }
      },
      {
        name: 'DD < 3',
        type: 'line',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report5Data.yAxis4,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: colors[3]
        }
      },
      {
        name: 'DD < 5',
        type: 'line',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report5Data.yAxis5,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: colors[4]
        }
      },
      {
        name: 'DD >= 5',
        type: 'line',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report5Data.yAxis6,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          color: colors[5]
        }
      }
    ]
  }
})
const _report6Opt = computed(() => {
  const color = ['#ff5c33', '#ffad33']
  return {
    title: {
      text: 'Unplanned Pipeline Pre-alert'
    },
    grid: $grid({ top: 50 }),
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          const seriesName = params[i].seriesName

          tip.push('<div style="width:5rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push('<span style="float: right">')
          tip.push($thousandBitSeparator(value))
          if (seriesName.indexOf('Percent') !== -1) {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: ['Percent', 'Lines'] }),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report6Data.xAxis
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: 'Percent',
        splitLine: {
          show: false
        }
      },
      {
        type: 'value',
        name: 'Lines',
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: 'Percent',
        type: 'line',
        label: {
          show: true,
          padding: 2,
          formatter: '{c}%',
          color: color[0]
        },
        itemStyle: {
          color: color[0]
        },
        lineStyle: {
          width: 2
        },
        smooth: false,
        data: pageCtl.report6Data.yAxis1
      },
      {
        name: 'Lines',
        type: 'bar',
        label: {
          show: true,
          position: 'top',
          color: color[1]
        },
        itemStyle: {
          color: color[1]
        },
        barWidth: '50%',
        yAxisIndex: 1,
        data: pageCtl.report6Data.yAxis2
      }
    ]
  }
})

</script>

<style lang="scss">
#piplineSP {
  .hide-conditions {
    margin-bottom: 10px;
  }

  .hide-conditions .el-input__inner {
    height: 26px !important;
  }

  .fullscreen:hover {
    color: var(--scp-text-color-success);
  }

  .pipeline-body .el-select, .pipeline-body .el-date-editor.el-input {
    width: calc(100% - 0.5rem);
  }

  .el-date-editor--monthrange.el-input, .el-date-editor--monthrange.el-input__inner {
    width: auto !important
  }

  .pull-left.drag-handle {
    width: calc(100% - 50px);
    cursor: move;
  }

  .page {
    height: 400px;
    margin-bottom: 10px;
  }
}
</style>
