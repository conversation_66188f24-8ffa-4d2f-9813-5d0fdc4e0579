<template>
  <div class="left-sidebar" id="SOTransfer">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.filterOpts"/>
          </el-col>
          <el-col :span="2">
            <el-select v-model="pageCtl.conditions.specialType" size="small" style="width: 100% !important;" class="search-group-left">
              <el-option label="MATERIAL" value="MATERIAL"/>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input :placeholder="pageCtl.conditions.specialType" size="small" v-model="pageCtl.conditions.specialContent" type="textarea"
                      style="width: 100% !important;" class="search-group-right"></el-input>
          </el-col>
          <el-col :span="4" style="padding-left: 10px">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"/>

            <el-button @click="toggleCalcWin" size="small" :loading="pageCtl.loading.calc" style="margin-left: 10px">
              <font-awesome-icon icon="fa-sliders"/>
            </el-button>
          </el-col>
        </el-row>
        <div v-show="pageCtl.visible.calc">
          <div class="subscript-container calc-form" style="margin-bottom: 10px;">
            <el-row>
              <el-col :span="24" class="content">
                <scp-cascader
                    style="width: 300px"
                    v-model="pageCtl.conditions.calcConditions.filterList"
                    :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                    :options="pageCtl.filterOpts"/>
                <span class="input-tips">需要调拨订单的范围</span>
              </el-col>
              <el-col :span="2" class="content">
                <el-select v-model="pageCtl.conditions.calcConditions.specialType" size="small" style="width: 100% !important;" class="search-group-left">
                  <el-option label="MATERIAL" value="MATERIAL"/>
                </el-select>
              </el-col>
              <el-col :span="22" class="content">
                <el-input :placeholder="pageCtl.conditions.calcConditions.specialType" size="small" v-model="pageCtl.conditions.calcConditions.specialContent"
                          style="width: 280px !important;" type="textarea" class="search-group-right"></el-input>
                <span class="input-tips">针对哪些MATERIAL执行调拨计算, 多个MATERIAL用换行隔开, 可以使用Excel直接复制</span>
              </el-col>

              <el-col :span="24" class="content">
                <el-row>
                  <el-col :span="11" class="dc-out">
                    <el-divider content-position="left">SO Transfer From</el-divider>
                    <el-col :span="24" class="content">
                      <el-input-number v-model="pageCtl.conditions.calcConditions.doOutSsPercent" controls-position="right" :min="0" :max="120"
                                       :step="0.1" style="width: 120px" size="small"/>
                      <span class="input-tips">计算调出SO时, DC允许保留的安全库存百分比<b>%</b></span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-select
                          style="width: 200px"
                          v-model="pageCtl.conditions.calcConditions.partiallDeliveried"
                          size="small">
                        <el-option label="Y" value="Y"/>
                        <el-option label="N" value="N"/>
                      </el-select>
                      <span class="input-tips">Partiall Deliveried, 是否需要将已经部分发货订单进行调出</span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-date-picker
                          v-model="pageCtl.conditions.calcConditions.transferEndDate"
                          type="date"
                          size="small"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          style="width: 200px;"
                          :clearable="false"
                          placeholder="Select Date">
                      </el-date-picker>
                      <span class="input-tips">CRD在{{ pageCtl.conditions.calcConditions.transferEndDate }}之前, 无法Fulfill的订单将会被调出</span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-select
                          style="width: 200px"
                          v-model="pageCtl.conditions.calcConditions.dcOutfulfillType"
                          size="small">
                        <el-option label="Un-Restrict" value="FULFILL_OR_NOT_UNRESTRICT"/>
                        <el-option label="Non-Block" value="FULFILL_OR_NOT_NONBLOCK"/>
                      </el-select>
                      <span class="input-tips">需要调出订单的类型, 默认Non-Block</span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-select
                          style="width: 200px"
                          v-model="pageCtl.conditions.calcConditions.dcOutSoPolicy"
                          size="small">
                        <el-option label="ALL" value="ALL"/>
                        <el-option label="MTO" value="MTO"/>
                        <el-option label="MTS" value="MTS"/>
                      </el-select>
                      <span class="input-tips">订单库存策略</span>
                    </el-col>
                    <el-col :span="24">
                      <div class="drag-div" style="width: 200px;float:left">
                        <div class="drag-item" v-for="(soDC, index) in pageCtl.conditions.calcConditions.soDCPriority" :key="soDC">
                          <el-button-group style="width: 100%;">
                            <el-button style="width: 144px">{{ soDC }}</el-button>
                            <el-button style="width: 28px" @click="moveUp('soDCPriority', index)">
                              <font-awesome-icon icon="angle-up" title="Move Up"/>
                            </el-button>
                            <el-button style="width: 28px" @click="moveDown('soDCPriority', index)">
                              <font-awesome-icon icon="angle-down" title="Move Down"/>
                            </el-button>
                          </el-button-group>
                        </div>
                      </div>
                      <span class="input-tips">按照 {{ pageCtl.conditions.calcConditions.soDCPriority.join(' → ') }} 顺序依次调出</span>
                    </el-col>
                  </el-col>
                  <el-col :span="1" class="dc-arrow">
                    →
                  </el-col>
                  <el-col :span="12" class="dc-in">
                    <el-divider content-position="left">SO Transfer To</el-divider>
                    <el-col :span="24" class="content">
                      <el-input-number v-model="pageCtl.conditions.calcConditions.ssPercent" controls-position="right" :min="0" :max="120"
                                       :step="0.1" style="width: 120px" size="small"/>
                      <span class="input-tips">计算调入SO时, DC需要保留的安全库存百分比<b>%</b></span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-date-picker
                          v-model="pageCtl.conditions.calcConditions.fulfillFixedDate"
                          type="date"
                          size="small"
                          format="YYYY/MM/DD"
                          value-format="YYYY/MM/DD"
                          style="width: 200px;"
                          :clearable="false"
                          placeholder="Select Date">
                      </el-date-picker>
                      <span class="input-tips">计算调入SO时, 需要为本DC保留CRD在{{
                          pageCtl.conditions.calcConditions.fulfillFixedDate
                        }}之前的SO库存</span>
                    </el-col>
                    <el-col :span="24" class="content">
                      <el-select
                          style="width: 200px"
                          v-model="pageCtl.conditions.calcConditions.dcInfulfillType"
                          size="small">
                        <el-option label="Un-Restrict" value="FULFILL_OR_NOT_UNRESTRICT"/>
                        <el-option label="Non-Block" value="FULFILL_OR_NOT_NONBLOCK"/>
                      </el-select>
                      <span class="input-tips">在计算SO锁库存的时候, 需要计算那种类型的SO</span>
                    </el-col>
                    <el-col :span="24">
                      <div class="drag-div" style="width: 200px;float:left">
                        <div class="drag-item" v-for="(sohDC, index) in pageCtl.conditions.calcConditions.sohDCPriority" :key="sohDC">
                          <el-button-group style="width: 100%;">
                            <el-button style="width: 144px">{{ sohDC }}</el-button>
                            <el-button style="width: 28px" @click="moveUp('sohDCPriority', index)">
                              <font-awesome-icon icon="angle-up" title="Move Up"/>
                            </el-button>
                            <el-button style="width: 28px" @click="moveDown('sohDCPriority', index)">
                              <font-awesome-icon icon="angle-down" title="Move Down"/>
                            </el-button>
                          </el-button-group>
                        </div>
                      </div>
                      <span class="input-tips">按照 {{ pageCtl.conditions.calcConditions.sohDCPriority.join(' → ') }} 顺序依次调入</span>
                    </el-col>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" class="content">
                <el-button
                    type="info"
                    @click="pageCtl.visible.calc = false">
                  Close
                </el-button>
                <el-button
                    type="primary"
                    @click="calc">
                  开始计算
                </el-button>
              </el-col>
              <el-col :span="24">
                <el-divider content-position="left">SO Transfer Logs</el-divider>
                <scp-table
                    ref="report1TableRef"
                    url="/demand/so_transfer/query_report1"
                    download-url="/demand/so_transfer/download_report1"
                    :context-menu-items="pageCtl.report1ContextMenuItems"
                    :columns="pageCtl.report1TableColumn"
                    :after-select="afterReport1Select"
                />
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="subscript-container">
          <scp-subscript id="DSTP"/>
          <el-row>
            <el-col :span="4" class="report-sub-title-right" style="top: 6px;">
              <el-select v-model="pageCtl.conditions.report2Type" size="small" style="width: 100%" multiple filterable clearable collapse-tags>
                <el-option
                    v-for="item in _pivotColumns"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <h3 style="text-align: left;margin-top:6px">SO Transfer Proposal List</h3>
          <scp-table
              :max-height="355"
              url="/demand/so_transfer/query_report2"
              download-url="/demand/so_transfer/download_report2"
              ref="report2TableRef"
              :lazy="true"
              :params="pageCtl.conditions"
              :columns="_report2TableColumn"
              :context-menu-items="pageCtl.report2ContextMenuItems"
              :after-select="afterReport2Select"
              :show-total="true"/>
        </div>

        <div class="subscript-container">
          <scp-subscript id="DSTL"/>
          <scp-table
              :max-height="355"
              url="/demand/so_transfer/query_report3"
              download-url="/demand/so_transfer/download_report3"
              ref="report3TableRef"
              :lazy="true"
              :params="pageCtl.conditions"
              :columns="pageCtl.report3TableColumn"/>
        </div>
      </div>
    </div>

    <!-- report1 transfer log-->
    <scp-draggable-resizable v-model="pageCtl.visible.report1Logs" w="60vw" h="450px" title="Transfer Log">
      <template v-slot="{ height }">
        <scp-md-preview :style="{ height : height - 100 + 'px'}" style="padding: 0 10px" v-model="pageCtl.report1Logs"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $setDefaultMaterialOwner: any = inject('$setDefaultMaterialOwner')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const searchRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()
const report3TableRef = ref()

const moveUp = (name, index) => {
  if (index !== 0) {
    pageCtl.conditions.calcConditions[name][index] = pageCtl.conditions.calcConditions[name].splice(index - 1, 1, pageCtl.conditions.calcConditions[name][index])[0]
  } else {
    pageCtl.conditions.calcConditions[name].push(pageCtl.conditions.calcConditions[name].shift())
  }
}

const moveDown = (name, index) => {
  if (index !== pageCtl.conditions.calcConditions[name].length - 1) {
    pageCtl.conditions.calcConditions[name][index] = pageCtl.conditions.calcConditions[name].splice(index + 1, 1, pageCtl.conditions.calcConditions[name][index])[0]
  } else {
    pageCtl.conditions.calcConditions[name].unshift(pageCtl.conditions.calcConditions[name].splice(index, 1)[0])
  }
}

const viewReport2Details = () => {
  $viewDetails({
    title: pageCtl.report2DetailsTitle,
    url: '/demand/so_transfer/query_report2_details',
    durl: '/demand/so_transfer/download_report2_details',
    params: pageCtl.conditions
  })
}

const searchReport1Log = () => {
  $axios({
    method: 'post',
    url: '/demand/so_transfer/query_report1_logs',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Logs = body
    pageCtl.visible.report1Logs = true
  })
}

const pageCtl = reactive({
  filterOpts: [],
  loading: {
    filter: false,
    calc: false
  },
  visible: {
    calc: false,
    report1Logs: false
  },
  conditions: {
    calcConditions: {
      partiallDeliveried: 'N',
      filterList: [],
      dcOutSoPolicy: 'ALL',
      ssPercent: 0,
      doOutSsPercent: 0,
      specialType: 'MATERIAL',
      specialContent: '',
      fulfillFixedDate: '', // 在此之前(包含选择当天)的fulfill订单, 将会保留库存
      transferEndDate: '', // 在此之前的不可被fulfill的订单, 将参与调拨
      dcInfulfillType: 'FULFILL_OR_NOT_NONBLOCK', // 在计算DC可用库存的时候, 需要扣掉的Fulfill订单的类型
      dcOutfulfillType: 'FULFILL_OR_NOT_NONBLOCK', // 在计算哪些SO可以调出的时候, 订单的类型
      soDCPriority: ['O001', 'N001', 'M001', 'I001', 'I003'],
      sohDCPriority: ['O001', 'N001', 'M001', 'I001', 'I003']
    },
    filterList: [],
    specialType: 'MATERIAL',
    specialContent: '',
    report1SelectedValues: '',
    report2Type: ['BU', 'ENTITY'],
    report2SelectedType: [],
    report2SelectedFrom: '',
    report2SelectedTo: ''
  },
  report1Logs: '',
  report1ContextMenuItems: {
    view_transfer_log: {
      name: 'View logs',
      callback: searchReport1Log
    },
    view_split0: { name: '---------' }
  },
  report1TableColumn: [
    { data: 'TRANSFER_SEQ' },
    { data: 'SALES_ORDER_NUMBER' },
    { data: 'SALES_ORDER_ITEM' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'CRD_DATE' },
    { data: 'DELIVERY_PRIORITY' },
    { data: 'TRANSFER_OR_NOT' },
    { data: 'TRANSFER_PLANT' },
    { data: 'TRANSFER_QTY', type: 'numeric' },
    { data: 'SO_TOTAL_QTY', type: 'numeric' },
    { data: 'OPR_TIME' }
  ],
  report2DetailsTitle: '',
  report2ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport2Details
    },
    view_split0: { name: '---------' }
  },
  report3TableColumn: [
    { data: 'BASIC_CHANGE_INFORMATION' },
    { data: 'CHANGE_DATE' },
    { data: 'PLANT' },
    { data: 'MATERIAL' },
    { data: 'REQUEST_QTY', type: 'numeric', precision: 2 },
    { data: 'GROSS_WEIGHT_KG', type: 'numeric', precision: 4 },
    { data: 'SO_NUMBER' },
    { data: 'SO_ITEM' },
    { data: 'ITEM_CATEGORY' },
    { data: 'SHIP_TO_CITY' },
    { data: 'CUSTOMER_CODE' },
    { data: 'CUSTOMER_NAME' },
    { data: 'ORIGINAL_SHIPPING_DC' },
    { data: 'CHANGED_SHIPPING_DC' },
    { data: 'CUSTOMER_REQUEST_DATE' },
    { data: 'ARRIVE_CUSTOMER_BY_CHANGE_DC' },
    { data: 'ORIGINAL_DC_LT', type: 'numeric', precision: 2 },
    { data: 'CHANGE_DC_LT', type: 'numeric', precision: 2 },
    { data: 'ORIGINAL_DC_COST', type: 'numeric', precision: 2 },
    { data: 'CHANGE_DC_COST', type: 'numeric', precision: 2 },
    { data: 'COST_IMPACT', type: 'numeric', precision: 2 }
  ]
})

onMounted(() => {
  initPage()
  search()
})

const initPage = () => {
  const now = new Date()
  pageCtl.conditions.calcConditions.fulfillFixedDate = $dateFormatter(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3), 'yyyy/MM/dd')
  pageCtl.conditions.calcConditions.transferEndDate = $dateFormatter(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 3), 'yyyy/MM/dd')

  $axios({
    method: 'post',
    url: '/demand/so_transfer/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.CASCADER
    searchRef.value.loadAndClick()
    $setDefaultMaterialOwner(pageCtl.filterOpts, pageCtl.conditions.calcConditions.filterList)
  })
}

const toggleCalcWin = () => {
  pageCtl.visible.calc = !pageCtl.visible.calc
}

const calc = () => {
  pageCtl.loading.calc = true
  $axios({
    method: 'post',
    url: '/demand/so_transfer/calc',
    data: pageCtl.conditions.calcConditions
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success('Success!')
      search()
    }
  }).finally(() => {
    pageCtl.loading.calc = false
  })
}

const search = () => {
  searchReport1()
  searchReport2()
  searchReport3()
}

const searchReport1 = () => {
  report1TableRef.value.search()
}

const searchReport2 = () => {
  report2TableRef.value.search()
}

const searchReport3 = () => {
  report3TableRef.value.search()
}

const afterReport1Select = (r) => {
  pageCtl.conditions.report1SelectedValues = r.TRANSFER_SEQ
}

const afterReport2Select = (r) => {
  const select = [] as any
  for (let i = 0; i < _report2Type.value.length; i++) {
    select.push(r[_report2Type.value[i]])
  }
  pageCtl.conditions.report2SelectedType = select

  if (select[0] === 'Total') {
    pageCtl.conditions.report2SelectedFrom = ''
    pageCtl.conditions.report2SelectedTo = ''
  } else {
    pageCtl.conditions.report2SelectedFrom = r.TRANSFER_FROM_DC
    pageCtl.conditions.report2SelectedTo = r.TRANSFER_TO_DC
  }

  pageCtl.report2DetailsTitle = 'View Details [' + select.join(', ') + ']'
}

const _report2Type = computed(() => {
  if (pageCtl.conditions.report2Type.length > 0) {
    return pageCtl.conditions.report2Type
  } else {
    return ['ENTITY']
  }
})

const _report2TableColumn = computed(() => {
  const columns: any = []

  for (let i = 0; i < _report2Type.value.length; i++) {
    columns.push({
      data: _report2Type.value[i]
    })
  }

  columns.push({
    data: 'TRANSFER_FROM_DC'
  }, {
    data: 'TRANSFER_TO_DC'
  }, {
    data: 'TRANSFER_QTY',
    type: 'numeric'
  }, {
    data: 'TRANSFER_VALUE',
    type: 'numeric'
  }, {
    data: 'TRANSFER_ORDER_LINE',
    type: 'numeric'
  })

  return columns
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

</script>

<style lang="scss">
#SOTransfer {
  .calc-form {
    padding-top: 10px;
    padding-left: 10px;
    box-shadow: 0 2px 4px rgba(255, 0, 0, .5), 0 0 6px rgba(255, 0, 0, .75);

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      margin-bottom: 10px;
    }

    .dc-out {
      padding: 0 10px 10px 0;
      border-bottom: 1px solid var(--scp-border-color-lighter);
      height: 450px
    }

    .dc-in {
      border-bottom: 1px solid var(--scp-border-color-lighter);
      padding: 0 10px 10px 10px;
      height: 450px
    }

    .dc-arrow {
      border-right: 1px dotted var(--scp-border-color-lighter);
      border-left: 1px dotted var(--scp-border-color-lighter);
      height: 450px;
      line-height: 340px;
      font-size: 1.5rem;
      text-align: center;
      border-bottom: 1px solid var(--scp-border-color-lighter);
    }

    .el-row {
      margin-bottom: 12px;
    }

    .input-tips {
      padding-left: 10px;
      font-style: italic;
      color: var(--scp-text-color-secondary);
    }

    .drag-div {
      width: 200px;
      border-bottom: 1px solid var(--scp-border-color-lighter);

      .drag-item {
        height: 24px;
        text-align: center;
        line-height: 24px;
        cursor: pointer;
      }

      .drag-item:hover {
        background-color: var(--scp-bg-color-fill);
      }
    }
  }
}
</style>
