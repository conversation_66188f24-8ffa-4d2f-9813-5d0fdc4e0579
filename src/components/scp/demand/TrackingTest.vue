<template>
  <div class="left-sidebar" id="demandTracking">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <!-- date type -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.dateType" size="small">
              <el-option
                  v-for="item in ['Month to Date','Quarter to Date','Year to Date']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- filter options -->
          <el-col :span="4">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                :options="pageCtl.filterOpts"/>
          </el-col>
          <!--'Net Net Price','Moving Average Price','Quantity','Line'-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small">
              <el-option
                  v-for="item in ['Net Net Price','Moving Average Price', 'Net Net Price HKD', 'Quantity', 'Line']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- fcst version -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.fcstVersion" size="small" :loading="pageCtl.loading.fcstVersion">
              <el-option
                  v-for="item in pageCtl.fcstVersion"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <!-- scope -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.scope" size="small">
              <el-option
                  v-for="item in ['SECI', 'PLANT']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- scope -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.subScope" size="small" collapse-tags multiple
                       placeholder="Sub-Scope">
              <el-option
                  v-for="item in _subScopeOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!-- warning value -->
          <el-col :span="2">
            <el-input-number v-model="pageCtl.warningValue" size="small" :precision="0" :step="5" :max="200" :min="0"
                             style="width: var(--scp-input-width) !important;"/>
          </el-col>
          <el-col :span="3">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['saleMonth','fcstVersion','report3Year']"
                        :afterExpand="moreConditions"/>
          </el-col>
        </el-row>
        <el-row class="search-box" v-show="pageCtl.conditionsExpanded">
          <!-- sales month -->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.saleMonth" size="small">
              <el-option
                  v-for="item in _saleMonthRange"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-select v-model="pageCtl.conditions.specialType" size="small" style="width: 100% !important;"
                       class="search-group-left">
              <el-option label="MATERIAL" value="MATERIAL"/>
              <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
              <el-option label="SHIP_TO_CODE" value="SHIP_TO"/>
              <el-option label="SHIP_TO_REGION" value="SHIP_TO_REGION"/>
              <el-option label="SOLD_TO" value="SOLD_TO"/>
              <el-option label="PROJECT_NAME" value="PROJECT_NAME"/>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-input :placeholder="pageCtl.conditions.specialType" size="small"
                      v-model="pageCtl.conditions.specialContent"
                      style="width: var(--scp-input-width) !important;" type="textarea"
                      class="search-group-right"></el-input>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.report1OrderBy" size="small"
                       style="width: var(--scp-input-width) !important;" placeholder="Order by"
                       clearable>
              <el-option label="Order Intake" value="ORDER_INTAKE"/>
              <el-option label="CRD" value="CRD"/>
              <el-option label="Sales" value="SALES"/>
              <el-option label="Back Order" value="BACKORDER"/>
              <el-option label="Backlog" value="BACKLOG"/>
              <el-option label="SOH" value="SOH"/>
              <el-option label="UD" value="UD"/>
              <el-option label="OUTPUT" value="OUTPUT"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.report1BacklogType" size="small"
                       style="width: var(--scp-input-width) !important;"
                       placeholder="Backlog Type">
              <el-option
                  v-for="item in ['OPEN_SO_W_O_GI', 'OPEN_SO_W_O_DEL']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-select v-model="pageCtl.unit" @change="searchReport1" size="small">
              <el-option
                  v-for="item in ['M', 'K', '1']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="part1">
            <div class="subscript-container" v-loading="pageCtl.loading.report1 || pageCtl.loading.filter">
              <scp-subscript id="DTDS"/>
              <div class="tree-table-box pull-left" @contextmenu.prevent="">
                <el-table ref="report1TableRef" style="border:0" :data="pageCtl.report1Data" row-key="id"
                          @expand-change="report1ExpandChange" border lazy :load="load"
                          :tree-props="{children: 'children', hasChildren: 'hasChildren'}" class="tree-table"
                          :row-class-name="warningRowClassName" @row-dblclick="report1RowDblick">
                  <el-table-column prop="category1" :label="pageCtl.conditions.category1Value" min-width="130px">
                    <template #header>
                      <el-tooltip effect="light" :show-after="500" :content="pageCtl.conditions.category1Value"
                                  placement="top-end">
                        <el-select v-model="pageCtl.conditions.category1Value" size="small" style="border:0 !important"
                                   class="hide-input-border" filterable>
                          <el-option
                              v-for="item in _pivotColumns"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-tooltip>
                    </template>
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" :content="scope.row.category1" placement="top-end">
                        <div class="category1-div" v-if="scope.row.category1 !== 'Total'"
                             :data-value="pageCtl.conditions.category1Value + '=' + scope.row.category1">
                          {{ scope.row.category1 }}
                        </div>
                        <div v-else>Total</div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="category2" :label="pageCtl.conditions.category2Value">
                    <template #header>
                      <el-tooltip effect="light" :show-after="500" :content="pageCtl.conditions.category2Value"
                                  placement="top-end">
                        <el-select v-model="pageCtl.conditions.category2Value" size="small" style="border:0 !important"
                                   class="hide-input-border" filterable>
                          <el-option
                              v-for="item in _pivotColumns"
                              :key="item"
                              :label="item"
                              :value="item">
                          </el-option>
                        </el-select>
                      </el-tooltip>
                    </template>
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" :content="scope.row.category2" placement="top-end">
                        <div class="category2-div"
                             :data-value="pageCtl.conditions.category2Value + '=' + scope.row.category2">
                          {{ scope.row.category2 }}
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="orderIntake" label="Order Intake" align="right">
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'Order Intake,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.orderIntake) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="crd" label="CRD" align="right">
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'CRD,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.crd) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sales" label="Sales" align="right">
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'Sales,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.sales) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="fcst" label="FCST" align="right">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category8Value" style="border:0 !important"
                                 class="hide-input-border">
                        <el-option
                            v-for="item in _category8ValueOpts"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'FCST,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.fcst) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="backorder" label="Back Order" align="right">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category5Value" style="border:0 !important"
                                 class="hide-input-border">
                        <el-option
                            v-for="item in ['Back Order', 'Back Order Block', 'Back Order Non-Block']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'Back Order,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.backorder) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="backlog" label="Backlog" align="right">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category6Value" style="border:0 !important"
                                 class="hide-input-border">
                        <el-option
                            v-for="item in ['Backlog', 'Backlog Block', 'Backlog Non-Block']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'Backlog,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.backlog) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ud" label="UD" align="right">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category7Value" style="border:0 !important"
                                 class="hide-input-border ud-header"
                                 multiple collapse-tags placeholder="UD" :filterable="false">
                        <el-option
                            v-for="item in [{
                            label: 'UD ≤7CD',
                            value: 'UD_NORMAL'
                          },{
                            label: 'UD ≤30CD',
                            value: 'UD_MID_AGING'
                          },{
                            label: 'UD >30CD',
                            value: 'UD_LONG_AGING'
                          },{
                            label: 'UD CB',
                            value: 'UD_CB'
                          }]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'UD,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.ud) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="soh" label="SOH" align="right">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category4Value" style="border:0 !important;"
                                 class="hide-input-border">
                        <el-option
                            v-for="item in ['SOH', 'SOH Aging <1Y', 'SOH Aging 1-2Y', 'SOH Aging >2Y']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'SOH,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.soh) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="output" label="FAC Output" align="right">
                    <template v-slot="scope">
                      <div v-contextmenu:contextmenu2Ref @contextmenu.prevent="rightClick2"
                           :data-value="'Output,' + (scope.row.category1 || scope.row.parentName) + ',' + (scope.row.category2 || '')">
                        {{ formatter(scope.row.output) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="percentBar" :label="pageCtl.conditions.category3Value" :min-width="80">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category3Value" style="border:0 !important"
                                 class="hide-input-border">
                        <el-option
                            v-for="item in ['Sales','CRD','Order Intake', 'Output']"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <el-tooltip effect="light" :show-after="500" placement="top-end">
                        <template #content>
                          Fulfill: {{ $thousandBitSeparator(scope.row.fulfill, 1) }}%
                        </template>
                        <div style="width: 100%;height:22px;position:relative;">
                          <div style="background-color: #b6e5bf; height:16px;position:absolute;top:3px;"
                               :style="{width:scope.row.targetAllWidth}">
                            <div
                                style="height:8px;border:solid 1px var(--scp-text-color-success);background-color: var(--scp-text-color-success); position: absolute;top:4px;"
                                :style="{width:scope.row.actualWidth}"></div>
                            <div
                                style="border-right: solid 1px var(--scp-border-color-error);height:22px; position: absolute;top:-3px;"
                                :style="{width:scope.row.targetWidth}">
                              <span style="color:var(--scp-text-color-primary)">{{
                                  $thousandBitSeparator(scope.row.fulfill, 1)
                                }}%</span>
                            </div>
                          </div>
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="clearfix"/>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="subscript-container subscript-container-left"
                 v-loading="pageCtl.loading.report2 || pageCtl.loading.filter">
              <scp-subscript id="DTDT"/>
              <el-row>
                <el-col :span="4" class="report-sub-title-right"
                        :style="{ transform: 'translateX(-150px) translateY(3px)' }">
                  <el-select v-model="pageCtl.conditions.report2Type" size="small" style="width: 100px">
                    <el-option
                        v-for="item in [{value: 'realtime', label: 'Till Now'},{value: 'fulltime', label: 'Full Range'}]"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <chart ref="report2Ref" :height="320" :option="_report2Opt"/>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="subscript-container subscript-container-right"
                 v-loading="pageCtl.loading.report2 || pageCtl.loading.report4 || pageCtl.loading.filter">
              <scp-subscript id="DTDA"/>
              <chart :height="320" :option="pageCtl.report4Opt"/>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="subscript-container" v-loading="pageCtl.loading.report3 || pageCtl.loading.filter">
              <scp-subscript id="DTDY"/>
              <el-row>
                <el-col :span="4" class="report-sub-title-right"
                        :style="{ transform: 'translateX(-125px) translateY(3px)' }">
                  <el-select v-model="pageCtl.conditions.report3Year" size="small" multiple collapse-tags
                             @change="searchReport3" style="width: 100px">
                    <el-option
                        v-for="item in [(new Date().getFullYear() - 1) + '', (new Date().getFullYear()) + '', (new Date().getFullYear() + 1) + '']"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <chart ref="report3Ref" :height="280" :option="_report3Opt"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="contextmenu2Ref">
      <v-contextmenu-item @click="viewReport1Details">
        View Details
      </v-contextmenu-item>
      <v-contextmenu-item @click="downloadReport1">
        Download this page
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch, nextTick } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $px2Rem: any = inject('$px2Rem')
const $axios: any = inject('$axios')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $calcMonthGap: any = inject('$calcMonthGap')
const $downloadFile: any = inject('$downloadFile')
const $message: any = inject('$message')
const $shortenNumber: any = inject('$shortenNumber')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const searchRef = ref()
const report1TableRef = ref()
const report2Ref = ref()
const report3Ref = ref()
const contextmenu2Ref = ref()

const pageCtl = reactive({
  initing: true, // 初次加载的标记位, 用来判断页面是不是第一次加载, 这样可以禁用一些watch函数, 从而让Variant可以正确的赋值
  warningValue: 130,
  unit: '1',
  filterConditions: '',
  filterOpts: [],
  conditionsExpanded: false,
  loading: {
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    filter: false,
    fcstVersion: false
  },
  conditions: {
    scope: 'SECI',
    subScope: ['SECI_OG'],
    type: 'Net Net Price',
    dateType: 'Month to Date',
    saleMonth: '',
    category1Value: 'BU',
    category2Value: 'PRODUCT_LINE',
    category3Value: 'Sales',
    category4Value: 'SOH',
    category5Value: 'Back Order',
    category6Value: 'Backlog',
    category7Value: ['UD_NORMAL', 'UD_MID_AGING', 'UD_LONG_AGING', 'UD_CB'],
    category8Value: 'SECI_CSF_DEMAND',
    fcstVersion: '',
    expandCategory1: '',
    viewType: '',
    viewCategory1: '',
    viewCategory2: '',
    selectedCategory1: '',
    selectedCategory2: '',
    filterList: [],
    specialType: 'MATERIAL',
    specialContent: '',

    report1BacklogType: 'OPEN_SO_W_O_GI',
    report1OrderBy: '',
    report2Type: 'realtime',
    report3Year: [new Date().getFullYear() + ''],
    report2SelectedDate: '', // x axis
    report2SelectedYear: '' // this year or last year
  },
  fcstVersion: [],
  report1LastExpandRow: null as any,
  report1Data: [],
  report2Title: '',
  report2Data: {} as any,
  report3Title: '',
  report3Data: {} as any,
  report4Title: '',
  report4DataRaw01: {} as any,
  report4DataRaw02: {},
  report4Opt: {}
})

watch(() => pageCtl.conditions.dateType, () => {
  pageCtl.conditions.saleMonth = _saleMonthRange.value[0]
})

watch(() => pageCtl.conditions.scope, () => {
  if (pageCtl.initing === false) {
    pageCtl.conditions.subScope = []
  }
  nextTick(() => {
    if (_category8ValueOpts.value.length > 0) {
      pageCtl.conditions.category8Value = _category8ValueOpts.value[0].value
    }
  })
})

watch(() => pageCtl.conditions.report2Type, () => {
  searchReport2()
})

onMounted(() => {
  initPage()

  const report2Chart = report2Ref.value.chart()
  const legend = _report2Opt.value.legend.data

  report2Chart.on('legendselectchanged', (obj) => {
    const name = obj.name

    // 如果用户选择了K线图, 那么禁用所有非此K线图的其他所有图例, 包括隐藏图例
    if (name.indexOf('(K)') !== -1) {
      for (let i = 0; i < legend.length; i++) {
        if (name !== legend[i]) {
          // 禁用正常图例
          report2Chart.dispatchAction({
            type: 'legendUnSelect',
            name: legend[i]
          })

          // 禁用隐藏图例
          report2Chart.dispatchAction({
            type: 'legendUnSelect',
            name: legend[i] + '1'
          })
        }
      }
      // 禁用FCST图例
      report2Chart.dispatchAction({
        type: 'legendUnSelect',
        name: 'Avg.FCST'
      })
    } else {
      // 如果用户选择的是非K线图, 那么禁用所有K线图
      for (let i = 0; i < legend.length; i++) {
        if (legend[i].indexOf('(K)') !== -1) {
          report2Chart.dispatchAction({
            type: 'legendUnSelect',
            name: legend[i]
          })
        }
      }

      // 启用选中图例对应的隐藏图例
      if (name === 'Sales' || name === 'Ord.Int.' || name === 'Output') {
        report2Chart.dispatchAction({
          type: obj.selected[name] ? 'legendSelect' : 'legendUnSelect',
          name: name + '1'
        })
      }
    }
  })

  report2Chart.on('dblclick', (obj) => {
    if (obj.seriesName && ((obj.seriesName.indexOf('Output') === -1 && obj.seriesName.indexOf('Ord.Int.') === -1 && obj.seriesName.indexOf('Sales') === -1) || obj.seriesName.indexOf('(K)') !== -1)) {
      return
    }
    if (obj.name === '[AVG]') {
      $message.warning('AVG does not support View Details')
      return
    }
    pageCtl.conditions.report2SelectedDate = obj.name
    if (obj.seriesName.replace('.1', '.') === 'Ord.Int.') {
      pageCtl.conditions.viewType = 'Order Intake'
    } else if (obj.seriesName.replace('.1', '.') === 'Sales') {
      pageCtl.conditions.viewType = 'Sales'
    } else if (obj.seriesName.replace('.1', '.') === 'Output') {
      pageCtl.conditions.viewType = 'Output'
    }
    pageCtl.conditions.report2SelectedYear = obj.seriesName.indexOf('1') === -1 ? 'THIS_YEAR' : 'LAST_YEAR'

    $viewDetails({
      title: '[' + pageCtl.conditions.viewType + '] ' + pageCtl.conditions.report2SelectedDate + ' - ' + (pageCtl.conditions.report2SelectedYear === 'LAST_YEAR' ? 'Last Year' : 'This Year'),
      url: '/demand/tracking_test/query_report2_details',
      durl: '/demand/tracking_test/download_data2',
      params: pageCtl.conditions
    })
  })
})

/*
 * @name: 查询四张报表
 * @desciption: 查询之前, 重置第2 3 4张报表的标题, 清除已选择的项目
 */

const search = () => {
  pageCtl.conditions.selectedCategory1 = ''
  pageCtl.conditions.selectedCategory2 = ''

  pageCtl.initing = false // 触发按钮事件时, 标记为页面加载完成

  pageCtl.report2Title = _titlePrefix.value + ' Actual Demand Comparison [Total]'
  pageCtl.report3Title = 'Demand Evolution [Total]'
  pageCtl.report4Title = _titlePrefix.value + ' Actual Demand vs FCST(M-' + $calcMonthGap(pageCtl.conditions.fcstVersion, $dateFormatter(new Date(), 'yyyyMM')) + ') [Total]'
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

/*
 * @name: 加载级联选择器
 * @desciption: 加载的时候, placeholder显示loading字样
 */

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/demand/tracking_test/init_page'
  }).then((body) => {
    const version = [] as any
    const currentMonth = $dateFormatter(new Date(), 'yyyyMM')
    const fcst = body.FCST
    pageCtl.conditions.saleMonth = body.LAST_WD_MONTH
    for (let i = 0; i < fcst.length; i++) {
      version.push({
        label: 'M-' + $calcMonthGap(fcst[i], currentMonth) + ' [' + fcst[i] + ']',
        value: fcst[i]
      })
    }
    pageCtl.fcstVersion = version
    pageCtl.conditions.fcstVersion = version[0].value
    pageCtl.filterOpts = body.CASCADER

    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
/*
* 显示更多查询条件
*/
const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}
/*
 * @name: 双击表1的列
 * @desciption: 根据双击列的内容, 重新生成第2 3 4张报表的标题, 并且将选择的信息保存起来, 供查询使用
 */
const report1RowDblick = (row) => {
  const subTitle = row.category2 ? row.category2 : row.category1
  pageCtl.report2Title = _titlePrefix.value + ' Actual Demand Comparison [' + subTitle + ']'
  pageCtl.report3Title = 'Demand Evolution [' + subTitle + ']'
  pageCtl.report4Title = _titlePrefix.value + ' Actual Demand vs FCST(M-' + $calcMonthGap(pageCtl.conditions.fcstVersion, $dateFormatter(new Date(), 'yyyyMM')) + ') [' + subTitle + ']'

  const category1 = (row.parentName || row.category1)
  if (category1 !== 'Total') {
    pageCtl.conditions.selectedCategory1 = (row.parentName || row.category1)
    pageCtl.conditions.selectedCategory2 = row.category2
  } else {
    pageCtl.conditions.selectedCategory1 = ''
    pageCtl.conditions.selectedCategory2 = ''
  }

  searchReport2()
  searchReport3()
  searchReport4()
}
/*
 * @name: 报表1查询
 * @desciption: 报表1的数据需要经过处理之后才能显示在页面中
 */
const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/demand/tracking_test/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = parseReport1Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}
/*
 * @name: 报表2查询
 * @desciption: 同报表1
 */
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/demand/tracking_test/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = parseReport2Data(body)
    pageCtl.report4DataRaw01 = parseReport4Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
    if (!pageCtl.loading.report4) { // 如果report2加载完成的时候report4也完成了, 那直接渲染report4
      pageCtl.report4Opt = parseReport4Opts()
    }
    if (pageCtl.conditions.scope && pageCtl.conditions.scope.indexOf('IG') !== -1) {
      report2Ref.value.chart().dispatchAction({
        type: 'legendUnSelect',
        name: 'Avg.FCST'
      })
    }
  })
}
/*
 * @name: 报表3查询
 * @desciption: 同报表1
 */
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/demand/tracking_test/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = parseReport3Data(body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
    if (pageCtl.conditions.scope && pageCtl.conditions.scope.indexOf('IG') !== -1) {
      report3Ref.value.chart().dispatchAction({
        type: 'legendUnSelect',
        name: 'FCST'
      })
    }
  })
}
/*
 * @name: 报表2查询
 * @desciption: fcst, order intake 和 sales在report2中已经查询过, 这里就不再查询, 仅仅查询crd和title的数据,然后将两者合并即可
 */
const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/demand/tracking_test/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4DataRaw02 = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
    if (!pageCtl.loading.report2) { // 如果report4加载完成的时候report2也完成了, 那直接渲染report4
      pageCtl.report4Opt = parseReport4Opts()
    }
  })
}
const formatter = (a) => {
  if (a) {
    return $thousandBitSeparator(a)
  } else {
    return a
  }
}
const warningRowClassName = ({ row }) => {
  if (parseFloat(row.fulfill) > pageCtl.warningValue) {
    if (row.category1) {
      return 'warning-row'
    }
    return 'warning-sub-row'
  } else {
    return ''
  }
}
const report1ExpandChange = (row) => {
  if (pageCtl.report1LastExpandRow && row.id === pageCtl.report1LastExpandRow.id) {
    return
  }
  // 展开一个子菜单的时候, 不会关闭其他子菜单
  // if (that.report1LastExpandRow !== null) {
  //   report1TableRef.value.toggleRowExpansion(that.report1LastExpandRow, false)
  // }
  pageCtl.report1LastExpandRow = row
}

const load = (tree, treeNode, resolve) => {
  pageCtl.conditions.expandCategory1 = tree.category1

  $axios({
    method: 'post',
    url: '/demand/tracking_test/query_report1_sub',
    data: pageCtl.conditions
  }).then((body) => {
    resolve(parseReport1Data(body))
  }).catch((error) => {
    console.log(error)
  })
}
const parseReport1Data = (data) => {
  const result = [] as any
  const type = pageCtl.conditions.type

  let suffix = ''
  switch (type) {
    case 'Net Net Price':
    case 'Net Net Price HKD':
      suffix = 'NetNetValue'
      break
    case 'Moving Average Price':
      suffix = 'CostValue'
      break
    case 'Quantity':
      suffix = 'Qty'
      break
    case 'Line':
      suffix = 'Line'
      break
  }
  for (let i = 0; i < data.length; i++) {
    const rawData = data[i]
    if (!rawData) {
      continue
    }
    const allWidth = 20 + Math.log(rawData['fcst' + suffix] || 1) * 2.5
    let actual = 0
    if (pageCtl.conditions.category3Value === 'CRD') {
      actual = rawData['crd' + suffix]
    } else if (pageCtl.conditions.category3Value === 'Sales') {
      actual = rawData['sales' + suffix]
    } else if (pageCtl.conditions.category3Value === 'Order Intake') {
      actual = rawData['orderIntake' + suffix]
    } else if (pageCtl.conditions.category3Value === 'Output') {
      actual = rawData['output' + suffix]
    }

    if (rawData['orderIntake' + suffix] ||
        rawData['crd' + suffix] ||
        rawData['sales' + suffix] ||
        rawData['fcst' + suffix] ||
        rawData['backorder' + suffix] ||
        rawData['backlog' + suffix] ||
        rawData['soh' + suffix] ||
        rawData['ud' + suffix] ||
        rawData['output' + suffix]) {
      const fcst = rawData['fcst' + suffix]
      const fcstFulfill = rawData['fcstFulfill' + suffix]

      result.push(
        pageCtl.unit === 'M'
          ? {
              id: rawData.id,
              category1: rawData.category1,
              category2: rawData.category2,
              orderIntake: $thousandBitSeparator(rawData['orderIntake' + suffix] /= 1000000, 1),
              crd: $thousandBitSeparator(rawData['crd' + suffix] /= 1000000, 1),
              sales: $thousandBitSeparator(rawData['sales' + suffix] /= 1000000, 1),
              fcst: $thousandBitSeparator(rawData['fcst' + suffix] /= 1000000, 1) || '',
              backorder: $thousandBitSeparator(rawData['backorder' + suffix] /= 1000000, 1) || '',
              backlog: $thousandBitSeparator(rawData['backlog' + suffix] /= 1000000, 1) || '',
              soh: $thousandBitSeparator(rawData['soh' + suffix] /= 1000000, 1) || '',
              ud: $thousandBitSeparator(rawData['ud' + suffix] /= 1000000, 1) || '',
              output: $thousandBitSeparator(rawData['output' + suffix] /= 1000000, 1),
              warning: rawData.warning,
              hasChildren: rawData.hasChildren,
              actualWidth: (!fcst ? 0 : actual / fcst * 100) + '%',
              targetWidth: (!fcst ? 0 : fcstFulfill / fcst * 100) + '%',
              fulfill: !fcstFulfill ? 0 : actual / fcstFulfill * allWidth,
              targetAllWidth: allWidth + '%',
              parentName: rawData.parentName
            }
          : (pageCtl.unit === 'K'
              ? {
                  id: rawData.id,
                  category1: rawData.category1,
                  category2: rawData.category2,
                  orderIntake: $thousandBitSeparator(rawData['orderIntake' + suffix] /= 1000, 1),
                  crd: $thousandBitSeparator(rawData['crd' + suffix] /= 1000, 1),
                  sales: $thousandBitSeparator(rawData['sales' + suffix] /= 1000, 1),
                  fcst: $thousandBitSeparator(rawData['fcst' + suffix] /= 1000, 1) || '',
                  backorder: $thousandBitSeparator(rawData['backorder' + suffix] /= 1000, 1) || '',
                  backlog: $thousandBitSeparator(rawData['backlog' + suffix] /= 1000, 1) || '',
                  soh: $thousandBitSeparator(rawData['soh' + suffix] /= 1000, 1) || '',
                  ud: $thousandBitSeparator(rawData['ud' + suffix] /= 1000, 1) || '',
                  output: $thousandBitSeparator(rawData['output' + suffix] /= 1000, 1),
                  warning: rawData.warning,
                  hasChildren: rawData.hasChildren,
                  actualWidth: (!fcst ? 0 : actual / fcst * 100) + '%',
                  targetWidth: (!fcst ? 0 : fcstFulfill / fcst * 100) + '%',
                  fulfill: !fcstFulfill ? 0 : actual / fcstFulfill * 100,
                  targetAllWidth: allWidth + '%',
                  parentName: rawData.parentName
                }
              : {
                  id: rawData.id,
                  category1: rawData.category1,
                  category2: rawData.category2,
                  orderIntake: rawData['orderIntake' + suffix],
                  crd: rawData['crd' + suffix],
                  sales: rawData['sales' + suffix],
                  fcst: fcst || '',
                  backorder: rawData['backorder' + suffix] || '',
                  backlog: rawData['backlog' + suffix] || '',
                  soh: rawData['soh' + suffix] || '',
                  ud: rawData['ud' + suffix] || '',
                  output: rawData['output' + suffix],
                  warning: rawData.warning,
                  hasChildren: rawData.hasChildren,
                  actualWidth: (!fcst ? 0 : actual / fcst * 100) + '%',
                  targetWidth: (!fcst ? 0 : fcstFulfill / fcst * 100) + '%',
                  fulfill: !fcstFulfill ? 0 : actual / fcstFulfill * 100,
                  targetAllWidth: allWidth + '%',
                  parentName: rawData.parentName
                })
      )
    }
  }
  return result
}
const parseReport2Data = (data) => {
  data = data.data
  const result: any = {
    xAxis: [],
    yAxis1: [],
    yAxis2: [],
    yAxis3: [],
    yAxis4: [],
    yAxis5: [],
    yAxis6: [],
    yAxis7: [],
    yAxis8: [],
    yAxis9: [],
    yAxis10: [],
    yAxis11: [],
    yAxis12: [],
    yAxis13: [],

    yAxis14: [], // output(k)
    yAxis15: [], // output y - 1
    yAxis16: [], // output y
    yAxis17: [], // output  y - 1 avg
    yAxis18: [], // output  y avg
    yAxis19: [], // output summary y - 1
    yAxis20: [] // output summary y
  }
  const type = pageCtl.conditions.type

  let suffix = ''
  switch (type) {
    case 'Net Net Price':
    case 'Net Net Price HKD':
      suffix = 'NetNetValue'
      break
    case 'Moving Average Price':
      suffix = 'CostValue'
      break
    case 'Quantity':
      suffix = 'Qty'
      break
    case 'Line':
      suffix = 'Line'
      break
  }

  let orderIntakeSummaryY1 = 0
  let orderIntakeSummary = 0
  let salesSummaryY1 = 0
  let salesSummary = 0
  let outputSummaryY1 = 0
  let outputSummary = 0
  let totalCount = 0 // 有效数据的数量
  let totalCountY1 = 0 // Y-1有效数据的数量
  for (let i = 0; i < data.length; i++) {
    const rawData = data[i]
    if (!rawData) {
      continue
    }

    // Y和Y-1的分母单独计数
    if (rawData['sales' + suffix + 'Y_1']) {
      totalCountY1++
    }

    if (rawData['sales' + suffix]) {
      totalCount++
    }

    if (rawData.xAxis.indexOf('WW') !== -1) {
      result.xAxis.push(rawData.xAxis.substr(5))
    } else {
      result.xAxis.push(rawData.xAxis)
    }

    const adf = rawData['adf' + suffix]
    const adfFulfill = rawData['adfFulfill' + suffix]
    result.yAxis1.push([rawData['sales' + suffix + 'Y_1'], rawData['sales' + suffix], rawData['sales' + suffix + 'Y_1'], rawData['sales' + suffix]])
    result.yAxis2.push([rawData['orderIntake' + suffix + 'Y_1'], rawData['orderIntake' + suffix], rawData['orderIntake' + suffix + 'Y_1'], rawData['orderIntake' + suffix]])

    if (pageCtl.conditions.report2Type === 'fulltime') {
      result.yAxis3.push(adf) // Avg.FCST
    } else {
      result.yAxis3.push(adfFulfill) // Avg.FCST Fulfill
    }

    result.yAxis4.push(rawData['adu' + suffix]) // Avg.Order
    result.yAxis5.push(rawData['orderIntake' + suffix + 'Y_1'])
    result.yAxis6.push(rawData['orderIntake' + suffix])
    result.yAxis7.push(rawData['sales' + suffix + 'Y_1'])
    result.yAxis8.push(rawData['sales' + suffix])
    result.yAxis14.push([rawData['output' + suffix + 'Y_1'], rawData['output' + suffix], rawData['output' + suffix + 'Y_1'], rawData['output' + suffix]])
    result.yAxis15.push(rawData['output' + suffix + 'Y_1'])
    result.yAxis16.push(rawData['output' + suffix])

    orderIntakeSummaryY1 += rawData['orderIntake' + suffix + 'Y_1']
    orderIntakeSummary += rawData['orderIntake' + suffix]
    salesSummaryY1 += rawData['sales' + suffix + 'Y_1']
    salesSummary += rawData['sales' + suffix]
    outputSummaryY1 += rawData['output' + suffix + 'Y_1']
    outputSummary += rawData['output' + suffix]

    result.yAxis9.push(orderIntakeSummaryY1)
    result.yAxis10.push(orderIntakeSummary)
    result.yAxis11.push(salesSummaryY1)
    result.yAxis12.push(salesSummary)
    result.yAxis19.push(outputSummaryY1)
    result.yAxis20.push(outputSummary)
  }
  result.xAxis.push('[AVG]')
  result.yAxis5.push(totalCountY1 === 0 ? 0 : orderIntakeSummaryY1 / totalCountY1) // order intake y-1 avg
  result.yAxis6.push(totalCount === 0 ? 0 : orderIntakeSummary / totalCount) // order intake y avg
  result.yAxis7.push(totalCountY1 === 0 ? 0 : salesSummaryY1 / totalCountY1) // sales y -1 avg
  result.yAxis8.push(totalCount === 0 ? 0 : salesSummary / totalCount) // sales y avg
  result.yAxis15.push(totalCount === 0 ? 0 : outputSummaryY1 / totalCount) // output y - 1 avg
  result.yAxis16.push(totalCount === 0 ? 0 : outputSummary / totalCount) // output y avg

  let ads: any // Avg.Sales
  if (totalCount > 0) {
    ads = salesSummary / totalCount // Avg.Sales
  } else {
    ads = null
  }
  for (let i = 0; i < data.length; i++) {
    result.yAxis13.push(ads)
  }
  return result
}
const parseReport3Data = (data) => {
  const result: any = { xAxis: [], yAxis1: [], yAxis2: [], yAxis3: [], yAxis4: [], yAxis5: [] }
  const type = pageCtl.conditions.type

  let suffix = ''
  switch (type) {
    case 'Net Net Price':
    case 'Net Net Price HKD':
      suffix = 'NetNetValue'
      break
    case 'Moving Average Price':
      suffix = 'CostValue'
      break
    case 'Quantity':
      suffix = 'Qty'
      break
    case 'Line':
      suffix = 'Line'
      break
  }

  for (let i = 0; i < data.length; i++) {
    const rawData = data[i]
    if (!rawData) {
      continue
    }

    result.xAxis.push(rawData.xAxis)
    result.yAxis1.push(rawData['crd' + suffix])
    result.yAxis2.push(rawData['orderIntake' + suffix])
    result.yAxis3.push(rawData['sales' + suffix])
    result.yAxis4.push(rawData['fcst' + suffix])
    result.yAxis5.push(rawData['output' + suffix])
  }
  return result
}
const parseReport4Data = (data) => {
  const fcstFulfillRatioMap = data.fcstFulfillRatioMap
  const result: any = { title: '', xAxis: [], yAxis1: [], yAxis3: [], yAxis4: [], yAxis5: [] }
  const type = pageCtl.conditions.type

  let suffix = ''
  let totalFcst = 0
  switch (type) {
    case 'Net Net Price':
    case 'Net Net Price HKD':
      suffix = 'NetNetValue'
      totalFcst = data.amfFulfillNetNetValue
      break
    case 'Moving Average Price':
      suffix = 'CostValue'
      totalFcst = data.amfFulfillCostValue
      break
    case 'Quantity':
      suffix = 'Qty'
      totalFcst = data.amfFulfillQty
      break
    case 'Line':
      suffix = 'Line'
      totalFcst = 0
      break
  }

  let fcst = 0
  let orderIntake = 0
  let sales = 0
  let output = 0
  for (let i = 0; i < data.data.length; i++) {
    const rawData = data.data[i]
    if ((!rawData) || rawData.lastYearData === true) {
      continue
    }
    if (rawData.xAxis.indexOf('WW') !== -1) {
      result.xAxis.push(rawData.xAxis.substr(5))
    } else {
      result.xAxis.push(rawData.xAxis)
    }

    fcst += totalFcst * (fcstFulfillRatioMap[rawData.xAxis] || 0)
    orderIntake += rawData['orderIntake' + suffix]
    sales += rawData['sales' + suffix]
    output += rawData['output' + suffix]

    result.yAxis1.push(fcst)
    result.yAxis3.push(orderIntake)
    result.yAxis4.push(sales)
    result.yAxis5.push(output)
  }
  return result
}

const parseReport4Opts = () => {
  return {
    title: {
      text: pageCtl.report4Title
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:9.5rem;">')
          tip.push(params[i].marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value, 1))
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: pageCtl.report4DataRaw02 ? ['FCST', 'CRD', 'Ord.Int.', 'Sales', 'Output'] : ['FCST', 'Ord.Int.', 'Sales', 'Output'],
      selected: {
        FCST: true,
        CRD: true,
        'Ord.Int.': true,
        Sales: true,
        Output: false
      }
    }),
    grid: $grid(),
    xAxis: [
      {
        type: 'category',
        data: _report4Data.value.xAxis,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        scale: true,
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }
    ],
    series: [
      {
        name: 'FCST',
        type: 'line',
        smooth: false,
        itemStyle: {
          color: '#ff5c33'
        },
        data: _report4Data.value.yAxis1
      },
      {
        name: 'CRD',
        type: 'line',
        smooth: false,
        itemStyle: {
          color: '#1aa3ff'
        },
        data: _report4Data.value.yAxis2
      },
      {
        name: 'Ord.Int.',
        type: 'line',
        smooth: false,
        itemStyle: {
          color: '#34608d'
        },
        data: _report4Data.value.yAxis3
      },
      {
        name: 'Sales',
        type: 'line',
        smooth: false,
        itemStyle: {
          color: '#209c20'
        },
        data: _report4Data.value.yAxis4
      },
      {
        name: 'Output',
        type: 'line',
        smooth: false,
        itemStyle: {
          color: '#fdcc02'
        },
        data: _report4Data.value.yAxis5
      }
    ]
  }
}
const viewReport1Details = () => {
  $viewDetails({
    title: '[' + pageCtl.conditions.viewType + '] ' + pageCtl.conditions.viewCategory1 + (pageCtl.conditions.viewCategory2 ? ' - ' + pageCtl.conditions.viewCategory2 : ''),
    url: '/demand/tracking_test/query_report1_details',
    durl: '/demand/tracking_test/download_data',
    params: pageCtl.conditions
  })
}
const rightClick2 = (e) => {
  const cs = e.target.dataset.value.split(',')
  pageCtl.conditions.viewType = cs[0]
  if (cs[1] !== 'Total') {
    pageCtl.conditions.viewCategory1 = cs[1]
    pageCtl.conditions.viewCategory2 = cs[2]
  } else {
    pageCtl.conditions.viewCategory1 = ''
    pageCtl.conditions.viewCategory2 = ''
  }
}

const downloadReport1 = () => {
  $downloadFile('/demand/tracking_test/download_report1', pageCtl.conditions)
}

const _category8ValueOpts = computed(() => {
  if (pageCtl.conditions.scope === 'SECI') {
    return [{
      label: 'FCST',
      value: 'SECI_CSF_DEMAND'
    }]
  } else if (pageCtl.conditions.scope === 'PLANT') {
    return [{
      label: 'MPP Dem. FCST',
      value: 'PLANT_MPP_DEMAND'
    }, {
      label: 'MPP Com. FCST',
      value: 'PLANT_MPP_COMMIT'
    }]
  }
  return []
})

const _subScopeOpts = computed(() => {
  if (pageCtl.conditions.scope === 'SECI') {
    return ['SECI_IG', 'SECI_OG', 'SEHK_IG', 'SEHK_OG']
  } else if (pageCtl.conditions.scope === 'PLANT') {
    return ['PLANT_IG_DOMESTIC', 'PLANT_IG_EXPORT', 'PLANT_OG_DOMESTIC', 'PLANT_OG_EXPORT']
  } else {
    return []
  }
})

const _saleMonthRange = computed(() => {
  const result = [] as any
  const dateType = pageCtl.conditions.dateType
  const currentYear = new Date().getFullYear()
  if (dateType === 'Month to Date') {
    const startDate = new Date()
    startDate.setDate(1)
    while (currentYear - startDate.getFullYear() < 3) {
      result.push($dateFormatter(startDate, 'yyyyMM'))
      startDate.setMonth(startDate.getMonth() - 1)
    }
  } else if (dateType === 'Quarter to Date') {
    const startDate = new Date()
    startDate.setDate(1)
    while (currentYear - startDate.getFullYear() < 3) {
      result.push($dateFormatter(startDate, 'yyyyQq'))
      startDate.setMonth(startDate.getMonth() - 3)
    }
  } else if (dateType === 'Year to Date') {
    const startDate = new Date()
    startDate.setDate(1)
    while (currentYear - startDate.getFullYear() < 3) {
      result.push($dateFormatter(startDate, 'yyyy'))
      startDate.setFullYear(startDate.getFullYear() - 1)
    }
  }

  return result
})

const _titlePrefix = computed(() => {
  switch (pageCtl.conditions.dateType) {
    case 'Month to Date':
      return 'MTD'
    case 'Quarter to Date':
      return 'QTD'
    case 'Year to Date':
      return 'YTD'
  }
  return ''
})

const _report2Opt = computed(() => {
  // 因为用户需求不定, 导致该报表不断修改, 所以该部分代码会很冗余, 并且有很多隐藏元素(隐藏是为了防止某一天用户突然回心转意)
  // 等用户使用稳定后, 可以考虑优化代码, 删除不用的代码
  const upColor = '#ec0000'
  const upBorderColor = '#ec0000'
  const downColor = '#00da3c'
  const downBorderColor = '#00da3c'

  const salesColor = '#04b304'
  const salesY1Color = 'rgb(225,239,225)'
  const orderIntakeColor = '#1058ad'
  const orderIntakeY1Color = 'rgb(207,222,239)'
  const outputColor = '#fdcc02'
  const outputY1Color = 'rgb(253,224,175)'

  const avgFcstColor = '#ff5c33'
  return {
    title: {
      text: pageCtl.report2Title
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let salesY1 = 0
        let outputY1 = 0
        let orderIntakeY1 = 0
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:9.5rem;">')
          let marker = params[i].marker

          marker = marker.replace('rgba(249,0,0,.0)', outputY1Color) // output summary y - 1
          marker = marker.replace('rgba(250,0,0,.0)', outputColor) // output summary y
          marker = marker.replace('rgba(251,0,0,.0)', avgFcstColor)
          marker = marker.replace('rgba(252,0,0,.0)', orderIntakeY1Color)
          marker = marker.replace('rgba(253,0,0,.0)', orderIntakeColor)
          marker = marker.replace('rgba(254,0,0,.0)', salesY1Color)
          marker = marker.replace('rgba(255,0,0,.0)', salesColor)

          tip.push(marker)
          tip.push(params[i].seriesName.replace(/1/g, ''))
          if (params[i].seriesId === '\u0000Ord.Int.1\u00000' || params[i].seriesId === '\u0000Sales1\u00000') {
            tip.push('(Y-1)')
          } else if (params[i].seriesId === '\u0000Ord.Int.\u00000' || params[i].seriesId === '\u0000Sales\u00000') {
            tip.push('(Y)')
          } else if (params[i].seriesId === '\u0000Ord.Int.\u00001') {
            tip.push('Sum(Y-1)')
            orderIntakeY1 = value
          } else if (params[i].seriesId === '\u0000Sales\u00001') {
            tip.push('.Sum(Y-1)')
            salesY1 = value
          } else if (params[i].seriesId === '\u0000Ord.Int.\u00002') {
            tip.push('Sum(Y)')
            tip.push('(' + (orderIntakeY1 ? (value / orderIntakeY1 * 100).toFixed(1) + '%' : '0') + ')')
          } else if (params[i].seriesId === '\u0000Sales\u00002') {
            tip.push('.Sum(Y)')
            tip.push('(' + (salesY1 ? (value / salesY1 * 100).toFixed(1) + '%' : '0') + ')')
          }

          if (params[i].seriesId === '\u0000Output\u00000') {
            tip.push('(Y)')
          } else if (params[i].seriesId === '\u0000Output1\u00000') {
            tip.push('(Y-1)')
          } else if (params[i].seriesId === '\u0000Output\u00001') {
            tip.push('.Sum(Y-1)')
            outputY1 = value
          } else if (params[i].seriesId === '\u0000Output\u00002') {
            tip.push('Sum(Y)')
            tip.push('(' + (outputY1 ? (value / outputY1 * 100).toFixed(1) + '%' : '0') + ')')
          }

          tip.push('<span style="float: right">')
          if (typeof value === 'object') {
            const tv = value[2] - value[1]
            tip.push((tv > 0 ? '+' : '') + $shortenNumber(tv))
          } else {
            tip.push($shortenNumber(value))
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: ['Ord.Int.(K)', 'Sales(K)', 'Output(K)', 'Ord.Int.', 'Sales', 'Output'],
      selected: {
        'Sales(K)': false,
        'Ord.Int.(K)': false,
        'Output(K)': false,
        Sales: true,
        'Ord.Int.': true,
        Output: false,
        Output1: false
      }
    }),
    grid: $grid({ right: -1 }),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report2Data.xAxis,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        splitLine: {
          show: false
        },
        scale: true,
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }, {
        show: false,
        axisLabel: {
          formatter: () => {
            return ''
          }
        }
      }
    ],
    series: [
      {
        name: 'Sales(K)',
        type: 'k',
        itemStyle: {
          color: upColor,
          color0: downColor,
          borderColor: upBorderColor,
          borderColor0: downBorderColor
        },
        data: pageCtl.report2Data.yAxis1
      },
      {
        name: 'Ord.Int.(K)',
        type: 'k',
        itemStyle: {
          color: upColor,
          color0: downColor,
          borderColor: upBorderColor,
          borderColor0: downBorderColor
        },
        data: pageCtl.report2Data.yAxis2
      },
      {
        name: 'Output(K)',
        type: 'k',
        itemStyle: {
          color: upColor,
          color0: downColor,
          borderColor: upBorderColor,
          borderColor0: downBorderColor
        },
        data: pageCtl.report2Data.yAxis14
      },
      {
        name: 'Avg.FCST',
        type: 'line',
        symbol: 'none',
        smooth: false,
        data: pageCtl.report2Data.yAxis3,
        itemStyle: {
          color: 'rgba(251,0,0,.0)'
        },
        markLine: {
          symbol: 'none', // 去掉警戒线最后面的箭头
          label: {
            position: 'middle', // 将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
            formatter: 'Avg.FCST',
            fontSize: 10,
            color: avgFcstColor
          },
          data: [{
            silent: false,
            lineStyle: {
              type: 'solid',
              color: avgFcstColor
            },
            yAxis: (pageCtl.report2Data.yAxis3 !== undefined && pageCtl.report2Data.yAxis3.length > 0) ? pageCtl.report2Data.yAxis3[0] : 0
          }]
        }
      },
      { // ADU放在这里, 是为了可以显示tooltips
        name: 'ADU',
        type: 'line',
        symbol: 'none',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report2Data.yAxis4,
        itemStyle: {
          color: 'rgba(253,0,0,.0)'
        }
      },
      { // Avg.Sales放在这里, 是为了可以显示tooltips
        name: 'Avg.Sales',
        type: 'line',
        symbol: 'none',
        smooth: false,
        yAxisIndex: 1,
        data: pageCtl.report2Data.yAxis13,
        itemStyle: {
          color: 'rgba(255,0,0,.0)'
        }
      },
      {
        name: 'Ord.Int.1',
        type: 'bar',
        symbol: 'none',
        itemStyle: {
          color: orderIntakeY1Color
        },
        smooth: false,
        data: pageCtl.report2Data.yAxis5
      },
      {
        name: 'Ord.Int.',
        type: 'bar',
        symbol: 'none',
        itemStyle: {
          color: orderIntakeColor
        },
        data: pageCtl.report2Data.yAxis6
      },
      {
        name: 'Sales1',
        type: 'bar',
        symbol: 'none',
        itemStyle: {
          color: salesY1Color
        },
        smooth: false,
        lineStyle: {
          opacity: 0.5
        },
        data: pageCtl.report2Data.yAxis7
      },
      {
        name: 'Sales',
        type: 'bar',
        symbol: 'none',
        itemStyle: {
          color: salesColor
        },
        smooth: false,
        data: pageCtl.report2Data.yAxis8
      },
      {
        name: 'Output1',
        type: 'bar',
        symbol: 'none',
        itemStyle: {
          color: outputY1Color
        },
        smooth: false,
        lineStyle: {
          opacity: 0.5
        },
        data: pageCtl.report2Data.yAxis15
      },
      {
        name: 'Output',
        type: 'bar',
        symbol: 'none',
        itemStyle: {
          color: outputColor
        },
        smooth: false,
        data: pageCtl.report2Data.yAxis16
      },
      { // 为了在Tooltips上显示去年Order Intake汇总
        name: 'Ord.Int.',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        itemStyle: {
          color: 'rgba(252,0,0,.0)'
        },
        yAxisIndex: 1,
        lineStyle: {
          opacity: 1
        },
        data: pageCtl.report2Data.yAxis9
      },
      { // 为了在Tooltips上显示今年Order Intake汇总
        name: 'Ord.Int.',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        itemStyle: {
          color: 'rgba(253,0,0,.0)'
        },
        yAxisIndex: 1,
        lineStyle: {
          opacity: 1
        },
        data: pageCtl.report2Data.yAxis10
      },
      { // 为了在Tooltips上显示去年Sales汇总
        name: 'Sales',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        itemStyle: {
          color: 'rgba(254,0,0,.0)'
        },
        yAxisIndex: 1,
        lineStyle: {
          opacity: 1
        },
        data: pageCtl.report2Data.yAxis11
      },
      { // 为了在Tooltips上显示今年Sales汇总
        name: 'Sales',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        yAxisIndex: 1,
        itemStyle: {
          color: 'rgba(255,0,0,.0)'
        },
        smooth: false,
        lineStyle: {
          opacity: 1
        },
        data: pageCtl.report2Data.yAxis12
      },
      { // 为了在Tooltips上显示去年Output汇总
        name: 'Output',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        itemStyle: {
          color: 'rgba(249,0,0,.0)'
        },
        yAxisIndex: 1,
        lineStyle: {
          opacity: 1
        },
        data: pageCtl.report2Data.yAxis19
      },
      { // 为了在Tooltips上显示今年Output汇总
        name: 'Output',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        yAxisIndex: 1,
        itemStyle: {
          color: 'rgba(250,0,0,.0)'
        },
        smooth: false,
        lineStyle: {
          opacity: 1
        },
        data: pageCtl.report2Data.yAxis20
      }
    ]
  }
})

const _report3Opt = computed(() => {
  return {
    title: {
      text: pageCtl.report3Title
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:7rem;">')
          tip.push(params[i].marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value, 1))
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({
      data: ['CRD', 'Ord.Int.', 'Sales', 'Output', 'FCST'],
      selected: {
        CRD: false,
        'Ord.Int.': true,
        Sales: false,
        Output: false
      }
    }),
    grid: $grid(),
    xAxis: [
      {
        type: 'category',
        data: pageCtl.report3Data.xAxis,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        splitLine: {
          show: false
        },
        scale: true,
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }
    ],
    series: [
      {
        name: 'CRD',
        type: 'bar',
        itemStyle: {
          color: '#1aa3ff'
        },
        data: pageCtl.report3Data.yAxis1
      },
      {
        name: 'Ord.Int.',
        type: 'bar',
        itemStyle: {
          color: '#34608d'
        },
        data: pageCtl.report3Data.yAxis2
      },
      {
        name: 'Sales',
        type: 'bar',
        itemStyle: {
          color: '#209c20'
        },
        data: pageCtl.report3Data.yAxis3
      },
      {
        name: 'Output',
        type: 'bar',
        itemStyle: {
          color: '#fdcc02'
        },
        data: pageCtl.report3Data.yAxis5
      },
      {
        name: 'FCST',
        type: 'bar',
        itemStyle: {
          color: '#ff5c33'
        },
        data: pageCtl.report3Data.yAxis4
      }
    ]
  }
})

const _report4Data = computed(() => {
  const data: any = { yAxis2: [] }
  if (pageCtl.report4DataRaw01) {
    for (const key in pageCtl.report4DataRaw01) {
      if (pageCtl.report4DataRaw01.hasOwnProperty(key)) {
        data[key] = pageCtl.report4DataRaw01[key]
      }
    }
  }

  const type = pageCtl.conditions.type
  if (pageCtl.report4DataRaw01 && pageCtl.report4DataRaw02 && pageCtl.report4DataRaw01.xAxis) {
    // let crd = 0
    for (let i = 0; i < pageCtl.report4DataRaw01.xAxis.length; i++) {
      const y2 = pageCtl.report4DataRaw02[pageCtl.report4DataRaw01.xAxis[i]]
      if (y2) {
        let suffix = ''
        switch (type) {
          case 'Net Net Price':
          case 'Net Net Price HKD':
            suffix = 'NetNetValue'
            break
          case 'Moving Average Price':
            suffix = 'CostValue'
            break
          case 'Quantity':
            suffix = 'Qty'
            break
          case 'Line':
            suffix = 'Line'
            break
        }
        // crd += y2['crd' + suffix]
        // data['yAxis2'].push(crd) // STD统计
        data.yAxis2.push(y2['crd' + suffix]) // VALUE统计
      } else {
        data.yAxis2.push(null)
      }
    }
  }

  return data
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
</script>
<style lang="scss">
#demandTracking {
  .ud-header {
    .el-select__tags {
      padding-top: 4px;
    }

    .el-tag--small {
      height: 12px !important;
    }

    .el-select__tags-text {
      max-width: fit-content !important;
      font-size: 0.4rem !important;
    }
  }

  .part1 {
    .el-select__wrapper {
      background-color: initial;
      box-shadow: 0 0 0 0;
    }

    .el-table, .el-table__expanded-cell {
      background-color: transparent;
    }
  }

  .el-table__placeholder {
    display: none;
  }

  .tree-table-box {
    height: 100%;
    width: calc(100% - 5px);
    overflow: auto;
  }

  .tree-table {
    .warning-sub-row {
      td:nth-child(2) {
        background-color: var(--scp-border-color-error);
        color: white;

        .cell {
          padding-left: 4px !important;
        }
      }
    }

    .warning-row {
      td:first-child {
        background-color: var(--scp-border-color-error);
        color: white;

        i {
          color: white;
        }
      }
    }

    table {
      tr:last-child {
        font-weight: bold;

        td:first-child {
          padding-left: 22px !important
        }
      }

      tr {
        background-color: transparent;

        th {
          padding: 2px 2px !important;
          border-bottom: 0 !important;
          border-right: solid 5px var(--scp-bg-color) !important;
          background-color: var(--scp-bg-color-fill);

          &:nth-last-child(1) {
            border-right: none !important;
          }

          .cell {
            font-size: 0.5rem !important;
            font-weight: normal;
            text-align: center;
            line-height: 21px !important;
            white-space: nowrap;
            padding: 0 !important;

            .el-select {
              margin-left: 10px;
              width: calc(100% - 10px) !important;

              .el-input__icon {
                width: 10px !important;
              }

              .el-select__tags {
                max-width: calc(100% - 20px) !important;

                span {
                  .el-tag:nth-child(2) {
                    font-size: .4rem;
                    color: var(--scp-text-color-secondary);
                    line-height: 2;
                  }

                  .el-tag {
                    max-width: calc(100% - 18px) !important;
                    padding: 0 !important;
                    margin-left: 0 !important;
                    font-size: 0.45rem;
                    color: var(--scp-text-color-primary);

                    .el-tag__close {
                      display: none !important;
                    }
                  }
                }
              }
            }
          }
        }

        td {
          padding: 0 !important;
          background-color: transparent;

          .cell {
            padding-left: 0 !important;
            padding-right: 5px !important;
            font-size: 0.5rem !important;
            line-height: 21px !important;
            white-space: nowrap;
          }
        }

        td.el-table_1_column_3, td.el-table_1_column_4, td.el-table_1_column_5, td.el-table_1_column_6 {
          text-align: right;
        }
      }
    }

    .el-scrollbar__view {
      width: 100%;
    }
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: var(--scp-bg-color-success) !important;
    color: #fff !important;
  }

  .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
    border-right: none;
  }

  .el-table td, .el-table th.is-leaf {
    border-bottom: none;
  }

  .el-table, .el-table__expanded-cell {
    background-color: transparent;
  }

  @media screen and (min-width: 1800px) {
    .part {
      height: 332px;
    }
  }

  .hide-input-border input {
    border: 0 !important;
  }

  .category1-div {
    width: calc(100% - 23px);
    height: 100%;
    float: right;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .category2-div {
    width: 100%;
    height: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .report2Select {
    .el-input.el-input--medium.el-input--suffix {
      z-index: 600;
    }
  }
}
</style>
