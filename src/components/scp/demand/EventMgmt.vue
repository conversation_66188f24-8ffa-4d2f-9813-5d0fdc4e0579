<template>
    <div class="left-sidebar" id="event">
        <div class="widget">
            <div class="widget-body">
                <el-row class="search-box">
                    <!--category-->
                    <el-col :span="6">
                        <scp-cascader
                                v-model="pageCtl.conditions.filterList"
                                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                                :options="pageCtl.filterOpts"
                        />
                    </el-col>
                    <el-col :span="3">
                        <el-select multiple
                                   collapse-tags
                                   v-model="pageCtl.conditions.eventClass"
                                   size="small"
                                   clearable
                                   placeholder="Event Class">
                            <el-option
                                    v-for="item in pageCtl.eventClassOpts"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select multiple
                                   collapse-tags
                                   v-model="pageCtl.conditions.eventStatus"
                                   size="small"
                                   clearable
                                   placeholder="Event Status">
                            <el-option
                                    v-for="item in pageCtl.eventStatusOpts"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select multiple
                                   collapse-tags
                                   v-model="pageCtl.conditions.cycleMonth"
                                   size="small"
                                   clearable
                                   placeholder="Cycle Month">
                            <el-option
                                    v-for="item in pageCtl.cycleMonthOpts"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="1">
                        <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['cycleMonth']"/>
                    </el-col>
                    <el-col :span="8" style="text-align: right">
                        <el-popconfirm title="此操作会复制上个月的数据到这个月, 重复的数据将会被忽略"
                                       iconColor="orange"
                                       @confirm="generateNewNote"
                                       confirmButtonType="warning"
                                       confirmButtonText='确定'
                                       cancelButtonText='取消'
                                       style="margin-left:5px">
                            <template #reference>
                                <el-button size="small" :loading="pageCtl.loading.generate">
                                    <font-awesome-icon icon="magic"/>&nbsp;
                                    Generate New Note
                                </el-button>
                            </template>
                        </el-popconfirm>
                    </el-col>
                </el-row>
                <div class="subscript-container">
                    <scp-subscript id="DEMN"/>
                    <scp-table
                            :params="pageCtl.conditions"
                            :max-height="400"
                            :margin-bottom="55"
                            :lazy="true"
                            url="/demand/event/query_report1"
                            save-url="/demand/event/save_report1"
                            download-url="/demand/event/download_report1"
                            ref="report1TableRef"
                            :columns="_report1TableColumn"
                            :editable="true"
                            :hiddenColumns="{columns: [0]}"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $message: any = inject('$message')

const report1TableRef = ref()
const searchRef = ref()

const pageCtl = reactive({
  isAdmin: false,
  filterOpts: [],
  eventClassOpts: [],
  eventStatusOpts: [],
  cycleMonthOpts: [],
  buOpts: [],
  salesOrgOpts: ['SECI-OG', 'SEHK-OG'],
  localProductLineOpts: [],
  localProductFamilyOpts: [],
  localProductSubfamilyOpts: [],
  loading: {
    filter: false,
    generate: false
  },
  conditions: {
    filterList: [],
    eventClass: [],
    eventStatus: [],
    cycleMonth: [$dateFormatter(new Date(), 'yyyy/MM')]
  }
})

onMounted(() => {
  initPage()
})

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/demand/event/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.eventClassOpts = body.eventClass
    pageCtl.eventStatusOpts = body.eventStatus
    pageCtl.cycleMonthOpts = body.cycleMonth
    pageCtl.buOpts = body.bu
    pageCtl.localProductLineOpts = body.local_product_line
    pageCtl.localProductFamilyOpts = body.local_product_family
    pageCtl.localProductSubfamilyOpts = body.local_product_subfamily
    pageCtl.isAdmin = body.isAdmin
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  report1TableRef.value.search()
}

const generateNewNote = () => {
  pageCtl.loading.generate = true
  $axios({
    method: 'post',
    url: '/demand/event/generate_new_note'
  }).then((body) => {
    $message.success(body + ' note generated')
    search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.generate = false
  })
}

const _report1TableColumn = computed(() => {
  const columns: any = [
    {
      data: 'ROW_ID'
    }, {
      data: 'CYCLE',
      type: 'date',
      dateFormat: 'YYYY/MM/DD'
    }, {
      data: 'BUSINESS_UNIT',
      editor: 'select',
      selectOptions: pageCtl.buOpts
    }, {
      data: 'SALES_ORGANIZATION',
      editor: 'select',
      selectOptions: pageCtl.salesOrgOpts
    }, {
      data: 'LOCAL_PRODUCT_LINE',
      editor: 'select',
      selectOptions: pageCtl.localProductLineOpts
    }, {
      data: 'LOCAL_PRODUCT_FAMILY',
      editor: 'select',
      selectOptions: pageCtl.localProductFamilyOpts
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY',
      editor: 'select',
      selectOptions: pageCtl.localProductSubfamilyOpts
    }, {
      data: 'EVENT_START_DATE',
      type: 'date',
      dateFormat: 'YYYY/MM/DD'
    }, {
      data: 'EVENT_END_DATE',
      type: 'date',
      dateFormat: 'YYYY/MM/DD'
    }, {
      data: 'EVENT_MONTH',
      type: 'date',
      dateFormat: 'YYYY/MM'
    }, {
      data: 'EVENT_CLASS',
      editor: 'select',
      selectOptions: pageCtl.eventClassOpts
    }]

  if (pageCtl.isAdmin) {
    columns.push({
      data: 'EVENT_STATUS',
      editor: 'select',
      selectOptions: pageCtl.eventStatusOpts
    }, {
      data: 'NOTICE_ANOUNCE_DATE',
      type: 'date',
      dateFormat: 'YYYY/MM/DD'
    }, {
      data: 'ORDER_SALES_DRIVEN',
      editor: 'select',
      selectOptions: ['SALES', 'ORDER']
    }, {
      data: 'PRICE_INCREASE_RATIO',
      title: 'Price Increase Ratio(%)'
    }, {
      data: 'INCENTIVE_RATIO',
      title: 'Incentive Ratio(%)'
    }, {
      data: 'ENRICHMENT_VALUE'
    }, {
      data: 'CHANNEL'
    }, {
      data: 'CONSIDER_OR_NOT',
      editor: 'select',
      selectOptions: ['Y', 'N']
    }, {
      data: 'REMARK'
    }, {
      data: 'SHOW_IN_DATA_PACK',
      editor: 'select',
      selectOptions: ['Y', 'N']
    })
  }
  return columns
})
</script>
