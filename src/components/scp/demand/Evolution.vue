<template>
    <div class="left-sidebar" id="evolution">
        <div class="widget">
            <div class="widget-body">
                <el-row class="search-box">
                    <el-col :span="5">
                        <scp-cascader
                                v-model="pageCtl.conditions.filterList"
                                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                                :options="pageCtl.filterOpts"/>
                    </el-col>
                    <el-col :span="2">
                        <el-select v-model="pageCtl.conditions.specialType" size="small" style="width: 100% !important;" class="search-group-left">
                            <el-option label="MATERIAL" value="MATERIAL"/>
                            <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
                            <el-option label="VENODR_NAME" value="VENODR_NAME"/>
                            <el-option label="LOCAL_PRODUCT_FAMILY" value="LOCAL_PRODUCT_FAMILY"/>
                            <el-option label="LOCAL_PRODUCT_SUBFAMILY" value="LOCAL_PRODUCT_SUBFAMILY"/>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <el-input :placeholder="pageCtl.conditions.specialType" size="small" v-model="pageCtl.conditions.specialContent"
                                  style="width: var(--scp-input-width) !important;" type="textarea" class="search-group-right"></el-input>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.dataType" size="small" style="width: var(--scp-input-width) !important;">
                            <el-option label="Order Intake" value="ORDER_INTAKE"/>
                            <el-option label="Sales" value="SALES"/>
                            <el-option label="CRD" value="CRD"/>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.valueType" size="small">
                            <el-option
                                    v-for="item in ['Net Net Price', 'Moving Average Price', 'Net Net Price HKD', 'Quantity', 'Line']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <!-- scope -->
                    <el-col :span="2">
                        <el-select v-model="pageCtl.conditions.scope" size="small">
                            <el-option
                                    v-for="item in ['SECI', 'PLANT']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <!-- scope -->
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.subScope" size="small" collapse-tags multiple placeholder="Sub-Scope">
                            <el-option
                                    v-for="item in _subScopeOpts"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="2">
                        <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['fcstVersion']"
                                    :afterExpand="moreConditions"/>
                    </el-col>
                </el-row>
                <el-row class="search-box" style="width: 100%" v-show="pageCtl.conditionsExpanded">
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.fcstVersion" size="small" :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'">
                            <el-option
                                    v-for="item in pageCtl.fcstVersion"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.dataScope" size="small">
                            <el-option
                                    v-for="item in ['Full FCST', 'Full History']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-row>

                <div class="subscript-container" v-loading="pageCtl.loading.filter || pageCtl.loading.report1">
                    <scp-subscript id="DDEV"/>
                    <el-row class="search-box">
                        <el-col :span="5">
                            <el-select v-model="pageCtl.conditions.report1GroupColumns"
                                       size="small"
                                       multiple
                                       collapse-tags
                                       filterable
                                       clearable
                                       placeholder="Columns">
                                <el-option
                                        v-for="item in _pivotColumns"
                                        :key="item"
                                        :label="item"
                                        :value="item">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="5">
                            <el-select v-model="pageCtl.report1SelectedColumns"
                                       size="small"
                                       multiple
                                       collapse-tags
                                       filterable
                                       clearable
                                       placeholder="Columns">
                                <el-option
                                        v-for="item in pageCtl.report1AllColumns"
                                        :key="item"
                                        :label="item"
                                        :value="item">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                    <scp-table
                            ref="report1TableRef"
                            :columns="_report1Headers['columns']"
                            :params="pageCtl.conditions"
                            :fixed-columns-left="_report1FixedLeft"
                            :lazy="true"
                            :show-total="true"
                            :context-menu-items="pageCtl.report1ContextMenuItems"
                            :after-select="afterReport1Select"
                            :enable-loading="false"
                            :before-search="()=>{pageCtl.loading.report1 = true}"
                            :after-search="()=>{pageCtl.loading.report1 = false}"
                            :nested-headers="_report1Headers['nestedHeaders']"
                            url="/demand/evolution/query_report1"
                            download-url="/demand/evolution/download_report1"
                    />
                </div>
            </div>
        </div>

        <!-- report1 trend -->
        <scp-draggable-resizable v-model="pageCtl.visible.report1Trends" :title="pageCtl.report1TrendsTitle" w="60vw" h="420px">
            <template v-slot="{ height }">
              <chart v-if="pageCtl.visible.report1Trends === true"
                  :style="{ width: pageCtl.pageWidth * 0.6 + 'px', height: height - 120 + 'px' }" :option="pageCtl.report1TrendsOpt"/>
            </template>
        </scp-draggable-resizable>

        <!-- report1 details -->
        <scp-draggable-resizable w="60vw" h="420px" :title="pageCtl.report1DetailsTitle" v-model="pageCtl.visible.report1Details">
            <template v-slot="{ height }">
                <el-row>
                    <el-col :span="24">
                        <scp-table
                                ref="report1DetailsTableRef"
                                :max-height="height - 150"
                                :lazy="true"
                                url="/demand/evolution/query_report1_details"
                                download-url="/demand/evolution/download_report1_details"
                                :params="_report1SelectConditions"
                                :columns="_report1DetailsColumns"/>
                    </el-col>
                </el-row>
            </template>
        </scp-draggable-resizable>

        <!-- report1 pipeline -->
        <scp-draggable-resizable w="60vw" h="420px" :title="pageCtl.report1PipelineTitle" v-model="pageCtl.visible.report1Pipeline">
            <template v-slot="{ height }">
                <el-row>
                    <el-col :span="24">
                        <scp-table
                                ref="report1PipelineTableRef"
                                :lazy="true"
                                :max-height="height - 150"
                                url="/demand/evolution/query_report1_pipeline"
                                download-url="/demand/evolution/download_report1_pipeline"
                                :params="_report1SelectConditions"
                                :columns="_report1PipelineColumns"/>
                    </el-col>
                </el-row>
            </template>
        </scp-draggable-resizable>
    </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $calcMonthGap: any = inject('$calcMonthGap')
const $dateFormatter: any = inject('$dateFormatter')
const $intersection: any = inject('$intersection')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const $axios: any = inject('$axios')

const searchRef = ref()
const report1TableRef = ref()
const report1DetailsTableRef = ref()
const report1PipelineTableRef = ref()

const searchReport1Details = () => {
  pageCtl.visible.report1Details = true
  report1DetailsTableRef.value.search()
}

const searchReport1Pipeline = () => {
  pageCtl.visible.report1Pipeline = true
  report1PipelineTableRef.value.search()
}

const searchReport1Trend = () => {
  pageCtl.visible.report1Trends = true
  pageCtl.report1TrendsOpt = {
    color: ['#4472c4', '#fc8452'],
    grid: $grid(),
    legend: $legend({ data: ['History', 'FCST'] }),
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        for (const i in params) {
          if (params.hasOwnProperty(i)) {
            const value = params[i].value || 0
            tip.push('<div style="width:7.5rem;">')
            tip.push(params[i].marker)
            tip.push(params[i].seriesName)
            tip.push(' : ')
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(value))
            tip.push('</span></div>')
          }
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      type: 'category',
      data: pageCtl.report1Trends.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series: [{
      name: 'History',
      data: pageCtl.report1Trends.yAxis1,
      type: 'line'
    }, {
      name: 'FCST',
      data: pageCtl.report1Trends.yAxis2,
      type: 'line',
      lineStyle: {
        type: 'dashed'
      }
    }]
  }
}

const pageCtl = reactive({
  fcstVersion: [] as Array<any>,
  filterOpts: [],
  conditionsExpanded: false,
  pageWidth: '' as any,
  conditions: {
    scope: 'SECI',
    subScope: ['SECI_OG', 'SPC_OG'],
    dataScope: 'Full History',
    fcstVersion: '',
    filterList: [],
    specialType: 'MATERIAL',
    specialContent: '',
    dataType: 'ORDER_INTAKE',
    valueType: 'Net Net Price',
    report1GroupColumns: ['CLUSTER_NAME', 'ENTITY'] // 用户用来分组的表头
  },
  selectedConditions: {
    detailsType: '',
    detailsMonth: '',
    pipelineMonth: '',
    groupColumns: [] as any,
    groupValue: []
  },
  report1TrendsOpt: {},
  loading: {
    filter: false,
    report1: false,
    report1Trends: false
  },
  visible: {
    report1Trends: false,
    report1Details: false,
    report1Pipeline: false
  },
  report1AllColumns: [], // 所有可用表头, 用来筛选, report1AllColumns = report1GroupColumns + dateColumn
  report1DataColumns: ['AMU', 'AMF', 'WEEKLY_ORDER_LINE', 'CURRENT_MONTH', 'CURRENT_YGR'],
  report1SelectedColumns: ['MATERIAL', 'PLANT_CODE'], // 非数据部分已选择的表头
  report1DateColumns: {} as any, // 数据表头
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: searchReport1Details
    },
    view_pipeline: {
      name: 'View pipeline',
      disabled: () => {
        return pageCtl.selectedConditions.pipelineMonth === ''
      },
      callback: searchReport1Pipeline
    },
    view_trend: {
      name: 'View trend',
      callback: searchReport1Trend
    },
    view_split0: { name: '---------' }
  },
  report1TrendsTitle: '',
  report1DetailsTitle: '',
  report1PipelineTitle: '',
  report1DetailsMaxHeight: 280,
  report1PipelineMaxHeight: 280,
  report1Trends: {
    xAxis: [],
    yAxis1: [],
    yAxis2: []
  }
})

onMounted(() => {
  initPage()
  pageCtl.pageWidth = document.documentElement.clientWidth
})

const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/demand/evolution/init_page'
  }).then((body) => {
    const version = [] as any
    const currentMonth = $dateFormatter(new Date(), 'yyyyMM')
    const fcst = body.FCST
    for (let i = 0; i < fcst.length; i++) {
      version.push({
        label: 'M-' + $calcMonthGap(fcst[i], currentMonth) + ' [' + fcst[i] + ']',
        value: fcst[i]
      })
    }
    pageCtl.fcstVersion = version
    pageCtl.conditions.fcstVersion = version[0].value

    pageCtl.filterOpts = body.CASCADER
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  searchReport1()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/demand/evolution/query_report1_columns',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1DateColumns = body

    const dateList = body.DATE_COLUMNS
    const selectedColumns = [] as any
    selectedColumns.push(...pageCtl.conditions.report1GroupColumns)
    selectedColumns.push(...pageCtl.report1DataColumns)
    selectedColumns.push(...dateList)
    pageCtl.report1SelectedColumns = selectedColumns

    const allColumns = [] as any
    allColumns.push(...pageCtl.conditions.report1GroupColumns)
    allColumns.push(...pageCtl.report1DataColumns)
    allColumns.push(...dateList)
    pageCtl.report1AllColumns = allColumns

    report1TableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const afterReport1Select = (r, c, a) => {
  const xAxis = [] as any
  const yAxis1 = [] as any
  const yAxis2 = [] as any
  const historyColumns = pageCtl.report1DateColumns.HIST_COLUMNS
  const fcstColumns = pageCtl.report1DateColumns.FCST_COLUMNS

  xAxis.push(...historyColumns)
  xAxis.push(...fcstColumns)

  for (let i = 0; i < historyColumns.length; i++) {
    yAxis1.push(r[historyColumns[i]])
    if (i === historyColumns.length - 1) {
      yAxis2.push(r[historyColumns[i]])
    } else {
      yAxis2.push(null)
    }
  }

  for (let i = 0; i < fcstColumns.length; i++) {
    yAxis1.push(null)
    yAxis2.push(r[fcstColumns[i]])
  }

  pageCtl.report1Trends = {
    xAxis,
    yAxis1,
    yAxis2
  }

  const title = [] as any
  let groupColumns = [...pageCtl.conditions.report1GroupColumns]
  if (groupColumns.length === 0) {
    groupColumns = ['CLUSTER_NAME', 'ENTITY']
  }
  for (let i = 0; i < groupColumns.length; i++) {
    title.push(r[groupColumns[i]])
  }

  pageCtl.report1TrendsTitle = title.join(',') + ' Trends'
  pageCtl.report1PipelineTitle = title.join(',') + ' Pipeline'

  if (a === 'CURRENT_MONTH') {
    pageCtl.selectedConditions.detailsType = 'CURRENT_MONTH'
    pageCtl.report1DetailsTitle = '[Current Month] ' + title.join(',') + ' Details'
  } else if (historyColumns.indexOf(a) !== -1) {
    pageCtl.selectedConditions.detailsMonth = a
    pageCtl.selectedConditions.detailsType = 'HIST'
    pageCtl.report1DetailsTitle = '[History] ' + title.join(',') + ' Details'
  } else if (fcstColumns.indexOf(a) !== -1) {
    pageCtl.selectedConditions.detailsMonth = a
    pageCtl.selectedConditions.detailsType = 'FCST'
    pageCtl.report1DetailsTitle = '[FCST] ' + title.join(',') + ' Details'
  } else {
    pageCtl.selectedConditions.detailsType = 'ALL'
    pageCtl.report1DetailsTitle = '[All History] ' + title.join(',') + ' Details'
  }

  pageCtl.selectedConditions.groupColumns = groupColumns
  pageCtl.selectedConditions.groupValue = title

  const pipeKey = 'PIPE_' + a
  if (r[pipeKey] && r[pipeKey] > 0) {
    pageCtl.selectedConditions.pipelineMonth = a
  } else {
    pageCtl.selectedConditions.pipelineMonth = ''
  }
}

const renderPipeline = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (!isNaN(value)) {
    td.innerHTML = $thousandBitSeparator(value, 0)
    td.style.textAlign = 'right'
  }
  if (r && r['PIPE_' + prop] && r['PIPE_' + prop] > 0) {
    td.style.setProperty('background-color', 'rgba(61, 205, 88, 0.4)', 'important')
  }
}

const _subScopeOpts = computed(() => {
  if (pageCtl.conditions.scope === 'SECI') {
    return ['SECI_IG', 'SECI_OG', 'SEHK_IG', 'SEHK_OG', 'SPC_IG', 'SPC_OG']
  } else if (pageCtl.conditions.scope === 'PLANT') {
    return ['PLANT_IG_DOMESTIC', 'PLANT_IG_EXPORT', 'PLANT_OG_DOMESTIC', 'PLANT_OG_EXPORT']
  } else {
    return []
  }
})

const _report1FixedLeft = computed(() => {
  return $intersection(_pivotColumns.value, pageCtl.report1SelectedColumns).length
})

const _report1Headers = computed(() => {
  if (!pageCtl.report1DateColumns.hasOwnProperty('DATE_COLUMNS')) {
    return {
      columns: [{ data: 'MATERIAL' }],
      nestedHeaders: [[{ colspan: 1 }]]
    }
  }

  const histList = pageCtl.report1DateColumns.HIST_COLUMNS
  const fcstList = pageCtl.report1DateColumns.FCST_COLUMNS

  const selectedData = $intersection(pageCtl.report1SelectedColumns, pageCtl.report1DataColumns)
  const selectedHist = $intersection(pageCtl.report1SelectedColumns, histList)
  const selectedFCST = $intersection(pageCtl.report1SelectedColumns, fcstList)

  const columns = [] as any
  const totalDisplayLength = pageCtl.report1SelectedColumns.length
  for (let i = 0; i < pageCtl.report1AllColumns.length; i++) {
    if (pageCtl.report1SelectedColumns.indexOf(pageCtl.report1AllColumns[i]) !== -1) {
      const columnName = pageCtl.report1AllColumns[i]
      if (pageCtl.conditions.report1GroupColumns.indexOf(columnName) !== -1) {
        columns.push({
          data: columnName
        })
      } else {
        columns.push({
          data: columnName,
          render: renderPipeline
        })
      }
    }
  }

  return {
    columns,
    nestedHeaders: [
      [
        { label: ' ', colspan: totalDisplayLength - selectedData.length - selectedHist.length - selectedFCST.length },
        { label: ' ', colspan: selectedData.length },
        { label: 'History', colspan: selectedHist.length },
        { label: 'FCST', colspan: selectedFCST.length }
      ]
    ]
  }
})

const _report1SelectConditions = computed(() => {
  return Object.assign({}, pageCtl.conditions, pageCtl.selectedConditions)
})

const _report1DetailsColumns = computed(() => {
  if (pageCtl.selectedConditions.detailsType === 'FCST') {
    const gap = $calcMonthGap(pageCtl.conditions.fcstVersion, pageCtl.selectedConditions.detailsMonth)
    const monthColumn = 'MONTH' + ((gap < 10) ? '0' + gap : '' + gap)

    return [{
      data: 'BU',
      title: 'Business Unit'
    }, {
      data: 'MATERIAL',
      title: 'Material'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'BOM_CATEGORY',
      title: 'BOM Category'
    }, {
      data: 'HEADER_MATERIAL',
      title: 'Header Material'
    }, {
      data: 'MATERIAL_OWNER_NAME',
      title: 'Material Owner Name'
    }, {
      data: 'MATERIAL_OWNER_SESA',
      title: 'Material Owner Sesa'
    }, {
      data: 'MRP_CONTROLLER',
      title: 'MRP Controller'
    }, {
      data: 'PRODUCT_LINE',
      title: 'Product Line'
    }, {
      data: 'LOCAL_BU',
      title: 'Local BU'
    }, {
      data: 'LOCAL_PRODUCT_FAMILY',
      title: 'Local Product Family'
    }, {
      data: 'LOCAL_PRODUCT_LINE',
      title: 'Local Product Line'
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY',
      title: 'Local Product Subfamily'
    }, {
      data: 'SALES_GROUP',
      title: 'Sales Group'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SHIPPING_POINT',
      title: 'Shipping Point'
    }, {
      data: 'SHIP_TO_REGION',
      title: 'Ship to Region'
    },
    {
      data: monthColumn,
      title: pageCtl.selectedConditions.detailsMonth + 'Qty',
      type: 'numeric'
    }, {
      data: 'AVG_SELLING_PRICE_RMB',
      title: 'Net Net Price',
      type: 'numeric',
      precision: 1
    }, {
      data: 'UNIT_COST',
      title: 'UNIT_COST',
      type: 'numeric',
      precision: 1
    }]
  } else if (pageCtl.conditions.dataType === 'ORDER_INTAKE') {
    return [{
      data: 'CALENDAR_MONTH',
      title: 'Sales Date'
    }, {
      data: 'BU',
      title: 'Business Unit'
    }, {
      data: 'SALES_ORDER_NUMBER',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    }, {
      data: 'SALES_ORDER_ITEM',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    }, {
      data: 'MATERIAL',
      title: 'Material'
    }, {
      data: 'MATERIAL_OWNER_NAME',
      title: 'Material Owner Name'
    }, {
      data: 'MATERIAL_OWNER_SESA',
      title: 'Material Owner Sesa'
    }, {
      data: 'PLANT_CODE',
      title: 'Plant Code'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SOLD_TO',
      title: 'Sold To'
    }, {
      data: 'MRP_CONTROLLER',
      title: 'MRP Controller'
    }, {
      data: 'PRODUCT_LINE',
      title: 'Product Line'
    }, {
      data: 'LOCAL_BU',
      title: 'Local BU'
    }, {
      data: 'LOCAL_PRODUCT_FAMILY',
      title: 'Local Product Family'
    }, {
      data: 'LOCAL_PRODUCT_LINE',
      title: 'Local Product Line'
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY',
      title: 'Local Product Subfamily'
    }, {
      data: 'SALES_GROUP',
      title: 'Sales Group'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SHIPPING_POINT',
      title: 'Shipping Point'
    }, {
      data: 'SHIP_TO_REGION',
      title: 'Ship to Region'
    },

    {
      data: 'ORDER_QUANTITY',
      title: 'Order Quantity',
      type: 'numeric'
    }, {
      data: 'AVG_SELLING_PRICE_RMB',
      title: 'Net Net Price',
      type: 'numeric',
      precision: 1
    }, {
      data: 'UNIT_COST',
      title: 'UNIT_COST',
      type: 'numeric',
      precision: 1
    }]
  } else if (pageCtl.conditions.dataType === 'CRD') {
    return [{
      data: 'CALENDAR_MONTH',
      title: 'CRD Date'
    }, {
      data: 'BU',
      title: 'Business Unit'
    }, {
      data: 'MATERIAL',
      title: 'Material'
    }, {
      data: 'MATERIAL_OWNER_NAME',
      title: 'Material Owner Name'
    }, {
      data: 'MATERIAL_OWNER_SESA',
      title: 'Material Owner Sesa'
    }, {
      data: 'PLANT_CODE',
      title: 'Plant Code'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SOLD_TO',
      title: 'Sold To'
    }, {
      data: 'MRP_CONTROLLER',
      title: 'MRP Controller'
    }, {
      data: 'PRODUCT_LINE',
      title: 'Product Line'
    }, {
      data: 'LOCAL_BU',
      title: 'Local BU'
    }, {
      data: 'LOCAL_PRODUCT_FAMILY',
      title: 'Local Product Family'
    }, {
      data: 'LOCAL_PRODUCT_LINE',
      title: 'Local Product Line'
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY',
      title: 'Local Product Subfamily'
    }, {
      data: 'SALES_GROUP',
      title: 'Sales Group'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SHIPPING_POINT',
      title: 'Shipping Point'
    }, {
      data: 'SHIP_TO_REGION',
      title: 'Ship to Region'
    },

    {
      data: 'ORDER_QUANTITY',
      title: 'Order Quantity',
      type: 'numeric'
    }, {
      data: 'AVG_SELLING_PRICE_RMB',
      title: 'Net Net Price',
      type: 'numeric',
      precision: 1
    }, {
      data: 'UNIT_COST',
      title: 'UNIT_COST',
      type: 'numeric',
      precision: 1
    }]
  } else if (pageCtl.conditions.dataType === 'SALES') {
    return [{
      data: 'CALENDAR_MONTH',
      title: 'Sales Date'
    }, {
      data: 'BU',
      title: 'Business Unit'
    }, {
      data: 'SALES_ORDER_NUMBER',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    }, {
      data: 'SALES_ORDER_ITEM',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    }, {
      data: 'MATERIAL',
      title: 'Material'
    }, {
      data: 'MATERIAL_OWNER_NAME',
      title: 'Material Owner Name'
    }, {
      data: 'MATERIAL_OWNER_SESA',
      title: 'Material Owner Sesa'
    }, {
      data: 'PLANT_CODE',
      title: 'Plant Code'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SOLD_TO',
      title: 'Sold To'
    }, {
      data: 'MRP_CONTROLLER',
      title: 'MRP Controller'
    }, {
      data: 'PRODUCT_LINE',
      title: 'Product Line'
    }, {
      data: 'LOCAL_BU',
      title: 'Local BU'
    }, {
      data: 'LOCAL_PRODUCT_FAMILY',
      title: 'Local Product Family'
    }, {
      data: 'LOCAL_PRODUCT_LINE',
      title: 'Local Product Line'
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY',
      title: 'Local Product Subfamily'
    }, {
      data: 'SALES_GROUP',
      title: 'Sales Group'
    }, {
      data: 'SALES_ORGANIZATION',
      title: 'Sales Organization'
    }, {
      data: 'SHIPPING_POINT',
      title: 'Shipping Point'
    }, {
      data: 'SHIP_TO_REGION',
      title: 'Ship to Region'
    },

    {
      data: 'SALES_QTY',
      title: 'Sales Qty',
      type: 'numeric'
    }, {
      data: 'AVG_SELLING_PRICE_RMB',
      title: 'Sales Net Net P',
      type: 'numeric',
      precision: 1
    }, {
      data: 'UNIT_COST',
      title: 'UNIT_COST',
      type: 'numeric',
      precision: 1
    }]
  } else {
    return []
  }
})

const _report1PipelineColumns = computed(() => {
  return [
    { data: 'CREATE_MONTH' },
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'CUSTOMER_CODE' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'SALES_NAME' },
    { data: 'MATERIAL_CLASS' },
    { data: 'MRP_CONTROLLER' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'PRODUCT_LINE' },
    { data: 'ENTITY' },
    { data: 'CLUSTER_NAME' },
    { data: 'BU' },
    { data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'STOCKING_POLICY' },
    { data: 'VENDOR_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'ACTIVENESS' },
    { data: 'SOURCE_CATEGORY' },
    { data: 'QTY', type: 'numeric', title: pageCtl.selectedConditions.pipelineMonth + ' Qty' },
    { data: 'AMU', type: 'numeric' },
    { data: 'AMF', type: 'numeric' },
    { data: 'WEEKLY_ORDER_LINE', type: 'numeric' }
  ]
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts).concat(['MATERIAL', 'ABC', 'CALCULATED_FMR']).sort()
})

</script>
