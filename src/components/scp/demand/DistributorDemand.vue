<template>
  <div class="left-sidebar" id="DistributorDemand">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.report1Version">
              <el-option v-for="item in pageCtl.conditions.report1VersionList"
                         :key="item"
                         :label="item"
                         :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts"
                        :filter-base="['DISTRIBUTOR_DEMAND_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.report1ViewColumns" size="small" placeholder="category"
                       filterable clearable multiple collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="[]"/>
          </el-col>
        </el-row>
        <scp-subscript id="DDR1"/>
        <scp-table
            ref="report1Ref"
            url="/demand/distributor_demand/query_report1"
            download-url="/demand/distributor_demand/download_report1"
            :params="pageCtl.conditions"
            :max-height="580"
            :after-select="afterReport1Selected"
            :lazy="true"
            :pagging="false"
            :dropdownMenu="false"
            :show-total="true"
            :editable="false"
            :fixed-columns-left="pageCtl.conditions.report1ViewColumns.length < 2 ? pageCtl.conditions.report1ViewColumns.length : 2"
            :context-menu-items="pageCtl.contextItems"
            :columns="pageCtl.report1Columns"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
const $axios: any = inject('$axios')
const $viewDetails: any = inject('$viewDetails')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $px2Rem: any = inject('$px2Rem')

const report1Ref = ref()
const searchRef = ref()

const viewReport1Details = () => {
  $viewDetails({
    title: pageCtl.report1DetailsTitle,
    url: '/demand/distributor_demand/query_report1_details',
    durl: '/demand/distributor_demand/download_report1_details',
    params: pageCtl.conditions
  })
}

const pageCtl = reactive({
  filterOpts: [],
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    report1ViewColumns: ['CUSTOMER_NAME', 'MATERIAL'],
    report1SelectedValue: [],
    report1SelectedColumn: '',
    report1Version: 'Current',
    report1VersionList: []
  },
  loading: {
    report1: false
  },
  contextItems: {
    view_details: {
      name: 'View Details',
      callback: viewReport1Details
    },
    view_split0: { name: '---------' }
  },
  report1DetailsTitle: '',
  report1Columns: []
})

onMounted(() => {
  initPage()
})

const initPage = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/demand/distributor_demand/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.conditions.report1VersionList = body.versionList
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(
    pageCtl.loading.report1 = false
  )
}

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

const search = () => {
  if (pageCtl.conditions.report1ViewColumns.length === 0) {
    pageCtl.conditions.report1ViewColumns = ['CUSTOMER_NAME']
  }
  searchReport1()
}

const searchReport1 = () => {
  pageCtl.conditions.report1SelectedValue = []
  report1Column()
  report1Ref.value.search()
}

const camelCaseStartPlaceholder = (word : string) => {
  const words = word.split('_')
  const camelCaseWords = words.map((word, index) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  })
  return camelCaseWords.join(' ')
}

const report1Column = () => {
  const columns = [] as any
  for (let i = 0; i < _report1SelectedColumns.value.length; i++) {
    columns.push({
      data: _report1SelectedColumns.value[i],
      title: _report1SelectedColumns.value[i]
    })
  }
  columns.push(
    { data: 'POTENTIAL_DEMAND_QTY', title: camelCaseStartPlaceholder('POTENTIAL_DEMAND_QTY'), type: 'numeric', render: renderRatioWeekly },
    { data: 'POTENTIAL_DEMAND_VALUE', title: camelCaseStartPlaceholder('POTENTIAL_DEMAND_VALUE'), type: 'numeric' },
    { data: 'ORDER_INTAKE_SO_FAR_QTY', title: camelCaseStartPlaceholder('ORDER_INTAKE_SO_FAR_QTY'), type: 'numeric' },
    { data: 'POTENTIAL_DEMAND_LEFT_QTY', title: camelCaseStartPlaceholder('POTENTIAL_DEMAND_LEFT_QTY'), type: 'numeric' },
    { data: 'AVAILABLE_STOCK_QTY', title: camelCaseStartPlaceholder('AVAILABLE_STOCK_QTY'), type: 'numeric' },
    { data: 'CURRENT_MONTH_FCST', title: camelCaseStartPlaceholder('CURRENT_MONTH_FCST'), type: 'numeric' }
  )
  pageCtl.report1Columns = columns
}

const renderRatioWeekly = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (value) {
    td.innerHTML = $thousandBitSeparator(value, 0)
  } else {
    td.innerHTML = value
  }

  const colors = ['#c12e34', '#c12e34', '#cb4a2a', '#cb4a2a', '#dc7318', '#dc7318', '#ccb601', '#ccb601', '#6a9a12', '#6a9a12', '#2c821d']
  if (value === 0) {
    let style = 'text-align: right; background-image: linear-gradient(to right, #2c821d 100%, white 100%) !important;color: #fff !important;'
    td.title = '100%'
    if (row === hotInstance.countRows() - 1) {
      style += 'font-weight: bold;'
    }
    td.style = style
  } else if (value) {
    const finish = r.ORDER_INTAKE_SO_FAR_QTY || 0
    const percent = finish / value * 100
    const color = colors[parseInt(Math.min(percent / 10, 10) + '')]
    const t = Math.min(percent + 2, 100)

    let style = 'text-align: right; background-image: linear-gradient(to right, ' + color + ' ' + percent + '%, var(--scp-bg-color) ' + t + '%) !important;'

    td.title = finish + '/' + value + ', ' + percent.toFixed(1) + '%'

    if (t >= 100) {
      style += 'color: #fff !important;'
    }
    if (row === hotInstance.countRows() - 1) {
      style += 'font-weight: bold;'
    }
    td.style = style
  }
}

const afterReport1Selected = (r, c, a) => {
  pageCtl.conditions.report1SelectedColumn = a
  if (r[_report1SelectedColumns.value[0]] === 'Total') {
    pageCtl.conditions.report1SelectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _report1SelectedColumns.value.length; i++) {
      selectedValue.push(r[_report1SelectedColumns.value[i]])
    }
    pageCtl.conditions.report1SelectedValue = selectedValue
  }

  let selectColumnName = ''
  for (let i = 0; i < pageCtl.report1Columns.length; i++) {
    const obj: any = pageCtl.report1Columns[i]
    if (a === obj.data && (a.indexOf('CUSTOMER_NAME') === 0)) {
      selectColumnName = obj.title
    }
  }
  const title = $join(...pageCtl.conditions.report1SelectedValue, selectColumnName)
  if (title) {
    pageCtl.report1DetailsTitle = 'View Details [' + title + ']'
  } else {
    if (pageCtl.conditions.report1SelectedValue.length > 0) {
      pageCtl.report1DetailsTitle = 'View Details [' + $join(...pageCtl.conditions.report1SelectedValue) + ']'
    } else {
      pageCtl.report1DetailsTitle = 'View Details [Total]'
    }
  }
}

const _report1SelectedColumns = computed(() => {
  if (pageCtl.conditions.report1ViewColumns.length > 0) {
    return pageCtl.conditions.report1ViewColumns
  } else {
    return ['CUSTOMER_NAME']
  }
})

</script>
