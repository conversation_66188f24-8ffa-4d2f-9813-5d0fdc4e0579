<template>
    <div class="left-sidebar" id="miniSIOP">
        <div class="widget">
            <div class="widget-body">
                <el-row class="search-box">
                    <el-col :span="5">
                        <scp-cascader
                                v-model="pageCtl.conditions.category"
                                :placeholder="pageCtl.loading.filter ? 'Loading...' : 'Filters'"
                                :options="pageCtl.reportFilterOpts"/>
                    </el-col>
                    <el-col :span="2">
                        <el-select v-model="pageCtl.conditions.specialType" size="small" style="width: 100% !important;" class="search-group-left">
                            <el-option label="MATERIAL" value="MATERIAL"/>
                            <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-input :placeholder="pageCtl.conditions.specialType" size="small" v-model="pageCtl.conditions.specialContent"
                                  style="width: var(--scp-input-width) !important;" type="textarea" class="search-group-right"></el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-date-picker
                                size="small"
                                v-model="pageCtl.conditions.dateRange"
                                type="monthrange"
                                unlink-panels
                                range-separator="to"
                                format="YYYYMM"
                                value-format="YYYYMM"
                                start-placeholder="Start month"
                                end-placeholder="End month">
                        </el-date-picker>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.fcstVersion" size="small" :loading="pageCtl.loading.filter"
                                   :placeholder="pageCtl.loading.filter ? 'Loading...' : 'FCST Version'">
                            <el-option
                                    v-for="item in pageCtl.fcstVersionOpts"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.type" size="small">
                            <el-option
                                    v-for="item in ['Net Net Price','Moving Average Price','Quantity']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="pageCtl.conditions.scope" size="small" collapse-tags multiple clearable>
                            <el-option
                                    v-for="item in ['SECI','SEHK']"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="1">
                        <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['fcstVersion','dateRange']"/>
                    </el-col>
                </el-row>
                <div class="subscript-container" v-loading="pageCtl.loading.report1 || pageCtl.loading.filter">
                    <scp-subscript id="DMOP"/>
                    <el-select v-model="pageCtl.report1xAxisSelected" size="small" multiple collapse-tags clearable style="float: right;margin-right: 112px;margin-top: 2px;width: 240px;z-index: 1000"
                               class="xAxisSelect">
                        <el-option
                                v-for="item in pageCtl.report1xAxis.filter(e => e !=='   ')"
                                :key="item"
                                :label="item"
                                :value="item">
                        </el-option>
                    </el-select>
                    <chart :height="600" :option="_report1Opt" ref="report1Ref"/>
                </div>
            </div>
            <div style="text-align: right">
                <el-button size="small" style="border: none !important;font-size: 0.4rem;color:var(--scp-text-color-secondary);font-style:oblique" @click="downloadMiniSIOPReport">
                    {{ pageCtl.loading.downloadMINISIOP === true ? 'Downloading...' : 'Download Report' }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $shortenNumber: any = inject('$shortenNumber')
const $axios: any = inject('$axios')
const $calcMonthGap: any = inject('$calcMonthGap')
const $downloadFile: any = inject('$downloadFile')
const $startWith: any = inject('$startWith')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1Ref = ref()

const pageCtl = reactive({
  loading: {
    report1: false,
    filter: false,
    downloadMINISIOP: false
  },
  conditions: {
    type: 'Net Net Price',
    scope: ['SECI'],
    category: [],
    material: '',
    fcstVersion: '',
    dateRange: [] as any,
    specialType: 'MATERIAL',
    specialContent: ''
  },
  fcstVersionOpts: [] as any,
  reportFilterOpts: [],
  report1Data: {},
  report1xAxis: ['Demand', 'STD Sales', 'STD Order Intake', 'STD CRD', 'SOH[Demand]',
    'GIT[Demand]', 'GAP[Demand]', 'Conf.Reource[Demand]', '   ', 'Ordered Demand',
    'SOH[Order]', 'GIT[Order]', 'GAP[Order]', 'Conf.Reource[Order]'],
  report1xAxisSelected: ['Demand', 'STD Sales', 'STD Order Intake', 'STD CRD', 'SOH[Demand]',
    'GIT[Demand]', 'GAP[Demand]', 'Conf.Reource[Demand]', 'Ordered Demand',
    'SOH[Order]', 'GIT[Order]', 'GAP[Order]', 'Conf.Reource[Order]'],
  report1yAxisRaw: [
    ['Unrealized <=30% FCST', 'Unrealized >30% FCST', 'Unrealized >70% FCST', 'Sales Exceed FCST >100%', 'Sales Exceed FCST >70%', 'Sales Exceed FCST >30%', 'Sales Exceed FCST <=30%'],
    ['Predicted Sales', 'Actual Sales Before Selected FCST', 'IG Sales', 'OI Exceed FCST >100%', 'OI Exceed FCST >70%', 'OI Exceed FCST >30%', 'OI Exceed FCST <=30%'],
    ['Predicted OI', 'Actual OI Before Selected FCST', 'IG OI', 'CRD Exceed FCST >100%', 'CRD Exceed FCST >70%', 'CRD Exceed FCST >30%', 'CRD Exceed FCST <=30%'],
    ['Predicted CRD', 'Actual CRD Before Selected FCST', 'IG CRD', 'Stock Aging >2Y', 'Stock Aging >1Y', 'For Future Demand', 'For Safety Stock'],
    ['Un-Issue Delivery >30CD', 'Un-Issue Delivery >7CD', 'Un-Issue Delivery <=7CD', 'For IG Order in Selected Period', 'IG Un-Issue Delivery', 'For Demand in Selected Period', 'For Future Order'],
    ['For Order in Selected Period', 'To be Fulfilled', 'CRD Out of Selected Period', 'CRD in Selected Period', 'Back Order']
  ],
  report1yAxisColorMap: {
    'Sales Exceed FCST >100%': 'rgba(0,128,0,0.6)',
    'Sales Exceed FCST >70%': 'rgba(0,128,0,0.7)',
    'Sales Exceed FCST >30%': 'rgba(0,128,0,0.8)',
    'Sales Exceed FCST <=30%': 'rgba(0,128,0,0.9)',
    'Predicted Sales': 'rgba(0,128,0,1)',
    'Unrealized <=30% FCST': 'rgba(255,102,0,0.6)',
    'Unrealized >30% FCST': 'rgba(255,102,0,0.8)',
    'Unrealized >70% FCST': 'rgba(255,102,0,1)',
    'IG Sales': 'rgba(0,128,0,0.4)',
    'Actual Sales Before Selected FCST': 'rgba(0,102,0,1)',
    'IG OI': 'rgba(47,117,181,0.4)',
    'OI Exceed FCST >100%': 'rgba(47,117,181,0.6)',
    'OI Exceed FCST >70%': 'rgba(47,117,181,0.7)',
    'OI Exceed FCST >30%': 'rgba(47,117,181,0.8)',
    'OI Exceed FCST <=30%': 'rgba(47,117,181,0.9)',
    'Predicted OI': 'rgba(47,117,181,1)',
    'Actual OI Before Selected FCST': 'rgba(31,78,120,1)',
    'IG CRD': 'rgba(153,51,102,0.4)',
    'CRD Exceed FCST >100%': 'rgba(153,51,102,0.6)',
    'CRD Exceed FCST >70%': 'rgba(153,51,102,0.7)',
    'CRD Exceed FCST >30%': 'rgba(153,51,102,0.8)',
    'CRD Exceed FCST <=30%': 'rgba(153,51,102,0.9)',
    'Predicted CRD': 'rgba(153,51,102,1)',
    'Actual CRD Before Selected FCST': 'rgba(102,0,51,1)',
    'Stock Aging >2Y': 'rgba(0,51,0,0.9)',
    'Stock Aging >1Y': 'rgba(0,51,0,0.7)',
    'For Future Demand': 'rgba(0,128,0,0.6)',
    'For Future Order': 'rgba(0,128,0,0.7)',
    'For Safety Stock': 'rgba(0,128,0,0.8)',
    'For IG Order in Selected Period': 'rgba(255,255,0,0.4)',
    'IG Un-Issue Delivery': 'rgba(255,255,0,0.7)',
    'For Demand in Selected Period': 'rgba(0,128,0,0.9)',
    'Un-Issue Delivery >30CD': 'rgba(255,0,0,1)',
    'Un-Issue Delivery >7CD': 'rgba(255,0,0,0.6)',
    'Un-Issue Delivery <=7CD': 'rgba(255,0,0,0.4)',
    'To be Fulfilled': 'rgba(255,102,0,0.6)',
    'CRD Out of Selected Period': 'rgba(153,51,102,0.7)',
    'CRD in Selected Period': 'rgba(153,51,102,1)',
    'Back Order': 'rgba(255,0,0,0.8)',
    'For Order in Selected Period': 'rgba(0,128,0,0.9)'
  },
  report1Markline: 0,
  report1DetailsParam: {} as any
})

onMounted(() => {
  filterInit()

  report1Ref.value.chart().on('dblclick', (param) => {
    if (param.name !== 'GAP[Order]' && param.name !== 'GAP[Demand]') {
      const temp: any = {}
      for (const key in pageCtl.conditions) {
        if (pageCtl.conditions.hasOwnProperty(key)) {
          temp[key] = pageCtl.conditions[key]
        }
      }

      temp.report1Category = param.name
      temp.report1SubCategory = param.seriesName

      pageCtl.report1DetailsParam = temp

      $viewDetails({
        title: param.name + ' - ' + param.seriesName,
        url: '/demand/mini_siop/query_report1_details',
        durl: '/demand/mini_siop/download_report1_details',
        params: pageCtl.report1DetailsParam,
        columns: _report1DetailsColumn.value
      })
    }
  })
})

const filterInit = () => {
  const year = new Date().getFullYear()
  const month = new Date().getMonth()
  if (month < 3) {
    pageCtl.conditions.dateRange = [year + '01', year + '03']
  } else if (month < 6) {
    pageCtl.conditions.dateRange = [year + '04', year + '06']
  } else if (month < 9) {
    pageCtl.conditions.dateRange = [year + '07', year + '09']
  } else if (month < 12) {
    pageCtl.conditions.dateRange = [year + '10', year + '12']
  }

  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/demand/mini_siop/init_page'
  }).then((body) => {
    pageCtl.reportFilterOpts = body.cascader

    const version = [] as any
    const currentMonth = $dateFormatter(new Date(), 'yyyyMM')
    const fcst = body.fcstVersion
    for (let i = 0; i < fcst.length; i++) {
      version.push({
        label: 'M-' + $calcMonthGap(fcst[i], currentMonth) + ' [' + fcst[i] + ']',
        value: fcst[i]
      })
    }
    pageCtl.fcstVersionOpts = version

    pageCtl.conditions.fcstVersion = pageCtl.fcstVersionOpts.length > 0 ? pageCtl.fcstVersionOpts[0].value : ''
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/demand/mini_siop/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    const raw = body
    for (let i = 0; i < pageCtl.report1xAxis.length; i++) {
      for (let j = 0; j < _report1yAxis.value.length; j++) {
        if (!raw[pageCtl.report1xAxis[i]]) {
          raw[pageCtl.report1xAxis[i]] = {}
        }
        if (!raw[pageCtl.report1xAxis[i]][_report1yAxis.value[j]]) {
          raw[pageCtl.report1xAxis[i]][_report1yAxis.value[j]] = 0
        }
      }
    }
    pageCtl.report1Markline = raw.Demand['Sales Exceed FCST >100%'] + raw.Demand['Sales Exceed FCST >70%'] + raw.Demand['Sales Exceed FCST >30%'] + raw.Demand['Sales Exceed FCST <=30%'] + raw.Demand['Predicted Sales'] + raw.Demand['Unrealized <=30% FCST'] + raw.Demand['Unrealized >30% FCST'] + raw.Demand['Unrealized >70% FCST'] + raw.Demand['Actual Sales Before Selected FCST']
    raw.$SupportValue1 = pageCtl.report1Markline - raw['STD Sales']['Sales Exceed FCST >100%'] - raw['STD Sales']['Sales Exceed FCST >70%'] - raw['STD Sales']['Sales Exceed FCST >30%'] - raw['STD Sales']['Sales Exceed FCST <=30%'] - raw['STD Sales']['Predicted Sales'] - raw['STD Sales']['Actual Sales Before Selected FCST']
    raw.$SupportValue2 = pageCtl.report1Markline - raw['STD Order Intake']['Predicted OI'] - raw['STD Order Intake']['OI Exceed FCST >100%'] - raw['STD Order Intake']['OI Exceed FCST >70%'] - raw['STD Order Intake']['OI Exceed FCST >30%'] - raw['STD Order Intake']['OI Exceed FCST <=30%'] - raw['STD Order Intake']['Actual OI Before Selected FCST']
    raw.$SupportValue3 = pageCtl.report1Markline - raw['STD CRD']['Predicted CRD'] - raw['STD CRD']['CRD Exceed FCST >100%'] - raw['STD CRD']['CRD Exceed FCST >70%'] - raw['STD CRD']['CRD Exceed FCST >30%'] - raw['STD CRD']['CRD Exceed FCST <=30%'] - raw['STD CRD']['Actual CRD Before Selected FCST']
    raw.$SupportValue4 = raw['STD Sales']['Sales Exceed FCST >100%'] + raw['STD Sales']['Sales Exceed FCST >70%'] + raw['STD Sales']['Sales Exceed FCST >30%'] + raw['STD Sales']['Sales Exceed FCST <=30%'] + raw['STD Sales']['Predicted Sales'] + raw['STD Sales']['Actual Sales Before Selected FCST']
    raw.$SupportValue5 = pageCtl.report1Markline - raw.$SupportValue4 - raw['SOH[Demand]']['For Demand in Selected Period'] - raw['SOH[Demand]']['Un-Issue Delivery >30CD'] - raw['SOH[Demand]']['Un-Issue Delivery >7CD'] - raw['SOH[Demand]']['Un-Issue Delivery <=7CD']
    raw.$SupportValue6 = raw['GIT[Demand]']['For Future Demand'] + raw['GIT[Demand]']['For Safety Stock'] + raw['GIT[Demand]']['For IG Order in Selected Period'] + raw['Conf.Reource[Demand]']['For Future Demand'] + raw['Conf.Reource[Demand]']['For Safety Stock'] + raw['Conf.Reource[Demand]']['For IG Order in Selected Period']
    raw.$SupportValue7 = Math.max(raw.$SupportValue5, 0) + raw['SOH[Demand]']['For Future Demand'] + raw['SOH[Demand]']['For Safety Stock'] + raw['SOH[Demand]']['For IG Order in Selected Period'] + raw['SOH[Demand]']['IG Un-Issue Delivery'] - raw['GIT[Demand]']['For IG Order in Selected Period']
    raw.$SupportValue9 = raw.$SupportValue4 + raw['SOH[Demand]']['For Demand in Selected Period'] + raw['SOH[Demand]']['Un-Issue Delivery >30CD'] + raw['SOH[Demand]']['Un-Issue Delivery >7CD'] + raw['SOH[Demand]']['Un-Issue Delivery <=7CD']
    raw['GAP[Demand]']['To be Fulfilled'] = Math.max(pageCtl.report1Markline - raw.$SupportValue9 - raw['GIT[Demand]']['For Demand in Selected Period'], 0)
    raw.$SupportValue10 = pageCtl.report1Markline - raw['GAP[Demand]']['To be Fulfilled']
    raw.$SupportValue11 = raw.$SupportValue9 + raw['GIT[Demand]']['For Demand in Selected Period']
    raw.$SupportValue13 = raw.$SupportValue7 + raw['GIT[Demand]']['For Future Demand'] + raw['GIT[Demand]']['For Safety Stock'] + raw['GIT[Demand]']['For IG Order in Selected Period'] - raw['Conf.Reource[Demand]']['For Demand in Selected Period']
    raw.$SupportValue14 = Math.max(pageCtl.report1Markline - raw['Ordered Demand']['Back Order'] - raw['Ordered Demand']['CRD in Selected Period'] - raw['Ordered Demand']['Sales Exceed FCST >100%'] - raw['Ordered Demand']['Sales Exceed FCST >70%'] - raw['Ordered Demand']['Sales Exceed FCST >30%'] - raw['Ordered Demand']['Sales Exceed FCST <=30%'] - raw['Ordered Demand']['Predicted Sales'] - raw['Ordered Demand']['Actual Sales Before Selected FCST'], 0)
    raw.$SupportValue15 = raw.$SupportValue4
    raw.$SupportValue16 = Math.max(pageCtl.report1Markline - raw.$SupportValue15 - raw['SOH[Order]']['For Order in Selected Period'] - raw['SOH[Order]']['Un-Issue Delivery >30CD'] - raw['SOH[Order]']['Un-Issue Delivery >7CD'] - raw['SOH[Order]']['Un-Issue Delivery <=7CD'], 0)
    raw.$SupportValue17 = raw.$SupportValue15 + raw['SOH[Order]']['For Order in Selected Period'] + raw['SOH[Order]']['Un-Issue Delivery >30CD'] + raw['SOH[Order]']['Un-Issue Delivery >7CD'] + raw['SOH[Order]']['Un-Issue Delivery <=7CD']
    raw.$SupportValue18 = raw.$SupportValue16 + raw['SOH[Order]']['For Future Demand'] + raw['SOH[Order]']['For Future Order'] + raw['SOH[Order]']['For Safety Stock'] + raw['SOH[Order]']['For IG Order in Selected Period'] + raw['SOH[Order]']['IG Un-Issue Delivery'] - raw['GIT[Order]']['For Order in Selected Period']
    raw['GAP[Order]']['To be Fulfilled'] = Math.max(raw['Ordered Demand']['CRD in Selected Period'] + raw['Ordered Demand']['Back Order'] - raw['SOH[Order]']['For Order in Selected Period'] - raw['SOH[Order]']['Un-Issue Delivery >30CD'] - raw['SOH[Order]']['Un-Issue Delivery >7CD'] - raw['SOH[Order]']['Un-Issue Delivery <=7CD'] - raw['GIT[Order]']['For Order in Selected Period'], 0)
    raw.$SupportValue20 = raw.$SupportValue17 + raw['GIT[Order]']['For Order in Selected Period']
    raw.$SupportValue21 = raw.$SupportValue20
    raw.$SupportValue23 = raw.$SupportValue18 + raw['GIT[Order]']['For Future Demand'] + raw['GIT[Order]']['For Future Order'] + raw['GIT[Order]']['For Safety Stock'] + raw['GIT[Order]']['For IG Order in Selected Period'] - raw['Conf.Reource[Order]']['For Order in Selected Period']
    raw.$SupportValue24 = raw['GIT[Order]']['For Future Demand'] + raw['GIT[Order]']['For Future Order'] + raw['GIT[Order]']['For Safety Stock'] + raw['GIT[Order]']['For IG Order in Selected Period'] + raw['Conf.Reource[Order]']['For Future Demand'] + raw['Conf.Reource[Order]']['For Future Order'] + raw['Conf.Reource[Order]']['For Safety Stock'] + raw['Conf.Reource[Order]']['For IG Order in Selected Period']
    for (const key in raw) {
      if (raw.hasOwnProperty(key)) {
        if (raw[key] < 0) {
          raw[key] = null
        }
      }
    }
    pageCtl.report1Data = parseReport1Data(raw)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const putReport1Value = (target, source, key, indexArray) => {
  target[key] = [null, null, null, null, null, null, null, null, null, null, null, null, null, null]
  for (let i = 0; i < indexArray.length; i++) {
    target[key][indexArray[i]] = source[pageCtl.report1xAxis[indexArray[i]]][key]
  }
}

const parseReport1Data = (raw) => {
  const result = {} as any

  result.$SupportLine1 = [null, null, null, null, raw.$SupportValue4, raw.$SupportValue9, raw.$SupportValue10, raw.$SupportValue11, null, null, raw.$SupportValue15, raw.$SupportValue17, raw.$SupportValue20, raw.$SupportValue21]
  putReport1Value(result, raw, 'Actual Sales Before Selected FCST', [0, 1, 9])
  putReport1Value(result, raw, 'Actual OI Before Selected FCST', [2])
  putReport1Value(result, raw, 'Actual CRD Before Selected FCST', [3])
  putReport1Value(result, raw, 'Predicted Sales', [0, 1, 9])
  putReport1Value(result, raw, 'Predicted OI', [2])
  putReport1Value(result, raw, 'Predicted CRD', [3])
  putReport1Value(result, raw, 'Sales Exceed FCST <=30%', [0, 1, 9])
  putReport1Value(result, raw, 'Sales Exceed FCST >30%', [0, 1, 9])
  putReport1Value(result, raw, 'Sales Exceed FCST >70%', [0, 1, 9])
  putReport1Value(result, raw, 'Sales Exceed FCST >100%', [0, 1, 9])
  putReport1Value(result, raw, 'Unrealized >70% FCST', [0])
  putReport1Value(result, raw, 'Unrealized >30% FCST', [0])
  putReport1Value(result, raw, 'Unrealized <=30% FCST', [0])
  putReport1Value(result, raw, 'OI Exceed FCST <=30%', [2])
  putReport1Value(result, raw, 'OI Exceed FCST >30%', [2])
  putReport1Value(result, raw, 'OI Exceed FCST >70%', [2])
  putReport1Value(result, raw, 'OI Exceed FCST >100%', [2])
  putReport1Value(result, raw, 'CRD Exceed FCST <=30%', [3])
  putReport1Value(result, raw, 'CRD Exceed FCST >30%', [3])
  putReport1Value(result, raw, 'CRD Exceed FCST >70%', [3])
  putReport1Value(result, raw, 'CRD Exceed FCST >100%', [3])
  putReport1Value(result, raw, 'Un-Issue Delivery <=7CD', [4, 10])
  putReport1Value(result, raw, 'Un-Issue Delivery >7CD', [4, 10])
  putReport1Value(result, raw, 'Un-Issue Delivery >30CD', [4, 10])
  putReport1Value(result, raw, 'For Demand in Selected Period', [4, 5, 7])
  putReport1Value(result, raw, 'Back Order', [9])
  putReport1Value(result, raw, 'CRD in Selected Period', [9])
  putReport1Value(result, raw, 'For Order in Selected Period', [10, 11, 13])
  putReport1Value(result, raw, 'To be Fulfilled', [6, 12])
  result.$SupportLine2 = [null, raw.$SupportValue1, raw.$SupportValue2, raw.$SupportValue3, raw.$SupportValue5, raw.$SupportValue7, null, raw.$SupportValue13, null, raw.$SupportValue14, raw.$SupportValue16, raw.$SupportValue18, null, raw.$SupportValue23]
  putReport1Value(result, raw, 'CRD Out of Selected Period', [9])
  putReport1Value(result, raw, 'IG Sales', [1])
  putReport1Value(result, raw, 'IG OI', [2])
  putReport1Value(result, raw, 'IG CRD', [3, 9])
  putReport1Value(result, raw, 'IG Un-Issue Delivery', [4, 10])
  putReport1Value(result, raw, 'For IG Order in Selected Period', [4, 5, 7, 10, 11, 13])
  putReport1Value(result, raw, 'For Safety Stock', [4, 5, 7, 10, 11, 13])
  putReport1Value(result, raw, 'For Future Order', [10, 11, 13])
  putReport1Value(result, raw, 'For Future Demand', [4, 5, 7, 10, 11, 13])
  result.$SupportLine3 = [null, null, null, null, raw.$SupportValue6, null, null, null, null, null, raw.$SupportValue24, null, null, null]
  putReport1Value(result, raw, 'Stock Aging >1Y', [4, 10])
  putReport1Value(result, raw, 'Stock Aging >2Y', [4, 10])
  return result
}

const parseDisplayValue = (data) => {
  const result = [] as any
  const displayIndex = _report1xAxisDisplay.value.index
  for (let i = 0; i < displayIndex.length; i++) {
    result.push(data[displayIndex[i]])
  }
  return result
}

const downloadMiniSIOPReport = () => {
  if (pageCtl.loading.downloadMINISIOP) {
    return
  }

  pageCtl.loading.downloadMINISIOP = true
  $downloadFile('/demand/mini_siop/download_report1_for_minisiop_meeting', pageCtl.conditions, () => {
    pageCtl.loading.downloadMINISIOP = false
  })
}

const _report1Opt = computed(() => {
  const series: any = []
  const color: any = []
  let hasMarkLine = false

  // series
  for (const key in pageCtl.report1Data) {
    if (!pageCtl.report1Data.hasOwnProperty(key)) {
      continue
    }
    if (key.indexOf('$Support') !== -1) {
      const obj: any = {
        name: key,
        type: 'bar',
        stack: 'Total',
        itemStyle: {
          borderColor: 'rgba(0,0,0,0)',
          color: 'rgba(0,0,0,0)'
        },
        emphasis: {
          itemStyle: {
            borderColor: 'rgba(0,0,0,0)',
            color: 'rgba(0,0,0,0)'
          }
        },
        data: parseDisplayValue(pageCtl.report1Data[key])
      }

      if (!hasMarkLine) {
        hasMarkLine = true
        obj.markLine = {
          symbol: 'none',
          silent: true,
          data: [{
            lineStyle: {
              type: 'dashed',
              color: '#adadaf'
            },
            label: {
              show: false
            },
            yAxis: pageCtl.report1Markline
          }]
        }
      }

      series.push(obj)
    } else {
      color.push(pageCtl.report1yAxisColorMap[key])
      series.push({
        name: key,
        type: 'bar',
        stack: 'Total',
        label: {
          show: false
        },
        data: parseDisplayValue(pageCtl.report1Data[key])
      })
    }
  }

  // legend
  const legend = [] as any
  const step = 100 / pageCtl.report1yAxisRaw.length

  if (series.length > 0) {
    for (let i = 0; i < pageCtl.report1yAxisRaw.length; i++) {
      legend.push({
        orient: 'horizontal',
        x: (2 + i * step) + '%',
        top: '20px',
        width: '50px',
        align: 'left',
        padding: [410, 0, 0, 0],
        data: pageCtl.report1yAxisRaw[i]
      })
    }
  }

  return {
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          show: true,
          title: {
            zoom: 'Zoom',
            back: 'Back'
          }
        },
        dataView: {
          lang: ['Data Viewer', 'Close', 'Refresh'],
          optionToContent: (opt) => {
            const axisData = opt.xAxis[0].data
            const series = opt.series
            let table = '<table style="width:4800px;" class="gridtable">'
            table += '<tbody>'
            table += '<tr>'
            table += '<th class="fixed">Type</th>'
            const emptyIndex = [] as any
            for (let i = 0; i < series.length; i++) {
              if ($startWith(series[i].name, '$')) {
                emptyIndex.push(i)
                continue
              }
              table += '<th>' + series[i].name + '</th>'
            }

            for (let i = 0; i < axisData.length; i++) {
              if (axisData[i].trim()) {
                table += '<tr>'
                table += '<td class="fixed">' + axisData[i] + '</td>'
                for (let j = 0; j < series.length; j++) {
                  if (emptyIndex.indexOf(j) === -1) {
                    table += '<td>' + (series[j].data[i] ? $shortenNumber(series[j].data[i]) : '') + '</td>'
                  }
                }
                table += '</tr>'
              } else {
                table += '<tr>'
                table += '<td colspan="' + (series.length + 1) + '" class="splitline">&nbsp;</td>'
                table += '</tr>'
              }
            }
            table += '</tbody></table>'
            return table
          },
          backgroundColor: 'var(--scp-bg-color)',
          textColor: 'var(--scp-text-color-primary)'
        },
        saveAsImage: {}
      }
    },
    dataZoom: [
      {
        type: 'slider',
        yAxisIndex: [0],
        start: 0,
        end: 100,
        animation: true,
        filterMode: 'none',
        showDetail: false,
        showDataShadow: false,
        width: 15
      }
    ],
    color,
    animation: true,
    grid: $grid({ bottom: 180 }),
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if ((!params.seriesName) || params.seriesName.indexOf('$Support') !== -1) {
          return null
        }
        const tip = [] as any
        tip.push(params.seriesName)
        tip.push('<div style="width:9rem;">')
        tip.push(params.marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value, 1))
        tip.push('(' + (pageCtl.report1Markline === 0 ? '0' : (params.value / pageCtl.report1Markline * 100).toFixed(1) + '%') + ')')
        tip.push('</span></div>')
        return tip.join('')
      }
    },
    legend,
    xAxis: {
      data: _report1xAxisDisplay.value.display,
      axisLabel: {
        interval: 0,
        rotate: 25
      }
    },
    yAxis: {
      show: false,
      axisLabel: {
        show: false
      },
      axisPointer: {
        show: true,
        type: 'line',
        lineStyle: {
          type: 'dashed',
          color: '#3dcd58'
        },
        label: {
          formatter: (params) => {
            return $shortenNumber(params.value, 1)
          }
        }
      }
    },
    series
  }
})

const _report1yAxis = computed(() => {
  const yAxis = [] as any
  for (let i = 0; i < pageCtl.report1yAxisRaw.length; i++) {
    yAxis.push(...pageCtl.report1yAxisRaw[i])
  }
  return yAxis
})

const _report1xAxisDisplay = computed(() => {
  const index = [] as any
  const display = [] as any
  for (let i = 0; i < pageCtl.report1xAxis.length; i++) {
    const x = pageCtl.report1xAxis[i]
    if (pageCtl.report1xAxisSelected.indexOf(x) !== -1 || x === '   ') {
      index.push(i)
      display.push(x)
    }
  }
  return {
    display,
    index
  }
})

const _report1DetailsColumn = computed(() => {
  const category = pageCtl.report1DetailsParam.report1Category
  const subCategory = pageCtl.report1DetailsParam.report1SubCategory
  const column = [
    { data: 'MATERIAL' },
    { data: 'PLANT_CODE' },
    { data: 'SALES_ORGANIZATION' },
    { data: 'SE_SCOPE' },
    { data: 'SOLD_TO' },
    { data: 'SOLD_TO_COUNTRY' },
    { data: 'SHIP_TO' },
    { data: 'SHIP_TO_REGION' },
    { data: 'SHIP_TO_SUB_REGION' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MRP_CONTROLLER' },
    { data: 'PRODUCT_LINE' },
    { data: 'ENTITY' },
    { data: 'CLUSTER_NAME' },
    { data: 'BU' },
    { data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'STOCKING_POLICY' },
    { data: 'VENDOR_CODE' },
    { data: 'VENDOR_NAME' },
    { data: 'ACTIVENESS' }
  ] as any

  switch (category) {
    case 'Demand':
      if (subCategory.indexOf('Unrealized') !== -1) {
        column.push({ data: 'FCST_QTY', type: 'numeric' })
        column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
        column.push({ data: 'AVG_SELLING_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'UNREALIZED_VALUE', type: 'numeric', precision: 1 })
      } else if (subCategory.indexOf('Predicted') !== -1) {
        column.push({ data: 'ORDER_QUANTITY', type: 'numeric' })
        column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
        column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })
        column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
        column.push({ data: 'PREDICTED_VALUE', type: 'numeric', precision: 1 })
      } else if (subCategory.indexOf('Exceed') !== -1) {
        column.push({ data: 'ORDER_QUANTITY', type: 'numeric' })
        column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
        column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })
        column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
        column.push({ data: 'EXCEED_VALUE', type: 'numeric', precision: 1 })
      } else {
        column.push({ data: 'ORDER_QUANTITY', type: 'numeric' })
        column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
        column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })
        column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
      }
      break
    case 'STD Sales':
      column.push({ data: 'ORDER_QUANTITY', type: 'numeric' })
      column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })
      column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
      break
    case 'STD Order Intake':
    case 'STD CRD':
      column.push({ data: 'ORDER_QUANTITY', type: 'numeric', precision: 1 })
      column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })
      column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
      if (subCategory.indexOf('Predicted') !== -1) {
        column.push({ data: 'PREDICTED_VALUE', type: 'numeric', precision: 1 })
      } else if (subCategory.indexOf('Exceed') !== -1) {
        column.push({ data: 'EXCEED_VALUE', type: 'numeric', precision: 1 })
      }
      break
    case 'SOH[Demand]':
    case 'SOH[Order]':
      column.push({ data: 'AVG_SELLING_PRICE', type: 'numeric', precision: 1 })
      column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
      column.push({ data: 'SAFETY_STOCK', type: 'numeric' })
      column.push({ data: 'ORDER_QUANTITY', type: 'numeric' })
      column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })

      if (subCategory.indexOf('Un-Issue Delivery ') !== -1) {
        column.push({ data: 'UD_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_OG_UD_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('IG Un-Issue Delivery') !== -1) {
        column.push({ data: 'UD_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_IG_UD_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Demand in Selected Period') !== -1) {
        column.push({ data: 'DEMAND_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Order in Selected Period') !== -1) {
        column.push({ data: 'ORDER_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For IG Order in Selected Period') !== -1) {
        column.push({ data: 'ORDER_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Future Order') !== -1) {
        column.push({ data: 'ORDER_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Future Demand') !== -1) {
        column.push({ data: 'PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_REMAIN_QTY', type: 'numeric' })
      } else {
        column.push({ data: 'PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'SOH_COVER_QTY', type: 'numeric' })
      }
      break
    case 'GIT[Demand]':
    case 'GIT[Order]':
    case 'Conf.Reource[Demand]':
    case 'Conf.Reource[Order]':
      column.push({ data: 'CATEGORY' })
      column.push({ data: 'QUANTITY', type: 'numeric' })
      column.push({ data: 'NET_NET_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'MOVING_AVERAGE_P', type: 'numeric', precision: 1 })
      column.push({ data: 'AVG_SELLING_PRICE', type: 'numeric', precision: 1 })
      column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })

      if (subCategory.indexOf('For Demand in Selected Period') !== -1) {
        column.push({ data: 'DEMAND_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'LA_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Order in Selected Period') !== -1) {
        column.push({ data: 'ORDER_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'LA_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For IG Order in Selected Period') !== -1) {
        column.push({ data: 'ORDER_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'LA_COVER_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Future Demand') !== -1) {
        column.push({ data: 'PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'LA_REMAIN_QTY', type: 'numeric' })
      } else if (subCategory.indexOf('For Future Order') !== -1) {
        column.push({ data: 'ORDER_PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'LA_COVER_QTY', type: 'numeric' })
      } else {
        column.push({ data: 'PRICE', type: 'numeric', precision: 1 })
        column.push({ data: 'LA_COVER_QTY', type: 'numeric' })
      }
      break
    case 'Ordered Demand':
      column.push({ data: 'OPEN_SO_QTY', type: 'numeric' })
      column.push({ data: 'OPEN_SO_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'OPEN_SO_MVP', type: 'numeric', precision: 1 })
      column.push({ data: 'BACK_ORDER_QTY', type: 'numeric', precision: 1 })
      column.push({ data: 'BACK_ORDER_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'BACK_ORDER_MVP', type: 'numeric', precision: 1 })
      column.push({ data: 'OPEN_UD_QTY', type: 'numeric' })
      column.push({ data: 'OPEN_UD_VALUE', type: 'numeric', precision: 1 })
      column.push({ data: 'OPEN_UD_MVP', type: 'numeric', precision: 1 })
      column.push({ data: 'UNIT_COST', type: 'numeric', precision: 1 })
      column.push({ data: 'AVG_SELLING_PRICE', type: 'numeric', precision: 1 })
      break
  }

  return column
})
</script>

<style lang="scss">
#miniSIOP {
  .xAxisSelect {
    .el-input.el-input--medium.el-input--suffix {
      z-index: 600;
    }
  }
}
</style>
