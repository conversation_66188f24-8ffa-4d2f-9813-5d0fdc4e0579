<template>
  <div class="left-sidebar" id="waterfall">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.reportFilterOpts" :filter-base="['DEMAND_FCST_V']"
                                    :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
              size="small"
              v-model="pageCtl.conditions.dateRange"
              type="monthrange"
              unlink-panels
              range-separator="to"
              format="YYYY/MM"
              value-format="YYYY/MM"
              start-placeholder="Start month"
              end-placeholder="End month"
              :picker-options="pageCtl.pickerOptions">
            </el-date-picker>
          </el-col>
          <el-col :span="2">
            <el-select style="width: 100% !important;" v-model="pageCtl.conditions.scopeRange" size="small" placeholder="Scope" clearable filterable>
              <el-option
                  v-for="item in ['SECI', 'PLANT']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.scope" size="small" placeholder="Sub-Scope" multiple collapse-tags clearable filterable>
              <el-option
                  v-for="item in _subScopeOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3" v-show="pageCtl.conditions.scopeRange === 'PLANT'">
            <el-select v-model="pageCtl.conditions.fcstMppSource" size="small" placeholder="Forecast Source" clearable filterable>
              <el-option
                  v-for="item in pageCtl.reportFcstScopeOptions"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <!--'Net Net Price','Moving Average Price','Quantity','Line'-->
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" size="small">
              <el-option
                  v-for="item in ['Net Net Price','Moving Average Price','Net Net Price HKD','Quantity','Line']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-select v-model="pageCtl.unit" size="small">
              <el-option
                  v-for="item in ['M', 'K', '1']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :data-exclude="['dateRange','dateRange2','report1SelectedMonth','report2SelectedDate']"
            :after-expand="moreConditions"/>
          </el-col>
        </el-row>

        <el-row class="search-box" v-show="pageCtl.conditionsExpanded" style="margin-bottom: var(--scp-widget-margin)">
          <el-col :span="5">
            <el-input v-model="pageCtl.conditions.material" placeholder="Material" size="small" class="textarea-input" type="textarea" style="width: var(--scp-input-width) !important;" clearable></el-input>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="14">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.filter || pageCtl.loading.report || pageCtl.loading.report2">
              <scp-subscript id="DDWF"/>
              <el-row class="search-box">
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report2FCSTType" size="small" style="width: calc(100% - 10px) !important;">
                    <el-option
                      v-for="item in ['M-0', 'M-1', 'M-2', 'M-3', 'M-4', 'M-5', 'M-6']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-tooltip class="item" effect="light" content="Upper" placement="bottom-end">
                    <el-input-number size="small" v-model="pageCtl.report2Upper" controls-position="right" :min="1" :max="200" style="width: calc(100% - 10px) !important;"/>
                  </el-tooltip>
                </el-col>
                <el-col :span="3">
                  <el-tooltip class="item" effect="light" content="Lower" placement="bottom-end">
                    <el-input-number size="small" v-model="pageCtl.report2Lower" controls-position="right" :min="1" :max="100" style="width: calc(100% - 10px) !important;"/>
                  </el-tooltip>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="pageCtl.report2LegendStep" size="small" placeholder="Change to Step Chart" filterable clearable multiple collapse-tags>
                    <el-option
                      v-for="item in pageCtl.report2StepChart"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-input-number size="small" v-model="pageCtl.report2LegendStepCount" controls-position="right" :min="1" :max="36" style="width: 100% !important;"/>
                </el-col>
              </el-row>
              <chart ref="report2Ref" :height="340" :option="_report2Opt"/>
            </div>
          </el-col>
          <el-col :span="10">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.filter">
              <scp-subscript id="DDFG"/>
              <el-row class="search-box">
                <el-col :span="10">
                  <el-select v-model="pageCtl.conditions.report3Column" size="small" collapse-tags multiple filterable clearable>
                    <el-option label="SOLD_TO_SHORT_NAME" value="SOLD_TO_SHORT_NAME"/>
                    <el-option label="SOLD_TO_FULL_NAME" value="SOLD_TO_FULL_NAME"/>
                    <el-option label="SOLD_TO_PARENT_NAME" value="SOLD_TO_PARENT_NAME"/>
                    <el-option label="SOLD_TO_PARENT_CODE" value="SOLD_TO_PARENT_CODE"/>
                    <el-option label="SHIP_TO_COUNTRY" value="SHIP_TO_COUNTRY"/>
                    <el-option label="SHIP_TO_SHORT_NAME" value="SHIP_TO_SHORT_NAME"/>
                    <el-option label="SHIP_TO_FULL_NAME" value="SHIP_TO_FULL_NAME"/>
                    <el-option label="SHIP_TO_PARENT_NAME" value="SHIP_TO_PARENT_NAME"/>
                    <el-option label="SHIP_TO_PARENT_CODE" value="SHIP_TO_PARENT_CODE"/>
                    <el-option label="PROJECT_NAME" value="PROJECT_NAME"/>
                  </el-select>
                </el-col>
                <el-col :span="10">
                  <el-date-picker
                    size="small"
                    v-model="pageCtl.conditions.dateRange2"
                    type="monthrange"
                    unlink-panels
                    range-separator="to"
                    format="YYYY/MM"
                    value-format="YYYY/MM"
                    start-placeholder="Start month"
                    end-placeholder="End month"
                    :picker-options="pageCtl.pickerOptions">
                  </el-date-picker>
                </el-col>
                <el-col :span="2">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <div style="height: 340px">
                <scp-table
                    ref="report3TableRef"
                    url="/demand/waterfall/query_report3"
                    :lazy="true"
                    :params="pageCtl.conditions"
                    :columns="_report3Columns"/>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="subscript-container" v-loading="pageCtl.loading.filter || pageCtl.loading.report">
          <scp-subscript id="DDFH"/>
          <el-row class="search-box">
            <el-col :span="3">
              <el-select v-model="pageCtl.report1Discrepancy" size="small">
                <el-option
                  v-for="item in [{
                    value: 0,
                    label : 'M-0'
                  },{
                    value: 1,
                    label : 'M-1'
                  },{
                    value: 2,
                    label : 'M-2'
                  },{
                    value: 3,
                    label : 'M-3'
                  },{
                    value: 4,
                    label : 'M-4'
                  },{
                    value: 5,
                    label : 'M-5'
                  },{
                    value: 6,
                    label : 'M-6'
                  }]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <scp-table2
            ref="waterFallTableRef"
            :columns="pageCtl.reportSettings.columns"
            :column-sorting="false"
            :filters="false"
            :nestedHeaders="pageCtl.reportSettings.nestedHeaders"
            :max-height="800"
            :data="_report1Data"
            :fixed-columns-left="1"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $thousandBitSeparator:any = inject('$thousandBitSeparator')
const $shortenNumber:any = inject('$shortenNumber')
const $px2Rem:any = inject('$px2Rem')
const $calcMonthGap:any = inject('$calcMonthGap')
const $endWith:any = inject('$endWith')
const $isEmpty:any = inject('$isEmpty')
const $startWith:any = inject('$startWith')
const $converColumnName:any = inject('$converColumnName')
const $viewDetails: any = inject('$viewDetails')

const $axios: any = inject('$axios')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report2Ref = ref()
const report3TableRef = ref()
const waterFallTableRef = ref()

const pageCtl = reactive({
  scopeOpts: ['SECI_OG', 'SECI_IG', 'PLANT_OG_EXPORT', 'PLANT_IG_EXPORT', 'PLANT_OG_DOMESTIC', 'PLANT_IG_DOMESTIC', 'SEHK_OG', 'SEHK_IG'],
  loading: {
    report: false,
    filter: false,
    report2: true,
    report3: true
  },
  unit: '1',
  report1Discrepancy: 3,
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    material: '',
    type: 'Net Net Price',
    category: [],
    fcstMppSource: '',
    scopeRange: 'SECI',
    scope: ['SECI_OG'],
    dateRange: [new Date().getFullYear() - 1 + '/01', new Date().getFullYear() + '/12'],
    dateRange2: [new Date().getFullYear() + '/01', new Date().getFullYear() + '/12'],
    report1SelectedVersion: '',
    report1SelectedMonth: '',
    report1SelectedMonthName: '',
    report2SelectedDate: '',
    report2SelectedType: '',
    report3Column: ['SOLD_TO_FULL_NAME'],
    report2FCSTType: 'M-0'
  },
  conditionsExpanded: false,
  pickerOptions: {
    shortcuts: [{
      text: 'This Year',
      onClick (picker) {
        const start = new Date()
        start.setMonth(0)
        const end = new Date()
        end.setMonth(11)
        picker.$emit('pick', [start, end])
      }
    }, {
      text: 'Last year',
      onClick (picker) {
        const start = new Date()
        start.setMonth(0)
        start.setFullYear(start.getFullYear() - 1)
        const end = new Date()
        end.setMonth(11)
        end.setFullYear(end.getFullYear() - 1)
        picker.$emit('pick', [start, end])
      }
    }]
  },
  reportFilterOpts: [],
  reportFcstScopeOptions: [],
  reportSettings: {
    columns: [{
      title: 'FCST Version',
      data: 'FCST_VERSION'
    }],
    nestedHeaders: [
      [
        { label: '', colspan: 1 }
      ]
    ]
  },
  reportRawData: [{}] as any,
  report1StartMonth: '',
  reportAccuracyKey: 'Actual CRD',
  report2Upper: 20,
  report2Lower: 20,
  report2StepChart: ['Sales', 'Order Intake', 'Displayed FCST', 'Upper Limited', 'Lower Limited'],
  report2Legend: ['Sales', 'Order Intake', 'Displayed FCST', 'FCST NPI', 'FCST EOL', 'Upper Limited', 'Lower Limited',
    'CRD-GI', 'CRD-UD', 'CRD-Backlog', 'Sales NPI', 'Sales EOL', 'Order NPI', 'Order EOL'],
  report2LegendSelected: {
    Sales: true,
    'Order Intake': true,
    'Displayed FCST': true,
    'Upper Limited': false,
    'Lower Limited': false,
    'CRD-GI': false,
    'CRD-UD': true,
    'CRD-Backlog': true,
    'FCST NPI': false,
    'FCST EOL': false,
    'Sales NPI': false,
    'Sales EOL': false,
    'Order NPI': false,
    'Order EOL': false
  } as any,
  report2LegendSelectedTemp: {},
  report2Data: {
    xAxis: [],
    yAxis1: [],
    yAxis2: [],
    yAxis6: [],
    yAxis7: [],
    yAxis8: []
  } as any,
  report2LegendStep: [],
  report2LegendStepCount: 3
})

onMounted(() => {
  filterInit()
  pageCtl.report2LegendSelectedTemp = pageCtl.report2LegendSelected

  report2Ref.value.chart().on('legendselectchanged', (obj) => {
    pageCtl.report2LegendSelectedTemp = obj.selected // 每次选择图例时, 都将现在的图例保存在临时变量中
  })

  report2Ref.value.chart().on('dblclick', (obj) => {
    if (obj.componentSubType === 'bar') {
      pageCtl.conditions.report2SelectedDate = obj.name
      pageCtl.conditions.report2SelectedType = obj.seriesName

      $viewDetails({
        title: '[' + obj.name + '] - ' + obj.seriesName,
        url: '/demand/waterfall/query_report2_details',
        durl: '/demand/waterfall/download_report2_details',
        params: pageCtl.conditions
      })
    }
  })
})

watch(() => pageCtl.conditions.report2FCSTType, () => {
  searchReport2(parseReport2Settings1(pageCtl.reportRawData))
})

watch(() => pageCtl.unit, () => {
  pageCtl.reportSettings = parseReportSettings()
  searchReport3()
})

const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}

const search = () => {
  // 防止缓存失效
  pageCtl.conditions.report1SelectedVersion = ''
  pageCtl.conditions.report1SelectedMonth = ''
  pageCtl.conditions.report1SelectedMonthName = ''
  searchReport1()
  searchReport3()
}

const searchReport1 = () => {
  pageCtl.loading.report = true
  $axios({
    method: 'post',
    url: '/demand/waterfall/query_report',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2LegendSelected = pageCtl.report2LegendSelectedTemp // 每次查询之前, 都将临时变量中的图例赋值给图例变量, 这样保证点查询按钮的时候, 选择的图例不会丢失
    pageCtl.reportSettings = parseReportSettings()
    pageCtl.reportRawData = body.waterfall
    pageCtl.report1StartMonth = pageCtl.reportRawData[0].FCST_VERSION
    searchReport2(parseReport2Settings1(body.waterfall))
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report = false
  })
}

const searchReport2 = (report1Data) => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/demand/waterfall/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = parseReport2Settings2(report1Data, body)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  report3TableRef.value.search()
}

const parseReportSettings = () => {
  const start = pageCtl.conditions.dateRange[0].replace('/', '')
  const end = pageCtl.conditions.dateRange[1].replace('/', '')
  const gap = $calcMonthGap(start, end)

  const monthName = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']

  const columns:any = [
    {
      title: 'FCST Version',
      data: 'FCST_VERSION',
      render: (hotInstance, td, row, column, prop, value) => {
        if (value === pageCtl.reportAccuracyKey) {
          td.style.backgroundColor = 'rgb(203 255 208)'
        }
        td.innerHTML = value
      }
    }
  ]

  const nestedHeaders:any = [
    [
      {
        label: '',
        colspan: 1
      }
    ]
  ]

  let nestedHLabel = Math.floor(parseInt(start) / 100.0)
  let colspan = 0
  let title = parseInt(start)
  for (let i = 0; i <= gap; i++) {
    if (title % 100 > 12) {
      title += 100
      title = title - (title % 100) + 1

      nestedHeaders[0].push({
        label: nestedHLabel,
        colspan
      })

      nestedHLabel = Math.floor(title / 100.0)
      colspan = 0
    }

    columns.push({
      title: monthName[title % 100 - 1],
      data: 'MONTH' + i,
      type: 'numeric',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        const r = hotInstance.getSourceDataAtRow(row)
        if (r.FCST_VERSION.indexOf('FCST Discrepancy') === -1 && value && !$endWith(value, '%')) {
          if (pageCtl.unit === 'M') {
            value /= 1000000
          } else if (pageCtl.unit === 'K') {
            value /= 1000
          }
          td.innerHTML = $thousandBitSeparator(value, 0)
        } else {
          td.innerHTML = value
        }
      }
    })
    colspan++
    title++
  }

  nestedHeaders[0].push({
    label: nestedHLabel,
    colspan
  })

  return {
    columns,
    nestedHeaders
  }
}

const parseReport2Settings1 = (report1Data) => {
  const start = pageCtl.conditions.dateRange[0].replace('/', '')
  const end = pageCtl.conditions.dateRange[1].replace('/', '')
  const gap = $calcMonthGap(start, end)

  const xAxis = [] as any
  let yAxis1 = []
  let yAxis2 = []

  // region xAxis
  let title = parseInt(start)
  const monthName = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']

  for (let i = 0; i <= gap; i++) {
    if (title % 100 > 12) {
      title += 100
      title = title - (title % 100) + 1
    }
    xAxis.push(monthName[title % 100 - 1] + '-' + (title + '').substr(2, 2))
    title++
  }
  // endregion

  // yAxis1-5
  for (let i = 0; i < report1Data.length; i++) {
    const type = report1Data[i].FCST_VERSION
    switch (type) {
      case 'Actual Sales':
        yAxis1 = copyReport2Value(report1Data[i])
        break
      case 'Actual Order Intake':
        yAxis2 = copyReport2Value(report1Data[i])
        break
    }
  }

  return {
    xAxis,
    yAxis1,
    yAxis2
  }
}

const parseReport2Settings2 = (report1Data, report2Data) => {
  const xAxis = report1Data.xAxis
  const yAxis6:any = []
  const yAxis7:any = []
  const yAxis8:any = []
  const yAxis13:any = []
  const yAxis14:any = []
  const yAxis15:any = []
  const yAxis16:any = []
  const yAxis17:any = []
  const yAxis18:any = []
  for (let i = 0; i < xAxis.length; i++) {
    const data = report2Data[xAxis[i]]
    if (data) {
      yAxis6.push(data.GI)
      yAxis7.push(data.UD)
      yAxis8.push(data.BACKLOG)
      yAxis13.push(data.FCST_NPI)
      yAxis14.push(data.FCST_EOL)
      yAxis15.push(data.SALES_NPI)
      yAxis16.push(data.SALES_EOL)
      yAxis17.push(data.ORDER_NPI)
      yAxis18.push(data.ORDER_EOL)
    }
  }
  report1Data.yAxis6 = yAxis6
  report1Data.yAxis7 = yAxis7
  report1Data.yAxis8 = yAxis8
  report1Data.yAxis13 = yAxis13
  report1Data.yAxis14 = yAxis14
  report1Data.yAxis15 = yAxis15
  report1Data.yAxis16 = yAxis16
  report1Data.yAxis17 = yAxis17
  report1Data.yAxis18 = yAxis18
  return report1Data
}

const copyReport2Value = (obj) => {
  const result = [] as any
  const keys = Object.keys(obj)
  let end = -1
  for (let i = 0; i < keys.length; i++) {
    if (keys[i].indexOf('MONTH') !== -1) {
      const index = parseInt(keys[i].replace('MONTH', ''))
      end = Math.max(index, end)
    }
  }
  for (let i = 0; i <= end; i++) {
    if ($isEmpty(obj['MONTH' + i]) === false) {
      result.push(obj['MONTH' + i])
    } else {
      result.push(null)
    }
  }
  return result
}

const filterInit = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/demand/waterfall/query_cascader'
  }).then((body) => {
    pageCtl.reportFilterOpts = body.CASCADER
    pageCtl.reportFcstScopeOptions = body.fcstScopeOptions
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const renderReport3Title = (hotInstance, td, row, column, prop, value) => {
  td.innerHTML = value
  td.title = value
}

const renderReport3Value = (hotInstance, td, row, column, prop, value) => {
  td.style.textAlign = 'right'
  if (value && !isNaN(value)) {
    if (pageCtl.unit === 'M') {
      value /= 1000000
    } else if (pageCtl.unit === 'K') {
      value /= 1000
    }
    td.innerHTML = $thousandBitSeparator(value, 1)
  } else {
    td.innerHTML = value
  }
}

const _report1Data = computed(() => {
  const result = [] as any

  // 为所有的数字添加千分号
  for (let i = 0; i < pageCtl.reportRawData.length; i++) {
    const v = pageCtl.reportRawData[i]
    const e = {}
    for (const k in v) {
      if (v.hasOwnProperty(k)) {
        if (k === 'FCST_VERSION') {
          e[k] = v[k]
          continue
        }
        let sv = v[k]
        if (sv && (!isNaN(sv))) {
          sv = sv.toFixed(0)
          e[k] = sv
        } else {
          e[k] = sv
        }
      }
    }
    result.push(e)
  }

  // 3M Discrepancy
  // 根据用户输入, 判断究竟要取哪个FCST
  const fcst = { FCST_VERSION: 'M-' + pageCtl.report1Discrepancy + ' FCST' }
  const fcstDiscrepancy = { FCST_VERSION: 'M-' + pageCtl.report1Discrepancy + ' FCST Discrepancy' }
  const discrepancy = { FCST_VERSION: 'M-' + pageCtl.report1Discrepancy + ' 3M Discrepancy' }

  // 先找FCST
  const fcstArray = [] as any
  for (let i = 0; i < pageCtl.report1Discrepancy; i++) {
    fcstArray.push(undefined)
  }
  for (let i = 0; i < pageCtl.reportRawData.length; i++) {
    const value = pageCtl.reportRawData[i]
    if (value.FCST_VERSION && value.FCST_VERSION.indexOf('20') === 0) {
      fcstArray.push(value['MONTH' + (pageCtl.report1Discrepancy + i)])
    }
  }
  for (let i = 0; i < pageCtl.report1Discrepancy; i++) {
    fcstArray.push(undefined)
  }

  // 再找Actual
  const actualArray = [] as any
  for (let i = 0; i < pageCtl.reportRawData.length; i++) {
    const value = pageCtl.reportRawData[i]
    if (value && value.FCST_VERSION === pageCtl.reportAccuracyKey) {
      for (let j = 0; j < fcstArray.length; j++) {
        actualArray.push(value['MONTH' + j])
      }
    }
  }

  // 赋值FCST
  for (let i = 0; i < fcstArray.length; i++) {
    if (fcstArray[i]) {
      fcst['MONTH' + i] = fcstArray[i]
    }
  }

  // 求FCST Discrepancy
  for (let i = 0; i < fcstArray.length; i++) {
    const fcstTemp = fcstArray[i]
    const actualTemp = actualArray[i]

    if ($isEmpty(fcstTemp) === false && $isEmpty(actualTemp) === false && fcstTemp !== 0) {
      fcstDiscrepancy['MONTH' + i] = ((actualTemp / fcstTemp - 1) * 100).toFixed(1) + '%'
    }
  }

  // 然后3M Discrepancy
  for (let i = 2; i < fcstArray.length; i++) {
    const fcst3Sum = fcstArray[i - 2] + fcstArray[i - 1] + fcstArray[i]
    const actual3Sum = actualArray[i - 2] + actualArray[i - 1] + actualArray[i]

    if ($isEmpty(fcst3Sum) === false && $isEmpty(actual3Sum) === false && fcst3Sum !== 0) {
      discrepancy['MONTH' + i] = ((actual3Sum / fcst3Sum - 1) * 100).toFixed(1) + '%'
    }
  }

  result.push(fcst)
  result.push(fcstDiscrepancy)
  result.push(discrepancy)
  return result
})

const _subScopeOpts = computed(() => {
  if (pageCtl.conditions.scopeRange === 'SECI') {
    return ['SECI_IG', 'SECI_OG', 'SEHK_IG', 'SEHK_OG']
  } else if (pageCtl.conditions.scopeRange === 'PLANT') {
    return ['PLANT_IG_DOMESTIC', 'PLANT_IG_EXPORT', 'PLANT_OG_DOMESTIC', 'PLANT_OG_EXPORT']
  } else {
    return []
  }
})

const _report2Opt = computed(() => {
  const yAxis3:any = []
  const yAxis4:any = []
  const yAxis5:any = []

  const index = parseInt(pageCtl.conditions.report2FCSTType.replace('M-', ''))

  const dataMatrix:any = []
  for (let i = 0; i < pageCtl.reportRawData.length; i++) {
    if ($startWith(pageCtl.reportRawData[i].FCST_VERSION + '', '2')) {
      dataMatrix.push(copyReport2Value(pageCtl.reportRawData[i]))
    }
  }

  // 取FCST前半段, 也就是可以正常获取M-n的FCST
  for (let i = 0; i < dataMatrix.length; i++) {
    const j = Math.max(0, i - index)
    yAxis3.push(dataMatrix[j][i])
  }

  // 取末段
  if (dataMatrix.length > 0) {
    const lastElement = dataMatrix[dataMatrix.length - 1 - index]
    let j = 0
    for (let i = 0; i < lastElement.length; i++) {
      if (lastElement[i]) {
        if (j > index) {
          yAxis3.push(lastElement[i])
        }
        j++
      }
    }
  }

  for (let i = 0; i < yAxis3.length; i++) {
    yAxis4.push(yAxis3[i] * (1 + pageCtl.report2Upper / 100.0))
    yAxis5.push(yAxis3[i] * (1 - pageCtl.report2Lower / 100.0))
  }

  const color = {
    Sales: '#42B4E6',
    'Order Intake': '#FFD100',
    'Displayed FCST': '#364F6B',
    'FCST NPI': '#364F6B',
    'FCST EOL': '#364F6B',
    'Upper Limited': '#364F6B',
    'Lower Limited': '#364F6B',
    'CRD-GI': '#8B8E95',
    'CRD-UD': '#FF3B30',
    'CRD-Backlog': '#3DCD58',
    'Sales NPI': '#42B4E6',
    'Sales EOL': '#42B4E6',
    'Order NPI': '#FFD100',
    'Order EOL': '#FFD100'
  }

  // 转step line
  const series:any = [{
    name: 'Sales',
    data: pageCtl.report2Data.yAxis1,
    type: 'line',
    lineStyle: {
      width: 3
    },
    itemStyle: {
      color: color.Sales,
      lineStyle: {
        color: color.Sales
      }
    }
  }, {
    name: 'Order Intake',
    data: pageCtl.report2Data.yAxis2,
    type: 'line',
    lineStyle: {
      width: 3
    },
    itemStyle: {
      color: color['Order Intake'],
      lineStyle: {
        color: color['Order Intake']
      }
    }
  }, {
    name: 'Displayed FCST',
    data: yAxis3,
    type: 'line',
    lineStyle: {
      width: 3
    },
    itemStyle: {
      color: color['Displayed FCST'],
      lineStyle: {
        color: color['Displayed FCST']
      }
    }
  }, {
    name: 'Upper Limited',
    data: yAxis4,
    type: 'line',
    lineStyle: {
      type: 'dashed',
      color: color['Upper Limited']
    }
  }, {
    name: 'Lower Limited',
    data: yAxis5,
    type: 'line',
    lineStyle: {
      type: 'dashed',
      color: color['Lower Limited']
    }
  }, {
    name: 'CRD-GI',
    data: pageCtl.report2Data.yAxis6,
    type: 'bar',
    stack: 'total',
    itemStyle: {
      color: color['CRD-GI']
    }
  }, {
    name: 'CRD-UD',
    data: pageCtl.report2Data.yAxis7,
    type: 'bar',
    stack: 'total',
    itemStyle: {
      color: color['CRD-UD']
    }
  }, {
    name: 'CRD-Backlog',
    data: pageCtl.report2Data.yAxis8,
    type: 'bar',
    stack: 'total',
    itemStyle: {
      color: color['CRD-Backlog']
    }
  }, {
    name: 'FCST NPI',
    data: pageCtl.report2Data.yAxis13,
    symbol: 'triangle',
    symbolSize: 8,
    type: 'line',
    itemStyle: {
      color: color['FCST NPI'],
      lineStyle: {
        color: color['FCST NPI']
      }
    }
  }, {
    name: 'FCST EOL',
    data: pageCtl.report2Data.yAxis14,
    symbol: 'roundRect',
    symbolSize: 8,
    type: 'line',
    itemStyle: {
      color: color['FCST EOL'],
      lineStyle: {
        color: color['FCST EOL']
      }
    }
  }, {
    name: 'Sales NPI',
    data: pageCtl.report2Data.yAxis15,
    symbol: 'triangle',
    symbolSize: 8,
    type: 'line',
    itemStyle: {
      color: color['Sales NPI'],
      lineStyle: {
        color: color['Sales NPI']
      }
    }
  }, {
    name: 'Sales EOL',
    data: pageCtl.report2Data.yAxis16,
    symbol: 'roundRect',
    symbolSize: 8,
    type: 'line',
    itemStyle: {
      color: color['Sales EOL'],
      lineStyle: {
        color: color['Sales EOL']
      }
    }
  }, {
    name: 'Order NPI',
    data: pageCtl.report2Data.yAxis17,
    symbol: 'triangle',
    symbolSize: 8,
    type: 'line',
    itemStyle: {
      color: color['Order NPI'],
      lineStyle: {
        color: color['Order NPI']
      }
    }
  }, {
    name: 'Order EOL',
    data: pageCtl.report2Data.yAxis18,
    symbol: 'roundRect',
    symbolSize: 8,
    type: 'line',
    itemStyle: {
      color: color['Order EOL'],
      lineStyle: {
        color: color['Order EOL']
      }
    }
  }]

  // 将折线变成stepline
  if (pageCtl.report2LegendStep.length !== 0) {
    for (let i = 0; i < pageCtl.report2LegendStep.length; i++) {
      for (let j = 0; j < series.length; j++) {
        if (series[j].name === pageCtl.report2LegendStep[i]) {
          series[j].step = 'middle'
          const data = series[j].data
          const dataNew = [] as any

          let total = 0
          let l = 0
          for (let k = 0; k < data.length; k++) {
            if (l < pageCtl.report2LegendStepCount) {
              total += data[k]
              l++
            } else {
              for (let p = 0; p < l; p++) {
                dataNew.push(total / l)
              }
              l = 1
              total = data[k]
            }
          }

          if (l > 0) {
            for (let p = 0; p < l; p++) {
              dataNew.push(total / l)
            }
          }

          series[j].data = dataNew
        }
      }
    }
  }

  return {
    toolbox: {
      show: true,
      feature: {
        dataZoom: {
          show: true,
          title: {
            zoom: 'Zoom',
            back: 'Back'
          }
        },
        dataView: {
          lang: ['Data Viewer', 'Close', 'Refresh'],
          optionToContent: (opt) => {
            const axisData = opt.xAxis[0].data
            const series = opt.series
            let table = '<table style="width:100%;" class="gridtable">'
            table += '<tbody>'
            table += '<tr>'
            table += '<th class="fixed">Type</th>'
            const emptyIndex:any = []
            for (let i = 0; i < series.length; i++) {
              if ($startWith(series[i].name, '$')) {
                emptyIndex.push(i)
                continue
              }
              table += '<th>' + series[i].name + '</th>'
            }

            for (let i = 0; i < axisData.length; i++) {
              if (axisData[i].trim()) {
                table += '<tr>'
                table += '<td class="fixed">' + axisData[i] + '</td>'
                for (let j = 0; j < series.length; j++) {
                  if (emptyIndex.indexOf(j) === -1) {
                    table += '<td>' + (series[j].data[i] ? $shortenNumber(series[j].data[i]) : '') + '</td>'
                  }
                }
                table += '</tr>'
              } else {
                table += '<tr>'
                table += '<td colspan="' + (series.length + 1) + '" class="splitline">&nbsp;</td>'
                table += '</tr>'
              }
            }
            table += '</tbody></table>'
            return table
          }
        },
        saveAsImage: {}
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const value = params[i].value || 0
          tip.push('<div style="width:9.5rem;">')
          const marker = params[i].marker

          tip.push(marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    grid: $grid(),
    xAxis: {
      type: 'category',
      data: pageCtl.report2Data.xAxis
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }, {
        type: 'value',
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }
    ],
    legend: $legend({ data: pageCtl.report2Legend, selected: pageCtl.report2LegendSelected }),
    series
  }
})

const _report3Columns = computed(() => {
  const column = [] as any
  if (pageCtl.conditions.report3Column.length > 0) {
    for (let i = 0; i < pageCtl.conditions.report3Column.length; i++) {
      column.push({
        data: pageCtl.conditions.report3Column[i],
        title: $converColumnName(pageCtl.conditions.report3Column[i]),
        width: 130,
        render: renderReport3Title
      })
    }
  } else {
    column.push({
      data: 'PROJECT_NAME',
      title: 'Project Name',
      width: 120,
      render: renderReport3Title
    })
  }

  column.push({
    data: 'SALES_VALUE',
    title: 'Sales',
    type: 'numeric',
    render: renderReport3Value
  }, {
    data: 'CRD_VALUE',
    title: 'CRD',
    type: 'numeric',
    render: renderReport3Value
  }, {
    data: 'ORDERINTAKE_VALUE',
    title: 'Order Intake',
    type: 'numeric',
    render: renderReport3Value
  }, {
    data: 'BACKLOG_VALUE',
    title: 'Backlog',
    type: 'numeric',
    render: renderReport3Value
  }, {
    data: 'UD_VALUE',
    title: 'UD',
    type: 'numeric',
    render: renderReport3Value
  }, {
    data: 'RATIO',
    title: 'Ratio',
    render: (hotInstance, td, row, column, prop, value) => {
      if (value) {
        td.innerHTML = (value * 100).toFixed(2) + '%'
        td.style.textAlign = 'right'
        const r = hotInstance.getSourceDataAtRow(row)
        if (r.TOTAL) {
          td.title = 'Total: ' + $thousandBitSeparator(r.TOTAL, 0)
        }
      } else {
        td.innerHTML = value
      }
    }
  })

  return column
})
</script>
