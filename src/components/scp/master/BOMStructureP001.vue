<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.categroy" placeholder="Categroy" filterable clearable multiple
                       collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['BOM_STRUCTURE_P001_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="5">
            <el-cascader
              style="width: calc(100% - 15px)"
              v-model="pageCtl.conditions.values"
              :options="pageCtl.oprOpts"
              placeholder="Display Columns"
              :props="{ multiple: true }"
              :collapse-tags="true"
              :filter-method="cascaderFilter"
              clearable
              filterable
              collapse-tags-tooltip
              class="scp-cascader"/>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :expand="true"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <scp-subscript id="MDBO"/>
          <scp-table
              ref="summaryTableRef"
              :download-specify-column="false"
              :columns="pageCtl.columns"
              :show-total="true"
              :params="pageCtl.conditions"
              :after-select="afterSelect"
              :fixedColumnsLeft="2"
              :context-menu-items="pageCtl.contextItems"
              :page-sizes="[20, 50, 100, 200, 500]"
              :lazy="true"
              url="/master/bom_structure_p001/query_report1"
              download-url="/master/bom_structure_p001/download_report1"
              :editable="false"/>
        </div>
      </div>
    </div>
  </div>

  <scp-draggable-resizable v-model="pageCtl.visible.bomDetail" h="420px" w="75vw" :title="pageCtl.bomDetailsTitle">
      <template v-slot="{ height }">
        <scp-table
            ref="bomDetailsRef"
            :max-height="height - 150"
            :lazy="true"
            :context-menu-items="pageCtl.contextDetailsItems"
            :after-select="afterSelectDetails"
            :params="pageCtl.conditions"
            url="/master/bom_structure_p001/query_report1_details"
            download-url="/master/bom_structure_p001/download_report1_details"
        />
      </template>
  </scp-draggable-resizable>

  <scp-draggable-resizable v-model="pageCtl.visible.routeDetail" h="320px" w="60vw" :title="pageCtl.routeDetailsTitle">
      <template v-slot="{ height }">
        <scp-table
            ref="routeDetailsRef"
            :max-height="height - 150"
            :page-sizes="[5, 10, 50, 100, 200, 500]"
            :lazy="true"
            :editable="false"
            :params="pageCtl.conditions"
            url="/master/bom_structure_p001/query_report1_route_details"
            download-url="/master/bom_structure_p001/download_report1_route_details"
        />
      </template>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const searchRef = ref()
const bomDetailsRef = ref()
const routeDetailsRef = ref()
const summaryTableRef = ref()

const _selectedCategroy = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return ['HEADER_PRODUCT_LINE', 'HEADER_PLANT_CODE']
  }
})

const _selectedValue = computed(() => {
  if (pageCtl.conditions.values.length > 0) {
    return pageCtl.conditions.values
  } else {
    return [['COUNT_DISTINCT', 'HEADER_MATERIAL']]
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'HEADER_MATERIAL', 'ITEM_MATERIAL', 'HEADER_MATERIAL_DESCRIPTION', 'ITEM_MATERIAL_DESCRIPTION')
})

const cascaderFilter = (node, val) => {
  return !!(node && node.text && node.text.toUpperCase().indexOf(val.toUpperCase()) !== -1)
}

const initPage = () => {
  pageCtl.loading.filter = true

  $axios({
    method: 'post',
    url: '/master/bom_structure_p001/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.oprOpts = body.cascaderOpr
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  if (pageCtl.conditions.values.length === 0) {
    pageCtl.conditions.values = _selectedValue.value
  }
  if (pageCtl.conditions.categroy.length === 0) {
    pageCtl.conditions.categroy = _selectedCategroy.value
  }
  parseTableColumn()
  summaryTableRef.value.search()
}

const afterSelect = (row) => {
  if (row[_selectedCategroy.value[0]] === 'Total') {
    pageCtl.conditions.selectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _selectedCategroy.value.length; i++) {
      selectedValue.push(row[_selectedCategroy.value[i]])
    }
    pageCtl.conditions.selectedValue = selectedValue
  }
}

const afterSelectDetails = (row) => {
  pageCtl.conditions.selectedDetailsValue = [row.HEADER_MATERIAL, row.HEADER_PLANT_CODE, row.ITEM_MATERIAL, row.ITEM_PLANT_CODE]
}

const viewBomDetails = () => {
  pageCtl.bomDetailsTitle = 'View Details ' + (pageCtl.conditions.selectedValue.length ? ('[' + pageCtl.conditions.selectedValue.join(', ') + ']') : '')
  pageCtl.visible.bomDetail = true
  bomDetailsRef.value.search()
}

const viewBomRouteDetails = () => {
  const v = pageCtl.conditions.selectedDetailsValue
  pageCtl.routeDetailsTitle = 'Route Details ' + (v.length ? ('[' + v[0] + '@' + v[1] + ' -> ' + v[2] + '@' + v[3] + ']') : '')
  pageCtl.visible.routeDetail = true
  routeDetailsRef.value.search()
}

const parseTableColumn = () => {
  const columns = [] as any
  for (let i = 0; i < _selectedCategroy.value.length; i++) {
    columns.push({
      data: _selectedCategroy.value[i],
      title: _selectedCategroy.value[i]
    })
  }

  for (let i = 0; i < _selectedValue.value.length; i++) {
    const key = _selectedValue.value[i].join('_').replace(/ /g, '_')
    columns.push({
      data: key,
      title: key,
      type: 'numeric'
    })
  }
  pageCtl.columns = columns
}

const pageCtl = reactive({
  filterOpts: [],
  oprOpts: [],
  columns: [],
  loading: {
    filter: false
  },
  visible: {
    bomDetail: false,
    routeDetail: false
  },
  bomDetailsTitle: '',
  routeDetailsTitle: '',
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    values: [],
    categroy: ['HEADER_PRODUCT_LINE', 'ITEM_PLANT_CODE'],
    selectedValue: [],
    selectedDetailsValue: []
  },
  contextItems: {
    view_details: {
      name: 'View details',
      callback: viewBomDetails
    },
    view_split0: { name: '---------' }
  },
  contextDetailsItems: {
    view_details: {
      name: 'View BOM route',
      callback: viewBomRouteDetails
    },
    view_split0: { name: '---------' }
  }
})

onMounted(() => {
  initPage()
})

</script>
