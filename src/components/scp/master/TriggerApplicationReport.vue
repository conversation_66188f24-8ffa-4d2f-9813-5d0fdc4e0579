<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :options="pageCtl.filterOpts"
                :loading="pageCtl.loading.filter"/>
          </el-col>
          <el-col :span="2">
            <el-select v-model="pageCtl.conditions.specialType" style="width: 100% !important;"
                       class="search-group-left">
              <el-option label="MATERIAL" value="MATERIAL"/>
              <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
              <el-option label="LOCAL_PRODUCT_FAMILY" value="LOCAL_PRODUCT_FAMILY"/>
              <el-option label="LOCAL_PRODUCT_SUBFAMILY" value="LOCAL_PRODUCT_SUBFAMILY"/>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input :placeholder="pageCtl.conditions.specialType" v-model="pageCtl.conditions.specialContent"
                      style="width: var(--scp-input-width) !important;" type="textarea"
                      class="search-group-right"></el-input>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.RCA" placeholder="RCA" filterable collapse-tags multiple
                       clearable>
              <el-option
                  v-for="item in pageCtl.rcaOpts"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.report2Months" placeholder="Month" multiple collapse-tags
                       filterable clearable>
              <el-option
                  v-for="item in _report2MonthRange"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.type" placeholder="Type">
              <el-option
                  v-for="item in ['Line', 'Qty', 'Order Value']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['selectedDate','report2Months']"
                        :afterExpand="moreConditions"/>
          </el-col>
          <el-col :span="1" style="text-align: right;padding-top: 2px">
            <el-popconfirm
                title="当天发送的邮件会在次日的报告中显示, 如果需要立即显示请点击确定, 频繁执行此方法会导致系统变慢, 确定执行吗?"
                iconColor="orange"
                @confirm="refreshReport"
                confirmButtonType="danger"
                cancelButtonType="primary"
                confirmButtonText='确定'
                cancelButtonText='取消'
                style="float:right">
              <template #reference>
                <font-awesome-icon icon="sync" :spin="pageCtl.loading.refresh"
                                   style="cursor: pointer;"/>
              </template>
            </el-popconfirm>
          </el-col>
        </el-row>
        <el-row class="search-box" v-show="pageCtl.conditionsExpanded">
          <el-col :span="5">
            <el-tooltip placement="bottom" effect="light">
              <div>
                <el-date-picker v-model="pageCtl.conditions.report1StartDate"
                                type="date"
                                style="width: var(--scp-input-width)"
                                :clearable="false"
                                format="YYYY/MM/DD"
                                value-format="YYYY/MM/DD">
                </el-date-picker>
              </div>
              <template #content>
                Trigger Report Start Date
              </template>
            </el-tooltip>

          </el-col>
        </el-row>

        <el-row>
          <el-col :span="16">
            <div class="subscript-container subscript-container-left" v-loading="pageCtl.loading.report2">
              <scp-subscript id="MDTE"/>
              <chart ref="report2Ref" :height="280" :option="_report2Opt"/>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="subscript-container subscript-container-right" v-loading="pageCtl.loading.report3">
              <scp-subscript id="MDTI"/>
              <chart ref="report3Ref" :height="280" :option="_report3Opt"/>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="MDTM"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.categories" placeholder="Categories" filterable clearable
                             collapse-tags multiple>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.categroy3" filterable>
                    <el-option
                        v-for="item in ['LT_RANGE','RCA_CODE']"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="1">
                  <el-button @click="searchReport1">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
                <el-col :span="15" style="text-align: right;line-height:2;height:100%;color: var(--scp-text-color-secondary)">
                  >> {{ pageCtl.conditions.selectedType }} - {{ pageCtl.conditions.selectedDate }}
                </el-col>
              </el-row>
              <scp-table2
                  ref="summaryTableRef"
                  :columns="pageCtl.columns"
                  :show-total="true"
                  :fixedColumnsLeft="_categories.length"
                  :showTotalPosition="_categories.length - 1"
                  :context-menu-items-reverse="true"
                  :context-menu-items="pageCtl.report1ContextMenuItems"
                  :after-select="afterReport1Select"
                  :max-height="300"
                  :data="pageCtl.report1Data"/>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="MDTD"/>
              <scp-table
                  ref="report4Ref"
                  :params="pageCtl.conditions"
                  url="/master/trigger_application_report/query_report4"
                  download-url="/master/trigger_application_report/download_report4"
                  :columns="pageCtl.report4Columns"
                  :editable="false"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $px2Rem: any = inject('$px2Rem')
const $shortenNumber: any = inject('$shortenNumber')
const $deepClone: any = inject('$deepClone')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $join: any = inject('$join')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const summaryTableRef = ref()
const report2Ref = ref()
const report3Ref = ref()
const report4Ref = ref()

const _categories = computed(() => {
  if (pageCtl.conditions.categories.length > 0) {
    return pageCtl.conditions.categories
  } else {
    return ['PRODUCT_LINE', 'ENTITY']
  }
})
const _report2Opt = computed(() => {
  const today = $dateFormatter(new Date(), 'yyyy/MM/dd')
  return {
    color: ['#5470c6', '#3dcd58', 'rgba(255, 0, 1, 0.3)'],
    legend: $legend({ data: ['Actived', 'Active', 'Projection'] }),
    grid: $grid(),
    title: {
      text: 'Evolution of Active Triggers',
      left: 'left'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as any
        const x = params[0].name
        tip.push('<div>')
        tip.push(x)
        tip.push('<br>')
        for (const i in params) {
          if (!params.hasOwnProperty(i)) {
            continue
          }

          const seriesName = params[i].seriesName

          if (x > today && seriesName !== 'Projection') {
            continue
          }

          if (x <= today && seriesName !== 'Active' && seriesName !== 'Actived') {
            continue
          }

          const value = params[i].value || 0

          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(seriesName)
          tip.push(' : ')
          tip.push('<span style="text-align: right">')
          tip.push($shortenNumber(value))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    xAxis: {
      type: 'category',
      data: pageCtl.report2Data.xAxis
    },
    yAxis: {
      type: 'value',
      scale: true,
      splitLine: {
        show: false
      }
    },
    series: [{
      name: 'Active',
      data: pageCtl.report2Data.yAxis,
      type: 'line'
    },
    {
      name: 'Projection',
      data: pageCtl.report2Data.yAxis2,
      type: 'line',
      lineStyle: { width: 2 },
      markLine: {
        symbol: ['none', 'none'],
        lineStyle: { type: 'dotted', color: '#3dcd58', width: 2 },
        label: { show: false },
        data: [
          { xAxis: today }
        ]
      }
    },
    {
      name: 'Actived',
      data: pageCtl.report2Data.yAxis3,
      type: 'line',
      lineStyle: { width: 2 }
    }]
  }
})
const _report3Opt = computed(() => {
  const colorSettings = {
    'No impact': '#2c821d',
    '<=7': '#7c9f0d',
    '<=14': '#e2b500',
    '<=30': '#da6c1d',
    '<=45': '#ca4729',
    '>45': '#c12e34'
  }
  const colors = [] as any
  const data = $deepClone(pageCtl.report3Data)
  let impactTotal = 0
  for (let i = 0; i < data.length; i++) {
    colors.push(colorSettings[data[i].name])
    data[i].label = { color: colorSettings[data[i].name] }
    if (data[i].name !== 'No impact') {
      impactTotal += data[i].value
    }
  }
  return {
    color: colors,
    title: {
      text: '% of Trigger in Actual SO Lines',
      left: 'left'
    },
    toolbox: $toolbox({ opts: [] }),
    legend: $legend({ type: 'pie' }),
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const tip = [] as any
        let percent = ' (' + params.percent + '%)'

        tip.push('<div style="width:9rem;">')
        tip.push(params.data.name)
        tip.push(': ')
        tip.push('<span style="float: right">')

        if (params.data.name !== 'No impact') {
          if (impactTotal !== 0) {
            percent += ' (' + (params.data.value / impactTotal * 100).toFixed(2) + '%)'
          }
          tip.push($shortenNumber(params.data.value))
          tip.push(percent)
          tip.push('</span>')
        } else {
          tip.push($shortenNumber(params.data.value))
          tip.push(percent)
          tip.push('</span>')
          tip.push('<br/>')
          tip.push('Impact: ')
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(impactTotal))
          tip.push(' (' + (100 - params.percent).toFixed(2) + '%)')
          tip.push('</span>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [
      {
        name: '% of Trigger in Actual SO Lines',
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})
const _report2MonthRange = computed(() => {
  const result = [] as any
  const currentYear = new Date().getFullYear()
  const startDate = new Date()
  startDate.setDate(1)
  while (currentYear - startDate.getFullYear() < 2) {
    result.push($dateFormatter(startDate, 'yyyyMM'))
    startDate.setMonth(startDate.getMonth() - 1)
  }
  return result
})
const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

const initPage = () => {
  pageCtl.loading.filter = true
  const lastSixMonth = new Date()
  lastSixMonth.setMonth(lastSixMonth.getMonth() - 6)
  pageCtl.conditions.report1StartDate = $dateFormatter(lastSixMonth, 'yyyy/MM/dd')

  const start = new Date()
  const month = start.getMonth() + 1
  pageCtl.conditions.report2Months.push(start.getFullYear() + (month >= 10 ? '' + month : '0' + month))
  $axios({
    method: 'post',
    url: '/master/trigger_application_report/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.rcaOpts = body.RCA
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const refreshReport = () => {
  if (!pageCtl.loading.refresh) {
    pageCtl.loading.refresh = true
    $axios({
      method: 'post',
      url: '/master/trigger_application_report/refresh_trigger_report'
    }).then(() => {
      $message.success('Trigger report refreshed!')
      search()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.refresh = false
    })
  }
}
const search = () => {
  pageCtl.conditions.selectedDate = $dateFormatter(new Date(), 'yyyy/MM/dd')
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}
const searchReport1 = () => {
  pageCtl.conditions.selectedValue1 = ''
  pageCtl.conditions.selectedValue2 = ''
  pageCtl.conditions.selectedValue3 = ''

  summaryTableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/master/trigger_application_report/query_report1_columns',
    data: pageCtl.conditions
  }).then((body) => {
    if (body && body.length > 0) {
      const columns = [] as any

      for (let i = 0; i < _categories.value.length; i++) {
        const name = _categories.value[i]
        columns.push({ data: name, title: name })
      }

      for (let i = 0; i < body.length; i++) {
        columns.push({
          title: body[i] === '<Blank>' ? '&lt;Blank&gt;' : body[i],
          data: body[i],
          type: 'numeric'
        })
      }
      columns.push({
        data: 'TOTAL',
        type: 'numeric'
      })
      pageCtl.columns = columns
    }
    searchReport1Data()
  }).catch((error) => {
    console.log(error)
  })
}
const searchReport1Data = () => {
  $axios({
    method: 'post',
    url: '/master/trigger_application_report/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    summaryTableRef.value.setLoading(false)
  })
}
const searchReport1Details = () => {
  $viewDetails({
    url: '/master/trigger_application_report/query_report1_details',
    durl: '/master/trigger_application_report/download_report1_details',
    params: pageCtl.conditions,
    title: pageCtl.report1DetailsTitle
  })
}
const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/master/trigger_application_report/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}
const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/master/trigger_application_report/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}
const searchReport4 = () => {
  report4Ref.value.search()
}

const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}
const afterReport1Select = (r, c, a, column) => {
  const selected = [] as any

  for (let i = 0; i < _categories.value.length; i++) {
    let v = r[_categories.value[i]] || ''
    if (v === 'Total') {
      v = ''
    }
    selected.push(v)
  }

  pageCtl.conditions.selectedValues = selected

  const name = pageCtl.columns[column].data
  if (_categories.value.indexOf(name) === -1 && name.indexOf('TOTAL') === -1) {
    pageCtl.conditions.selectedValue3 = name
  } else {
    pageCtl.conditions.selectedValue3 = ''
  }
  pageCtl.report1DetailsTitle = $join(...pageCtl.conditions.selectedValues, pageCtl.conditions.selectedValue3) || 'Total'
}

onMounted(() => {
  initPage()
  report2Ref.value.chart().on('dblclick', (param) => {
    pageCtl.conditions.selectedDate = param.name
    pageCtl.conditions.selectedType = param.seriesName
    searchReport1()
    searchReport4()
  })

  report3Ref.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.selectedReport3Name = params.name

    $viewDetails({
      url: '/master/trigger_application_report/query_report3_details',
      durl: '/master/trigger_application_report/download_report3_details',
      params: pageCtl.conditions,
      title: 'View Details [' + params.name + ']'
    })
  })
})

const pageCtl = reactive({
  conditionsExpanded: '',
  filterOpts: [],
  rcaOpts: [],
  loading: {
    filter: false,
    report2: false,
    report3: false,
    refresh: false
  },
  columns: [{}] as any,
  conditions: {
    report1StartDate: '',
    filterList: [],
    categories: ['PRODUCT_LINE', 'ENTITY'],
    categroy3: 'LT_RANGE',
    specialType: 'MATERIAL',
    specialContent: '',
    report2Months: [] as any,
    selectedDate: $dateFormatter(new Date(), 'yyyy/MM/dd'),
    selectedType: 'Active',
    selectedValues: [],
    selectedValue3: '',
    RCA: [],
    type: 'Line',
    selectedReport3Name: ''
  } as any,
  report1Data: [],
  report2Data: {
    xAxis: [],
    yAxis: []
  } as any,
  report3Data: [],
  report1DetailsTitle: '',
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: searchReport1Details
    },
    view_split0: { name: '---------' }
  },
  report4Columns: [
    { data: 'MATERIAL' },
    { data: 'VALIDATE_FROM' },
    { data: 'VALIDATE_TO' },
    { data: 'STOCKING_POLICY' },
    { data: 'PLANNED_DELIV_TIME', type: 'numeric' },
    { data: 'GR_PROCESSING_TIME', type: 'numeric' },
    { data: 'QMAX_LEAD_TIME', type: 'numeric' },
    { data: 'QMAX_LEAD_TIME_GROUP' },
    { data: 'SHORTAGE_FLAG' },
    { data: 'RCA' },
    { data: 'METHOD' },
    { data: 'BTN_DATE' },
    { data: 'BTN_WEEK' },
    { data: 'REASON_DETAILS' },
    { data: 'UNDER_MKT_ALLOCATION_OR_NOT' },
    { data: 'BU' },
    { data: 'CLUSTER_NAME' },
    { data: 'PRODUCT_LINE' },
    { data: 'MATERIAL_OWNER_NAME' },
    { data: 'MATERIAL_OWNER_SESA' },
    { data: 'MRP_CONTROLLER' },
    { data: 'ENTITY' },
    { data: 'LOCAL_BU' },
    { data: 'LOCAL_PRODUCT_LINE' },
    { data: 'LOCAL_PRODUCT_FAMILY' },
    { data: 'LOCAL_PRODUCT_SUBFAMILY' },
    { data: 'MKT' },
    { data: 'AMF_ONE_MM', type: 'numeric' },
    { data: 'AMU_ONE_MM', type: 'numeric' },
    { data: 'MAX_AMU_AMF', type: 'numeric' },
    { data: 'UU_STOCK', type: 'numeric' },
    { data: 'STOCK_IN_QI', type: 'numeric' },
    { data: 'STOCK_COV_AMF', type: 'numeric', precision: 2 },
    { data: 'STOCK_COV_AMU', type: 'numeric', precision: 2 },
    { data: 'STOCK_COV_MAX_AMU_AMF', type: 'numeric', precision: 2 },
    { data: 'OPEN_LA', type: 'numeric' },
    { data: 'OPEN_PO', type: 'numeric' },
    { data: 'OPEN_SO', type: 'numeric' },
    { data: 'Cur.Mon M-1 Fulfill', type: 'numeric' },
    { data: 'Last.Mon M-1 Fulfill', type: 'numeric' },
    { data: 'Cur.Mon M-3 Fulfill', type: 'numeric' },
    { data: 'Last.Mon M-3 Fulfill', type: 'numeric' },
    { data: 'Cur.Mon M-1 FCST', type: 'numeric' },
    { data: 'Last.Mon M-1 FCST', type: 'numeric' },
    { data: 'Cur.Mon M-3 FCST', type: 'numeric' },
    { data: 'Last.Mon M-3 FCST', type: 'numeric' },
    { data: 'Cur.Mon CRD', type: 'numeric' },
    { data: 'Last.Mon CRD', type: 'numeric' },
    { data: 'REQUEST_BY' },
    { data: 'REQUEST_BY_NAME' }
  ]
})
</script>
