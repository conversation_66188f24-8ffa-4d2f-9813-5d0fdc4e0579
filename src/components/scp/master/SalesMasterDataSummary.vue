<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-select v-model="pageCtl.conditions.categroy" placeholder="Categroy" filterable clearable multiple
                       collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['SALES_MASTER_DATE_SUMMARY_V']"
                        :after-apply="search"/>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions" :expand="true"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <scp-subscript id="MDBO"/>
          <scp-table
              ref="summaryTableRef"
              :download-specify-column="false"
              :columns="pageCtl.columns"
              :show-total="true"
              :params="pageCtl.conditions"
              :after-select="afterSelect"
              :fixedColumnsLeft="2"
              :context-menu-items="pageCtl.contextItems"
              :page-sizes="[20, 50, 100, 200, 500]"
              :lazy="true"
              url="/master/sales_master_data_summary/query_report1"
              download-url="/master/sales_master_data_summary/download_report1"
              :editable="false"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')

const searchRef = ref()
const summaryTableRef = ref()

const _selectedCategroy = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return ['PLANT_CODE']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'PLANT_CODE', 'YY_PRD_LINE')
})
const initPage = () => {
  pageCtl.loading.filter = true

  $axios({
    method: 'post',
    url: '/master/sales_master_data_summary/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  parseTableColumn()
  summaryTableRef.value.search()
}
const viewMasterDataDetails = () => {
  $viewDetails({
    url: '/master/sales_master_data_summary/query_report1_details',
    durl: '/master/sales_master_data_summary/download_report1_details',
    params: pageCtl.conditions,
    title: 'View Summary Details ' + (pageCtl.conditions.selectedValue.length ? ('[' + pageCtl.conditions.selectedValue.join(', ') + ']') : '')
  })
}

const pageCtl = reactive({
  filterOpts: [],
  typeOpts: ['Qty', 'Cost Value', 'Selling Value'],
  calcTypeOpts: ['Sum', 'Avg', 'Max', 'Min', 'Std'],
  columns: [],
  loading: {
    filter: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    material: '',
    type: 'Qty',
    calcType: 'Sum',
    categroy: ['RULES1', 'RULES2_1', 'RULES2_2', 'RULES3', 'PRODUCT_LINE', 'SALES_ORGANIZATION'],
    selectedValue: []
  },
  contextItems: {
    view_details: {
      name: 'View details',
      callback: viewMasterDataDetails
    },
    view_split0: { name: '---------' }
  }
})
const afterSelect = (row) => {
  if (row[_selectedCategroy.value[0]] === 'Total') {
    pageCtl.conditions.selectedValue = []
  } else {
    const selectedValue = [] as any
    for (let i = 0; i < _selectedCategroy.value.length; i++) {
      selectedValue.push(row[_selectedCategroy.value[i]])
    }
    pageCtl.conditions.selectedValue = selectedValue
  }
}
const parseTableColumn = () => {
  const columns = [] as any
  for (let i = 0; i < _selectedCategroy.value.length; i++) {
    columns.push({
      data: _selectedCategroy.value[i],
      title: _selectedCategroy.value[i]
    })
  }
  columns.push(
    { data: 'MATERIAL' },
    { data: 'MATERIAL_DESCRIPTION' },
    { data: 'PRODUCT_HIERARCHY' },
    { data: 'YY_GDP' },
    { data: 'GDP' }
  )
  pageCtl.columns = columns
}
onMounted(() => {
  initPage()
  search()
})

</script>
