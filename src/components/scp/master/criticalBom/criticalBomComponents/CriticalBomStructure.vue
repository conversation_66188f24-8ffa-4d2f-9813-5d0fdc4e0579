<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.displayTab" @tab-click="handleTabClick">
          <el-tab-pane label="Bom Structure" name="BOM_STRUCTURE">
            <el-row class="search-box">
              <el-col :span="4">
                <el-select v-model="pageCtl.conditions.fields" size="small" placeholder="Filter Field" filterable
                           multiple collapse-tags>
                  <el-option
                      v-for="item in ['PLANT_TYPE','CLUSTER_NAME','ENTITY','PRODUCT_LINE','PRODUCTION_LINE','LOCAL_PRODUCT_LINE','LOCAL_PRODUCT_FAMILY','LOCAL_PRODUCT_SUBFAMILY',
                                      'HYPER_CARE_LEVEL','MRP_CONTROLLER','MRP_CONTROLLER_DESCRIPTION','STOCKING_POLICY','VENDOR_CODE','VENDOR_NAME','VENDOR_SHORT_NAME','VENDOR_FULL_NAME',
                                      'VENDOR_PARENT_NAME','VENDOR_PARENT_CODE','VENDOR_MATERIAL','SOURCE_CATEGORY','PLANNED_DELIV_TIME','LT_RANGE','UNIT_COST','SAFETY_STOCK',
                                      'AMU_ONEMM','AVG_MONTHLY_ORDER_INTAKE','AMF_ONEMM','COV','SS2','SS3','CALCULATED_ABC','CALCULATED_FMR','NEW_PRODUCTS','PROCUREMENT_TYPE',
                                      'FOLLOW_UP_MATERIAL','PRODN_SUPERVISOR','IN_HOUSE_PRODN_LT','STARS_CODE','LAST_SO_SALES_DATE','NONE_SALES_DURATION','NONE_SALES_PERIOD',
                                      'GRA','COMMODITY_CODE','STARS_DESCRIPTION','PLANNING_ALIVE_STATUS','XBOARD','RES_COV_STK','RES_COV_STK_RANGE','RES_COV_STK_LA','RES_COV_STK_LA_RANGE',
                                      'RES_COV_STK_OPO','RES_COV_STK_OPO_RANGE','FIRST_CONSUMPTION_RANGE','FIRST_PO_CREATE_RANGE','FIRST_SO_CREATE_RANGE','LAST_SO_SALES_RANGE','PRD_BOM_COMPONENT',
                                      'FIRST_CONS_DATE','FIRST_SO_DATE','FIRST_PO_DATE','COV_RANGE','RISK_LEVEL','ADU_END_ORDER','STDDEV_END_ORDER','COV_END_ORDER','COV_RANGE_END_ORDER',
                                      'NUM_OF_WHERE_USE','NUM_OF_WHERE_USE_RANGE','PRODUCT_GROUP_A','PRODUCT_GROUP_B','PRODUCT_GROUP_C','PRODUCT_GROUP_D','PRODUCT_GROUP_E','TOTAL_STOCK_QTY','UU_STOCK_QTY',
                                      'QI_STOCK_QTY','BLOCK_STOCK_QTY','PAST_DUE_SO_QTY','PAST_DUE_SO_QTY_NON_BLOCK','TOTAL_OPEN_SO_QTY','WITHIN_LT_SO_QTY','WITHOUT_LT_SO_QTY','TOTAL_OPEN_SO_QTY_NON_BLOCK',
                                      'WITHIN_LT_SO_NON_BLOCK','WITHOUT_LT_SO_NON_BLOCK','OPEN_SO_QTY','OPEN_PO_QTY','PASTDUE_PO_QTY','OPEN_PO_LA_QTY','OPEN_PO_AB_QTY','ORDER_RESERVATION_QTY',
                                      'PASTDUE_ORDER_RESERVATION','ORDER_RESERVATION_QTY_WITHIN_7_DAYS','ORDER_RESERVATION_QTY_WITHOUT_7_DAYS']"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select size="small" v-model="pageCtl.conditions.plant" placeholder="Plant" filterable
                           multiple collapse-tags>
                  <el-option
                      v-for="item in pageCtl.plantOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pageCtl.conditions.selectType"
                           class="search-group-left">
                  <el-option label="WHERE_USED_BOM" value="UP"/>
                  <el-option label="EXPLODE BOM" value="DOWN"/>
                </el-select>
              </el-col>
              <el-col :span="1">
                <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                            :data-exclude="['dateRange']"/>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <div class="subscript-container">
                  <scp-subscript id="PONB"/>
                  <scp-table
                      ref="report1TableRef"
                      :download-specify-column="false"
                      :columns="pageCtl.columns"
                      :nested-headers="pageCtl.nestedHeaders"
                      :merge-cells="pageCtl.mergeCells"
                      :fixed-columns-left="5"
                      :lazy="true"
                      :params="pageCtl.conditions"
                      :after-select="afterReport1Select"
                      :after-search="afterReport1Search"
                      :context-menu-items="pageCtl.report1MenuItems"
                      :page-sizes="[20, 100, 200, 500]"
                      url="/master/bom/query_report1"
                      download-url="/master/bom/download_report1"
                      :column-sorting="false"
                      :filters="false"
                      :editable="false"
                  />
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="Critical Material List" name="CRITICAL_MATERIAL">
            <div class="subscript-container">
              <div class="subscript-title">Material Input</div>
              <scp-subscript id="SDCM"/>
              <scp-table
                  url="/master/bom/query_report2"
                  download-url="/master/bom/download_report2"
                  save-url="/master/bom/save_report2"
                  ref="report2TableRef"
                  :contextMenuItemsReverse="true"
                  :contextMenuItems="pageCtl.report2MenuItems"
                  :lazy="true"
                  :columns="_report2TableColumn"
                  :hiddenColumns="{columns: [0]}"/>
            </div>
          </el-tab-pane>
        </el-tabs>
        <!-- upload report2 -->
        <scp-upload
            ref="upload2Ref"
            title="Upload Critical Material"
            :show-btn="false"
            w="600px"
            h="220px"
            upload-url='/master/bom/upload_report2'
            download-template-url='/master/bom/download_report2_template'
            :on-upload-end="searchReport2">
          <template #tip>
            <p>This file will <b>replace</b> the data you uploaded before</p>
          </template>
        </scp-upload>

        <!-- view sankey chart -->
        <scp-draggable-resizable w="95vw" h="85vh" v-model="pageCtl.sankeyChartVisible" title="View Sankey Chart">
          <template v-slot="{ height }">
            <div
                style="padding: 8px; background-color: #f0f2f5; border-bottom: 1px solid #d9d9d9; font-size: 12px; color: #666;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <span><strong>操作提示：</strong> 鼠标滚轮缩放 | 图表边缘拖拽移动 | Ctrl+拖拽强制移动 | 每次打开自动重置视图</span>
                  <span v-if="pageCtl.sankeyChartData.data?.length > 50" style="color: #fa8c16; margin-left: 10px;">
                    <strong>数据量较大，建议使用缩放功能查看详细内容</strong>
                  </span>
                </div>
                <div>
                  <span style="margin-right: 10px; color: #1890ff;">
                    缩放: {{ Math.round(pageCtl.chartZoom * 100) }}%
                  </span>
                  <el-button size="small" @click="resetZoom">重置视图</el-button>
                  <el-button size="small" @click="zoomIn">放大</el-button>
                  <el-button size="small" @click="zoomOut">缩小</el-button>
                  <el-button size="small" @click="fitToScreen">适应屏幕</el-button>
                </div>
              </div>
            </div>
            <div
                ref="chartContainer"
                :style="{
                height: height - 150 + 'px',
                width: '100%',
                overflow: 'auto',
                position: 'relative',
                backgroundColor: '#fafafa'
              }"
                @wheel="handleWheel"
                @mousedown="handleMouseDown"
                @mousemove="handleMouseMove"
                @mouseup="handleMouseUp"
                @mouseleave="handleMouseUp">
              <div
                  ref="chartWrapper"
                  :style="{
                  transform: `scale(${pageCtl.chartZoom}) translate(${pageCtl.chartTranslateX}px, ${pageCtl.chartTranslateY}px)`,
                  transformOrigin: '0 0',
                  transition: pageCtl.isTransitioning ? 'transform 0.3s ease' : 'none',
                  width: '100%',
                  height: '100%',
                  minWidth: '1200px',
                  minHeight: '600px'
                }">
                <chart ref="sankeyChartRef" :style="{ height: '100%', width: '100%' }" :option="_sankeyOpt"/>
              </div>
            </div>
          </template>
        </scp-draggable-resizable>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, nextTick, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const upload2Ref = ref()
const upload3Ref = ref()
const $dateFormatter: any = inject('$dateFormatter')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $deepClone: any = inject('$deepClone')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $viewDetails: any = inject('$viewDetails')
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $axios: any = inject('$axios')
const $join: any = inject('$join')
const $message: any = inject('$message')

const searchRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()
const sankeyChartRef = ref()
const chartContainer = ref()
const chartWrapper = ref()

const showUpload2Win = () => {
  upload2Ref.value.showUploadWin()
}

const showUpload3Win = () => {
  upload3Ref.value.showUploadWin()
}

const viewSankeyChart = () => {
  pageCtl.loading.sankeyChart = true

  // 先重置所有状态，确保干净的初始状态
  resetAllChartState()

  // 然后显示弹窗
  pageCtl.sankeyChartVisible = true

  $axios({
    method: 'post',
    url: '/master/bom/query_sankey_chart',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.sankeyChartData = body

    // 始终从标准状态开始，不进行自动缩放调整
    pageCtl.chartZoom = 1
    pageCtl.chartTranslateX = 0
    pageCtl.chartTranslateY = 0

    const dataCount = body.data?.length || 0
    console.log(`Sankey chart loaded: ${dataCount} nodes, ${body.links?.length || 0} links, reset to default view`)

    // 延迟一下确保图表渲染完成
    setTimeout(() => {
      if (sankeyChartRef.value) {
        sankeyChartRef.value.resize()
      }
    }, 200)
  }).catch((error) => {
    console.log(error)
    $message.error('Failed to load sankey chart data')
  }).finally(() => {
    pageCtl.loading.sankeyChart = false
  })
}

// 缩放和拖拽相关方法
const resetZoom = () => {
  pageCtl.isTransitioning = true
  pageCtl.chartZoom = 1
  pageCtl.chartTranslateX = 0
  pageCtl.chartTranslateY = 0
  setTimeout(() => {
    pageCtl.isTransitioning = false
  }, 300)
}

// 完全重置所有图表状态
const resetAllChartState = () => {
  pageCtl.chartZoom = 1
  pageCtl.chartTranslateX = 0
  pageCtl.chartTranslateY = 0
  pageCtl.isDragging = false
  pageCtl.dragStartX = 0
  pageCtl.dragStartY = 0
  pageCtl.isTransitioning = false
  // 清空之前的数据
  pageCtl.sankeyChartData = {
    data: [],
    links: []
  }
  console.log('Chart state reset to default')
}

const zoomIn = () => {
  pageCtl.isTransitioning = true
  pageCtl.chartZoom = Math.min(pageCtl.chartZoom * 1.2, 3)
  setTimeout(() => {
    pageCtl.isTransitioning = false
  }, 300)
}

const zoomOut = () => {
  pageCtl.isTransitioning = true
  pageCtl.chartZoom = Math.max(pageCtl.chartZoom / 1.2, 0.5)
  setTimeout(() => {
    pageCtl.isTransitioning = false
  }, 300)
}

const handleWheel = (event) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? 0.9 : 1.1
  pageCtl.chartZoom = Math.max(0.5, Math.min(3, pageCtl.chartZoom * delta))
}

const handleMouseDown = (event) => {
  // 使用更简单的方法：检查是否按住了Ctrl键来区分拖拽意图
  // 或者检查是否在图表的边缘区域
  const target = event.target
  const isCanvas = target.tagName === 'CANVAS'

  // 如果是Canvas元素，需要更谨慎
  if (isCanvas) {
    // 检查是否在Canvas的边缘区域（用于拖拽）
    const rect = target.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    const margin = 50 // 边缘区域大小

    const isInMargin = x < margin || y < margin ||
        x > rect.width - margin ||
        y > rect.height - margin

    // 只有在边缘区域或按住Ctrl键时才允许拖拽容器
    if (isInMargin || event.ctrlKey) {
      pageCtl.isDragging = true
      pageCtl.dragStartX = event.clientX - pageCtl.chartTranslateX
      pageCtl.dragStartY = event.clientY - pageCtl.chartTranslateY
      event.preventDefault()
    }
  } else {
    // 如果不是Canvas，允许拖拽
    pageCtl.isDragging = true
    pageCtl.dragStartX = event.clientX - pageCtl.chartTranslateX
    pageCtl.dragStartY = event.clientY - pageCtl.chartTranslateY
    event.preventDefault()
  }
}

const handleMouseMove = (event) => {
  if (pageCtl.isDragging) {
    pageCtl.chartTranslateX = event.clientX - pageCtl.dragStartX
    pageCtl.chartTranslateY = event.clientY - pageCtl.dragStartY
  }
}

const handleMouseUp = () => {
  pageCtl.isDragging = false
}

const fitToScreen = () => {
  pageCtl.isTransitioning = true
  const dataCount = pageCtl.sankeyChartData.data?.length || 0

  // 根据数据量计算合适的缩放比例
  if (dataCount > 100) {
    pageCtl.chartZoom = 0.3
  } else if (dataCount > 50) {
    pageCtl.chartZoom = 0.5
  } else if (dataCount > 20) {
    pageCtl.chartZoom = 0.7
  } else {
    pageCtl.chartZoom = 1
  }

  // 重置位置到中心
  pageCtl.chartTranslateX = 0
  pageCtl.chartTranslateY = 0

  setTimeout(() => {
    pageCtl.isTransitioning = false
  }, 300)
}

const pageCtl = reactive({
  displayTab: 'BOM_STRUCTURE',
  rcaTips: {},
  columns: [],
  nestedHeaders: [],
  mergeCells: [],
  savedColumnWidths: [], // 保存列宽度的数组
  userAuth: '',
  plantOpts: [],
  conditions: {
    $scpFilter: {
      cascader: [['STATUS', 'OPEN']]
    },
    resultType: 'Line',
    fields: [],
    columns: [] as any,
    selectType: 'DOWN',
    plant: ['P001', 'X001'],
    selectedRow: {} as any,
    selectedValue: '',
    selectedColumn: '',
    selectedTop: ''
  },
  filterOpts: [],
  report1SelectedValues: [] as any,
  report1SelectedType: '',
  report1MenuItems: {
    view_chart: {
      name: 'View Chart',
      callback: viewSankeyChart
    },
    view_split0: { name: '---------' }
  },
  report1Update: {},
  report2MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload2Win
    },
    view_split: { name: '---------' }
  },
  sankeyChartVisible: false,
  sankeyChartData: {
    data: [],
    links: []
  },
  loading: {
    sankeyChart: false
  },
  // 缩放和拖拽相关状态
  chartZoom: 1,
  chartTranslateX: 0,
  chartTranslateY: 0,
  isDragging: false,
  dragStartX: 0,
  dragStartY: 0,
  isTransitioning: false
})

onMounted(() => {
  initPage()
  search()
})

// 监听桑基图弹窗的显示和关闭事件
watch(() => pageCtl.sankeyChartVisible, (newVal, oldVal) => {
  if (newVal === true && oldVal === false) {
    // 弹窗打开时确保重置状态
    setTimeout(() => {
      pageCtl.chartZoom = 1
      pageCtl.chartTranslateX = 0
      pageCtl.chartTranslateY = 0
      pageCtl.isDragging = false
      pageCtl.isTransitioning = false
    }, 50)
  } else if (oldVal === true && newVal === false) {
    // 弹窗关闭时重置所有状态
    setTimeout(() => {
      resetAllChartState()
    }, 100)
  }
})

// 监听表格数据变化，包括分页变化
watch(() => {
  // 监听表格实例的数据变化
  if (report1TableRef.value) {
    const tableInstance = report1TableRef.value
    return {
      currentPage: tableInstance.getCurrentPage ? tableInstance.getCurrentPage() : 1,
      pageSize: tableInstance.getPageSize ? tableInstance.getPageSize() : 10,
      dataLength: tableInstance.getData ? tableInstance.getData().length : 0
    }
  }
  return null
}, (newVal, oldVal) => {
  if (newVal && oldVal && report1TableRef.value) {
    // 检查是否是分页变化
    const isPageChange = newVal.currentPage !== oldVal.currentPage ||
        newVal.pageSize !== oldVal.pageSize

    if (isPageChange) {
      console.log('Page change detected, triggering table structure update')

      // 延迟执行以确保数据已更新
      setTimeout(() => {
        const tableData = report1TableRef.value.getData()
        if (tableData && tableData.length > 0) {
          // 重新解析表格结构并生成合并单元格配置
          parseTableStructure(tableData)
          generateMergeCells(tableData)
        }
      }, 100)
    }
  }
}, {
  deep: true,
  flush: 'post' // 确保在DOM更新后执行
})

const handleTabClick = (tab) => {
  if (tab.paneName === 'BOM_STRUCTURE') {
  } else if (tab.paneName === 'CRITICAL_MATERIAL') {
    searchReport2()
  }
}
const search = () => {
  report1TableRef.value.search()
}

const searchReport2 = () => {
  report2TableRef.value.search()
}

const _report2TableColumn = computed(() => {
  return [{
    data: 'ROW_ID'
  }, {
    data: 'MATERIAL'
  }, {
    data: 'PLANT_CODE'
  }]
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/master/bom/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.plantOpts = body.plantList
  }).catch((error) => {
    console.log(error)
  })
}

const afterReport1Select = (r, rc, c) => {
  const values = [r.TYPE, c]
  for (let i = 0; i < _fields.value.length; i++) {
    values.push(r[_fields.value[i]])
  }
  pageCtl.report1SelectedValues = values

  // 保存选中行的数据用于桑基图查询
  pageCtl.conditions.selectedRow = r
  pageCtl.conditions.selectedValue = rc
  pageCtl.conditions.selectedColumn = c
  pageCtl.conditions.selectedTop = r.TOP || ''
}

const _fields = computed(() => {
  let fields = pageCtl.conditions.fields
  if (fields.length === 0) {
    fields = ['ENTITY']
  }
  return fields
})

// 在数据搜索完成后，根据返回的数据动态设置多层表头
const afterReport1Search = () => {
  // 获取表格数据
  const tableData = report1TableRef.value.getData()

  if (tableData && tableData.length > 0) {
    // 解析数据并生成多层表头
    parseTableStructure(tableData)
    // 生成合并单元格配置
    generateMergeCells(tableData)
  } else {
    pageCtl.columns = []
    pageCtl.nestedHeaders = []
    pageCtl.mergeCells = []
  }
}

// 解析表格结构，生成基于字段名前两个字符的动态多层表头
const parseTableStructure = (tableData) => {
  if (!tableData || tableData.length === 0) return

  // 收集所有行中的所有字段名，确保不遗漏任何列
  const allFieldNames = new Set()
  const fieldSampleValues = {} // 存储每个字段的第一个非空值，用于类型推断

  // 遍历所有数据行收集字段信息
  tableData.forEach(row => {
    if (row && typeof row === 'object') {
      Object.keys(row).forEach(fieldName => {
        allFieldNames.add(fieldName)
        // 如果还没有找到该字段的非空值，尝试从当前行获取
        if (fieldSampleValues[fieldName] === undefined &&
            row[fieldName] !== undefined &&
            row[fieldName] !== null &&
            row[fieldName] !== '') {
          fieldSampleValues[fieldName] = row[fieldName]
        }
      })
    }
  })

  // 转换为数组并保持原始顺序（基于第一行的字段顺序）
  const firstRow = tableData[0] || {}
  const firstRowFields = Object.keys(firstRow)
  const fieldNames = []

  // 先添加第一行中存在的字段（保持原始顺序）
  firstRowFields.forEach(fieldName => {
    if (allFieldNames.has(fieldName)) {
      fieldNames.push(fieldName)
      allFieldNames.delete(fieldName) // 从集合中移除，避免重复
    }
  })

  // 再添加其他行中存在但第一行中不存在的字段
  allFieldNames.forEach(fieldName => {
    fieldNames.push(fieldName)
  })

  // 分离固定列和动态列
  const fixedColumnsCount = 5 // 前三列固定
  const fixedColumns = fieldNames.slice(0, fixedColumnsCount)
  const dynamicColumns = fieldNames.slice(fixedColumnsCount)

  // 动态收集动态列的前缀并分组（跳过前三列）
  const prefixGroups = {}
  const prefixOrder = [] // 保持前缀出现的顺序

  // 只对动态列按照前两个字符分组
  dynamicColumns.forEach(fieldName => {
    const prefix = fieldName.substring(0, 2).toUpperCase()

    if (!prefixGroups[prefix]) {
      prefixGroups[prefix] = []
      prefixOrder.push(prefix) // 记录前缀第一次出现的顺序
    }
    prefixGroups[prefix].push(fieldName)
  })

  // 构建列定义，保持字段在数据中的原始顺序
  const columns = [] as any

  // 按照字段在数据中的顺序添加列
  fieldNames.forEach(fieldName => {
    // 使用收集到的样本值来创建列定义
    const sampleValue = fieldSampleValues[fieldName]
    columns.push(createColumn(fieldName, sampleValue, tableData))
  })

  // 构建嵌套表头
  const nestedHeaders = [[]]

  // 为前三列添加固定表头（不分组）
  fixedColumns.forEach(() => {
    nestedHeaders[0].push({
      label: '', // 固定列不显示分组标签
      colspan: 1
    })
  })

  // 为动态列按照前缀在数据中出现的顺序创建表头分组
  prefixOrder.forEach(prefix => {
    if (prefixGroups[prefix] && prefixGroups[prefix].length > 0) {
      nestedHeaders[0].push({
        label: prefix,
        colspan: prefixGroups[prefix].length
      })
    }
  })

  // 只有在列定义发生变化时才更新
  if (JSON.stringify(pageCtl.columns) !== JSON.stringify(columns)) {
    pageCtl.columns = columns
    pageCtl.nestedHeaders = nestedHeaders

    console.log('Dynamic multi-level headers generated:', pageCtl.nestedHeaders)
    console.log('Prefix groups:', prefixGroups)
    console.log('Prefix order:', prefixOrder)
    console.log('Columns generated:', pageCtl.columns)
    console.log('Field sample values:', fieldSampleValues)

    // 重新渲染表格以应用新的列定义和合并单元格
    setTimeout(() => {
      report1TableRef.value.redrawTable()
      // 在重新渲染后恢复列宽度
      setTimeout(() => {
        restoreColumnWidths()
      }, 100)
    }, 100)
  }
}

// 创建列定义的辅助函数
const createColumn = (fieldName, fieldValue, tableData = null) => {
  // 提取字段名，去掉前缀（如L0_、L1_等）
  const displayName = fieldName.includes('_')
    ? fieldName.substring(fieldName.indexOf('_') + 1)
    : fieldName

  const column = {
    data: fieldName,
    title: displayName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  } as any

  // 根据字段名和数据类型推断列类型
  const fieldNameUpper = fieldName.toUpperCase()

  // 如果没有样本值，尝试从所有数据中找到第一个非空值进行类型推断
  let sampleValue = fieldValue
  if ((sampleValue === undefined || sampleValue === null || sampleValue === '') && tableData) {
    for (let i = 0; i < tableData.length; i++) {
      const rowValue = tableData[i][fieldName]
      if (rowValue !== undefined && rowValue !== null && rowValue !== '') {
        sampleValue = rowValue
        break
      }
    }
  }

  // if (['QUANTITY', 'SS', 'AMU', 'SOH', 'LEVEL', 'ITEM_NUMBER', 'QTY'].includes(fieldNameUpper) ||
  //     fieldNameUpper.includes('_QTY') ||
  //     fieldNameUpper.includes('_QUANTITY') ||
  //     fieldNameUpper.includes('_AMOUNT') ||
  //     fieldNameUpper.includes('_VALUE')) {
  //   column.type = 'numeric'
  //   column.render = (hotInstance, td, row, column, prop, value) => {
  //     td.style.textAlign = 'right'
  //     td.innerHTML = value !== undefined && value !== null && value !== ''
  //       ? value
  //       : '0'
  //   }
  // } else if (['VALID_FROM', 'VALID_TO', 'DATE', 'CREATE_DATE', 'UPDATE_DATE'].includes(fieldNameUpper) ||
  //            fieldNameUpper.includes('_DATE')) {
  //   column.type = 'date'
  //   column.dateFormat = 'YYYY/MM/DD'
  // } else if (typeof sampleValue === 'number') {
  //   column.type = 'numeric'
  //   column.render = (hotInstance, td, row, column, prop, value) => {
  //     td.style.textAlign = 'right'
  //     td.innerHTML = value !== undefined && value !== null && value !== ''
  //       ? value
  //       : '0'
  //   }
  // } else {
  //   column.type = 'text'
  //   // 为文本列添加渲染函数，确保空值显示为空字符串而不是消失
  //   column.render = (hotInstance, td, row, column, prop, value) => {
  //     td.innerHTML = value !== undefined && value !== null
  //       ? value
  //       : ''
  //   }
  // }

  return column
}

// 保存当前列宽度
const saveColumnWidths = () => {
  const hotInstance = report1TableRef.value?.getHotInstance()
  if (hotInstance && pageCtl.columns.length > 0) {
    pageCtl.savedColumnWidths = []
    for (let i = 0; i < pageCtl.columns.length; i++) {
      const width = hotInstance.getColWidth(i)
      pageCtl.savedColumnWidths[i] = width || 'auto'
    }
    console.log('Saved column widths:', pageCtl.savedColumnWidths)
  }
}

// 恢复列宽度
const restoreColumnWidths = () => {
  const hotInstance = report1TableRef.value?.getHotInstance()
  if (hotInstance && pageCtl.savedColumnWidths.length > 0) {
    // 使用保存的列宽度更新表格设置
    hotInstance.updateSettings({
      colWidths: [...pageCtl.savedColumnWidths]
    })
    console.log('Restored column widths:', pageCtl.savedColumnWidths)

    // 强制重新渲染以确保宽度正确应用
    setTimeout(() => {
      hotInstance.render()
    }, 50)
  }
}

// 生成合并单元格配置
const generateMergeCells = (tableData) => {
  if (!tableData || tableData.length === 0) {
    pageCtl.mergeCells = []
    return
  }

  // 在生成合并配置前保存当前列宽度
  saveColumnWidths()

  const mergeCells = []
  const fixedColumnsCount = 5 // 前三列需要合并

  // 获取字段名列表
  const fieldNames = Object.keys(tableData[0] || {})
  const fixedColumnFields = fieldNames.slice(0, fixedColumnsCount)

  // 为每一列生成合并配置
  fixedColumnFields.forEach((fieldName, colIndex) => {
    let currentValue = null
    let startRow = 0
    let rowSpan = 1

    for (let rowIndex = 0; rowIndex < tableData.length; rowIndex++) {
      const cellValue = tableData[rowIndex][fieldName]

      if (rowIndex === 0) {
        // 第一行，初始化
        currentValue = cellValue
        startRow = 0
        rowSpan = 1
      } else if (cellValue === currentValue) {
        // 相同值，增加行跨度
        rowSpan++
      } else {
        // 不同值，保存之前的合并配置（如果行跨度大于1）
        if (rowSpan > 1) {
          mergeCells.push({
            row: startRow,
            col: colIndex,
            rowspan: rowSpan,
            colspan: 1
          })
        }

        // 开始新的合并区域
        currentValue = cellValue
        startRow = rowIndex
        rowSpan = 1
      }
    }

    // 处理最后一个合并区域
    if (rowSpan > 1) {
      mergeCells.push({
        row: startRow,
        col: colIndex,
        rowspan: rowSpan,
        colspan: 1
      })
    }
  })

  pageCtl.mergeCells = mergeCells
  console.log('Generated merge cells:', mergeCells)

  // 在下一个tick中恢复列宽度，确保合并单元格配置已经应用
  nextTick(() => {
    restoreColumnWidths()
  })
}

// 桑基图配置
const _sankeyOpt = computed(() => {
  const dataCount = pageCtl.sankeyChartData.data?.length || 0
  const linkCount = pageCtl.sankeyChartData.links?.length || 0

  // 根据数据量动态调整参数
  const dynamicNodeGap = Math.max(2, Math.min(15, 300 / Math.max(dataCount, 1)))
  const dynamicNodeWidth = Math.max(8, Math.min(25, 500 / Math.max(dataCount, 1)))
  const dynamicFontSize = Math.max(8, Math.min(14, 200 / Math.max(dataCount, 1)))

  return {
    title: {
      text: `BOM Structure Flow (${dataCount} 节点, ${linkCount} 连接)`,
      left: 'center',
      top: 15,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: function (params) {
        if (params.dataType === 'edge') {
          return `<div style="max-width: 400px; word-wrap: break-word; font-size: 12px;">
                    <strong style="color: #1890ff;">${params.data.source}</strong><br/>
                    <span style="font-size: 16px;">↓</span><br/>
                    <strong style="color: #52c41a;">${params.data.target}</strong><br/>
                    <span style="color: #666; font-size: 11px;">数量: ${params.data.value}</span>
                  </div>`
        } else {
          return `<div style="max-width: 400px; word-wrap: break-word; font-size: 12px;">
                    <strong style="color: #1890ff;">${params.data.name}</strong><br/>
                    <span style="color: #666; font-size: 11px;">数量: ${params.data.value || 'N/A'}</span>
                  </div>`
        }
      }
    },
    toolbox: {
      show: true,
      right: 15,
      top: 50,
      feature: {
        saveAsImage: {
          show: true,
          title: '保存为图片',
          pixelRatio: 2
        }
      }
    },
    series: [
      {
        type: 'sankey',
        left: 50,
        top: 80,
        right: 50,
        bottom: 50,
        data: pageCtl.sankeyChartData.data || [],
        links: pageCtl.sankeyChartData.links || [],
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            opacity: 0.8
          }
        },
        lineStyle: {
          color: 'source',
          curveness: 0.5,
          opacity: 0.6
        },
        itemStyle: {
          borderColor: 'rgba(0,0,0,0.1)',
          borderWidth: 0.5
        },
        label: {
          fontSize: dynamicFontSize,
          color: '#333',
          position: 'right',
          distance: 5,
          formatter: function (params) {
            const name = params.name || ''
            // 根据字体大小调整显示长度
            const maxLength = Math.floor(dynamicFontSize * 2)
            if (name.length > maxLength) {
              return name.substring(0, maxLength) + '...'
            }
            return name
          }
        },
        nodeGap: dynamicNodeGap,
        nodeWidth: dynamicNodeWidth,
        layoutIterations: Math.min(64, Math.max(16, dataCount * 2)),
        orient: 'horizontal',
        nodeAlign: 'justify',
        draggable: false,
        levels: [
          {
            depth: 0,
            itemStyle: {
              color: '#1890ff'
            },
            lineStyle: {
              opacity: 0.4
            }
          },
          {
            depth: 1,
            itemStyle: {
              color: '#52c41a'
            },
            lineStyle: {
              opacity: 0.4
            }
          },
          {
            depth: 2,
            itemStyle: {
              color: '#faad14'
            },
            lineStyle: {
              opacity: 0.4
            }
          },
          {
            depth: 3,
            itemStyle: {
              color: '#f5222d'
            },
            lineStyle: {
              opacity: 0.4
            }
          }
        ]
      }
    ]
  }
})

</script>
