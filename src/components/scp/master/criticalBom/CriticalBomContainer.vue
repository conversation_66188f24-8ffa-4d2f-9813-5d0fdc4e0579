<template>
  <div id="e2e">
    <el-container>
      <el-aside>
        <el-menu
            :default-active="pageCtl.activeMenu"
            collapse
            popper-effect="light"
            @select="handleMenuClick"
        >
          <el-menu-item index="BOM Structure">
            <el-icon>
              <icon-menu/>
            </el-icon>
            <template #title>BOM Structure</template>
          </el-menu-item>
          <el-menu-item index="Material Risk">
            <el-icon>
              <tools/>
            </el-icon>
            <template #title>Material Risk</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-main class="right-main">
        <keep-alive>
          <component :is="pageCtl.currentComponent"></component>
        </keep-alive>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>

import { markRaw, reactive } from 'vue'
import {
  Document,
  Menu as IconMenu, Calendar, Tools
} from '@element-plus/icons-vue'

import BOMStructure from '@/components/scp/master/criticalBom/criticalBomComponents/CriticalBomStructure.vue'
import MaterialRisk from '@/components/starter/private/MaterialRisk.vue'
import DCConfiguration from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryConfiguration.vue'
import E2ERCA from '@/components/scp/customer/e2eDelivery/e2eDeliveryComponents/EndToEndDeliveryRCA.vue'

const pageCtl = reactive({
  activeMenu: 'BOM Structure' as string,
  currentComponent: markRaw(BOMStructure) as any
})

const handleMenuClick = (index: string) => {
  pageCtl.activeMenu = index
  switch (index) {
    case 'BOM Structure':
      pageCtl.currentComponent = markRaw(BOMStructure)
      break
    case 'Material Risk':
      pageCtl.currentComponent = markRaw(MaterialRisk)
      break
    case 'E2E Delivery Configuration':
      pageCtl.currentComponent = markRaw(DCConfiguration)
      break
    case 'E2E RCA':
      pageCtl.currentComponent = markRaw(E2ERCA)
      break
  }
}
</script>

<style lang="scss">
#e2e {
  .widget-body {
    padding-left: 0;
  }

  .el-menu {
    border: 0;
  }

  .el-aside {
    width: 35px;
    overflow: hidden;

    display: flex;
    position: fixed;
    top: 45px;
  }

  .el-menu-item .el-menu-tooltip__trigger {
    width: 35px;
    padding: 0;
    display: flow;
    text-align: center;
  }

  .el-menu--collapse {
    width: 35px;
  }

  .el-main {
    --el-main-padding: 0px;
    padding-left: 35px;
  }

}

</style>
