<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.categroy" placeholder="Categroy" filterable clearable multiple
                       collapse-tags>
              <el-option
                  v-for="item in _pivotColumns"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <scp-cascader v-model="pageCtl.conditions.filterList"
                          :loading="pageCtl.loading.filter"
                          :options="pageCtl.filterOpts"/>
          </el-col>
          <el-col :span="5">
            <div class="search-group-right" style="width: var(--scp-input-width) !important;">
              <el-date-picker
                  v-model="pageCtl.conditions.dateRange"
                  type="daterange"
                  unlink-panels
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  range-separator="to"
                  :clearable="false"
                  class="search-group-right"
                  style="width: calc(100% - 20px) !important;">
              </el-date-picker>
            </div>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                        :data-exclude="['dateRange']"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <h3 style="text-align: left;">Planning Bom Ratio</h3>
          <scp-subscript id="MDBO"/>
          <scp-table
              ref="summaryTableRef"
              :download-specify-column="false"
              :columns="pageCtl.columnsSummary"
              :show-total="false"
              :params="pageCtl.conditions"
              :page-sizes="[20, 50, 100, 200, 500]"
              :lazy="true"
              url="/master/eto_bom/query_report1"
              download-url="/master/eto_bom/download_report1"
              :editable="false"/>
        </div>
        <div class="subscript-container">
          <h3 style="text-align: left;">Planning Bom Header</h3>
          <scp-subscript id="MDBO"/>
          <scp-table
              ref="report2TableRef"
              :download-specify-column="false"
              :columns="pageCtl.columnsReport2"
              :show-total="false"
              :params="pageCtl.conditions"
              :page-sizes="[20, 50, 100, 200, 500]"
              :lazy="true"
              url="/master/eto_bom/query_report2"
              download-url="/master/eto_bom/download_report2"
              :editable="false"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

import { computed, inject, onMounted, reactive, ref } from 'vue'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const searchRef = ref()
const summaryTableRef = ref()
const report2TableRef = ref()

const pageCtl = reactive({
  filterOpts: [],
  columnsSummary: [], // 第一张表的列定义
  columnsReport2: [], // 第二张表的列定义
  headers: [],
  loading: {
    filter: false,
    report1: false,
    report2: false
  },
  conditions: {
    filterList: [],
    categroy: ['PLANNING_MATERIAL', 'PLANNING_BOM_COMPONENT', 'PLANT_CODE'] as any,
    dateRange: [] as any,
    report1ColumnNames: []
  }
})

const initPage = () => {
  const now = new Date()
  pageCtl.loading.filter = true
  pageCtl.conditions.dateRange = [$dateFormatter(new Date(now.getFullYear(), now.getMonth() - 8, 1), 'yyyy/MM/dd'), $dateFormatter(new Date(), 'yyyy/MM/dd')]
  $axios({
    method: 'post',
    url: '/master/eto_bom/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  console.log(pageCtl.conditions.categroy)
  if (pageCtl.conditions.categroy === null || pageCtl.conditions.categroy.length === 0) {
    pageCtl.conditions.categroy = ['PLANNING_MATERIAL', 'PLANNING_BOM_COMPONENT', 'PLANT_CODE']
  }
  parseTableColumnSummary()
  parseTableColumnReport2()
  summaryTableRef.value.search()
  report2TableRef.value.search()
}

const splitAndCapitalize = (str) => {
  const words = str.split('_')

  return words.map((word) => {
    const lowercasedWord = word.toLowerCase()
    const firstLetter = lowercasedWord.charAt(0).toUpperCase()
    const remainingLetters = lowercasedWord.substring(1)
    return firstLetter + remainingLetters
  }).join(' ')
}

const parseTableColumnSummary = () => {
  const columns = [] as any
  for (let i = 0; i < _selectedCategroy.value.length; i++) {
    columns.push({
      data: _selectedCategroy.value[i],
      title: splitAndCapitalize(_selectedCategroy.value[i])
    })
  }
  columns.push(
    {
      data: 'ACCUMULATE_BOM_RATIO',
      title: splitAndCapitalize('ACCUMULATE_BOM_RATIO'),
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    },
    {
      data: 'ACCUMULATE_COMPONENT_USAGE',
      title: splitAndCapitalize('ACCUMULATE_COMPONENT_USAGE'),
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    },
    {
      data: 'ACCUMULATE_PLANNING_MATERIAL_OUTPUT',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    },
    {
      data: 'ACCUMULATE_WBS_FREQUENCY',
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    }
  )
  pageCtl.columnsSummary = columns
}

const parseTableColumnReport2 = () => {
  // 这里定义第二张表的列
  const columns = [] as any
  columns.push(
    {
      data: 'SO_MATERIAL',
      title: splitAndCapitalize('SO_MATERIAL'),
      render: (hotInstance, td, row, column, prop, value) => {
        td.innerHTML = value
      }
    },
    {
      data: 'PLANT_CODE',
      title: splitAndCapitalize('PLANT_CODE'),
      render: (hotInstance, td, row, column, prop, value) => {
        td.innerHTML = value
      }
    },
    {
      data: 'PLANNING_MATERIAL',
      title: splitAndCapitalize('PLANNING_MATERIAL'),
      render: (hotInstance, td, row, column, prop, value) => {
        td.innerHTML = value
      }
    },
    {
      data: 'RATIO',
      title: splitAndCapitalize('RATIO'),
      render: (hotInstance, td, row, column, prop, value) => {
        td.style.textAlign = 'right'
        td.innerHTML = value
      }
    }
  )
  pageCtl.columnsReport2 = columns
}

const _selectedCategroy = computed(() => {
  if (pageCtl.conditions.categroy.length > 0) {
    return pageCtl.conditions.categroy
  } else {
    return ['ENTITY']
  }
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts, 'PLANT_CODE')
})

onMounted(() => {
  initPage()
})

</script>
