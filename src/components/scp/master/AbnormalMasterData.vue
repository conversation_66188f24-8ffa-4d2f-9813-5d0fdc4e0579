<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.displayTab">
          <el-tab-pane label="Abnormal Master Data" name="ABNORMAL_MASTER_DATA">
            <div class="subscript-container">
              <scp-subscript id="MDAS"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.categroies" placeholder="Categroies" filterable clearable
                             collapse-tags multiple>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <scp-cascader
                      v-model="pageCtl.conditions.filterList"
                      :loading="pageCtl.loading.filter"
                      :options="pageCtl.filterOpts"/>
                </el-col>
                <el-col :span="8">
                  <scp-cascader
                      v-model="pageCtl.conditions.selectedGroup"
                      :loading="pageCtl.loading.filter"
                      placeholder-text='Group'
                      :options="pageCtl.filterGroupOpts"/>
                </el-col>
                <el-col :span="3">
                  <el-input v-model="pageCtl.conditions.material" placeholder="Material" class="textarea-input"
                            style="width: var(--scp-input-width) !important;" clearable type="textarea"></el-input>
                </el-col>
                <el-col :span="2">
                  <el-input-number v-model="pageCtl.warningValue" :precision="1" :step="0.1" :max="100" :min="0"
                                   style="width: var(--scp-input-width) !important;"/>
                </el-col>
                <el-col :span="1">
                  <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"/>
                </el-col>
              </el-row>
              <scp-table
                  ref="report1TableRef"
                  :columns="pageCtl.dashboardSettings.columns"
                  :nestedHeaders="pageCtl.dashboardSettings.nestedHeaders"
                  :params="pageCtl.conditions"
                  :pagging="false"
                  :after-select="afterReport1Select"
                  :fixedColumnsLeft="_Categroies.length"
                  :context-menu-items="pageCtl.report1ContextItems"
                  :lazy="true"
                  :max-height="320"
                  url="/master/abnormal_master_data/query_dashboard"
                  :editable="false"/>
            </div>

            <div class="subscript-container">
              <scp-subscript id="MDAD"/>
              <el-row style="margin-bottom: var(--scp-widget-margin)" class="search-box">
                <el-col :span="24" style="text-align: right;padding-right:var(--scp-widget-margin)">
                  <el-button-group style="margin-left:10px">
                    <el-tooltip class="item" effect="light" content="Expand Table" placement="bottom-end" :show-after="500">
                      <el-button @click="expandAll">
                        <font-awesome-icon icon="expand-arrows-alt"/>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="light" content="Collapse Table" placement="bottom-end"
                                :show-after="500">
                      <el-button @click="collapseAll">
                        <font-awesome-icon icon="compress-arrows-alt"/>
                      </el-button>
                    </el-tooltip>
                  </el-button-group>
                </el-col>
              </el-row>
              <scp-table
                  ref="resultTableRef"
                  :columns="pageCtl.detailsSettings.columns"
                  :params="pageCtl.conditions"
                  :nestedHeaders="pageCtl.detailsSettings.nestedHeaders"
                  :fixedColumnsLeft="2"
                  :lazy="true"
                  url="/master/abnormal_master_data/query_result"
                  download-url="/master/abnormal_master_data/download_result"
                  :editable="false"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Filter Rules" name="FILTER_RULES">
            <div class="subscript-container">
              <scp-subscript id="MDAR"/>
              <el-row style="margin-bottom: var(--scp-widget-margin)" class="search-box" v-show="_showCalcBtn">
                <el-col :span="24" style="text-align: right;padding-right: 15px">
                  <el-popconfirm title="确定重新计算数据? 这大概需要10分钟"
                                 iconColor="orange"
                                 @confirm="manualCheck"
                                 confirmButtonType="warning"
                                 cancel-button-type="primary"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'
                                 style="margin-left:5px">
                    <template #reference>
                      <el-button :type="pageCtl.recalcStatus" :loading="pageCtl.loading.recalc">
                        <font-awesome-icon icon="fa-solid fa-wand-magic-sparkles"/> &nbsp;重新计算
                      </el-button>
                    </template>
                  </el-popconfirm>
                </el-col>
              </el-row>
              <scp-table
                  ref="ruleTableRef"
                  :columns="pageCtl.filterColumn"
                  :contextMenuItems="pageCtl.contextMenuItems"
                  :maxHeight="pageCtl.tableHeight"
                  :afterSelect="afterRuleSelect"
                  :page-sizes="[20, 50, 100, 200, 500]"
                  url="/master/abnormal_master_data/query_rules"
                  :editable="false"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Error Code" name="ERROR_CODE">
            <div class="subscript-container">
              <scp-subscript id="MDAC"/>
              <scp-datagrid bindTo="ABNORMAL_MASTER_DATA_ERROR_CODE" :cache-keys="pageCtl.errorCodeSetting['cacheKeys']"/>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- filter rule details-->
    <scp-draggable-resizable v-model="pageCtl.dialogVisible" w="90vw" h="90vh"
                             :title="pageCtl.dialogTitle" :save="saveRule">
      <template v-slot="{height}">
        <div style="padding: 10px" :style="{'height': (height - 150) + 'px'}">
          <el-row>
            <el-col :span="8" style="margin-bottom: 10px;">
              <el-input v-model="pageCtl.ruleObj.ruleName" placeholder="New Rule Name" style="width:90%"></el-input>
            </el-col>
            <el-col :span="4" style="margin-bottom: 10px;">
              <el-switch
                  v-model="pageCtl.ruleObj.enable"
                  active-color="#13ce66"
                  inactive-color="#ff4949">
              </el-switch>
            </el-col>
          </el-row>
          <scp-table2 style="margin-bottom:10px;"
                      :pagging="false"
                      :paggingSettingEnable="false"
                      :context-menu-items="{'row_below': {},'remove_row': {},'split':'-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;'}"
                      :showContextMenu="true"
                      :columns="pageCtl.newRuleColumn"
                      :maxHeight="200"
                      :data="pageCtl.ruleObj.ruleColumns"/>
          <scp-ace-editor v-model="pageCtl.ruleObj.sqlScript" lang="sql"
                          :style="{'height': (height - 350) + 'px'}"
                          style="border:1px solid var(--scp-border-color);width: 100%"></scp-ace-editor>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

// region 属性和注入方法
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $deepClone: any = inject('$deepClone')
const $renderColumnName: any = inject('$renderColumnName')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $join: any = inject('$join')
const $viewDetails: any = inject('$viewDetails')

const searchRef = ref()
const report1TableRef = ref()
const resultTableRef = ref()
const ruleTableRef = ref()

const _Categroies = computed(() => {
  if (pageCtl.conditions.categroies.length > 0) {
    return pageCtl.conditions.categroies
  } else {
    return ['CLUSTER_NAME', 'ENTITY']
  }
})

const _showCalcBtn = computed(() => {
  const username = localStorage.getItem('username')
  return username ? (username.toLowerCase() === 'sesa513408' || username.toLowerCase() === 'sesa466946') : false
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
// endregion

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/master/abnormal_master_data/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.filterOpts
    pageCtl.filterGroupOpts = body.filterGroupOpts
    pageCtl.nestedHeaders = body.nestedHeader
    pageCtl.reasonCode = body.reasonCode
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
const search = () => {
  pageCtl.dashboardSettings = generateDashTableSettings()
  pageCtl.detailsSettings = generateDetailTableSettings()
  searchReport1()
  searchReport2()
}
const searchReport1 = () => {
  report1TableRef.value.search()
}
const searchReport2 = () => {
  resultTableRef.value.search()
}

// region Filter Rules
const newRule = () => {
  pageCtl.ruleObj = {
    ruleName: '',
    ruleColumns: [{}],
    sqlScript: '',
    enable: true
  }
  pageCtl.dialogVisible = true
  pageCtl.dialogTitle = 'New Rules'
}
const editRule = () => {
  $axios({
    method: 'post',
    url: '/master/abnormal_master_data/query_rule_by_id',
    data: {
      group_name: pageCtl.selectedGroupName
    }
  }).then((body) => {
    pageCtl.ruleObj.rowid = body.ROW_ID
    pageCtl.ruleObj.ruleName = body.GROUP_NAME
    pageCtl.ruleObj.enable = body.ENABLE === '1'
    pageCtl.ruleObj.sqlScript = body.SQL_SCRIPT
    pageCtl.ruleObj.ruleColumns = JSON.parse(body.OUTPUT_COLUMN)
    pageCtl.dialogTitle = 'Edit Rules'
    pageCtl.dialogVisible = true
  }).catch((error) => {
    console.log(error)
  })
}
const saveRule = () => {
  pageCtl.ruleObj.columnStr = JSON.stringify(pageCtl.ruleObj.ruleColumns)
  pageCtl.ruleObj.enableStr = pageCtl.ruleObj.enable ? '1' : '0'
  $axios({
    method: 'post',
    url: '/master/abnormal_master_data/save_rule',
    data: pageCtl.ruleObj
  }).then(() => {
    $message.success(pageCtl.ruleObj.ruleName + ' Saved')
    pageCtl.dialogVisible = false
    ruleTableRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}
const renderEnableStyle = (hotInstance, td, row, column, prop, value) => {
  if (value === '1') {
    td.innerHTML = '<div class="rule-enable"><div></div></div>'
  } else {
    td.innerHTML = '<div class="rule-disenable"><div></div></div>'
  }
}
const renderOutputStyle = (hotInstance, td, row, column, prop, value) => {
  const jv = JSON.parse(value)
  if (jv === null) {
    td.innerHTML = value
    return
  }
  const html = [] as any
  for (let i = 0; i < jv.length; i++) {
    html.push(jv[i].name)
  }
  const htmlstr = '【' + html.join('】【') + '】'
  const L = htmlstr.length
  if (L < 120) {
    td.innerHTML = htmlstr
  } else {
    td.innerHTML = htmlstr.substring(0, 120) + '...'
  }
}
const afterRuleSelect = (row) => {
  if (row) {
    pageCtl.selectedGroupName = row.GROUP_NAME
  } else {
    pageCtl.selectedGroupName = ''
  }
}
const manualCheck = () => {
  pageCtl.loading.recalc = true
  $axios({
    method: 'post',
    url: '/master/abnormal_master_data/start_checking'
  }).then(() => {
    $message.success('done!')
    pageCtl.recalcStatus = 'primary'
  }).catch((error) => {
    console.log(error)
    pageCtl.recalcStatus = 'danger'
  }).finally(() => {
    pageCtl.loading.recalc = false
  })
}
// endregion

// region Dashboard
const collapseAll = () => {
  if (resultTableRef.value) {
    resultTableRef.value.getHotInstance().getPlugin('collapsibleColumns').collapseAll()
  }
}
const expandAll = () => {
  if (resultTableRef.value) {
    resultTableRef.value.getHotInstance().getPlugin('collapsibleColumns').expandAll()
  }
}
const generateDashTableSettings = () => {
  const columns = [] as any
  for (let i = 0; i < _Categroies.value.length; i++) {
    columns.push({
      data: _Categroies.value[i],
      render: renderRow
    })
  }

  columns.push({
    data: 'TOTAL',
    title: 'Total No. of Material',
    type: 'numeric',
    render: renderRow
  })

  const nestedHeaders = [[{
    label: '',
    colspan: _Categroies.value.length
  }, {
    label: '',
    colspan: 1
  }]]

  const selectedGroupArray = [] as any
  for (let i = 0; i < pageCtl.conditions.selectedGroup.length; i++) {
    const name = pageCtl.conditions.selectedGroup[i][0]
    if (selectedGroupArray.indexOf(name) === -1) {
      selectedGroupArray.push(name)
    }
  }

  for (const key in pageCtl.nestedHeaders) {
    if (pageCtl.nestedHeaders.hasOwnProperty(key)) {
      if (selectedGroupArray.length > 0) {
        if (selectedGroupArray.indexOf(key) === -1) {
          continue
        }
      }
      const groupIndex = key.split('-')[0]
      columns.push({
        data: groupIndex + '_TOTAL',
        title: 'No. of Abnormal',
        type: 'numeric',
        render: renderRow
      }, {
        data: groupIndex + '_PERCENT',
        title: '%',
        type: 'numeric',
        render: renderRow
      })

      nestedHeaders[0].push({
        label: key,
        colspan: 2
      })
    }
  }

  return {
    columns,
    nestedHeaders
  }
}
const generateDetailTableSettings = () => {
  const columns = [
    {
      data: 'MATERIAL'
    }, {
      data: 'PLANT_CODE'
    }, {
      data: 'MATERIAL_OWNER_NAME'
    }, {
      data: 'MATERIAL_OWNER_SESA'
    }, {
      data: 'MRP_CONTROLLER'
    }, {
      data: 'PRODUCT_LINE'
    }, {
      data: 'CLUSTER_NAME'
    }, {
      data: 'ENTITY'
    }, {
      data: 'BU',
      title: 'BU'
    }, {
      data: 'LOCAL_BU',
      title: 'Local BU'
    }, {
      data: 'LOCAL_PRODUCT_FAMILY'
    }, {
      data: 'LOCAL_PRODUCT_LINE'
    }, {
      data: 'LOCAL_PRODUCT_SUBFAMILY'
    }, {
      data: 'ACTIVENESS'
    }, {
      data: 'DELETION'
    }
  ]

  const viewDetailsColumns = $deepClone(columns)

  const nestedHeaders = [
    [
      {
        colspan: 1
      }, {
        colspan: 1
      }, {
        label: 'Category',
        colspan: 11
      }
    ]
  ]

  const selectedGroupArray = [] as any
  for (let i = 0; i < pageCtl.conditions.selectedGroup.length; i++) {
    const name = pageCtl.conditions.selectedGroup[i][0]
    if (selectedGroupArray.indexOf(name) === -1) {
      selectedGroupArray.push(name)
    }
  }

  for (const key in pageCtl.nestedHeaders) {
    if (pageCtl.nestedHeaders.hasOwnProperty(key)) {
      if (selectedGroupArray.length > 0) {
        if (selectedGroupArray.indexOf(key) === -1) {
          continue
        }
      }
      const arr = pageCtl.nestedHeaders[key]
      for (let i = 0; i < arr.length; i++) {
        const c = {
          data: arr[i],
          title: $renderColumnName(arr[i].split('_').slice(1).join('_'))
        } as any
        if (arr[i].indexOf('REASON_CODE') !== -1) {
          c.render = parseReasonCodeTips

          viewDetailsColumns.push(
            {
              data: arr[i],
              title: $renderColumnName(arr[i]),
              render: parseReasonCodeTips
            }
          )
        }

        columns.push(c)
      }

      nestedHeaders[0].push({
        label: key,
        colspan: arr.length
      })
    }
  }
  columns.push({
    data: '',
    title: ''
  })
  return {
    columns,
    nestedHeaders,
    viewDetailsColumns
  }
}
const renderRow = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (value) {
    let html: string

    if (prop.indexOf('TOTAL') !== -1) {
      td.style.textAlign = 'right'
      html = $thousandBitSeparator(value)
    } else if (prop.indexOf('_PERCENT') !== -1) {
      if (value * 100 > pageCtl.warningValue) {
        td.style.color = 'rgb(239 0 0) !important'
      }
      td.style.textAlign = 'right'
      html = (value * 100).toFixed(2) + '%'
    } else {
      html = value
    }

    if (r[_Categroies.value[_Categroies.value.length - 1]] === 'Total') {
      td.innerHTML = '<b>' + html + '</b>'
    } else {
      td.innerHTML = html
    }
  } else {
    td.style.textAlign = 'right'
    td.innerHTML = value
  }
}
const delayer = (action, delay = 600) => {
  if (pageCtl.timer) {
    clearTimeout(pageCtl.timer)
  }
  pageCtl.timer = setTimeout(() => {
    action.call()
  }, delay)
}
const parseReasonCodeTips = (hotInstance, td, row, column, prop, value) => {
  const tips = [] as any
  if (value) {
    const vs = value.split(',')
    for (let i = 0; i < vs.length; i++) {
      const v = vs[i]
      if (pageCtl.reasonCode[v]) {
        tips.push('-【' + v + '】 ' + pageCtl.reasonCode[v])
      } else {
        tips.push('-【' + v + '】')
      }
    }
  }

  if (tips.length > 0) {
    let html = '<div title="#content#">'
    html += value
    html += '</div>'
    td.innerHTML = html.replace('#content#', tips.join('&#13;'))
  } else {
    td.innerHTML = value
  }
}
const afterReport1Select = (r, rc, c, column) => {
  if (column >= _Categroies.value.length) {
    pageCtl.conditions.selectedKey = c
  } else {
    pageCtl.conditions.selectedKey = ''
  }
  const selected = [] as any
  for (let i = 0; i < _Categroies.value.length; i++) {
    selected.push(r[_Categroies.value[i]])
  }

  pageCtl.conditions.selectedValues = selected

  pageCtl.masterDataDetailsTitle = 'View Details [' + $join(pageCtl.conditions.selectedKey, ...selected) + ']'
}
const viewMasterDataDetails = () => {
  $viewDetails({
    url: '/master/abnormal_master_data/query_master_data_details',
    durl: '/master/abnormal_master_data/download_master_data_details',
    params: pageCtl.conditions,
    title: pageCtl.masterDataDetailsTitle,
    columns: pageCtl.detailsSettings.viewDetailsColumns
  })
}
// endregion

const pageCtl = reactive({
  recalcStatus: 'default' as any,
  conditions: {
    categroies: ['CLUSTER_NAME', 'ENTITY'],
    group: [],
    material: '',
    selectedGroup: [],
    selectedKey: '',
    selectedValues: []
  } as any,
  timer: null as any,
  warningValue: 30 as any,
  loading: {
    filter: false,
    recalc: false
  },
  dialogVisible: false,
  dialogTitle: '',
  displayTab: 'ABNORMAL_MASTER_DATA',
  selectedGroupName: '',
  nestedHeaders: {},
  tableHeight: 450,
  ruleObj: {
    rowid: '',
    ruleName: '',
    ruleColumns: [{}],
    sqlScript: '',
    enable: true
  } as any,
  saveRuleResult: '',
  errorCodeSetting: {
    cacheKeys: '1d::c.s.t.s.i.MasterDataServiceImpl'
  },
  newRuleColumn: [
    {
      data: 'name',
      title: 'Name'
    },
    {
      data: 'length',
      title: 'Length'
    }
  ],
  filterColumn: [
    {
      data: 'GROUP_NAME',
      readOnly: true
    },
    {
      data: 'OUTPUT_COLUMN',
      readOnly: true,
      render: renderOutputStyle
    },
    {
      data: 'ENABLE',
      readOnly: true,
      render: renderEnableStyle
    }
  ],
  contextMenuItems: {
    new_rule: {
      name: 'New rule',
      callback: newRule
    },
    edit_rule: {
      name: 'Edit rule',
      disabled: () => {
        return pageCtl.selectedGroupName === ''
      },
      callback: editRule
    },
    view_split: { name: '---------' }
  },
  filterOpts: [] as any,
  filterGroupOpts: [],
  dashboardSettings: {
    columns: [{ data: '' }, { data: '' }],
    nestedHeaders: [[{
      label: '',
      colspan: 2
    }]]
  } as any,
  detailsSettings: {
    columns: [{ data: '' }, { data: '' }],
    nestedHeaders: [[{
      label: '',
      colspan: 2
    }]],
    viewDetailsColumns: [{ data: '' }, { data: '' }]
  } as any,
  reasonCode: {},
  masterDataDetailsTitle: '',
  report1ContextItems: {
    view_details: {
      name: 'View details',
      disabled () {
        return pageCtl.conditions.selectedKey === 'TOTAL'
      },
      callback: viewMasterDataDetails
    },
    view_split0: { name: '---------' }
  }
})

watch(() => pageCtl.warningValue, () => {
  delayer(() => {
    report1TableRef.value.redrawTable()
  })
})

onMounted(() => {
  initPage()
})

</script>

<style>
.rule-enable, .rule-disenable {
  margin-top: 3px;
  width: 26px;
  height: 16px;
  border-radius: 10px;
  background-color: var(--scp-bg-color-fill);
}

.rule-enable div, .rule-disenable div {
  margin: 2px;
  width: 12px;
  height: 12px;
  background-color: #ffffff;
  border-radius: 50%;
}

.rule-enable {
  background-color: var(--scp-text-color-success);
}

.rule-enable div {
  float: right;
}

.rule-disenable div {
  float: left;
}
</style>
