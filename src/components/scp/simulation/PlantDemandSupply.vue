<template>
  <div class="left-sidebar" id="damandSupply">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.displayTab" @tab-click="handleTabClick">
          <el-tab-pane label="Demand Supply" name="DEMAND_SUPPLY">
            <el-row class="search-box">
              <el-col :span="4">
                <scp-cascader
                    v-model="pageCtl.conditions.filterList"
                    :options="pageCtl.filterOpts"/>
              </el-col>
              <el-col :span="2">
                <el-select v-model="pageCtl.conditions.viewBy">
                  <el-option
                      v-for="item in pageCtl.viewBy"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <scp-cascader
                    v-model="pageCtl.conditions.demandSupplySelections"
                    :options="pageCtl.demandSupplyOpts"
                    placeholder-text="Demand & Supply"/>
              </el-col>
              <el-col :span="2">
                <el-select v-model="pageCtl.conditions.specialType" style="width: 100% !important;"
                           class="search-group-left">
                  <el-option label="MATERIAL" value="MATERIAL"/>
                  <el-option label="GROUP_MATERIAL" value="GROUP_MATERIAL"/>
                  <el-option label="VENDOR_CODE" value="VENDOR_CODE"/>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input :placeholder="pageCtl.conditions.specialType" v-model="pageCtl.conditions.specialContent"
                          style="width: var(--scp-input-width) !important;"
                          type="textarea" class="search-group-right"></el-input>
              </el-col>
              <el-col :span="3">
                <el-select v-model="pageCtl.conditions.valueBy" style="width: 100% !important;">
                  <el-option label="Quantity" value="Quantity"/>
                  <el-option label="Moving Average Price" value="Moving Average Price"/>
                  <el-option label="Net Net Price" value="Net Net Price"/>
                </el-select>
              </el-col>
              <el-col :span="5" style="padding-left: 10px">
                <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"
                            :after-expand="moreConditions"/>
              </el-col>
            </el-row>
            <el-row class="search-box" v-show="pageCtl.conditionsExpanded">
              <el-col :span="4">
                <el-select v-model="pageCtl.conditions.criticalID" placeholder="Critical Level">
                  <el-option
                      v-for="item in pageCtl.criticalLevelList"
                      :key="item['VALUE']"
                      :label="item['LABEL']"
                      :value="item['VALUE']">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="6">
                <scp-cascader v-model="pageCtl.conditions.demandDueSelections"
                              placeholder-text="Future Due || Past Due"
                              :options="pageCtl.demandDueOpts"/>
              </el-col>
              <el-col :span="2">
                <el-select v-model="pageCtl.conditions.order" style="width: 100% !important;" class="search-group-left">
                  <el-option label="Ascending" value="ASC"/>
                  <el-option label="Descending" value="DESC"/>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="pageCtl.conditions.orderBy" placeholder="Order By" clearable class="search-group-right"
                           collapse-tags multiple>
                  <el-option
                      v-for="item in _orderBy"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="8">
                <span style="font-size: 8px;color: rgb(192, 204, 204)">{{ $join(...pageCtl.conditions.orderBy) }}</span>
              </el-col>
            </el-row>
            <el-row class="search-box" v-show="pageCtl.conditionsExpanded" style="margin-bottom: 10px">
              <el-col :span="4" v-for="(item,index) in pageCtl.conditions.ratio" :key="index">
                <el-input v-model="item.ratio" class="ratio-style" style="width: var(--scp-input-width)">
                  <template #prepend>{{ item.label }}</template>
                </el-input>
              </el-col>
            </el-row>
            <div class="subscript-container" :style="{minHeight: ($store.state.pageHeight - 150) + 'px'}">
              <scp-subscript id="SDMP" :after-toggle="afterReport1Toggle" ref="sdmpRef"/>
              <div class="front" style="transform-style: flat; !important;">
                <scp-table2
                    ref="report1TableRef"
                    :data="pageCtl.report1Data"
                    :column-sorting="false"
                    :max-height="_report1Height"
                    v-loading="pageCtl.loading.report1"
                    :columns="pageCtl.report1Columns"
                    :after-select="afterReport1Select"
                    :after-change="afterReport1ChangeComments"
                    :context-menu-items="pageCtl.report1ContextMenuItems"
                    :context-menu-items-reverse="true"
                    :mergeCells="pageCtl.report1MergeCells"
                    :on-search="searchReport1"
                    :fixed-columns-left="2"
                />
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box" style="width: calc(30% - 15px)">
                  <div class="box-title">Critical Level Options</div>
                  <el-select v-model="pageCtl.criticalLevelSelected" placeholder="Critical Level" style="width: 260px"
                             @change="criticalLevelChanged">
                    <el-option
                        v-for="item in pageCtl.criticalLevelList"
                        :key="item['VALUE']"
                        :label="item['LABEL']"
                        :value="item['VALUE']">
                    </el-option>
                  </el-select>
                  <div style="height: 20px"></div>
                  <div class="box-title" v-show="pageCtl.criticalLevelComments !== ''">Comments</div>
                  <pre v-show="pageCtl.criticalLevelComments !== ''">{{ pageCtl.criticalLevelComments }}</pre>
                </div>
                <div class="box" style="width: calc(70% - 15px);height: 100%;overflow-y: auto">
                  <div class="box-title">Code</div>
                  <scp-ace-editor v-model="pageCtl.criticalLevelCode" lang="javascript"
                                  style="border:1px solid var(--scp-border-color);height:400px;width: calc(100% - 5px)"/>
                  <br>
                  <div class="box-title">Avalible Variables</div>
                  <scp-table2
                      ref="report1AvalibleVariablesTable"
                      :data="pageCtl.report1AvalibleVariablesData"
                      :column-sorting="false"
                      :max-height="240"
                      :columns="[
                    {data: 'TYPE'},
                    {data: 'PASTDUE'},
                    {data: 'WK01'},
                    {data: 'WK02'},
                    {data: 'WK03'},
                    {data: 'WK04'},
                    {data: 'WK05'},
                    {data: 'WK06'},
                    {data: 'WK07'},
                    {data: 'WK08'},
                    {data: 'WK09'},
                    {data: 'WK11'},
                    {data: 'WK12'},
                    {data: 'WK13'},
                    {data: 'WK14'},
                    {data: 'WK15'}
                  ]"
                      :fixed-columns-left="1"
                  />
                  <div class="box-footer">
                    <el-button
                        @click="sdmpRef.toggleView()">
                      Back
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="PO Commitment" name="PO_COMMITMENT">
            <div class="subscript-container">
              <scp-subscript id="SDPC"/>
              <scp-table
                  url="/simulation/plant_demand_supply/query_report3"
                  download-url="/simulation/plant_demand_supply/download_report3"
                  save-url="/simulation/plant_demand_supply/save_report3"
                  ref="report3TableRef"
                  :contextMenuItemsReverse="true"
                  :contextMenuItems="pageCtl.report3MenuItems"
                  :lazy="true"
                  :columns="_report3TableColumn"
                  :hiddenColumns="{columns: [0]}"
                  :update-data-enable="true"
                  :create-data-enable="true"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Critical Material" name="CRITICAL_MATERIAL">
            <div class="subscript-container">
              <scp-subscript id="SDCM"/>
              <scp-table
                  url="/simulation/plant_demand_supply/query_report4"
                  download-url="/simulation/plant_demand_supply/download_report4"
                  save-url="/simulation/plant_demand_supply/save_report4"
                  ref="report4TableRef"
                  :contextMenuItemsReverse="true"
                  :contextMenuItems="pageCtl.report4MenuItems"
                  :lazy="true"
                  :columns="_report4TableColumn"
                  :hiddenColumns="{columns: [0]}"/>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <scp-upload
        title="Upload PO Commitment"
        :show-btn="false"
        w="600px"
        h="220px"
        upload-url='/simulation/plant_demand_supply/upload_report3'
        download-template-url='/simulation/plant_demand_supply/download_report3_template'
        :on-upload-end="searchReport3">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <scp-upload
        ref="upload3Ref"
        title="Upload PO Commitment"
        :show-btn="false"
        w="600px"
        h="220px"
        upload-url='/simulation/plant_demand_supply/upload_report3'
        download-template-url='/simulation/plant_demand_supply/download_report3_template'
        :on-upload-end="searchReport3">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <scp-upload
        ref="upload4Ref"
        title="Upload Critical Material"
        :show-btn="false"
        w="600px"
        h="220px"
        upload-url='/simulation/plant_demand_supply/upload_report4'
        download-template-url='/simulation/plant_demand_supply/download_report4_template'
        :on-upload-end="searchReport3">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onBeforeMount, onMounted, reactive, ref, watch } from 'vue'
import ScpUpload from '@/components/starter/components/Upload.vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { useStore } from 'vuex'

const searchRef = ref()
const upload3Ref = ref()
const upload4Ref = ref()
const report1TableRef = ref()
const report3TableRef = ref()
const report4TableRef = ref()
const sdmpRef = ref()
const $store = useStore()
const $join: any = inject('$join')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $viewDetails: any = inject('$viewDetails')

const viewReport1Details = () => {
  pageCtl.report1DetailsColumns = parseReport1DetailsColumns()
  const selected = pageCtl.conditions.selected

  $viewDetails({
    url: '/simulation/plant_demand_supply/query_report1_details',
    durl: '/simulation/plant_demand_supply/download_report1_details',
    params: pageCtl.conditions,
    title: 'View Details [' + $join(selected.report1Plant, selected.report1Material, selected.report1GroupMaterial, selected.report1Category) + ']',
    columns: pageCtl.report1DetailsColumns
  })
}

const saveReport1Comments = () => {
  $axios({
    method: 'post',
    url: '/simulation/plant_demand_supply/save_report1_comments',
    data: {
      comments: pageCtl.report1CommentsUpdate
    }
  }).then(() => {
    $message.success('Comments saved.')
    searchReport1()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report1CommentsUpdate = {}
  })
}

const viewReport1HistoryComments = () => {
  $viewDetails({
    url: '/simulation/plant_demand_supply/query_report1_history_comments',
    durl: '/simulation/plant_demand_supply/download_report1_history_comments',
    params: pageCtl.conditions.selected,
    title: 'View Historic Comments [' + pageCtl.conditions.selected.report1HistPlantCode + ', ' + (pageCtl.conditions.selected.report1HistGroupMaterial || pageCtl.conditions.selected.report1HistMaterial) + ']',
    columns: pageCtl.report1HistCommentsColumns
  })
}

const showUpload3Win = () => {
  upload3Ref.value.showUploadWin()
}

const showUpload4Win = () => {
  upload4Ref.value.showUploadWin()
}

const pageCtl = reactive({
  filterOpts: [],
  displayTab: 'DEMAND_SUPPLY',
  viewBy: ['By Day', 'By Week', 'By Month'],
  dayList: [] as Array<string>,
  weekList: [] as Array<string>,
  monthList: [] as Array<string>,
  criticalLevelList: [] as Array<any>,
  conditionsExpanded: false,
  conditions: {
    filterList: [],
    order: 'Descending',
    orderBy: [],
    viewBy: 'By Day',
    valueBy: 'Quantity',
    demandSupplySelections: [],
    demandDueSelections: [],
    specialType: 'MATERIAL',
    specialContent: '',
    ratio: [{
      label: 'Depedent Requirement(%)',
      value: 'Depedent Requirement',
      ratio: 100
    }, {
      label: 'Indepedent Requirement(%)',
      value: 'Indepedent Requirement',
      ratio: 100
    }, {
      label: 'FCST Commitement(%)',
      value: 'FCST Commitement',
      ratio: 100
    }, {
      label: 'Safety Stock(%)',
      value: 'Safety Stock',
      ratio: 100
    }, {
      label: '+AMU as SS(%)',
      value: 'AMU',
      ratio: 0
    }, {
      label: '+AMF as SS(%)',
      value: 'AMF',
      ratio: 0
    }],
    selected: {
      report1Material: '',
      report1GroupMaterial: '',
      report1Plant: '',
      report1Category: '',
      report1Date: '',

      report1HistMaterial: '',
      report1HistGroupMaterial: '',
      report1HistPlantCode: ''
    },
    criticalID: ''
  },
  demandDueOpts: [
    {
      label: 'Future Due',
      value: 'FUTURE_DUE',
      children: [
        {
          label: 'Depedent Requirement',
          value: 'Depedent Requirement'
        },
        {
          label: 'Indepedent Requirement',
          value: 'Indepedent Requirement'
        },
        {
          label: 'Open Sales Order',
          value: 'Open Sales Order'
        },
        {
          label: 'Order Reservation',
          value: 'Order Reservation'
        }
      ]
    }, {
      label: 'Past Due',
      value: 'PAST_DUE',
      children: [
        {
          label: 'Depedent Requirement',
          value: 'Depedent Requirement'
        },
        {
          label: 'Indepedent Requirement',
          value: 'Indepedent Requirement'
        },
        {
          label: 'Open Sales Order',
          value: 'Open Sales Order'
        },
        {
          label: 'Order Reservation',
          value: 'Order Reservation'
        }
      ]
    }
  ],
  demandSupplyOpts: [
    {
      label: 'COLUMNS',
      value: 'COLUMNS',
      children: [
        {
          label: 'MATERIAL_CATEGORY',
          value: 'MATERIAL_CATEGORY'
        }, {
          label: 'VENDOR_CODE',
          value: 'VENDOR_CODE'
        }, {
          label: 'VENDOR_SEARCH_NAME',
          value: 'VENDOR_SEARCH_NAME'
        }, {
          label: 'VENDOR_NAME',
          value: 'VENDOR_NAME'
        }, {
          label: 'DATA_LINE',
          value: 'DATA_LINE'
        }, {
          label: 'LEAD_TIME',
          value: 'LEAD_TIME'
        }, {
          label: 'LT_RANGE',
          value: 'LT_RANGE'
        }, {
          label: 'MATERIAL_ST_PLANT',
          value: 'MATERIAL_ST_PLANT'
        }, {
          label: 'ABC',
          value: 'ABC'
        }, {
          label: 'CALCULATED_ABC',
          value: 'CALCULATED_ABC'
        }, {
          label: 'CALCULATED_FMR',
          value: 'CALCULATED_FMR'
        }, {
          label: 'PROCUREMENT_TYPE',
          value: 'PROCUREMENT_TYPE'
        }, {
          label: 'ACTIVENESS',
          value: 'ACTIVENESS'
        }, {
          label: 'STOCKING_POLICY',
          value: 'STOCKING_POLICY'
        }, {
          label: 'COUNTRY',
          value: 'COUNTRY'
        }, {
          label: 'MATERIAL_OWNER_NAME',
          value: 'MATERIAL_OWNER_NAME'
        }, {
          label: 'MATERIAL_OWNER_SESA',
          value: 'MATERIAL_OWNER_SESA'
        }, {
          label: 'MRP_CONTROLLER',
          value: 'MRP_CONTROLLER'
        }, {
          label: 'ROUNDING_VALUE',
          value: 'ROUNDING_VALUE'
        }, {
          label: 'PRODUCT_LINE',
          value: 'PRODUCT_LINE'
        }, {
          label: 'ENTITY',
          value: 'ENTITY'
        }, {
          label: 'CLUSTER_NAME',
          value: 'CLUSTER_NAME'
        }, {
          label: 'BU',
          value: 'BU'
        }, {
          label: 'LOCAL_BU',
          value: 'LOCAL_BU'
        }, {
          label: 'LOCAL_PRODUCT_FAMILY',
          value: 'LOCAL_PRODUCT_FAMILY'
        }, {
          label: 'LOCAL_PRODUCT_LINE',
          value: 'LOCAL_PRODUCT_LINE'
        }, {
          label: 'LOCAL_PRODUCT_SUBFAMILY',
          value: 'LOCAL_PRODUCT_SUBFAMILY'
        }, {
          label: 'PRODUCTION_LINE',
          value: 'PRODUCTION_LINE'
        }, {
          label: 'PLANT_TYPE',
          value: 'PLANT_TYPE'
        }, {
          label: 'REPL_STRATEGY',
          value: 'REPL_STRATEGY'
        }, {
          label: 'SOURCE_CATEGORY',
          value: 'SOURCE_CATEGORY'
        }, {
          label: 'UNIT_COST',
          value: 'UNIT_COST'
        }, {
          label: 'GROSS_WEIGHT_IN_KG',
          value: 'GROSS_WEIGHT_IN_KG'
        }, {
          label: 'MOQ_TO_CUSTOMER',
          value: 'MOQ_TO_CUSTOMER'
        }, {
          label: 'MINIMUM_LOT_SIZE',
          value: 'MINIMUM_LOT_SIZE'
        }, {
          label: 'RV_TO_CUSTOMER',
          value: 'RV_TO_CUSTOMER'
        }, {
          label: 'AVG_MONTHLY_ORDER_INTAKE',
          value: 'AVG_MONTHLY_ORDER_INTAKE'
        }, {
          label: 'AVG_MONTHLY_SALES',
          value: 'AVG_MONTHLY_SALES'
        }, {
          label: 'PURCHASING_GROUP',
          value: 'PURCHASING_GROUP'
        }, {
          label: 'SS3',
          value: 'SS3'
        }, {
          label: 'AMF_ONEMM',
          value: 'AMF_ONEMM'
        }, {
          label: 'AMU_ONEMM',
          value: 'AMU_ONEMM'
        }, {
          label: 'TRIGGER_BTN_DATE',
          value: 'TRIGGER_BTN_DATE'
        }, {
          label: 'TRIGGER_DELAY_DAYS_CD',
          value: 'TRIGGER_DELAY_DAYS_CD'
        }, {
          label: 'PO_COVERAGE_DAYS',
          value: 'PO_COVERAGE_DAYS'
        }, {
          label: 'OPEN_PO_QTY',
          value: 'OPEN_PO_QTY'
        }
      ]
    },
    {
      label: 'DEMAND',
      value: 'DEMAND_CATEGORY',
      children: [
        {
          label: 'Depedent Requirement',
          value: 'Dep_FCST'
        },
        {
          label: 'Indepedent Requirement',
          value: 'Ind_FCST'
        },
        {
          label: 'Open Sales Order',
          value: 'Demand_SO'
        },
        {
          label: 'Order Reservation',
          value: 'Demand_MO'
        },
        {
          label: 'Transfer PO',
          value: 'Transfer_PO'
        }
      ]
    },
    {
      label: 'OPEN_SO',
      value: 'OPEN_SO',
      children: [
        {
          label: 'OSO_CB',
          value: 'OSO_CB'
        }, {
          label: 'UD_CB',
          value: 'UD_CB'
        }, {
          label: 'UD_LONG_AGING',
          value: 'UD_LONG_AGING'
        }, {
          label: 'UD_MID_AGING',
          value: 'UD_MID_AGING'
        }, {
          label: 'OSO_NORMAL',
          value: 'OSO_NORMAL'
        }, {
          label: 'UD_NORMAL',
          value: 'UD_NORMAL'
        }
      ]
    },
    {
      label: 'ROWS',
      value: 'ROWS',
      children: [
        {
          label: 'DEMAND_TOTAL',
          value: 'DEMAND_TOTAL'
        }, {
          label: 'DEMAND_DETAILS',
          value: 'DEMAND_DETAILS'
        }, {
          label: 'SUPPLY_TOTAL',
          value: 'SUPPLY_TOTAL'
        }, {
          label: 'SUPPLY_DETAILS',
          value: 'SUPPLY_DETAILS'
        }, {
          label: 'BALANCE',
          value: 'BALANCE'
        }
      ]
    },
    {
      label: 'SUPPLY',
      value: 'CONFIRM_CAT',
      children: [
        {
          label: 'PO AB',
          value: 'AB'
        },
        {
          label: 'PO LA',
          value: 'LA'
        },
        {
          label: 'NO-AB/LA',
          value: 'NON_ABLA'
        },
        {
          label: 'MANUAL',
          value: 'MANUAL'
        },
        {
          label: 'FCST Commitment',
          value: 'FCST Commitment'
        }
      ]
    },
    {
      label: 'TRANSFER_PO',
      value: 'TRANSFER_PO',
      children: [
        {
          label: 'TRANSFER_PO_AT',
          value: 'TRANSFER_PO_AT'
        }, {
          label: 'TRANSFER_PO_Q',
          value: 'TRANSFER_PO_Q'
        }, {
          label: 'TRANSFER_PO_Z',
          value: 'TRANSFER_PO_Z'
        }, {
          label: 'TRANSFER_PO_E',
          value: 'TRANSFER_PO_E'
        }, {
          label: 'TRANSFER_PO_OTHERS',
          value: 'TRANSFER_PO_OTHERS'
        }
      ]
    }
  ],
  loading: {
    filter: false,
    report1: false
  },
  report1Data: [],
  report1Columns: [{}] as Array<any>,
  report1MergeCells: [] as Array<any>,
  report1ContextMenuItems: {
    view_details: {
      name: 'View details',
      callback: viewReport1Details,
      disabled: () => {
        return pageCtl.conditions.selected.report1Category === ''
      }
    },
    save_comments: {
      name: 'Save comments',
      callback: saveReport1Comments,
      disabled: () => {
        return Object.keys(pageCtl.report1CommentsUpdate).length === 0
      }
    },
    view_history_comments: {
      name: 'View historic comments',
      callback: viewReport1HistoryComments,
      disabled: () => {
        return pageCtl.conditions.selected.report1HistPlantCode === ''
      }
    },
    view_split0: { name: '---------' }
  },
  report1DetailsColumns: [] as Array<any>,
  report3MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload3Win
    },
    view_split: { name: '---------' }
  },
  report4MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload4Win
    },
    view_split: { name: '---------' }
  },
  report1CommentsUpdate: {},
  report1HistCommentsColumns: [{
    data: 'WEEK'
  }, {
    data: 'PLANT_CODE'
  }, {
    data: 'MATERIAL'
  }, {
    data: 'COMMENTS'
  }, {
    data: 'CREATE_BY'
  }, {
    data: 'CREATE_DATE'
  }, {
    data: 'UPDATE_BY'
  }, {
    data: 'UPDATE_DATE'
  }],
  criticalLevelCode: '',
  criticalLevelComments: '',
  criticalLevelSelected: '',
  report1AvalibleVariablesData: [] as Array<any>
})

const handleTabClick = (tab) => {
  if (tab.paneName === 'DEMAND_SUPPLY') {
    searchReport1()
  } else if (tab.paneName === 'PO_COMMITMENT') {
    searchReport3()
  } else if (tab.paneName === 'CRITICAL_MATERIAL') {
    searchReport4()
  }
}

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/simulation/plant_demand_supply/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.dayList = body.dayList
    pageCtl.weekList = body.weekList
    pageCtl.monthList = body.monthList
    pageCtl.criticalLevelList = body.criticalLevelList
    if (pageCtl.criticalLevelList.length > 0) {
      pageCtl.conditions.criticalID = pageCtl.criticalLevelList[0].VALUE
    }
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  searchReport1()
}

const searchReport1 = () => {
  clearSelection()
  pageCtl.report1CommentsUpdate = {} // 每次查询清空comments更新情况
  pageCtl.loading.report1 = true
  pageCtl.report1Columns = parseReport1Columns()
  $axios({
    method: 'post',
    url: '/simulation/plant_demand_supply/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = body
    pageCtl.report1MergeCells = [
      { row: 0, col: 0, rowspan: 1, colspan: 2 },
      { row: 1, col: 0, rowspan: 1, colspan: 2 },
      { row: 2, col: 0, rowspan: 1, colspan: 2 }
    ]
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport3 = () => {
  report3TableRef.value.search()
}

const searchReport4 = () => {
  report4TableRef.value.search()
}

const renderLineColor = (hotInstance, td, row, column, prop, value) => {
  const r = hotInstance.getSourceDataAtRow(row)
  if (td.parentNode) {
    if (r.COLOR === 0) {
      td.parentNode.setAttribute('class', 'striped-even3-tr')
    } else {
      td.parentNode.setAttribute('class', 'striped-odd3-tr')
    }
  }
  if (r.fontWeight) {
    td.style.fontWeight = r.fontWeight
    td.style.textAlign = 'right'
  }
  td.innerHTML = value
}

const renderSupply = (hotInstance, td, row, column, prop, value) => {
  value = value || 0
  const r = hotInstance.getSourceDataAtRow(row)
  td.innerHTML = $thousandBitSeparator(value, 0)
  if (r.CATEGORY && r.CATEGORY.indexOf('Supply') === 0) {
    let abPercent = 0
    let laPercent = 0
    let manualPercent = 0
    let nonPercent = 0

    const manual = r['TIPS_' + prop + '_MANUAL'] || 0
    const ab = r['TIPS_' + prop + '_AB'] || 0
    const la = r['TIPS_' + prop + '_LA'] || 0
    const non = r['TIPS_' + prop + '_NON'] || 0
    const total = manual + ab + la + non

    if (total !== 0) {
      manualPercent = manual / total * 100
      laPercent = manualPercent + la / total * 100
      abPercent = laPercent + ab / total * 100
      nonPercent = abPercent + non / total * 100

      const manualColor = '#216216'
      const laColor = '#469037'
      const abColor = '#6ba75f'
      const nonColor = '#95bf8d'

      td.setAttribute('style', 'background-image: linear-gradient(to right, ' +
          manualColor + ' ' + manualPercent + '%, ' +
          laColor + ' ' + manualPercent + '%, ' +
          laColor + ' ' + laPercent + '%, ' +
          abColor + ' ' + laPercent + '%, ' +
          abColor + ' ' + abPercent + '%, ' +
          nonColor + ' ' + abPercent + '%, ' +
          nonColor + ' ' + nonPercent + '%) !important')
      td.style.color = 'white'
    }

    let title = ''
    title += 'MANUAL: ' + $thousandBitSeparator(manual, 0) + '\r\n'
    title += 'LA: ' + $thousandBitSeparator(la, 0) + '\r\n'
    title += 'AB: ' + $thousandBitSeparator(ab, 0) + '\r\n'
    title += 'NON: ' + $thousandBitSeparator(non, 0)
    if (r.PLANT_CODE.indexOf('Supply') !== 0) {
      td.title = title
    }
  } else if (r.CATEGORY && r.CATEGORY.indexOf('Supply') === -1 && r.CATEGORY.indexOf('Balance') !== 0 && r.CATEGORY.indexOf('Transfer') !== 0) {
    let oscbPercent = 0
    let udcbPercent = 0
    let udLongPercent = 0
    let udMidPercent = 0
    let osoNormalPercent = 0
    let udNormalPercent = 0

    const oscb = r['TIPS_' + prop + '_OSO_CB'] || 0
    const udcb = r['TIPS_' + prop + '_UD_CB'] || 0
    const udLong = r['TIPS_' + prop + '_UD_LONG_AGING'] || 0
    const udMid = r['TIPS_' + prop + '_UD_MID_AGING'] || 0
    const osoNormal = r['TIPS_' + prop + '_OSO_NORMAL'] || 0
    const udNormal = r['TIPS_' + prop + '_UD_NORMAL'] || 0

    const total = oscb + udcb + udLong + udMid + osoNormal + udNormal

    if (total !== 0) {
      oscbPercent = oscb / total * 100
      udcbPercent = oscbPercent + udcb / total * 100
      udLongPercent = udcbPercent + udLong / total * 100
      udMidPercent = udLongPercent + udMid / total * 100
      osoNormalPercent = udMidPercent + osoNormal / total * 100
      udNormalPercent = osoNormalPercent + udNormal / total * 100

      const oscblColor = '#ff6e03'
      const udcbColor = '#ff7916'
      const udLongColor = '#ff8a32'
      const udMidColor = '#ff9d54'
      const osoNormalColor = '#feb278'
      const udNormalColor = '#ffc69b'

      td.setAttribute('style', 'background-image: linear-gradient(to right, ' +
          oscblColor + ' ' + oscbPercent + '%, ' +
          udcbColor + ' ' + oscbPercent + '%, ' +
          udcbColor + ' ' + udcbPercent + '%, ' +
          udLongColor + ' ' + udcbPercent + '%, ' +
          udLongColor + ' ' + udLongPercent + '%, ' +
          udMidColor + ' ' + udLongPercent + '%, ' +
          udMidColor + ' ' + udMidPercent + '%, ' +
          osoNormalColor + ' ' + udMidPercent + '%, ' +
          osoNormalColor + ' ' + osoNormalPercent + '%, ' +
          udNormalColor + ' ' + osoNormalPercent + '%, ' +
          udNormalColor + ' ' + udNormalPercent + '%) !important')
      td.style.color = 'white'
    }

    let title = ''
    title += 'OSO_CB: ' + $thousandBitSeparator(oscb, 0) + '\r\n'
    title += 'UD_CB: ' + $thousandBitSeparator(udcb, 0) + '\r\n'
    title += 'UD_LONG_AGING: ' + $thousandBitSeparator(udLong, 0) + '\r\n'
    title += 'UD_MID_AGING: ' + $thousandBitSeparator(udMid, 0) + '\r\n'
    title += 'OSO_NORMAL: ' + $thousandBitSeparator(osoNormal, 0) + '\r\n'
    title += 'UD_NORMAL: ' + $thousandBitSeparator(udNormal, 0)
    if (r.PLANT_CODE.indexOf('Demand') !== 0) {
      td.title = title
    }
  } else if (r.CATEGORY && r.CATEGORY.indexOf('Transfer') === 0) {
    let transferPOAtPercent = 0
    let transferPOQPercent = 0
    let transferPOZPercent = 0
    let transferPOEPercent = 0
    let transferPOOthersPercent = 0

    const transferPOAt = r['TIPS_' + prop + '_TRANSFER_PO_AT'] || 0
    const transferPOQ = r['TIPS_' + prop + '_TRANSFER_PO_Q'] || 0
    const transferPOZ = r['TIPS_' + prop + '_TRANSFER_PO_Z'] || 0
    const transferPOE = r['TIPS_' + prop + '_TRANSFER_PO_E'] || 0
    const transferPOOthers = r['TIPS_' + prop + '_TRANSFER_PO_OTHERS'] || 0

    const total = transferPOAt + transferPOQ + transferPOZ + transferPOE + transferPOOthers

    if (total !== 0) {
      transferPOAtPercent = transferPOAt / total * 100
      transferPOQPercent = transferPOQ / total * 100
      transferPOZPercent = transferPOZ / total * 100
      transferPOEPercent = transferPOE / total * 100
      transferPOOthersPercent = transferPOOthers / total * 100

      const transferPOAtColor = '#ff6e03'
      const transferPOQColor = '#ff7916'
      const transferPOZColor = '#ff8a32'
      const transferPOEColor = '#ff9d54'
      const transferPOOthersColor = '#feb278'

      td.setAttribute('style', 'background-image: linear-gradient(to right, ' +
          transferPOAtColor + ' ' + transferPOAtPercent + '%, ' +
          transferPOQColor + ' ' + transferPOAtPercent + '%, ' +
          transferPOQColor + ' ' + transferPOQPercent + '%, ' +
          transferPOZColor + ' ' + transferPOQPercent + '%, ' +
          transferPOZColor + ' ' + transferPOZPercent + '%, ' +
          transferPOEColor + ' ' + transferPOZPercent + '%, ' +
          transferPOEColor + ' ' + transferPOEPercent + '%, ' +
          transferPOOthersColor + ' ' + transferPOEPercent + '%, ' +
          transferPOOthersColor + ' ' + transferPOOthersPercent + '%) !important')
      td.style.color = 'white'
    }

    let title = ''
    title += 'TRANSFER_PO_AT: ' + $thousandBitSeparator(transferPOAt, 0) + '\r\n'
    title += 'TRANSFER_PO_Q: ' + $thousandBitSeparator(transferPOQ, 0) + '\r\n'
    title += 'TRANSFER_PO_Z: ' + $thousandBitSeparator(transferPOZ, 0) + '\r\n'
    title += 'TRANSFER_PO_E: ' + $thousandBitSeparator(transferPOE, 0) + '\r\n'
    title += 'TRANSFER_PO_OTHERS: ' + $thousandBitSeparator(transferPOOthers, 0) + '\r\n'
    td.title = title
  } else {
    td.title = ''
  }
  if (value < 0) {
    td.setAttribute('style', 'background-color: #c9422e !important')
    td.style.color = 'white'
  }

  td.style.textAlign = 'right'
}

const moreConditions = (expand) => {
  pageCtl.conditionsExpanded = expand
}

const clearSelection = () => {
  for (const key in pageCtl.conditions.selected) {
    if (typeof pageCtl.conditions.selected[key] === 'string') {
      pageCtl.conditions.selected[key] = ''
    } else {
      if (pageCtl.conditions.selected[key] instanceof Array) {
        pageCtl.conditions.selected[key] = []
      } else if (pageCtl.conditions.selected[key] instanceof Object) {
        pageCtl.conditions.selected[key] = {}
      }
    }
  }
}

const afterReport1Select = (r, a, b) => {
  if (r.CATEGORY !== 'Balance' && (b === 'PAST_DUE' ||
      (pageCtl.conditions.viewBy === 'By Day' && pageCtl.dayList.indexOf(b) !== -1) ||
      (pageCtl.conditions.viewBy === 'By Week' && pageCtl.weekList.indexOf(b) !== -1) ||
      (pageCtl.conditions.viewBy === 'By Month' && pageCtl.monthList.indexOf(b) !== -1))) {
    if (r.MATERIAL) {
      pageCtl.conditions.selected.report1Material = r.MATERIAL
      pageCtl.conditions.selected.report1GroupMaterial = r.GROUP_MATERIAL
      pageCtl.conditions.selected.report1Plant = r.PLANT_CODE
    } else {
      pageCtl.conditions.selected.report1Material = ''
      pageCtl.conditions.selected.report1GroupMaterial = ''
      pageCtl.conditions.selected.report1Plant = ''
    }

    pageCtl.conditions.selected.report1Date = b
    pageCtl.conditions.selected.report1Category = r.CATEGORY
  } else {
    clearSelection()
  }

  if (r.PLANT_CODE !== 'Demand Total' && r.PLANT_CODE !== 'Supply Total' && r.PLANT_CODE !== 'Balance') {
    pageCtl.conditions.selected.report1HistMaterial = r.MATERIAL
    pageCtl.conditions.selected.report1HistGroupMaterial = r.GROUP_MATERIAL
    pageCtl.conditions.selected.report1HistPlantCode = r.PLANT_CODE
  } else {
    pageCtl.conditions.selected.report1HistMaterial = ''
    pageCtl.conditions.selected.report1HistGroupMaterial = ''
    pageCtl.conditions.selected.report1HistPlantCode = ''
  }
}

const parseReport1Columns = () => {
  const columns = [{
    data: 'PLANT_CODE',
    title: 'Plant Code',
    render: renderLineColor
  }, {
    data: 'MATERIAL',
    title: 'Material'
  },
  {
    data: 'GROUP_MATERIAL',
    title: 'Group'
  },
  {
    data: 'STOCK_ON_HAND',
    title: 'Stock On Hand',
    type: 'numeric'
  },
  {
    data: 'SAFETY_STOCK',
    title: 'SS',
    type: 'numeric'
  },
  {
    data: 'AMF',
    title: 'AMF',
    type: 'numeric'
  },
  {
    data: 'AMU',
    title: 'AMU',
    type: 'numeric'
  },
  {
    data: 'MTD_ORDER_INTAKE',
    title: 'Mtd Order Intake',
    type: 'numeric'
  },
  {
    data: 'MTD_SALES',
    title: 'Mtd Sales',
    type: 'numeric'
  },
  {
    data: 'COMMENTS',
    title: 'Comments',
    width: 240,
    render: (hotInstance, td, row, column, prop, value) => {
      if (value) {
        td.title = value
      }
      td.innerHTML = value
    }
  }] as Array<any>

  if (pageCtl.conditions.viewBy === 'By Week') {
    columns.push({
      data: 'CRITICAL_LEVEL',
      title: 'Critical Level',
      width: 100
    })
  }

  columns.push({
    data: 'CATEGORY',
    title: 'Category'
  })

  const array = pageCtl.conditions.demandSupplySelections.filter(e => e[0] === 'COLUMNS').map(e => e[1])

  for (let i = 0; i < array.length; i++) {
    columns.push({
      data: array[i]
    })
  }
  // By Day || By Week || By Month
  columns.push({
    data: 'PAST_DUE',
    title: 'Past Due',
    render: renderSupply
  })
  if (pageCtl.conditions.viewBy === 'By Day') {
    for (let i = 0; i < pageCtl.dayList.length; i++) {
      const day = pageCtl.dayList[i]
      columns.push({
        data: pageCtl.dayList[i],
        title: day.substr(0, 4) + '/' + day.substr(4, 2) + '/' + day.substr(6, 2),
        render: renderSupply
      })
    }
  } else if (pageCtl.conditions.viewBy === 'By Week') {
    for (let i = 0; i < pageCtl.weekList.length; i++) {
      columns.push({
        data: pageCtl.weekList[i],
        title: pageCtl.weekList[i],
        render: renderSupply
      })
    }
  } else {
    for (let i = 0; i < pageCtl.monthList.length; i++) {
      columns.push({
        data: pageCtl.monthList[i],
        title: pageCtl.monthList[i],
        render: renderSupply
      })
    }
  }
  return columns
}

const parseReport1DetailsColumns = () => {
  if (pageCtl.conditions.selected.report1Category === 'Supply_Manual') {
    return [
      { data: 'DATE' },
      { data: 'PLANT_CODE' },
      { data: 'MATERIAL' },
      { data: 'GROUP_MATERIAL' },
      { data: 'STOCK_ON_HAND', type: 'numeric' },
      { data: 'SAFETY_STOCK', type: 'numeric' },
      { data: 'AMF', type: 'numeric' },
      { data: 'AMU', type: 'numeric' },
      { data: 'CATEGORY' },
      { data: 'MATERIAL_CATEGORY' },
      { data: 'VENDOR_CODE' },
      { data: 'VENDOR_SEARCH_NAME' },
      { data: 'VENDOR_NAME' },
      { data: 'LEAD_TIME' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_ST_PLANT' },
      { data: 'ABC' },
      { data: 'CALCULATED_ABC' },
      { data: 'CALCULATED_FMR' },
      { data: 'PROCUREMENT_TYPE' },
      { data: 'ACTIVENESS' },
      { data: 'STOCKING_POLICY' },
      { data: 'COUNTRY' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ROUNDING_VALUE' },
      { data: 'PRODUCT_LINE' },
      { data: 'ENTITY' },
      { data: 'CLUSTER_NAME' },
      { data: 'BU' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PLANT_TYPE' },
      { data: 'REPL_STRATEGY' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'QUANTITY', type: 'numeric' },
      { data: 'UNIT_COST', type: 'numeric' },
      { data: 'AVG_SELLING_PRICE_RMB', type: 'numeric' },
      { data: 'GROSS_WEIGHT_IN_KG', type: 'numeric' },
      { data: 'MANUAL_QTY', type: 'numeric' },
      { data: 'MANUAL_MVP', type: 'numeric' },
      { data: 'MANUAL_VALUE', type: 'numeric' },
      { data: 'GROSS_WEIGHT_IN_KG', type: 'numeric' }
    ]
  } else if (pageCtl.conditions.selected.report1Category === 'Supply Total' || pageCtl.conditions.selected.report1Category.indexOf('Supply') === 0) {
    return [
      { data: 'DATE' },
      { data: 'PLANT_CODE' },
      { data: 'MATERIAL' },
      { data: 'GROUP_MATERIAL' },
      { data: 'PURCH_ORDER_NUMBER' },
      { data: 'PURCH_ORDER_ITEM' },
      { data: 'STOCK_ON_HAND', type: 'numeric' },
      { data: 'SAFETY_STOCK', type: 'numeric' },
      { data: 'AMF', type: 'numeric' },
      { data: 'AMU', type: 'numeric' },
      { data: 'CATEGORY' },
      { data: 'MATERIAL_CATEGORY' },
      { data: 'VENDOR_CODE' },
      { data: 'VENDOR_SEARCH_NAME' },
      { data: 'VENDOR_NAME' },
      { data: 'LEAD_TIME' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_ST_PLANT' },
      { data: 'ABC' },
      { data: 'CALCULATED_ABC' },
      { data: 'CALCULATED_FMR' },
      { data: 'PROCUREMENT_TYPE' },
      { data: 'ACTIVENESS' },
      { data: 'STOCKING_POLICY' },
      { data: 'COUNTRY' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ROUNDING_VALUE' },
      { data: 'PRODUCT_LINE' },
      { data: 'ENTITY' },
      { data: 'CLUSTER_NAME' },
      { data: 'BU' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PLANT_TYPE' },
      { data: 'REPL_STRATEGY' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'QUANTITY', type: 'numeric' },
      { data: 'UNIT_COST', type: 'numeric' },
      { data: 'AVG_SELLING_PRICE_RMB', type: 'numeric' },
      { data: 'GROSS_WEIGHT_IN_KG', type: 'numeric' },
      { data: 'AB_QTY', type: 'numeric' },
      { data: 'LA_QTY', type: 'numeric' },
      { data: 'NON_ABLA_QTY', type: 'numeric' },
      { data: 'AB_MVP', type: 'numeric' },
      { data: 'LA_MVP', type: 'numeric' },
      { data: 'NON_ABLA_MVP', type: 'numeric' },
      { data: 'AB_NET_NET_PRICE', type: 'numeric' },
      { data: 'LA_NET_NET_PRICE', type: 'numeric' },
      { data: 'NON_ABLA_NET_NET_PRICE', type: 'numeric' },
      { data: 'MANUAL_QTY', type: 'numeric' },
      { data: 'MANUAL_MVP', type: 'numeric' },
      { data: 'MANUAL_VALUE', type: 'numeric' }
    ]
  } else {
    return [
      { data: 'DATE' },
      { data: 'MATERIAL' },
      { data: 'PLANT_CODE' },
      { data: 'GROUP_MATERIAL' },
      { data: 'DEMAND_CATEGORY' },
      { data: 'QUANTITY', type: 'numeric' },
      { data: 'ORDER_NUMBER' },
      { data: 'ORDER_ITEM' },
      { data: 'OSO_NORMAL' },
      { data: 'OSO_CB' },
      { data: 'UD_CB' },
      { data: 'UD_MID_AGING' },
      { data: 'UD_LONG_AGING' },
      { data: 'UD_NORMAL' },
      { data: 'TRANSFER_PO_AT' },
      { data: 'TRANSFER_PO_Q' },
      { data: 'TRANSFER_PO_Z' },
      { data: 'TRANSFER_PO_E' },
      { data: 'TRANSFER_PO_OTHERS' },
      { data: 'MATERIAL_CATEGORY' },
      { data: 'VENDOR_CODE' },
      { data: 'VENDOR_SEARCH_NAME' },
      { data: 'VENDOR_NAME' },
      { data: 'LEAD_TIME' },
      { data: 'LT_RANGE' },
      { data: 'MATERIAL_ST_PLANT' },
      { data: 'ABC' },
      { data: 'CALCULATED_ABC' },
      { data: 'CALCULATED_FMR' },
      { data: 'PROCUREMENT_TYPE' },
      { data: 'ACTIVENESS' },
      { data: 'STOCKING_POLICY' },
      { data: 'COUNTRY' },
      { data: 'MATERIAL_OWNER_NAME' },
      { data: 'MATERIAL_OWNER_SESA' },
      { data: 'MRP_CONTROLLER' },
      { data: 'ROUNDING_VALUE' },
      { data: 'PRODUCT_LINE' },
      { data: 'ENTITY' },
      { data: 'CLUSTER_NAME' },
      { data: 'BU' },
      { data: 'LOCAL_BU' },
      { data: 'LOCAL_PRODUCT_FAMILY' },
      { data: 'LOCAL_PRODUCT_LINE' },
      { data: 'LOCAL_PRODUCT_SUBFAMILY' },
      { data: 'PRODUCTION_LINE' },
      { data: 'PLANT_TYPE' },
      { data: 'REPL_STRATEGY' },
      { data: 'SOURCE_CATEGORY' },
      { data: 'UNIT_COST', type: 'numeric' },
      { data: 'AVG_SELLING_PRICE_RMB', type: 'numeric' },
      { data: 'GROSS_WEIGHT_IN_KG', type: 'numeric' }
    ]
  }
}

const afterReport1ChangeComments = (changes) => {
  if (changes) {
    const ht = report1TableRef.value.getHotInstance()
    changes.forEach(([row, prop, oldValue, newValue]) => {
      const r = ht.getSourceDataAtRow(row)
      if (prop === 'COMMENTS' && r.PLANT_CODE !== 'Demand Total' && r.PLANT_CODE !== 'Supply Total' && r.PLANT_CODE !== 'Balance') {
        let key
        if (r.GROUP_MATERIAL) {
          key = r.GROUP_MATERIAL + '#' + r.PLANT_CODE
        } else {
          key = r.MATERIAL + '#' + r.PLANT_CODE
        }
        pageCtl.report1CommentsUpdate[key] = r.COMMENTS
      }
    })
  }
}

const afterReport1Toggle = (viewType) => {
  if (viewType === 'back') {
    pageCtl.report1AvalibleVariablesData = [{
      TYPE: 'Supply Total',
      PASTDUE: '_sup00',
      WK01: '_sup01',
      WK02: '_sup02',
      WK03: '_sup03',
      WK04: '_sup04',
      WK05: '_sup05',
      WK06: '_sup06',
      WK07: '_sup07',
      WK08: '_sup08',
      WK09: '_sup09',
      WK10: '_sup10',
      WK11: '_sup11',
      WK12: '_sup12',
      WK13: '_sup13',
      WK14: '_sup14',
      WK15: '_sup15'
    }, {
      TYPE: 'Demand Total',
      PASTDUE: '_dem00',
      WK01: '_dem01',
      WK02: '_dem02',
      WK03: '_dem03',
      WK04: '_dem04',
      WK05: '_dem05',
      WK06: '_dem06',
      WK07: '_dem07',
      WK08: '_dem08',
      WK09: '_dem09',
      WK10: '_dem10',
      WK11: '_dem11',
      WK12: '_dem12',
      WK13: '_dem13',
      WK14: '_dem14',
      WK15: '_dem15'
    }, {
      TYPE: 'Balance',
      PASTDUE: '_ban00',
      WK01: '_ban01',
      WK02: '_ban02',
      WK03: '_ban03',
      WK04: '_ban04',
      WK05: '_ban05',
      WK06: '_ban06',
      WK07: '_ban07',
      WK08: '_ban08',
      WK09: '_ban09',
      WK10: '_ban10',
      WK11: '_ban11',
      WK12: '_ban12',
      WK13: '_ban13',
      WK14: '_ban14',
      WK15: '_ban15'
    }]
  }
}

const criticalLevelChanged = () => {
  $axios({
    method: 'post',
    url: '/simulation/plant_demand_supply/query_report1_critical_level',
    data: {
      criticalID: pageCtl.conditions.criticalID
    }
  }).then((body) => {
    pageCtl.criticalLevelCode = body.SCRIPTS
    pageCtl.criticalLevelComments = body.COMMENTS
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.report1CommentsUpdate = {}
  })
}

const _orderBy = computed(() => {
  const order = ['MATERIAL', 'STOCK_ON_HAND', 'SAFETY_STOCK', 'AMF', 'AMU', 'PAST_DUE']
  if (pageCtl.conditions.viewBy === 'By Day') {
    order.push(...pageCtl.dayList)
  } else if (pageCtl.conditions.viewBy === 'By Week') {
    order.push(...pageCtl.weekList)
  } else {
    order.push(...pageCtl.monthList)
  }
  return order
})

const _report1Height = computed(() => {
  return document.documentElement.clientHeight - 200
})

const _report3TableColumn = computed(() => {
  return [{
    data: 'ROW_ID'
  }, {
    data: 'PURCH_ORDER_NUMBER'
  }, {
    data: 'PURCH_ORDER_ITEM'
  }, {
    data: 'SEQUENTIAL_NUMBER'
  }, {
    data: 'PLANT_CODE'
  }, {
    data: 'MATERIAL'
  }, {
    data: 'QTY',
    type: 'numeric'
  }, {
    data: 'COMMIT_DATE',
    type: 'date'
  }]
})

const _report4TableColumn = computed(() => {
  return [{
    data: 'ROW_ID'
  }, {
    data: 'PLANT_CODE'
  }, {
    data: 'MATERIAL'
  }]
})

watch(() => pageCtl.conditions.viewBy, () => {
  searchReport1()
})

onBeforeMount(() => {
  pageCtl.demandSupplyOpts[2].children.sort((e1, e2) => e1.label.localeCompare(e2.label)).join('\', \'')
})

onMounted(() => {
  initPage()
})
</script>

<style lang="scss">
#damandSupply {
  .el-dropdown {
    width: 100%;

    .el-button:first-child {
      width: calc(100% - 42px)
    }
  }

  .back {
    .el-radio {
      margin-right: 0 !important;
    }
  }
}

.ratio-style {
  .el-input-group__prepend {
    width: 60%;
    padding: 0 4px !important;
    text-align: center;
  }
}

.white-backgroud {
  td {
    background-color: #fff !important;
  }
}

.gray-backgroud {
  td {
    background-color: var(--scp-bg-color-fill) !important;
  }
}
</style>
