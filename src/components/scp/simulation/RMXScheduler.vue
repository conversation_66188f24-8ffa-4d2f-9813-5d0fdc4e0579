<template>
  <div class="left-sidebar" id="rmxScheduler">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.displayTab" @tab-click="handleTabClick">
          <el-tab-pane label="Scheduler" name="SCHEDULER">
            <el-steps style="max-width: 450px" :active="pageCtl.scheduler.activeStep" process-status="finish" finish-status="wait">
              <el-step title="上传待排产订单" @click="pageCtl.scheduler.activeStep=0"/>
              <el-step title="设置排产逻辑" @click="pageCtl.scheduler.activeStep=1"/>
              <el-step title="查看排产结果" @click="pageCtl.scheduler.activeStep=2"/>
            </el-steps>
            <el-divider border-style="dashed" style="margin-top: 5px;"/>
            <!-- 上传待排产订单 -->
            <el-row v-show="pageCtl.scheduler.activeStep===0">
              <el-col :span="24">
                <scp-table
                    ref="reportTableRef"
                    :lazy="true"
                    :editable="false"
                    :context-menu-items="pageCtl.scheduler.report1ContextMenuItems"
                    url="/simulation/rmx_scheduler/query_report1"
                    download-url="/simulation/rmx_scheduler/download_report1"
                    :columns="report1Columns"
                />
              </el-col>
            </el-row>

            <el-row v-show="pageCtl.scheduler.activeStep===1" style="margin-bottom: var(--scp-widget-margin) ">
              <el-col :span="24">
                <el-date-picker
                  style="width: 200px"
                  v-model="pageCtl.scheduler.config.startDate"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  type="date"
                  :clearable="false"
                  placeholder="Date">
                </el-date-picker>
                <span class="input-tips">
                  开始排产日期, 默认从Firm订单之后的第一个工作日开始
                </span>
              </el-col>
            </el-row>

            <el-row v-show="pageCtl.scheduler.activeStep===1" style="margin-bottom: var(--scp-widget-margin)">
              <el-col :span="8">
                <!-- 多功能柜不能联排 -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      多功能柜必须穿插单功能柜
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.mult.thresholdEnable" size="small">
                        单柜功能数大于
                        <el-input v-model="pageCtl.scheduler.config.mult.threshold" class="settings-input" type="number" :min="1" :max="3"/>
                        时, 订单被识别为多功能柜订单
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
              <el-col :span="8">
                <!-- IEC -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      IEC柜
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.iec.maxCapacityEnable" size="small">
                        每个班次, IEC柜订单不能超过
                        <el-input v-model="pageCtl.scheduler.config.iec.maxCapacity" class="settings-input" type="number" :min="0" :max="60"/>
                        台
                      </el-checkbox>
                    </li>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.iec.incompatibilityEnable" size="small">
                        IEC柜订单, 不可连续排产
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
              <el-col :span="8">
                <!-- PT-Cr -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      PT-Cr柜
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.ptcr.maxCapacityEnable" size="small">
                        每个班次, PT-Cr柜订单不能超过
                        <el-input v-model="pageCtl.scheduler.config.ptcr.maxCapacity" class="settings-input" type="number" :min="0" :max="60"/>
                        台
                      </el-checkbox>
                    </li>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.ptcr.incompatibilityEnable" size="small">
                        PT-Cr/Vr柜订单, 不可连续排产
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
            </el-row>

            <el-row v-show="pageCtl.scheduler.activeStep===1" style="margin-bottom: var(--scp-widget-margin)">
              <el-col :span="8">
                <!-- Q柜 -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      Q柜
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.q.maxCapacityEnable" size="small">
                        每个班次, Q柜订单不能超过
                        <el-input v-model="pageCtl.scheduler.config.q.maxCapacity" class="settings-input" type="number" :min="0" :max="60"/>
                        功能数
                      </el-checkbox>
                    </li>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.q.incompatibilityEnable" size="small">
                        Q柜订单, 不可连续排产
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
              <el-col :span="8">
                <!-- TE -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      TE柜
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.te.maxCapacityEnable" size="small">
                        每个班次, TE柜订单不能超过
                        <el-input v-model="pageCtl.scheduler.config.te.maxCapacity" class="settings-input" type="number" :min="0" :max="60"/>
                        功能数
                      </el-checkbox>
                    </li>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.te.incompatibilityEnable" size="small">
                        TE柜订单, 不可连续排产
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
              <el-col :span="8">
                <!-- 24kv -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      V2电压等级24KV
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.v24k.maxCapacityEnable" size="small">
                        每个班次, 24KV柜订单不能超过全天产能的
                        <el-input v-model="pageCtl.scheduler.config.v24k.maxCapacity" class="settings-input" type="number" :min="0" :max="100"/>
                        %
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
            </el-row>

            <el-row v-show="pageCtl.scheduler.activeStep===1">
              <el-col :span="8">
                <!-- VB产能 -->
                <el-card class="settings-card" shadow="never">
                  <template #header>
                    <div class="card-header">
                      V/B柜最大产能
                    </div>
                  </template>
                  <ul>
                    <li>
                      <el-checkbox v-model="pageCtl.scheduler.config.vb.maxCapacityEnable" size="small">
                        V/B柜最大产能不能超过全天产能的
                        <el-input v-model="pageCtl.scheduler.config.vb.maxCapacity" class="settings-input" type="number" :min="0" :max="100"/>
                        %
                      </el-checkbox>
                    </li>
                  </ul>
                </el-card>
              </el-col>
            </el-row>

            <el-row style="margin-top: 48px; text-align: left" v-show="pageCtl.scheduler.activeStep===1">
              <el-col :span="24">
                <el-button @click="searchReport1StartSchedule" type="primary" :loading="pageCtl.scheduler.loading.scheduling">开始排产</el-button>
              </el-col>
            </el-row>

            <el-row v-show="pageCtl.scheduler.activeStep===2">
              <el-col :span="24">
                <scp-table
                    ref="reportResultTableRef"
                    :lazy="true"
                    :editable="false"
                    url="/simulation/rmx_scheduler/query_report1_result"
                    download-url="/simulation/rmx_scheduler/download_report1_result"
                    :columns="report1Columns"
                />
              </el-col>
            </el-row>

            <scp-upload
                ref="report1UploadRef"
                title="上传待排产订单"
                text="Select a file"
                w="600px"
                h="260px"
                upload-url='/simulation/rmx_scheduler/upload_report1'
                download-template-url="/simulation/rmx_scheduler/download_report1_upload_template"
                :show-btn="false"
                :on-upload-end="onReport1UploadEnd">
              <template #tip>
                <p>1. 上传之前请确认格式是否正确, 可以点击<b>&nbsp;Download Template&nbsp;</b>按钮查看模版.</p>
                <p>2. 如果上传的Excel包含多个Sheet, 系统会选取第一个Sheet的数据进行导入.</p>
              </template>
            </scp-upload>
          </el-tab-pane>
          <el-tab-pane label="Calendar" name="CALENDAR">
            <div v-loading="pageCtl.calendar.loading.query">
              <el-row style="margin-bottom: var(--scp-widget-margin)">
                <el-col :span="12">
                  <el-button @click="prevYear">
                    <font-awesome-icon icon="angle-left"/>
                  </el-button>
                  <span @click="loadCalendar" style="cursor: pointer;margin-left: 8px">{{ pageCtl.calendar.year }}</span>
                  <el-button @click="nextYear" value="year" style="margin-left: 8px">
                    <font-awesome-icon icon="angle-right"/>
                  </el-button>
                </el-col>
                <el-col :span="12" style="display: flex;justify-content: flex-end;align-content: center">
                  <div style="margin-left: 8px;line-height: 26px;">Workday：</div>
                  <div style="margin-left: 8px;background: #fff !important" class="workday"></div>
                  <div style="margin-left: 8px;line-height: 26px;">Weekend / Holiday：</div>
                  <div style="margin-left: 8px;" class="weekend"></div>
                </el-col>
              </el-row>

              <div class="monthly_box">
                <div v-if="pageCtl.calendar.data.length > 1" style="width: 100%;display: flex;flex-wrap: wrap;justify-content: space-between">
                  <div class="monthly" style="position: relative;" v-for="(month,index) in pageCtl.calendar.data" :key="index">
                    <font-awesome-layers style="
                      font-size: 6rem;
                      font-weight: bold;
                      color: rgba(0,0,0,0.1);
                      position: absolute;
                      user-select: none;
                      left: calc(100% / 2 - 3rem);
                      top: calc(50% - 4rem);
                      pointer-events: none">
                      <font-awesome-layers-text :value="index + 1"/>
                    </font-awesome-layers>
                    <table class="table table-bordered" style="border-top:0;border-left:0;">
                      <thead>
                      <tr>
                        <th v-for="(title,index) in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']" :key="index">{{ title }}</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="(week,index) in month" :key="index">
                        <td v-for="(day,index) in week" v-bind:data-fullday="day.text" :key="index"
                            v-bind:class="day.d ? (day.wd==='0'?'weekend':'workday'):''"
                            :style="{color: day.text === $dateFormatter(new Date(), 'yyyy/MM/dd')  ? 'var(--scp-text-color-success)': 'inherit',
                                   border: day.text === $dateFormatter(new Date(), 'yyyy/MM/dd')  ? '2px solid var(--scp-text-color-success)': ''}">
                          <div v-if="day.d" @click="()=>setDayCapacity(day.text)">
                            {{ day.d }}
                            <p class="day-capacity" v-html="day.capacity"/>
                          </div>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div v-else><h1 class="empty-calendar">No Calendar Defined</h1></div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="VIP Customer" name="VIP_CUSTOMER">
            <scp-datagrid bindTo="RMX_SCHED_VIP_PRIORIRY"/>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <scp-draggable-resizable w="480px" h="130px" :title="'Set Capacity - ' + pageCtl.calendar.selectedDay"
                             v-model="pageCtl.calendar.visible.modify" :save="saveCapacity" :save-loading="pageCtl.calendar.loading.save">
      <template v-slot>
        <el-row style="padding: var(--scp-widget-margin); padding-top: 0.5rem;" class="set-capacity">
          <el-col :span="3" class="title">早/中/晚</el-col>
          <el-col :span="21">
            <el-select v-model="pageCtl.calendar.capacity.shift1" style="width: 90px" filterable>
              <el-option
                  v-for="(item, index) in _capacityOpts"
                  :key="index"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
            <b>&nbsp;/&nbsp;</b>
            <el-select v-model="pageCtl.calendar.capacity.shift2" style="width: 90px" filterable>
              <el-option
                  v-for="(item, index) in _capacityOpts"
                  :key="index"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
            <b>&nbsp;/&nbsp;</b>
            <el-select v-model="pageCtl.calendar.capacity.shift3" style="width: 90px" filterable>
              <el-option
                  v-for="(item, index) in _capacityOpts"
                  :key="index"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
            <el-button @click="setHoliday" style="margin-left: 34px"
                       v-show="!(pageCtl.calendar.capacity.shift1 === '-' && pageCtl.calendar.capacity.shift2 === '-' && pageCtl.calendar.capacity.shift3 === '-')">
              一键放假
            </el-button>
            <el-button @click="setWorkdingDay" style="margin-left: 34px"
                       v-show="pageCtl.calendar.capacity.shift1 === '-' && pageCtl.calendar.capacity.shift2 === '-' && pageCtl.calendar.capacity.shift3 === '-'">
              一键上班
            </el-button>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'
import ScpDatagrid from '@/components/starter/components/Datagrid.vue'
import ScpTable from '@/components/starter/components/Table.vue'
import ScpUpload from '@/components/starter/components/Upload.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')

const reportTableRef = ref()
const reportResultTableRef = ref()
const report1UploadRef = ref()

const uploadReport1 = () => {
  report1UploadRef.value.showUploadWin()
}

const onReport1UploadEnd = () => {
  report1UploadRef.value.closeUploadWin()
  searchReport1()
  initStartDate()
}

const pageCtl = reactive({
  displayTab: 'SCHEDULER',
  scheduler: {
    activeStep: 0,
    report1ContextMenuItems: {
      upload: {
        name: '<b>Upload</b>',
        callback: uploadReport1
      },
      view_split0: { name: '---------' }
    },
    loading: {
      scheduling: false
    },
    config: {
      startDate: '',
      mult: {
        thresholdEnable: true,
        threshold: 2
      },
      iec: {
        maxCapacityEnable: true,
        maxCapacity: 4,
        incompatibilityEnable: true
      },
      ptcr: {
        maxCapacityEnable: true,
        maxCapacity: 4,
        incompatibilityEnable: true
      },
      q: {
        maxCapacityEnable: true,
        maxCapacity: 4,
        incompatibilityEnable: true
      },
      te: {
        maxCapacityEnable: true,
        maxCapacity: 7,
        incompatibilityEnable: true
      },
      v24k: {
        maxCapacityEnable: true,
        maxCapacity: 25
      },
      vb: {
        maxCapacityEnable: true,
        maxCapacity: 70
      }
    }
  },
  calendar: {
    loading: {
      query: false,
      save: false
    },
    visible: {
      modify: false
    },
    year: new Date().getFullYear(),
    data: [],
    selectedDay: '',
    capacity: {
      shift1: '',
      shift2: '',
      shift3: ''
    },
    capacityOpts: []
  }
})

const initPage = () => {
  handleTabClick({ paneName: pageCtl.displayTab })
  searchReport1ScheduleResult()
  initStartDate()
}

const initStartDate = () => {
  $axios({
    method: 'post',
    url: '/simulation/rmx_scheduler/init_page'
  }).then((body) => {
    pageCtl.scheduler.config.startDate = body.startDate
  }).catch((error) => {
    console.log(error)
  })
}

const _capacityOpts = computed(() => {
  const opts = ['-']
  for (let i = 20; i <= 80; i++) {
    opts.push(i + '')
  }
  return opts
})

const handleTabClick = (tab) => {
  if (tab.paneName === 'SCHEDULER') {
    searchReport1()
  }
}

const searchReport1 = () => {
  reportTableRef.value.search()
}

const searchReport1StartSchedule = () => {
  const config = pageCtl.scheduler.config
  if (config.mult.threshold < 0) {
    config.mult.threshold = 0
  }
  if (config.mult.threshold > 3) {
    config.mult.threshold = 3
  }
  if (config.iec.maxCapacity < 0) {
    config.iec.maxCapacity = 0
  }
  if (config.iec.maxCapacity > 60) {
    config.iec.maxCapacity = 60
  }
  if (config.ptcr.maxCapacity < 0) {
    config.ptcr.maxCapacity = 0
  }
  if (config.ptcr.maxCapacity > 60) {
    config.ptcr.maxCapacity = 60
  }
  if (config.q.maxCapacity < 0) {
    config.q.maxCapacity = 0
  }
  if (config.q.maxCapacity > 60) {
    config.q.maxCapacity = 60
  }
  if (config.te.maxCapacity < 0) {
    config.te.maxCapacity = 0
  }
  if (config.te.maxCapacity > 60) {
    config.te.maxCapacity = 60
  }
  if (config.v24k.maxCapacity < 0) {
    config.v24k.maxCapacity = 0
  }
  if (config.v24k.maxCapacity > 100) {
    config.v24k.maxCapacity = 100
  }
  pageCtl.scheduler.loading.scheduling = true
  $axios({
    method: 'post',
    url: '/simulation/rmx_scheduler/query_report1_start_schedule',
    data: {
      config: JSON.stringify(pageCtl.scheduler.config),
      startDate: pageCtl.scheduler.config.startDate
    }
  }).then(() => {
    $message.success('排产完成, 请查看结果')
    pageCtl.scheduler.activeStep = 2
    searchReport1ScheduleResult()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.scheduler.loading.scheduling = false
  })
}

const searchReport1ScheduleResult = () => {
  reportResultTableRef.value.search()
}

const prevYear = () => {
  pageCtl.calendar.year--
  loadCalendar()
}

const nextYear = () => {
  pageCtl.calendar.year++
  loadCalendar()
}

const loadCalendar = () => {
  pageCtl.calendar.loading.query = true
  $axios({
    method: 'post',
    url: '/simulation/rmx_scheduler/query_report2',
    data: {
      year: pageCtl.calendar.year
    }
  }).then((body) => {
    pageCtl.calendar.data = body as Array<any>
    for (let i = 0; i < 12; i++) {
      if (pageCtl.calendar.data[i][0][0]) {
        switch (pageCtl.calendar.data[i][0][0].w) {
          case 'Sun':
            pageCtl.calendar.data[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Sat':
            pageCtl.calendar.data[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Fri':
            pageCtl.calendar.data[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Thu':
            pageCtl.calendar.data[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Wed':
            pageCtl.calendar.data[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Tue':
            pageCtl.calendar.data[i][0].unshift('')
        }
      } else {
        break
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.calendar.loading.query = false
  })
}

const setDayCapacity = (day) => {
  $axios({
    method: 'post',
    url: '/simulation/rmx_scheduler/query_report2_day_capacity',
    data: {
      day
    }
  }).then((body) => {
    pageCtl.calendar.selectedDay = day
    pageCtl.calendar.capacity = body
    pageCtl.calendar.visible.modify = true
  }).catch((error) => {
    console.log(error)
  })
}

const saveCapacity = () => {
  pageCtl.calendar.loading.save = true
  $axios({
    method: 'post',
    url: '/simulation/rmx_scheduler/update_report2_day_capacity',
    data: {
      day: pageCtl.calendar.selectedDay,
      shift1: pageCtl.calendar.capacity.shift1,
      shift2: pageCtl.calendar.capacity.shift2,
      shift3: pageCtl.calendar.capacity.shift3
    }
  }).then(() => {
    $message.success('Capacity updated.')
    pageCtl.calendar.visible.modify = false
    loadCalendar()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.calendar.loading.save = false
  })
}

const setHoliday = () => {
  pageCtl.calendar.capacity.shift1 = '-'
  pageCtl.calendar.capacity.shift2 = '-'
  pageCtl.calendar.capacity.shift3 = '-'
}

const setWorkdingDay = () => {
  pageCtl.calendar.capacity.shift1 = '60'
  pageCtl.calendar.capacity.shift2 = '60'
  pageCtl.calendar.capacity.shift3 = '60'
}

onMounted(() => {
  initPage()
  loadCalendar()
})

const report1Columns = reactive([
  { data: 'NO', title: '序号' },
  { data: 'EXPECTED_DATE', title: '预计上线日期' },
  { data: 'ACTUAL_DATE', title: '排产上线日期' },
  { data: 'MO_NUMBER', title: '订单号' },
  { data: 'EXPECTED_FINISH_DATE', title: '预计完成日期' },
  { data: 'CUSTOMER_NAME', title: '客户' },
  { data: 'SALES_ORDER_NUMBER', title: '销售订单号' },
  { data: 'SALES_ORDER_ITEM', title: '行号' },
  { data: 'FAMILY_NAME', title: 'D+1可排' },
  { data: 'MATERIAL', title: '成品型号' },
  { data: 'MATERIAL_TYPE', title: '柜型描述' },
  { data: 'UNIT_FUNCTION_QTY', title: '功能' },
  { data: 'QTY', title: '数量' },
  { data: 'OPEN_QTY', title: '未完台数' },
  { data: 'TOTAL_OPEN_FUNCTION_QTY', title: '未完功能' },
  { data: 'FINISH_DATE', title: '完成日期' },
  { data: 'CUSTOMER_TYPE', title: '客户范围' },
  { data: 'MO_CREATE_DATE', title: 'MO创建日期' },
  { data: 'CUSTOMER_DELIVERY_DATE', title: '客户交期' },
  { data: 'STANDARD_DELIVERY_DATE', title: '标准交期' },
  { data: 'CUSTOMER_REQUEST_DATE', title: 'CRD' },
  { data: 'STATION_DESC', title: '站点信息' },
  { data: 'TYPE3', title: 'TYPE3' },
  { data: 'LOW_VOLTAGE_ROOM', title: '低压间' },
  { data: 'REMARKS', title: '变更原因' },
  { data: 'EXPECTED_PRODUCTION_DATE', title: '预计上线日期' },
  { data: 'SO_CREATE_DATE', title: 'SO创建日期' },
  { data: 'PACKAGE_INFO', title: '包材信息' },
  { data: 'VOLTAGE_LEVEL', title: '电压等级' },
  { data: 'TANK', title: 'TANK' },
  { data: 'ANNOTATIONS', title: '批注' },
  { data: 'FRONT_FRAME', title: '前框' },
  { data: 'BOTTOM_FRAME', title: '底框' },
  { data: 'SO_ITEM', title: 'SO/行号' },
  { data: 'MO_QTY', title: 'MO数量' },
  { data: 'DEMAND', title: '需求' },
  { data: 'NEW_EXCEPTION', title: '新异常' },
  { data: 'VCB', title: 'VCB' },
  { data: 'HHHH', title: 'HHHH' },
  { data: 'IIII', title: 'IIII' },
  { data: 'JJJJ', title: 'JJJJ' },
  { data: 'KKKK', title: 'KKKK' },
  { data: 'LLLL', title: 'LLLL' },
  { data: 'MMMM', title: 'MMMM' },
  { data: 'NNNN', title: 'NNNN' },
  { data: 'OOOO', title: 'OOOO' },
  { data: 'PPPP', title: 'PPPP' },
  { data: 'CUSTOMER_REQUEST_WEEK', title: '客交周' },
  { data: 'STANDARD_REQUEST_WEEK', title: '标交周' },
  { data: 'ORDER_INTAKE_WEEK', title: '进单周' },
  { data: 'TTTT', title: 'TTTT' },
  { data: 'UUUU', title: 'UUUU' }
])

</script>

<style lang="scss">
#rmxScheduler {
  .not-full {
    color: var(--scp-text-color-success) !important;
  }

  .overload {
    color: var(--scp-text-color-error) !important;
  }

  .el-step__head, .el-step__title {
    font-size: inherit !important;
    cursor: pointer;
  }

  .settings-card {
    width: calc(100% - var(--scp-widget-margin));
    height: 100px;
  }

  .settings-input {
    width: 54px;
    margin: 0 3px;
  }

  .el-card__header {
    padding: calc(var(--scp-widget-margin) / 1.5) !important;

    .el-checkbox__label {
      font-weight: bold !important;
    }
  }

  .card-header {
    font-weight: bold !important;
  }

  .el-card__body {
    padding: var(--scp-widget-margin) !important;
  }
}
</style>

<style lang="scss" scoped>
#rmxScheduler {
  .set-capacity {
    padding-top: 15px;

    .title {
      text-align: center;
      font-weight: bold;
      line-height: 2;
    }
  }

  .calendar-item-box {
    padding: 0
  }

  .calendar-item {
    line-height: 1rem !important;
    padding-right: 0.833rem !important;
    font-size: 0.5rem !important;
  }

  .hover-icon {
    opacity: 0
  }

  .calendar-item:hover .hover-icon {
    opacity: 1
  }

  .workday {
    width: 0.75rem;
    height: 0.75rem;
    background: #fff;
    margin-top: 3px;
    border: 1px solid var(--scp-border-color-lighter);
    box-shadow: 1px 1px 2px var(--scp-border-color-lighter);
  }

  .weekend {
    width: 0.75rem;
    height: 0.75rem;
    background: #ffd980;
    margin-top: 3px;
    border: 1px solid var(--scp-border-color-lighter);
    box-shadow: 1px 1px 2px var(--scp-border-color-lighter);
  }

  .monthly_box {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    align-items: stretch
  }

  .monthly_box .monthly {
    width: calc(100% / 4 - 8px);
    background: no-repeat center;
    box-sizing: border-box;
  }

  .monthly_box .monthly table {
    width: 100%;
    text-align: center;
  }

  .monthly_box .monthly table tr th, .monthly_box .monthly table tr td {
    text-align: center;
    line-height: 1.42857143;
    background-color: rgba(240, 240, 240, 0.6);
    vertical-align: middle;
    border-bottom: 1px solid var(--scp-border-color-lighter);
  }

  .monthly_box .monthly table tr td {
    font-size: 0.625rem;
    padding: 5px !important;
    width: 1.333rem;
    height: 1.75rem;

    .day-capacity {
      font-size: 0.35rem;
      color: #a1a1a1;
      font-style: italic;
    }
  }

  .monthly_box .monthly table tr td:hover {
    cursor: pointer
  }

  .monthly_box .monthly table tr td.weekend {
    background-color: rgba(255, 217, 128, 0.5) !important;
  }

  .monthly_box .monthly table tr td.workday {
    background-color: rgba(255, 255, 255, 0) !important;
  }

  .monthly_box .monthly table tr td p {
    margin-bottom: 0;
    font-size: 0.333rem;
  }

  .fa-layers {
    color: rgba(151, 159, 158, 0.3) !important;
  }

  .empty-calendar {
    color: var(--scp-text-color-lighter);
    height: 12.5rem;
    line-height: 12.5rem;
    font-size: 2rem;
  }
}
</style>
