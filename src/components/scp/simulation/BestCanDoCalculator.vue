<template>
  <div class="left-sidebar" id="best_can_do">
    <div class="widget">
      <div class="widget-body">
        <el-container style="height: calc(100% - 10px)">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);" class="left-container">
            <scp-tree-menu
              ref="treeRef"
              url="/simulation/best_can_do_calculator/query_batch_list"
              :new-click="showNewBatch"
              :node-click="clickNode"
            >
              <template v-slot="{ node, data }">
                <span class="custom-tree-node" :title="infoOverview(data.key)">
                    <slot :node="node" :data="data">
                      <span>{{ node.label }}
                        <span v-if="data.subLabel === 'not start'" style="color: var(--scp-text-color-secondary) !important; font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{data.subLabel}}</span>
                        <span v-if="data.subLabel === 'processing'" style="color: var(--scp-text-color-success) !important; font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{data.subLabel}}</span>
                        <span v-if="data.subLabel === 'finished'" style="color: var(--scp-text-color-highlight) !important; font-size: 10px;margin-left:5px" v-show="!!data.subLabel">{{data.subLabel}}</span>
                        <span v-show="data.children.length > 0">({{ data.children.length }})</span>
                        <span v-show="!!data.subLabel">
                            <div class="pull-right" style="width: 96px;text-align: right">
                               <el-tooltip class="item" effect="light" :show-after="1000" content="Share Variant">
                                 <font-awesome-icon class="add-name" style="line-height:24px;margin-right: 8px" icon="fa-solid fa-share-nodes"
                                                    @click="shareCondition(data.key, data.label)"/>
                               </el-tooltip>
                            </div>
                        </span>
                      </span>
                    </slot>
                </span>
              </template>
            </scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0 var(--scp-widget-margin)">
            <h1 v-show="!pageCtl.conditions.batchId" style="text-align: center; color: var(--scp-text-color-lighter);height: 300px;line-height: 300px; font-size: xx-large">Select or Create a Simulation</h1>
            <el-tabs v-show="!!pageCtl.conditions.batchId" v-model="pageCtl.currentTab" @tab-click="handleTabClick">
              <el-tab-pane label="Restriction & Demand" name="first">
                <div v-show="!!pageCtl.selectedBatchInfo['USER_NAME']" v-loading="pageCtl.loading.selectedBatchInfo" style="margin-bottom: var(--scp-widget-margin); display: flex; align-items: center;">
                  <h3 style="margin-right: 35px;display: inline;">
                    {{ pageCtl.selectedBatchInfo['NAME'] }}
                    <span class="maintain-by"> by {{ pageCtl.selectedBatchInfo['USER_NAME'] }}({{ pageCtl.selectedBatchInfo['SESA_CODE'] }})</span>
                  </h3>
                  <el-button size="small" @click="sendSimulateRequest" type="primary" :loading="_selectedBatchExecting">
                    <font-awesome-icon :icon="['far', 'pen-to-square']" />&nbsp;
                    Simulate
                  </el-button>
                  <el-popconfirm title="确定删除此任务?"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="deleteBatch"
                                 confirmButtonType="danger"
                                 cancelButtonType="primary"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'
                                 style="margin-left:15px">
                    <template #reference>
                      <el-button type="danger" size="small" :loading="pageCtl.loading.delete" v-show="!_selectedBatchExecting">
                        <font-awesome-icon :icon="['fa-solid', 'fa-xmark']"/>&nbsp;
                        Delete
                      </el-button>
                    </template>
                  </el-popconfirm>
                  <span v-show="pageCtl.selectedBatchInfo.MODULE==='module2'|| pageCtl.selectedBatchInfo.MODULE=== 'module3'"
                        style="margin-left: 30px; margin-right: 5px;">平衡系数:</span>
                  <el-col :span="2">
                    <el-input-number v-show="pageCtl.selectedBatchInfo.MODULE==='module2'|| pageCtl.selectedBatchInfo.MODULE=== 'module3'"
                      v-model="pageCtl.conditions.balanceValue" size="small" :precision="0" :step="10" :max="1000" :min="0"
                                     style="margin-left: 5px; width: var(--scp-input-width) !important;"/>
                  </el-col>
                </div>
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin)"  v-show="pageCtl.selectedBatchInfo.MODULE==='module1'|| pageCtl.selectedBatchInfo.MODULE=== null">
                  <div class="subscript-title">Demand Input</div>
                  <scp-subscript id="SCDP"/>
                  <scp-table
                    ref="report1TableRef"
                    :params="pageCtl.conditions"
                    url="/simulation/best_can_do_calculator/query_report1"
                    download-url="/simulation/best_can_do_calculator/download_report1"
                    save-url="/simulation/best_can_do_calculator/save_report1"
                    :lazy="true"
                    :contextMenuItems="pageCtl.report1MenuItems"
                    :columns="pageCtl.report1Column"
                    :hiddenColumns="{columns: [0]}"
                    :update-data-enable="true"
                    :create-data-enable="true"/>
                </div>

                <div class="subscript-container">
                  <div class="subscript-title">BOM List</div>
                  <scp-subscript id="SCDD"/>
                  <scp-table
                    :params="pageCtl.conditions"
                    url="/simulation/best_can_do_calculator/query_report2"
                    download-url="/simulation/best_can_do_calculator/download_report2"
                    save-url="/simulation/best_can_do_calculator/save_report2"
                    ref="report2TableRef"
                    :contextMenuItems="pageCtl.report2MenuItems"
                    :lazy="true"
                    :columns="pageCtl.report2Column"
                    :hiddenColumns="{columns: [0]}"
                    :update-data-enable="true"
                    :create-data-enable="true"/>
                </div>
                <div class="subscript-container" v-show="pageCtl.selectedBatchInfo.MODULE==='module3'">
                  <div class="subscript-title">Fix Recourse List</div>
                  <scp-subscript id="SCDA"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/simulation/best_can_do_calculator/query_report11"
                      download-url="/simulation/best_can_do_calculator/download_report11"
                      save-url="/simulation/best_can_do_calculator/save_report11"
                      ref="report11TableRef"
                      :contextMenuItems="pageCtl.report11MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report11Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
                <div class="subscript-container" v-show="pageCtl.selectedBatchInfo.MODULE==='module1'|| pageCtl.selectedBatchInfo.MODULE=== null">
                  <div class="subscript-title">Recourse Input</div>
                  <scp-subscript id="SCDA"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/simulation/best_can_do_calculator/query_report3"
                      download-url="/simulation/best_can_do_calculator/download_report3"
                      save-url="/simulation/best_can_do_calculator/save_report3"
                      ref="report3TableRef"
                      :contextMenuItems="pageCtl.report3MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report3Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>

                <div class="subscript-container" v-show="pageCtl.selectedBatchInfo.MODULE==='module2' || pageCtl.selectedBatchInfo.MODULE==='module3'">
                  <div class="subscript-title">Recourse Input</div>
                  <scp-subscript id="SCDA"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      url="/simulation/best_can_do_calculator/query_report9"
                      download-url="/simulation/best_can_do_calculator/download_report9"
                      save-url="/simulation/best_can_do_calculator/save_report9"
                      ref="report9TableRef"
                      :contextMenuItems="pageCtl.report9MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report9Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
              </el-tab-pane>
              <el-tab-pane label="BCD Log" name="second">
                <pre id="batchLogsPre" :style="{height: _batchLogsHeight + 'px', overflow: 'auto'}" v-loading="pageCtl.loading.logging">{{ pageCtl.batchLogs.join('\r\n') }}
                </pre>
              </el-tab-pane>
              <el-tab-pane label="Result" name="third">
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);" v-show="pageCtl.selectedBatchInfo.MODULE==='module1' || pageCtl.selectedBatchInfo.MODULE===null">
                  <div class="subscript-title">Best Can Do</div>
                  <scp-subscript id="SCDR"/>
                  <scp-table
                    :download-specify-column="false"
                    :params="pageCtl.conditions"
                    url="/simulation/best_can_do_calculator/query_report4"
                    download-url="/simulation/best_can_do_calculator/download_report4"
                    ref="report4TableRef"
                    :lazy="true"
                    :columns="pageCtl.report4Column"
                    :editable="false"/>
                </div>

                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);" v-show="pageCtl.selectedBatchInfo.MODULE==='module1' || pageCtl.selectedBatchInfo.MODULE===null">
                  <div class="subscript-title">Recourse Shortage</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                    :download-specify-column="false"
                    :params="pageCtl.conditions"
                    url="/simulation/best_can_do_calculator/query_report5"
                    download-url="/simulation/best_can_do_calculator/download_report5"
                    ref="report5TableRef"
                    :lazy="true"
                    :columns="pageCtl.report5Column"
                    :editable="false"/>
                </div>

                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);" v-show="pageCtl.selectedBatchInfo.MODULE==='module2' || pageCtl.selectedBatchInfo.MODULE==='module3'">
                  <div class="subscript-title">Output Max</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :download-specify-column="false"
                      :params="pageCtl.conditions"
                      url="/simulation/best_can_do_calculator/query_report10"
                      download-url="/simulation/best_can_do_calculator/download_report10"
                      ref="report10TableRef"
                      :lazy="true"
                      :columns="pageCtl.report10Column"
                      :editable="false"/>
                </div>

                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);">
                  <div class="subscript-title">Remain Recourse</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :download-specify-column="false"
                      :params="pageCtl.conditions"
                      url="/simulation/best_can_do_calculator/query_report8"
                      download-url="/simulation/best_can_do_calculator/download_report8"
                      ref="report8TableRef"
                      :lazy="true"
                      :columns="pageCtl.report8Column"
                      :editable="false"/>
                </div>
              </el-tab-pane>
              <el-tab-pane label="Parameter" name="fifth">
                <pre v-loading="pageCtl.loading.selectedBatchInfo">{{pageCtl.selectedBatchInfo['PARAMS']}}</pre>
              </el-tab-pane>
              <el-tab-pane label="Configuration" name="fourth">
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);">
                  <div class="subscript-title">Task History</div>
                  <scp-table
                    :params="pageCtl.conditions"
                    :max-height="600"
                    ref="taskQueueTableRef"
                    url="/simulation/best_can_do_calculator/query_task_queue"
                    :lazy="true"
                    :columns="pageCtl.taskQueueColumns"/>
                </div>
                <div class="subscript-container" style="margin-bottom: var(--scp-widget-margin);">
                  <div class="subscript-title">Component Substitution</div>
                  <scp-subscript id="SCDE"/>
                  <scp-table
                      :params="pageCtl.conditions"
                      :download-specify-column="false"
                      url="/simulation/best_can_do_calculator/query_report7"
                      download-url="/simulation/best_can_do_calculator/download_report7"
                      save-url="/simulation/best_can_do_calculator/save_report7"
                      ref="report7TableRef"
                      :contextMenuItems="pageCtl.report7MenuItems"
                      :lazy="true"
                      :columns="pageCtl.report7Column"
                      :hiddenColumns="{columns: [0]}"
                      :update-data-enable="true"
                      :create-data-enable="true"/>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-main>
        </el-container>
      </div>
    </div>

    <!-- new batch -->
    <scp-draggable-resizable w="700px" h="550px" v-model="pageCtl.visible.newBatch" title="New Task"
                             save-text="Create Task" :save="createBatch" :save-loading="pageCtl.loading.newBatch">
      <template v-slot="{height}">
        <el-form ref="newBatchForm" :model="pageCtl.newBatchForm" label-width="150px" :style="{ height: height - 120 + 'px'}" style="padding:20px 15px 5px 5px">
          <el-form-item label="New Task Name" required prop="name">
            <el-row>
              <el-col :span="16">
                <el-input v-model="pageCtl.newBatchForm.name" placeholder="Task Name" size="small" style="width: 100%"></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Group" required prop="groups">
            <el-row>
              <el-col :span="16">
                <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newBatchForm.groups"
                  style="width: 100%"
                  :maxlength="30"
                  size="small"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Task Group"
                  show-word-limit
                ></el-autocomplete>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Plant Code" required prop="plant">
            <el-row>
              <el-col :span="16">
                <el-select size="small" v-model="pageCtl.newBatchForm.plant" placeholder="Plant" style="width: 100%;" filterable>
                  <el-option
                      v-for="item in pageCtl.plantOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="Algorithm Module" required prop="module">
            <el-row>
              <el-col :span="16">
                <el-select v-model="pageCtl.newBatchForm.module" size="small" style="width: 100%;" placeholder="Mode" >
                  <el-option label="输入需求,BOM,原材料计算能生产的最大值（偏向于缺料模拟）" value="module1"/>
                  <el-option label="输入Bom,原材料,计算生产多少能使库存最低（偏向于退市模拟）" value="module2"/>
                  <el-option label="输入Bom,原材料,补齐什么原材料能使消耗最大(偏向于高价值物料消耗)" value="module3" />
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item   v-if="['module2'].includes(pageCtl.newBatchForm.module)">
          <el-col :span="5">
            <el-checkbox v-model="pageCtl.newBatchForm.addPO" disabled>Add Open PO Qty </el-checkbox>
          </el-col>
            <el-col :span="5">
              <el-checkbox v-model="pageCtl.newBatchForm.minusFcst" disabled>Minus Rolling FCST </el-checkbox>
            </el-col>
          </el-form-item>
          <el-form-item  label="出货方式" required prop="type" v-if="['module1'].includes(pageCtl.newBatchForm.module)">
            <el-row>
              <el-col :span="16">
                <el-select v-model="pageCtl.newBatchForm.type" size="small" style="width: 100%" placeholder="出货方式">
                  <el-option label="整行出货" value="WHOLE_LINE"/>
                  <el-option label="分批出货" value="SPLIT_LINE"/>
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item  label="计算策略" required prop="strategy" v-if="['module1'].includes(pageCtl.newBatchForm.module)">
            <el-row>
              <el-col :span="16">
                <el-select v-model="pageCtl.newBatchForm.strategy" size="small" style="width: 100%" placeholder="计算策略">
                  <el-option label="QTY BEST" value="QTY_BEST"/>
                  <el-option label="VALUE BEST" value="VALUE_BEST"/>
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </template>
    </scp-draggable-resizable>

    <!-- upload report1 -->
    <scp-upload
        ref="upload1Ref"
        title="Upload Demand input"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/simulation/best_can_do_calculator/upload_report1'
        download-template-url='/simulation/best_can_do_calculator/download_report1_template'
        :on-upload-end="() => report1TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <!-- upload report2 -->
    <scp-upload
        ref="upload2Ref"
        title="Upload BOM List"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/simulation/best_can_do_calculator/upload_report2'
        download-template-url='/simulation/best_can_do_calculator/download_report2_template'
        :on-upload-end="() => report2TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <!-- upload report11 -->
    <scp-upload
        ref="upload11Ref"
        title="Upload Fix Recourse List"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/simulation/best_can_do_calculator/upload_report11'
        download-template-url='/simulation/best_can_do_calculator/download_report11_template'
        :on-upload-end="() => report11TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>

    <!-- upload report3 -->
    <scp-upload
        ref="upload3Ref"
        title="Upload Recourse Input"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/simulation/best_can_do_calculator/upload_report3'
        download-template-url='/simulation/best_can_do_calculator/download_report3_template'
        :on-upload-end="() => report3TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <!-- upload report9 -->
    <scp-upload
        ref="upload9Ref"
        title="Upload Recourse Input"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/simulation/best_can_do_calculator/upload_report9'
        download-template-url='/simulation/best_can_do_calculator/download_report9_template'
        :on-upload-end="() => report9TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <scp-upload
        ref="upload7Ref"
        title="Upload Component Substitution"
        :show-btn="false"
        w="600px"
        h="200px"
        upload-url='/simulation/best_can_do_calculator/upload_report7'
        download-template-url='/simulation/best_can_do_calculator/download_report7_template'
        :on-upload-end="() => report7TableRef.search()"
        :upload-params="{'batchId' : pageCtl.conditions.batchId}">
      <template #tip>
        <p>This file will <b>replace</b> the data you uploaded before</p>
      </template>
    </scp-upload>
    <!-- report1 details table -->
    <scp-draggable-resizable w="60vw" h="450px"
      v-model="pageCtl.visible.compareOverviewDetails"
      :title="pageCtl.compareOverviewDetailsTitle">
      <template v-slot="{height}">
        <scp-table
          style="margin-bottom:10px;"
          :max-height="height - 150"
          ref="compareOverviewDetailsTableRef"
          :lazy="true"
          url="/simulation/best_can_do/compare_overview_details"
          download-url="/simulation/best_can_do/download_compare_overview_details"
          :params="pageCtl.conditions"
          :columns="pageCtl.compareOverviewDetailsColumns"/>
      </template>
    </scp-draggable-resizable>

    <!-- compare1 details -->
    <scp-draggable-resizable w="60vw" h="450px"
      :title="pageCtl.compare1DetailsTitle"
      v-model="pageCtl.compare1DetailsVisible">
      <template v-slot="{height}">
        <scp-table
          style="margin-bottom:10px;"
          :max-height="height - 150"
          ref="compare1DetailsTableRef"
          url="/simulation/best_can_do/compare_report_1_details"
          download-url="/simulation/best_can_do/download_compare_report_1_details"
          :lazy="true"
          :params="pageCtl.conditions"
          :columns="pageCtl.compare1DetailsColumns"/>
      </template>
    </scp-draggable-resizable>

    <!-- compare2 details -->
    <scp-draggable-resizable w="60vw" h="450px"
        :title="pageCtl.compare2DetailsTitle"
        v-model="pageCtl.compare2DetailsVisible">
      <template v-slot="{height}">
        <scp-table
          style="margin-bottom:10px;"
          :max-height="height - 150"
          ref="compare2DetailsTableRef"
          url="/simulation/best_can_do/compare_report_2_details"
          download-url="/simulation/best_can_do/download_compare_report_2_details"
          :lazy="true"
          :params="pageCtl.conditions"
          :columns="pageCtl.compare2DetailsColumns"/>
      </template>
    </scp-draggable-resizable>

    <!-- compare3 details -->
    <scp-draggable-resizable w="60vw" h="450px"
                             :title="pageCtl.compare3DetailsTitle"
      v-model="pageCtl.compare3DetailsVisible">
      <template v-slot="{height}">
        <scp-table
          style="margin-bottom:10px;"
          :max-height="height - 150"
          ref="compare3DetailsTableRef"
          url="/simulation/best_can_do/compare_report_3_details"
          download-url="/simulation/best_can_do/download_compare_report_3_details"
          :lazy="true"
          :params="pageCtl.conditions"
          :columns="pageCtl.compare3DetailsColumns"/>
      </template>
    </scp-draggable-resizable>

    <!-- compare4 details -->
    <scp-draggable-resizable w="60vw" h="450px"
      v-model="pageCtl.compare4DetailsVisible"
      :title="pageCtl.compare4DetailsTitle">
      <template v-slot="{height}">
        <scp-table
          style="margin-bottom:10px;"
          :max-height="height - 150"
          ref="compare4DetailsTableRef"
          url="/simulation/best_can_do/compare_report_4_details"
          download-url="/simulation/best_can_do/download_compare_report_4_details"
          :lazy="true"
          :params="pageCtl.conditions"
          :columns="pageCtl.compare4DetailsColumns"/>
      </template>
    </scp-draggable-resizable>
    <!-- share email-->
    <scp-draggable-resizable v-model="visibleCtl.share" :save="shareConditionAction" :save-loading="loadingCtl.share" save-text="Share"
                             w="60vw" h="600px" :title="data.shareTitle">
      <template v-slot="{height}">
        <div style="padding: 5px 5px 0 5px;">
          <el-row>
            <el-col :span="24">
              <el-select v-model="data.sharedUsers" :placeholder="loadingCtl.query ? 'Loading...' : 'Share to'" style="width: 100% !important;"
                         collapse-tags clearable multiple filterable>
                <el-option
                    v-for="user in data.allUsers" :key="user['VAL']" :label="user['LABEL']" :value="user['VAL']">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="24">
              <scp-ck-editor v-model="data.remarks" :style="{height: (height - 180) + 'px'}"/>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import mqtt from 'mqtt'
import ScpUpload from '@/components/starter/components/Upload.vue'
import { computed, inject, onMounted, reactive, ref, nextTick, watch } from 'vue'
import search from '@/components/starter/components/Search.vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'

const $thousandBitSeparator:any = inject('$thousandBitSeparator')
const $shortenNumber:any = inject('$shortenNumber')
const $px2Rem:any = inject('$px2Rem')
const $randomString:any = inject('$randomString')
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $isEmpty: any = inject('$isEmpty')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')

const compare1ChartRef = ref()
const compare2ChartRef = ref()
const compare3ChartRef = ref()
const compare4LeftChartRef = ref()
const compare4RightChartRef = ref()
const compare1DetailsTableRef = ref()
const compare2DetailsTableRef = ref()
const compare3DetailsTableRef = ref()
const compare4DetailsTableRef = ref()
const compareOverviewDetailsTableRef = ref()
const treeRef = ref()
const report1TableRef = ref()
const report2TableRef = ref()
const report11TableRef = ref()
const report3TableRef = ref()
const report4TableRef = ref()
const report5TableRef = ref()
const report10TableRef = ref()
const report6TableRef = ref()
const report7TableRef = ref()
const report8TableRef = ref()
const report9TableRef = ref()
const aptExcludeTableRef = ref()
const taskQueueTableRef = ref()
const upload1Ref = ref()
const upload2Ref = ref()
const upload11Ref = ref()
const upload3Ref = ref()
const upload9Ref = ref()
const upload7Ref = ref()

onMounted(() => {
  connectMqtt()
  initPage()
  initExistsGroup()
  pageCtl.pageWidth = document.documentElement.clientWidth - 360
})

const showUpload1Win = () => {
  upload1Ref.value.showUploadWin()
}

const showUpload2Win = () => {
  upload2Ref.value.showUploadWin()
}

const showUpload11Win = () => {
  upload11Ref.value.showUploadWin()
}

const showUpload3Win = () => {
  upload3Ref.value.showUploadWin()
}

const showUpload9Win = () => {
  upload9Ref.value.showUploadWin()
}

const showUpload7Win = () => {
  upload7Ref.value.showUploadWin()
}

const pageCtl = reactive({
  pageWidth: 0 as any,
  currentTab: '',
  client: {} as any,
  moOpts: [],
  soOpts: [],
  poOpts: [],
  plantOpts: [],
  existsGroup: [],
  bindTable: 'BCD_REMOVE_RM',
  visible: {
    newBatch: false,
    simulate: false,
    compareOverviewDetails: false
  },
  loading: {
    filter: false,
    logging: false,
    newBatch: false,
    delete: false,
    simulateRequest: false,
    selectedBatchInfo: false,
    compare1: false,
    compare2: false,
    compare3: false,
    compare4: false,
    compareOverview: false
  },
  firstLoad: {
    tab1: false,
    tab2: false,
    tab3: false,
    tab4: false,
    tab6: false
  },
  compare1DetailsVisible: false,
  compare2DetailsVisible: false,
  compare3DetailsVisible: false,
  compare4DetailsVisible: false,
  compareOverviewDetailsTitle: '',
  compare1DetailsTitle: '',
  compare2DetailsTitle: '',
  compare3DetailsTitle: '',
  compare4DetailsTitle: '',
  compareOverviewDetailsColumns: [
    { data: 'BATCH_ID' },
    { data: 'MO' },
    { data: 'PLANT_CODE' },
    { data: 'MATERIAL' },
    { data: 'LEGEND' },
    { data: 'QTY' },
    { data: 'BCD_LEVEL_A' },
    { data: 'BCD_LEVEL_B' },
    { data: 'BCD_LEVEL_C' },
    { data: 'MRP_CONTROLLER' },
    { data: 'PLANNED_DELIV_TIME' },
    { data: 'LT_RANGE' },
    { data: 'VALUE' }
  ],
  compare1DetailsColumns: [
    { data: 'BATCH_ID' },
    { data: 'MO' },
    { data: 'PLANT_CODE' },
    { data: 'MATERIAL' },
    { data: 'LEGEND' },
    { data: 'QTY' },
    { data: 'BCD_LEVEL_A' },
    { data: 'BCD_LEVEL_B' },
    { data: 'BCD_LEVEL_C' },
    { data: 'MRP_CONTROLLER' },
    { data: 'PLANNED_DELIV_TIME' },
    { data: 'LT_RANGE' },
    { data: 'VALUE' }
  ],
  compare2DetailsColumns: [
    { data: 'BATCH_ID' },
    { data: 'PLANT_CODE' },
    { data: 'MATERIAL' },
    { data: 'REQUIREMENT' },
    { data: '"SOH+PO"' },
    { data: 'USAGE' },
    { data: 'WIP_USAGE' },
    { data: 'WIP_PROD' },
    { data: 'SHORTAGE_AFFECTED_LINE' },
    { data: 'SOH_REST' },
    { data: 'SHORTAGE' },
    { data: 'MRP_CONTROLLER' },
    { data: 'MRP_GROUP' },
    { data: 'BCD_LEVEL_A' },
    { data: 'BCD_LEVEL_B' },
    { data: 'BCD_LEVEL_C' },
    { data: 'UNIT_COST' }
  ],
  compare3DetailsColumns: [
    { data: 'BATCH_ID' },
    { data: 'MO' },
    { data: 'PLANT_CODE' },
    { data: 'REQUEST_DATE' },
    { data: 'MATERIAL' },
    { data: 'BOM_COMPONENT' },
    { data: 'MRP_CONTROLLER' },
    { data: 'MRP_TYPE' },
    { data: 'LT_RANGE' },
    { data: 'UNIT_COST' },
    { data: 'BCD_LEVEL_A' },
    { data: 'BCD_LEVEL_B' },
    { data: 'BCD_LEVEL_C' },
    { data: 'REQUEST_QTY' },
    { data: 'REQUIREMENT' },
    { data: 'REQUIREMENT_SUMOVER' },
    { data: 'SOH_LEFT_PCS' },
    { data: 'USED_QTY', type: 'numeric' },
    { data: 'SOH_PCS' },
    { data: 'AFFECTED_FG_PCS' },
    { data: 'RM_SHORTAGE' }
  ],
  compare4DetailsColumns: [
    { data: 'BATCH_ID' },
    { data: 'PLANT_CODE' },
    { data: 'MATERIAL' },
    { data: 'ADD_STEPS' },
    { data: 'SOH_LEFT' },
    { data: 'USAGE' },
    { data: 'WIP_PROD' },
    { data: 'UNIT_COST' }
  ],
  OverviewData: {
    LEFT_BCD: 0,
    LEFT_DEMAND: 0,
    LEFT_SEMI_FINISH_GOODS: 0,
    RIGHT_BCD: 0,
    RIGHT_DEMAND: 0,
    RIGHT_SEMI_FINISH_GOODS: 0
  },
  conditions: {
    leafDepth: 2,
    comparePage: false,
    compare1XAxisType: 'MRP_CONTROLLER',
    compare2XAxisType: 'MATERIAL',
    compare3XAxisType: 'BCD_LEVEL_A',
    compareOverviewDetailsType: '',
    compare1SelectedSeriesName: '',
    compare1SelectedName: '',
    compare2SelectedSeriesName: '',
    compare2SelectedName: '',
    compare3SelectedSeriesName: '',
    compare3SelectedName: '',
    compare4SelectedSeriesName: '',
    compare4SelectedName: '',
    compare4SelectedTaskId: [] as Array<any>,
    xAxisType1List: ['BCD_LEVEL_A', 'BCD_LEVEL_B', 'BCD_LEVEL_C', 'MRP_CONTROLLER'],
    xAxisType2List: ['BCD_LEVEL_A', 'BCD_LEVEL_B', 'BCD_LEVEL_C', 'MRP_CONTROLLER', 'MATERIAL'],
    batchId: '',
    module: '',
    taskNameList: [],
    taskInfo: [],
    selectedValue: '',
    compare1Data: [] as any,
    selectedxAxis: [],
    xAxisOpts: [],
    compare3SelectedxAxis: [],
    compare3SelectedxAxisOpts: [],
    compareTaskL: [] as Array<any>,
    compareTaskR: [] as Array<any>,
    level1: '',
    level2: '',
    report1Tooltips: [],
    compare2Data: [] as any,
    compare3Data: [] as any,
    compareValue: 'QTY',
    compareValueOpts: ['QTY', 'VALUE', 'LINE'],
    compare4LeftData: [] as any,
    compare4RightData: [] as any,
    balanceValue: 0
  },
  selectedBatchInfo: {
    NAME: '',
    USER_NAME: '',
    SESA_CODE: '',
    WORK_MODE: '',
    PARAMS: '',
    MODULE: ''
  } as any,
  newBatchForm: {
    name: '',
    groups: '',
    plant: '',
    module: '',
    strategy: 'QTY_BEST',
    type: 'WHOLE_LINE',
    dateRange: [],
    priority: [],
    step: '',
    step_mode: '',
    addPO: '',
    minusFcst: ''
  },
  report1MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload1Win
    },
    view_split: { name: '---------' }
  },
  report2MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload2Win
    },
    view_split: { name: '---------' }
  },
  report11MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload11Win
    },
    view_split: { name: '---------' }
  },
  report3MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload3Win
    },
    view_split: { name: '---------' }
  },
  report9MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload9Win
    },
    view_split: { name: '---------' }
  },
  report7MenuItems: {
    upload: {
      name: 'Upload',
      callback: showUpload7Win
    },
    view_split: { name: '---------' }
  },
  report1Column: [
    { data: 'ROW_ID' },
    { data: 'PRIMARY_KEY' },
    { data: 'MATERIAL' },
    { data: 'PRIORITY' },
    { data: 'QTY', type: 'strict-numeric' }

  ],
  report2Column: [
    { data: 'ROW_ID' },
    { data: 'MATERIAL' },
    { data: 'BOM_COMPONENT' },
    { data: 'USAGE', type: 'strict-numeric' }
  ],
  report11Column: [
    { data: 'ROW_ID' },
    { data: 'FIX_RECOURSE' }
  ],
  report3Column: [
    { data: 'ROW_ID' },
    { data: 'MATERIAL' },
    { data: 'STEP0', type: 'strict-numeric' },
    { data: 'STEP1', type: 'strict-numeric' },
    { data: 'STEP2', type: 'strict-numeric' },
    { data: 'STEP3', type: 'strict-numeric' },
    { data: 'STEP4', type: 'strict-numeric' },
    { data: 'STEP5', type: 'strict-numeric' },
    { data: 'STEP6', type: 'strict-numeric' },
    { data: 'STEP7', type: 'strict-numeric' },
    { data: 'STEP8', type: 'strict-numeric' },
    { data: 'STEP9', type: 'strict-numeric' },
    { data: 'STEP10', type: 'strict-numeric' },
    { data: 'STEP11', type: 'strict-numeric' },
    { data: 'STEP12', type: 'strict-numeric' },
    { data: 'STEP13', type: 'strict-numeric' },
    { data: 'STEP14', type: 'strict-numeric' },
    { data: 'STEP15', type: 'strict-numeric' },
    { data: 'STEP16', type: 'strict-numeric' },
    { data: 'STEP17', type: 'strict-numeric' },
    { data: 'STEP18', type: 'strict-numeric' },
    { data: 'STEP19', type: 'strict-numeric' },
    { data: 'STEP20', type: 'strict-numeric' },
    { data: 'STEP21', type: 'strict-numeric' },
    { data: 'STEP22', type: 'strict-numeric' },
    { data: 'STEP23', type: 'strict-numeric' },
    { data: 'STEP24', type: 'strict-numeric' },
    { data: 'STEP25', type: 'strict-numeric' },
    { data: 'STEP26', type: 'strict-numeric' },
    { data: 'STEP27', type: 'strict-numeric' },
    { data: 'STEP28', type: 'strict-numeric' },
    { data: 'STEP29', type: 'strict-numeric' },
    { data: 'STEP30', type: 'strict-numeric' },
    { data: 'STEP31', type: 'strict-numeric' },
    { data: 'STEP32', type: 'strict-numeric' },
    { data: 'STEP33', type: 'strict-numeric' },
    { data: 'STEP34', type: 'strict-numeric' },
    { data: 'STEP35', type: 'strict-numeric' }

  ],
  report9Column: [
    { data: 'ROW_ID' },
    { data: 'BOM_COMPONENT' },
    { data: 'STEP0', type: 'strict-numeric' }
  ],
  report4Column: [
  ],
  report5Column: [
  ],
  report10Column: [
  ],
  report8Column: [
  ],
  report6Column: [
  ],
  report7Column: [
    { data: 'ROW_ID' },
    { data: 'MATERIAL_IN_BOM' },
    { data: 'FOLLOW_UP_MATERIAL' }
  ],
  simulateForm: {
    batchId: '',
    type: '',
    strategy: '',
    module: '',
    balanceValue: ''
  },
  batchLogs: [] as Array<any>,
  taskQueueColumns: [
    { data: 'BATCH_ID', title: 'Task ID' },
    { data: 'NAME', title: 'Task Name' },
    { data: 'USERNAME', title: 'Username' },
    { data: 'WORK_MODE', title: 'Work Mode' },
    { data: 'CREATE_DATE', title: 'Create Time' },
    { data: 'START_TIME', title: 'Start Time' },
    { data: 'END_TIME', title: 'End Time' },
    { data: 'EXEC_TIME_IN_MIN', title: 'Time Cost(min)', type: 'numeric', precision: 1 },
    { data: 'STATUS', title: 'Status' }
  ]
})

interface Conditions {
  COND_ID: string,
  CONDITIONS: string,
  SHARED_BY: string,
  NAME: string,
  WITH_DATE: string,
  SHARED_REMARKS: string,
  IS_DEFAULT: string
}

const data = reactive({
  batch_id: '',
  cid: '',
  name: '',
  withDate: false,
  allUsers: [],
  sharedUsers: [],
  remarks: '',
  shareTitle: '',
  conditions: [] as Array<Conditions>,
  expand: false,
  enableUrlShare: false,
  enableDefaultPage: false
})

const compareSearch = () => {
  pageCtl.conditions.selectedxAxis = []
  pageCtl.conditions.compare3SelectedxAxis = []
  compareReport1()
  compareReport2()
  compareReport3()
  compareReport4Left()
  compareReport4Right()
  compareOverview()

  compare1ChartRef.value.chart().off('dblclick').on('dblclick', (obj) => {
    pageCtl.conditions.compare1SelectedName = obj.name
    pageCtl.conditions.compare1SelectedSeriesName = obj.seriesName
    pageCtl.compare1DetailsTitle = 'View Details [' + obj.seriesName + ' > ' + obj.name + ']'
    pageCtl.compare1DetailsVisible = true
    compare1DetailsTableRef.value.search()
  })
  compare2ChartRef.value.chart().off('dblclick').on('dblclick', (obj) => {
    pageCtl.conditions.compare2SelectedName = obj.treePathInfo[1].name
    pageCtl.conditions.compare2SelectedSeriesName = obj.treePathInfo[2].name
    pageCtl.compare2DetailsTitle = 'View Details [' + obj.treePathInfo[1].name + ' > ' + obj.treePathInfo[2].name + ']'
    pageCtl.compare2DetailsVisible = true
    compare2DetailsTableRef.value.search()
  })
  compare3ChartRef.value.chart().off('dblclick').on('dblclick', (obj) => {
    pageCtl.conditions.compare3SelectedName = obj.name
    pageCtl.conditions.compare3SelectedSeriesName = obj.seriesName
    pageCtl.compare3DetailsTitle = 'View Details [' + obj.seriesName + ' > ' + obj.name + ']'
    pageCtl.compare3DetailsVisible = true
    compare3DetailsTableRef.value.search()
  })
  compare4LeftChartRef.value.chart().off('dblclick').on('dblclick', (obj) => {
    pageCtl.conditions.compare4SelectedName = obj.name
    pageCtl.conditions.compare4SelectedTaskId = pageCtl.conditions.compareTaskL
    pageCtl.conditions.compare4SelectedSeriesName = obj.seriesName
    pageCtl.compare4DetailsTitle = 'View Details [' + obj.seriesName + ' > ' + obj.name + ']'
    pageCtl.compare4DetailsVisible = true
    compare4DetailsTableRef.value.search()
  })
  compare4RightChartRef.value.chart().off('dblclick').on('dblclick', (obj) => {
    pageCtl.conditions.compare4SelectedName = obj.name
    pageCtl.conditions.compare4SelectedTaskId = pageCtl.conditions.compareTaskR
    pageCtl.conditions.compare4SelectedSeriesName = obj.seriesName
    pageCtl.compare4DetailsTitle = 'View Details [' + obj.seriesName + ' > ' + obj.name + ']'
    pageCtl.compare4DetailsVisible = true
    compare4DetailsTableRef.value.search()
  })
}

const compareOverviewDetails = (type) => {
  let title = 'View Details ['
  switch (type) {
    case 'LEFT_BEST_CAN_DO':
      title += 'LEFT - BEST CAN DO'
      break
    case 'LEFT_DEMAND':
      title += 'LEFT - DEMAND'
      break
    case 'LEFT_SEMI_FINISH_GOODS':
      title += 'LEFT - SEMI-FINISH_GOODS'
      break
    case 'RIGHT_BCD':
      title += 'RIGHT - BEST CAN DO'
      break
    case 'RIGHT_DEMAND':
      title += 'RIGHT - DEMAND'
      break
    case 'RIGHT_SEMI_FINISH_GOODS':
      title += 'RIGHT - SEMI-FINISH GOODS'
      break
  }
  title += ']'

  pageCtl.compareOverviewDetailsTitle = title
  pageCtl.conditions.compareOverviewDetailsType = type
  pageCtl.visible.compareOverviewDetails = true
  nextTick(() => {
    compareOverviewDetailsTableRef.value.search()
  })
}

const compareOverview = () => {
  pageCtl.loading.compareOverview = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/compare_report_overview',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.OverviewData = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compareOverview = false
  })
}

const compareReport1 = () => {
  pageCtl.loading.compare1 = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/compare_report_1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.compare1Data = body
    if (pageCtl.conditions.selectedxAxis.length === 0) {
      pageCtl.conditions.xAxisOpts = body.xAxis
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compare1 = false
  })
}

const compareReport2 = () => {
  pageCtl.loading.compare2 = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/compare_report_2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.compare2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compare2 = false
  })
}

const compareReport3 = () => {
  pageCtl.loading.compare3 = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/compare_report_3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.compare3Data = body
    if (pageCtl.conditions.compare3SelectedxAxis.length === 0) {
      pageCtl.conditions.compare3SelectedxAxisOpts = body.xAxis
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compare3 = false
  })
}

const compareReport4Left = () => {
  pageCtl.loading.compare4 = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/compare_report_4_left',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.compare4LeftData = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compare4 = false
  })
}

const compareReport4Right = () => {
  pageCtl.loading.compare4 = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/compare_report_4_right',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.conditions.compare4RightData = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.compare4 = false
  })
}

const connectMqtt = () => {
  const username = (localStorage.getItem('username') || 'nobody').toLowerCase()
  const topic = 'scp/dss/ui/bcd/' + username
  pageCtl.client = mqtt.connect('wss://scp-dss.cn.schneider-electric.com:61615/mqtt', {
    clientId: 'scp-ui-best-can-do-' + username + '-' + $randomString(4)
  })
  pageCtl.client.on('connect', () => {
    pageCtl.client.subscribe(topic, { qos: 0 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + topic)
      }
    })
  })

  pageCtl.client.on('message', (topic, message) => {
    try {
      const logObj = JSON.parse(message + '')
      pageCtl.batchLogs.push(logObj.time + ' - ' + logObj.message)
    } catch (e) {
      console.warn(e)
    }
  })
}

const initPage = () => {
  queryTaskInfo()
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/init_page'
  }).then((body) => {
    pageCtl.moOpts = body.moList
    pageCtl.soOpts = body.soList
    pageCtl.poOpts = body.poList
    pageCtl.plantOpts = body.plantList
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const createTypeChanged = () => {
  pageCtl.newBatchForm = {
    name: '',
    groups: '',
    plant: '',
    module: '',
    strategy: 'QTY_BEST',
    type: 'WHOLE_LINE',
    dateRange: [],
    priority: [],
    step: '',
    step_mode: '',
    addPO: '',
    minusFcst: ''
  }
}

const queryTaskInfo = () => {
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/query_task_info'
  }).then((body) => {
    pageCtl.conditions.taskNameList = body.nameList
    pageCtl.conditions.taskInfo = body.taskInfo
  }).catch((error) => {
    console.log(error)
  })
}

const generateTask = () => {
  let batchId:any = pageCtl.conditions.selectedValue[1].split('@')
  batchId = batchId[batchId.length - 1]
  pageCtl.newBatchForm = JSON.parse(pageCtl.conditions.taskInfo[batchId])
}

const initExistsGroup = () => {
  $axios({
    method: 'post',
    url: '/simulation/best_can_do/init_exists_group'
  }).then((body) => {
    pageCtl.existsGroup = body
  }).catch((error) => {
    console.log(error)
  })
}

const showNewBatch = () => {
  createTypeChanged()
  pageCtl.visible.newBatch = true
}

const clickNode = (e) => {
  const taskInfo = JSON.parse(pageCtl.conditions.taskInfo[e.key])
  pageCtl.conditions.compareTaskL = [taskInfo.groups, taskInfo.name + '@' + e.key]
  resetTabSearch()
  pageCtl.currentTab = 'first'
  pageCtl.conditions.batchId = e.key
  nextTick(() => {
    searchTab1()
  })
}

const createBatch = () => {
  if (pageCtl.newBatchForm.name === '') {
    $message.error('Please input the task name')
    return
  }
  if (pageCtl.newBatchForm.groups === '') {
    $message.error('Please input a groups')
    return
  }
  if (pageCtl.newBatchForm.plant === '') {
    $message.error('Please select a plant code')
    return
  }
  pageCtl.loading.newBatch = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/save_new_batch',
    data: pageCtl.newBatchForm
  }).then((body) => {
    pageCtl.conditions.batchId = body
    pageCtl.visible.newBatch = false
    treeRef.value.search()
    queryTaskInfo()
    initExistsGroup()
    infoOverview()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.newBatch = false
  })
}

const infoOverview = (key?) => {
  if (key !== null) {
    const taskInfo = JSON.parse(pageCtl.conditions.taskInfo[key])
    let addPO:any = taskInfo.addPO
    let minusFcst:any = taskInfo.minusFcst
    if (addPO === 'true') {
      addPO = 'ADD_PO'
    } else {
      addPO = 'NONE_PO'
    }
    if (minusFcst === 'true') {
      minusFcst = 'MINUS_FCST'
    } else {
      minusFcst = 'NONE_FCST'
    }
    return taskInfo.name + ' ' + addPO + ' ' + minusFcst
  } else {
    return null
  }
}

const searchTab1 = () => {
  pageCtl.firstLoad.tab1 = true
  pageCtl.loading.selectedBatchInfo = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/query_batch_info',
    data: pageCtl.conditions
  }).then(body => {
    pageCtl.selectedBatchInfo = body
  }).catch(err => {
    console.warn(err)
  }).finally(() => {
    pageCtl.loading.selectedBatchInfo = false
  })
  report1TableRef.value.search()
  report2TableRef.value.search()
  report11TableRef.value.search()
  report3TableRef.value.search()
  report9TableRef.value.search()
}

const searchTab2 = () => {
  pageCtl.firstLoad.tab2 = true
  pageCtl.loading.logging = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/query_execute_logs',
    data: {
      batchId: pageCtl.conditions.batchId
    }
  }).then((body) => {
    pageCtl.batchLogs = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.logging = false
  })
}

const searchTab3 = () => {
  pageCtl.firstLoad.tab3 = true
  report4TableRef.value.search()
  report5TableRef.value.search()
  report10TableRef.value.search()
  report8TableRef.value.search()
  // report6TableRef.value.search()
}

const searchTab4 = () => {
  pageCtl.firstLoad.tab4 = true
  searchTaskQueue()
  report7TableRef.value.search()
}

const resetTabSearch = () => {
  pageCtl.firstLoad.tab1 = false
  pageCtl.firstLoad.tab2 = false
  pageCtl.firstLoad.tab3 = false
  pageCtl.firstLoad.tab4 = false
  pageCtl.firstLoad.tab6 = false
  pageCtl.batchLogs = []
}

const searchTaskQueue = () => {
  taskQueueTableRef.value.search()
}

const handleTabClick = (e) => {
  if (e.paneName === 'first' && !pageCtl.firstLoad.tab1) {
    searchTab1()
  } else if (e.paneName === 'second' && !pageCtl.firstLoad.tab2) {
    searchTab2()
  } else if (e.paneName === 'third' && !pageCtl.firstLoad.tab3) {
    searchTab3()
  } else if (e.paneName === 'fourth' && !pageCtl.firstLoad.tab4) {
    searchTab4()
  } else if (e.paneName === 'sixth' && !pageCtl.firstLoad.tab6) {
    pageCtl.conditions.comparePage = true
    nextTick(() => {
      compareSearch()
    })
  }
}

const deleteBatch = () => {
  pageCtl.loading.delete = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/delete_batch',
    data: pageCtl.conditions
  }).then((body) => {
    if (parseInt(body) === 0) {
      $message.success('Task deleted')
      pageCtl.conditions.batchId = ''
      treeRef.value.search()
      initExistsGroup()
    } else {
      $message.error('You cannot delete this task')
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.delete = false
  })
}

const showSimulate = () => {
  pageCtl.visible.simulate = true
}

const sendSimulateRequest = () => {
  pageCtl.loading.simulateRequest = true
  pageCtl.simulateForm.batchId = pageCtl.conditions.batchId
  pageCtl.simulateForm.type = pageCtl.newBatchForm.type
  pageCtl.simulateForm.strategy = pageCtl.newBatchForm.strategy
  pageCtl.simulateForm.module = pageCtl.conditions.module
  pageCtl.simulateForm.balanceValue = pageCtl.conditions.balanceValue
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/send_simulate_request',
    data: pageCtl.simulateForm
  }).then((body) => {
    console.log(pageCtl.simulateForm.balanceValue)
    if (body === '0') {
      $message.success('Request sent')
      pageCtl.visible.simulate = false
      pageCtl.currentTab = 'second'
    } else {
      $message.error(body)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.simulateRequest = false
  })
}

const scrollBatchLogs = () => {
  const batchLogsPre:any = document.getElementById('batchLogsPre')
  if (batchLogsPre.scrollTop === 0 || batchLogsPre.scrollHeight - batchLogsPre.scrollTop - _batchLogsHeight.value < 500) {
    batchLogsPre.scrollTo({ top: batchLogsPre.scrollHeight, behavior: 'smooth' })
  }
}

const visibleCtl = reactive({
  pop: false,
  share: false
})

const loadingCtl = reactive({
  share: false,
  query: false,
  params: false,
  init: false
})

const shareCondition = (key, label) => {
  visibleCtl.share = true
  data.batch_id = key
  data.name = label
  data.shareTitle = 'Share [ ' + key + '-' + label + ' ]'
  $axios({
    method: 'post',
    url: '/search/query_all_users'
  }).then((body) => {
    data.allUsers = body
  }).catch((error) => {
    console.log(error)
  })
}

const shareConditionAction = () => {
  if (data.sharedUsers.length === 0) {
    $message.error('Please select at least one user to share')
    return
  }
  loadingCtl.share = true
  $axios({
    method: 'post',
    url: '/simulation/best_can_do_calculator/share_condition',
    data: {
      batch_id: data.batch_id,
      name: data.name,
      withDate: data.withDate,
      users: data.sharedUsers,
      remarks: data.remarks
    }
  }).then((body) => {
    if (body) {
      $message.error(body + '')
    } else {
      $message.success(data.name + ' shared!')
      visibleCtl.share = false
      data.remarks = ''
      data.sharedUsers = []
      data.cid = ''
      data.name = ''
      data.withDate = false
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    loadingCtl.share = false
  })
}

const _compare1Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const yAxisData = pageCtl.conditions.compare1Data

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: 'bar',
        smooth: false,
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  return {
    title: {
      text: 'BCD Optimization Result'
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove'
    },
    legend: {
      data: legend,
      type: 'scroll',
      bottom: 10,
      padding: [0, 0, 25, 0]
    },
    grid: {
      top: $px2Rem(40),
      right: $px2Rem(20),
      bottom: $px2Rem(85),
      left: $px2Rem(55)
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['20%', '20%'],
      data: pageCtl.conditions.compare1Data.xAxis || [],
      axisLabel: {
        // interval: 0, //控制x坐标轴是否显示全
        textStyle: {
          color: (value) => {
            return /^L_/.test(value)
              ? '#000000'
              : /^R_/.test(value)
                ? '#40b27d'
                : '#FF004D'
          }
        },
        fontSize: 14
      },
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series
  }
})

watch(() => pageCtl.conditions.compare2Data, () => {
  if (pageCtl.conditions.compare2Data.length === 2) {
    pageCtl.conditions.compare2Data[0].name = 'LEVEL_A'
    pageCtl.conditions.compare2Data[1].name = 'LEVEL_B'
  } else if (pageCtl.conditions.compare2Data.length === 1) {
    pageCtl.conditions.compare2Data[0].name = 'LEVEL_A'
  }
})

watch(() => pageCtl.currentTab, () => {
  if (pageCtl.currentTab === 'second') {
    scrollBatchLogs()
  }
})

const _compare2Opt = computed(() => {
  const rootName = 'BCD Global Shortage Summary'
  return {
    title: {
      text: 'BCD Global Shortage Summary'
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo
        const levels:any = []
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)

          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)

            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }
          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      nodeClick: false,
      animation: false,
      leafDepth: 2,
      left: $px2Rem(10),
      right: $px2Rem(10),
      top: $px2Rem(30),
      bottom: $px2Rem(30),
      data: pageCtl.conditions.compare2Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _compare3Opt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const yAxisData = pageCtl.conditions.compare3Data

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: 'bar',
        stack: 'total',
        smooth: false,
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  return {
    title: {
      text: 'BCD Shortage Analysis',
      triggerEvent: true
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }
          const value = p[i].value || 0
          if ($shortenNumber(value) !== 0) {
            total += value
            tip.push('<div style="width:9.5rem;">')

            const marker = p[i].marker
            tip.push(marker)
            tip.push(p[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(value))
            tip.push('</span></div>')
          }
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: {
      data: legend,
      type: 'scroll',
      left: '5%',
      bottom: 10,
      padding: [0, 0, 25, 0]
    },
    grid: {
      top: $px2Rem(40),
      right: $px2Rem(50),
      bottom: $px2Rem(100),
      left: $px2Rem(80)
    },
    yAxis: {
      type: 'category',
      boundaryGap: ['20%', '20%'],
      data: pageCtl.conditions.compare3Data.xAxis || [],
      axisLabel: {
        // interval: 0, //控制x坐标轴是否显示全
        textStyle: {
          fontSize: 10,
          color: (value) => {
            return /^L_/.test(value)
              ? '#000000'
              : /^R_/.test(value)
                ? '#40b27d'
                : '#FF004D'
          }
        },
        fontSize: 14
      },
      triggerEvent: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      },
      triggerEvent: true
    },
    toolbox: {
      show: true,
      feature: {
        magicType: {
          type: ['stack']
        }
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        bottom: '0%',
        xAxisIndex: [0],
        start: 0,
        end: 100
      },
      {
        type: 'slider',
        show: true,
        yAxisIndex: [0],
        left: '97%',
        start: 100,
        end: 70
      },
      {
        type: 'inside',
        yAxisIndex: [0],
        start: 29,
        end: 36,
        zoomOnMouseWheel: false,
        moveOnMouseWheel: true
      }
    ],
    series
  }
})

const _compare4LeftOpt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const yAxisData = pageCtl.conditions.compare4LeftData
  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: 'bar',
        smooth: false,
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  return {
    title: {
      text: 'BCD SOH Evolution (LEFT TASK)'
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }
          const value = p[i].value || 0
          if ($shortenNumber(value) !== 0) {
            total += value
            tip.push('<div style="width:9.5rem;">')

            const marker = p[i].marker
            tip.push(marker)
            tip.push(p[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(value))
            tip.push('</span></div>')
          }
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: {
      data: legend,
      type: 'scroll',
      bottom: 10,
      padding: [0, 0, 25, 0]
    },
    grid: {
      top: $px2Rem(40),
      right: $px2Rem(20),
      bottom: $px2Rem(85),
      left: $px2Rem(55)
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['20%', '20%'],
      data: pageCtl.conditions.compare4LeftData.xAxis || [],
      axisLabel: {
        // interval: 0, //控制x坐标轴是否显示全
        textStyle: {
          color: '#000000'
        },
        fontSize: 14
      },
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series
  }
})

const _compare4RightOpt = computed(() => {
  const series = [] as any
  const legend = [] as any
  const yAxisData = pageCtl.conditions.compare4RightData

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: 'bar',
        smooth: false,
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }
  return {
    title: {
      text: 'BCD SOH Evolution (RIGHT TASK)'
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as any
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')
        let total = 0.0
        const p = params
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }
          const value = p[i].value || 0
          if ($shortenNumber(value) !== 0) {
            total += value
            tip.push('<div style="width:9.5rem;">')

            const marker = p[i].marker
            tip.push(marker)
            tip.push(p[i].seriesName)
            tip.push('<span style="float: right">')
            tip.push($shortenNumber(value))
            tip.push('</span></div>')
          }
        }
        tip.push('<div style="width:9.5rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: {
      data: legend,
      type: 'scroll',
      bottom: 10,
      padding: [0, 0, 25, 0]
    },
    grid: {
      top: $px2Rem(40),
      right: $px2Rem(20),
      bottom: $px2Rem(85),
      left: $px2Rem(55)
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['20%', '20%'],
      data: pageCtl.conditions.compare4RightData.xAxis || [],
      axisLabel: {
        // interval: 0, //控制x坐标轴是否显示全
        textStyle: {
          color: '#40b27d'
        },
        fontSize: 14
      },
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    series
  }
})

const _selectedBatchExecting = computed(() => {
  return pageCtl.selectedBatchInfo.START_TIME && !pageCtl.selectedBatchInfo.END_TIME
})

const _batchLogsHeight = computed(() => {
  return Math.max(document.body.clientHeight - 200, 400)
})
</script>

<style lang="scss">
#best_can_do {
  .el-form-item__content {
    display: block;
  }

  height: calc(100% - 25px);

  .widget {
    height: 100%;
  }

  .widget-body {
    height: 100%;
  }

  .maintain-by {
    font-size: 10px;
    font-weight: normal;
    color: var(--scp-text-color-secondary);
    font-style: italic;
  }

  .bcd-widget {
    .el-card {
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      margin: 0 10px 0 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary);
          margin: 0 !important;
        }
      }
    }
  }
}
</style>
