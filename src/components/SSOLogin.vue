<template>
  <!-- PINGSSO 登录, 用于正常用户登录 -->
  <div class="sso-login">
    <img :src="data.bgImageUrl" class="bg" alt=""/>
    <div class="bg-mask"></div>
    <div style="width: 100%;z-index: 0;position: fixed;left: 0;top: 0;" @contextmenu.prevent="showLogin">
      <img src="/img/logo-green.svg" style="width: 15%;margin: 5px 5px" alt="GSC China SCP Decision Support System"/>
    </div>
    <div style="text-align: center">
      <div class="login-container" v-show="data.showing">
        <div class="login-page">
          <h3 class="title">GSC China SCP DSS</h3>
          <el-button class="ssobtn" type="primary" @click="loginPingSSO">
            <font-awesome-icon icon="power-off"/>
            &nbsp;&nbsp;SIGN IN WITH PINGSSO
          </el-button>
        </div>
      </div>
      <p v-if="$store.state.enableManualLogin"><el-link type="primary" @click="gotoLoginPage" style="color: var(--scp-text-color-hightlight-lighter);margin-top: 10px "><i>Manual Login</i></el-link></p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onBeforeMount, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

const $route = useRoute()
const $store = useStore()
const $router = useRouter()
const $axios: any = inject('$axios')
const $redirectHttp2Https: any = inject('$redirectHttp2Https')

const data = reactive({
  showing: false,
  loginCnt: 3,
  bgImageUrl: ''
})

/**
 * 系统不支持非HTTPS访问, 除了localhost
 * 1. 当用户进入系统登录页面时, 会先判断当前token是否有效
 * 2. 如果token有效, 则直接跳转到redirect参数指定的页面, 或最后一次访问的页面
 * 3. 如果token无效, 则出现PINGSSO登录画面
 */
const loginPingSSO = () => {
  $store.commit('setEnableManualLogin', false)
  $router.push('/pingsso')
}

const showLogin = () => {
  data.loginCnt--
  if (data.loginCnt <= 0) {
    $router.push('/afe6c103f31da8654539b176a551493f')
  }
}

const gotoLoginPage = () => {
  $router.push('/afe6c103f31da8654539b176a551493f')
}

const autoLogin = () => {
  // 如果页面带参数redirect, 则优先跳转redirect
  // 否则跳转最后一次停留的页面
  let nextPath = '/'
  const lastPage = localStorage.getItem('page')
  if ($route.query.redirect) {
    nextPath = $route.query.redirect + ''
    localStorage.setItem('page', nextPath)
  } else if (lastPage) {
    nextPath = lastPage
  }

  // 校验token
  if (localStorage.getItem('token')) {
    $axios({
      method: 'post',
      url: '/check_route_auth',
      headers: {
        route: nextPath
      }
    }).then((body) => {
      // 如果返回408, 说明用户token失效, 显示登录页面
      if (body.status === 408) {
        data.showing = true
      } else {
        // 在这一级不判断route权限, 交由router.beforeEach拦截器来判断
        $router.push(nextPath)
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      // 防止页面卡住, 1秒后显示登录页
      setTimeout(() => {
        data.showing = true
      }, 1000)
    })
  } else {
    // 如果没有token, 也显示登录页
    data.showing = true
  }
}

onBeforeMount(() => {
  $redirectHttp2Https() // 强制跳转https
  autoLogin() // 尝试自动登录
  // 切换背景图
  const r = parseInt(Math.random() * 18 + '')
  data.bgImageUrl = '/img/login-bg' + r + '.jpg'
})
</script>
<style lang="scss">
@keyframes fadenum12 {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}

.sso-login {
  width: 100%;
  height: 100%;

  .bg {
    width: 100%;
    height: 100%;
    z-index: -2;
    position: fixed;
    left: 0;
    top: 0;
  }

  .bg-mask {
    width: 100%;
    height: 100%;
    z-index: -1;
    position: fixed;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .login-container {
    animation: fadenum12 1s 1;

    .login-page {
      margin: 0 auto;
      width: 480px !important;
      height: 150px !important;
      margin-top: 16% !important;
      border-radius: 4px;
      box-shadow: none !important;
      -webkit-box-shadow: none !important;
      border: none !important;
      background: none !important;

      .title {
        text-transform: uppercase;
        color: #fff !important;
        font-size: 38px !important;
        letter-spacing: normal !important;
        text-align: center !important;
      }

      .ssobtn {
        width: 100%;
        height: 50px;
        margin-top: 30px;
        border-radius: 8px;
        font-size: 16px !important;
        box-shadow: 0 0 5px var(--scp-text-color-highlight);

        .el-icon-switch-button {
          font-size: 18px !important;
        }
      }
    }
  }
}
</style>
