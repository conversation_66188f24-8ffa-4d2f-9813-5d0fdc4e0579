<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body">
        <!--   顶部按钮   -->
        <el-row style="margin-bottom: 10px">
          <el-col :span="3">
            <el-button @click="showComposeMail">
              <font-awesome-icon :icon="['far', 'pen-to-square']"/>&nbsp;
              Compose Mail
            </el-button>
          </el-col>
          <el-col :span="11">
            <el-button size="small" @click="queryUnreadMail" style="margin-right: 5px"
                       :style="{borderColor: pageCtl.unread ? 'var(--scp-text-color-highlight)': 'var(--scp-border-color)', color: pageCtl.unread ? 'var(--scp-text-color-highlight)': 'var(--scp-text-color-primary)'}">
              <font-awesome-icon icon="filter"/>&nbsp;
              Unread
            </el-button>
            <el-dropdown>
              <el-button size="small" style="margin-right: 5px">
                More&nbsp;&nbsp;<font-awesome-icon icon="chevron-down"/>
              </el-button>
              <template v-slot:dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="markAllAsRead">Mark All as Read</el-dropdown-item>
                  <el-dropdown-item @click="deleteMail">Delete</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button @click="refreshClick" style="margin-right: 5px">
              <font-awesome-icon icon="refresh"/>&nbsp;
              Refresh
            </el-button>
            <el-input v-model="pageCtl.input" placeholder="Search" style="width:200px;margin-right: 5px"
                      class="el-input"
                      @keypress.enter="queryMail" clearable></el-input>
          </el-col>
          <el-col :span="10" style="text-align: right">
            <el-button>
              &nbsp;{{ _startRow }}-{{ _endRow }} of {{ pageCtl.total }}&nbsp;
            </el-button>
            <el-button-group style="margin-left:10px">
              <el-button :disabled="pageCtl.currentPage <= 1" @click="pageUp">
                &nbsp;<font-awesome-icon icon="chevron-left"/>&nbsp;
              </el-button>
              <el-button :disabled="pageCtl.currentPage >= Math.ceil(pageCtl.total / pageCtl.pageSize)"
                         @click="pageDown">
                &nbsp;<font-awesome-icon icon="chevron-right"/>&nbsp;
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>

        <div class="subscript-container">
          <scp-subscript id="MAIL"/>
          <div>
            <el-row style="margin-bottom: 10px;">
              <el-col :span="3">
                <div style="margin-left: 10px">
                  <div class="main-box" @click="pageCtl.folder = 'inbox'"
                       :class="pageCtl.folder ==='inbox' ? 'active-menu' : ''">
                    <font-awesome-icon :icon="['far', 'envelope']" size="xl"/>&nbsp;&nbsp;
                    <p>Inbox</p>
                    <el-badge v-if="pageCtl.inbox > 0" :value="pageCtl.inbox" :max="99" class="mail-badge"></el-badge>
                  </div>
                  <div class="main-box" @click="pageCtl.folder = 'sent'"
                       :class="pageCtl.folder ==='sent' ? 'active-menu' : ''">
                    <font-awesome-icon :icon="['far', 'fa-paper-plane']" size="xl"/>&nbsp;&nbsp;
                    <p>Sent</p>
                  </div>
                  <div class="main-box" @click="pageCtl.folder = 'draft'"
                       :class="pageCtl.folder ==='draft' ? 'active-menu' : ''">
                    <font-awesome-icon :icon="['far', 'fa-edit']" size="xl"/>&nbsp;&nbsp;
                    <p>Draft</p>
                    <el-badge v-if="pageCtl.draft > 0" :value="pageCtl.draft" :max="99" class="mail-badge"></el-badge>
                  </div>
                </div>
              </el-col>
              <el-col :span="21" style="height:100%;padding: 10px;"
                      v-loading="pageCtl.mailLoading">
                <div v-if="pageCtl.mailList.length > 0">
                  <div class="main-list" v-for="item in pageCtl.mailList" :key="item['mail_id']"
                       :style="{fontWeight:(item['save_folder'] === 'inbox' && item['read_time'] === null) ? 600 : 400}"
                       @dblclick="queryMailByID(item['mail_id'])" v-loading="item['loading']">
                    <el-row style="padding:4px 0">
                      <el-col :span="1">
                        <el-checkbox v-model="item['checked']"></el-checkbox>
                      </el-col>
                      <el-col :span="1">
                        <i v-if="item['send_by'].toLowerCase() === 'system'" title="Mail from system notification">
                          <font-awesome-icon :icon="['fas', 'bell']"/>
                        </i>
                        <i v-else title="Mail from your colleague" style="margin-left: 3px;">
                          <font-awesome-icon :icon="['fas', 'user']"/>
                        </i>
                        <i v-if="item['mail'] === '1'" title="Mail sent to your @se.com mailbox" style="margin-left: 3px;">
                          <font-awesome-icon :icon="['far', 'fa-paper-plane']"/>
                        </i>
                        <i v-else>&nbsp;</i>
                      </el-col>
                      <el-col :span="4">
                        <p>{{ item['send_by'] }}</p>
                      </el-col>
                      <el-col :span="13" class="mail-body">
                        <p>{{ item['subject'] }}</p>
                        <p>- {{ item['body'] }}</p>
                      </el-col>
                      <el-col :span="5" style="text-align: right;">
                        {{ item['send_time'] }}
                      </el-col>
                    </el-row>
                  </div>
                </div>
                <div v-else style="text-align: center">
                  <el-row style="padding:4px 0">
                    <el-col :span="24">
                      We didn't find anything to show here.
                    </el-col>
                  </el-row>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- new mail -->
    <scp-draggable-resizable id="newMailDialog" w="70vw" h="75vh" v-model="pageCtl.newMailDialogVisible"
                             title="Compose Mail"
                             :save="sendMail" save-text="Send" :save-loading="pageCtl.sendMailBtnLoading"
                             :draft="saveDraft" draft-text="Save as Draft" :draft-loading="pageCtl.sendMailBtnLoading">
      <template v-slot="{height}">
        <el-row style="padding: 10px 10px 0 10px">
          <el-col :span="21">
            <el-input type="text" class="send-message" placeholder="Subject"
                      v-model="pageCtl.newMail['subject']"></el-input>
          </el-col>
          <el-col :span="3" style="text-align: right">
            <el-checkbox v-model="pageCtl.newMail['mail']">SE Mail</el-checkbox>
          </el-col>
        </el-row>
        <el-row style="padding: 0 10px 0 10px">
          <el-col :span="24">
            <el-select
                v-model="pageCtl.newMail['to']"
                multiple
                filterable
                allow-create
                remote
                :reserve-keyword="false"
                class="send-message"
                placeholder="To"
                style="width: 100%"
                :remote-method="(keyword)=>queryMailTo(keyword)"
                :loading="pageCtl.mailToLoading">
              <el-option
                  v-for="item in pageCtl.mailtoOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <div style="padding:0 10px">
          <scp-ck-editor v-model="pageCtl.newMail['body']" :style="{height: (height - 220) + 'px'}"/>
        </div>
      </template>
    </scp-draggable-resizable>

    <!-- view mail -->
    <scp-draggable-resizable id="readMailDialog" w="70vw" h="75vh" v-model="pageCtl.readMailDialogVisible"
                             :title="pageCtl.openedMail['subject']">
      <template v-slot="{height}">
        <el-row style="padding: 10px 10px 0 10px">
          <el-col :span="20">
            <b>From : </b>{{ pageCtl.openedMail['send_by'] }}
          </el-col>
          <el-col :span="4" style="text-align: right;">
            <i v-if="pageCtl.openedMail['mail'] === '1'" class="el-icon-position"></i>
            <i v-else>&nbsp;</i>
            <b style="margin-left:5px">{{ pageCtl.openedMail['send_time'] }}</b>
          </el-col>
        </el-row>
        <div style="padding:10px 10px 0 10px">
          <scp-ck-editor v-model="pageCtl.openedMail['body']" :style="{height: (height - 180) + 'px'}"/>
        </div>
      </template>
    </scp-draggable-resizable>

  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, watch } from 'vue'
import { useStore } from 'vuex'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'

const $store = useStore()
const $axios: any = inject('$axios')
const $message: any = inject('$message')

const pageCtl = reactive({
  input: '',
  newMailDialogVisible: false,
  readMailDialogVisible: false,
  mailLoading: false,
  newMail: {} as any,
  currentPage: 1,
  pageSize: 5,
  folder: 'inbox',
  mailList: [] as any,
  total: 0,
  inbox: 0,
  draft: 0,
  display: 0,
  openedMail: {},
  mailToLoading: false,
  mailtoOpts: [],
  timeout: null as any,
  sendMailBtnLoading: false,
  unread: false
})

const _startRow = computed(() => {
  if (pageCtl.display === 0) {
    return 0
  }
  return (pageCtl.currentPage - 1) * pageCtl.pageSize + 1
})
const _endRow = computed(() => {
  if (pageCtl.display === 0) {
    return 0
  }
  return Math.min(pageCtl.currentPage * pageCtl.pageSize, pageCtl.total)
})

const queryMail = () => {
  pageCtl.mailLoading = true
  $axios({
    method: 'post',
    url: '/mailbox/query_mail_list',
    data: {
      currentPage: pageCtl.currentPage,
      pageSize: pageCtl.pageSize,
      folder: pageCtl.folder,
      unread: pageCtl.unread,
      input: pageCtl.input
    }
  }).then((body) => {
    pageCtl.mailList = body.list
    pageCtl.display = body.list.length
    pageCtl.total = body.total
    pageCtl.inbox = body.inbox
    pageCtl.draft = body.draft
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.mailLoading = false
  })
}
const refreshClick = () => {
  pageCtl.input = ''
  queryMail()
}
const queryUnreadMail = () => {
  pageCtl.unread = !pageCtl.unread
  queryMail()
}
const queryMailByID = (mailid) => {
  let mail = {} as any
  for (let i = 0; i < pageCtl.mailList.length; i++) {
    const m = pageCtl.mailList[i]
    if (m.mail_id === mailid) {
      mail = m
    }
  }

  if ((!mail.mail_id) || mail.loading) {
    return
  }

  mail.loading = true
  $axios({
    method: 'post',
    url: '/mailbox/query_mail_by_id',
    data: {
      mailid,
      folder: pageCtl.folder
    }
  }).then((body) => {
    if (body) {
      mail.loading = false
      if (pageCtl.folder === 'draft') {
        pageCtl.newMail = body
        pageCtl.newMail.mail = (pageCtl.newMail.mail === '1')
        showComposeMail()
      } else {
        pageCtl.openedMail = body
        showReadMail()
        if (pageCtl.folder === 'inbox') {
          if (mail.read_time === null) {
            mail.read_time = new Date()
          }
          pageCtl.inbox--
        }
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryNotification()
  })
}
const deleteMail = () => {
  const checkedList = [] as any
  for (let i = 0; i < pageCtl.mailList.length; i++) {
    const m = pageCtl.mailList[i]
    if (m.checked) {
      checkedList.push(m.mail_id)
    }
  }

  if (checkedList.length === 0) {
    $message.error('Please select items to delete')
    return
  }

  pageCtl.mailLoading = true
  $axios({
    method: 'post',
    url: '/mailbox/delete_mail',
    data: {
      checkedList,
      folder: pageCtl.folder
    }
  }).then(() => {
    queryMail()
  }).catch((error) => {
    console.log(error)
  })
}
const markAllAsRead = () => {
  $axios({
    method: 'post',
    url: '/mailbox/mark_all_mail_as_read'
  }).then(() => {
    for (let i = 0; i < pageCtl.mailList.length; i++) {
      if (pageCtl.mailList[i].read_time === null) {
        pageCtl.mailList[i].read_time = new Date()
      }
    }
    pageCtl.inbox = 0
  }).catch((error) => {
    console.log(error)
  })
}
const queryMailTo = (keywords) => {
  if (keywords.length > 0) {
    pageCtl.mailToLoading = true

    if (pageCtl.timeout != null) {
      clearTimeout(pageCtl.timeout)
    }
    pageCtl.timeout = setTimeout(() => {
      pageCtl.timeout = null
      $axios({
        method: 'post',
        url: '/mailbox/query_mailto_by_keywords',
        data: {
          keywords
        }
      }).then((body) => {
        nextTick(() => {
          pageCtl.mailToLoading = false
          pageCtl.mailtoOpts = body.filter(item => {
            return item.label.toLowerCase().indexOf(keywords.toLowerCase()) > -1
          })
        })
      }).catch((error) => {
        console.log(error)
      })
    }, 650)
  } else {
    pageCtl.mailtoOpts = []
  }
}
const showComposeMail = () => {
  pageCtl.newMailDialogVisible = true
}
const showReadMail = () => {
  pageCtl.readMailDialogVisible = true
}
const saveDraft = () => {
  pageCtl.newMail.type = 0
  doSendMail()
}
const sendMail = () => {
  pageCtl.newMail.type = 1
  doSendMail()
}
const doSendMail = () => {
  pageCtl.sendMailBtnLoading = true
  $axios({
    method: 'post',
    url: '/mailbox/send_mail',
    data: pageCtl.newMail
  }).then(() => {
    pageCtl.newMailDialogVisible = false
    if (pageCtl.newMail.type === 0) {
      $message.success('Draft saved successfully')
    } else {
      $message.success('Mail sent successfully')
    }
    queryMail()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.sendMailBtnLoading = false
    queryNotification()
  })
}
const pageUp = () => {
  if (pageCtl.currentPage > 1) {
    pageCtl.currentPage--
    queryMail()
  }
}
const pageDown = () => {
  if (pageCtl.currentPage < Math.ceil(pageCtl.total / pageCtl.pageSize)) {
    pageCtl.currentPage++
    queryMail()
  }
}
const queryNotification = () => {
  $axios({
    method: 'post',
    url: '/system/query_notification',
    data: {
      cachable: 'N'
    }
  }).then((body) => {
    $store.commit('setMail', body.mail)
    $store.commit('setChangeLogs', body.changeLog)
    $store.commit('setMaintainer', body.maintainer)
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  queryMail()
})
watch(() => pageCtl.folder, () => {
  pageCtl.currentPage = 1
  queryMail()
})
watch(() => pageCtl.newMailDialogVisible, (newVal) => {
  if (!newVal) {
    pageCtl.newMail = {}
  }
})

</script>

<style lang="scss" scoped>
.mail-reader {
  height: 350px;
  width: 100%;
  border: 1px solid var(--scp-border-color-lighter);
  background-color: var(--scp-bg-color-fill);
}

.mail-body {
  width: calc(100% - 465px);
  color: var(--scp-text-color-primary);

  p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.main-box {
  cursor: pointer;
  position: relative;
  line-height: 36px;

  i {
    font-size: 18px;
    color: var(--scp-text-color-highlight);
    padding: 0 10px;
  }

  p {
    display: inline-block;
    color: var(--scp-text-color-primary);
    margin-bottom: 0
  }

  .mail-badge {
    position: absolute;
    right: 10px;
  }
}

.main-box:hover {
  background-color: var(--scp-bg-color-fill);
}

.main-list {
  line-height: 30px;
  padding: 0 10px;
  border-top: solid 1px var(--scp-border-color-lighter);

  i {
    color: var(--scp-text-color-primary)
  }

  p {
    margin: 0 !important;
  }
}

.main-list:hover {
  background-color: var(--scp-bg-color-fill-lighter);
}

.send-message {
  border-radius: 10px;
  margin-bottom: 10px;
}

.active-menu {
  p {
    color: var(--scp-text-color-highlight) !important;
    font-weight: bold;
  }
}
</style>
