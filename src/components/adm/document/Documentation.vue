<template>
  <div class="left-sidebar" id="scpDssDocumentation">
    <div style="padding-top: 5px" class="documentation-container">
      <div class="documentation-container-left">
        <scp-tree-menu
            ref="treeRef"
            url="/system/documentation/query_doc_list"
            :new-click="()=>pageCtl.visible.newDocDiv = true"
            :node-click="nodeClick"
        ></scp-tree-menu>
      </div>
      <div class="documentation-container-right" :style="{minHeight: _initHeight + 'px'}"
           v-loading="pageCtl.loading.content">
        <div v-show="pageCtl.viewDoc.CREATE_BY !== ''" style="margin-bottom: 10px">
          <div class="markdown-body">
            <h2>{{ pageCtl.viewDoc.SUBJECT }}
              <span style="float: right;text-align:right; font-size: 0.45rem;color: var(--scp-text-color-secondary);font-weight: normal;">
                <span class="documentation-container-right-print-hidden">
                <font-awesome-icon icon="share-alt" style="margin-right: 10px;color: var(--scp-text-color-highlight);cursor: pointer" @click="shareDoc"
                                   title="分享文档"/>
                <font-awesome-icon icon="edit" style="margin-right: 10px;color: var(--scp-text-color-success);cursor: pointer" @click="showModifyDocWin"
                                   title="编辑"/>
                <el-popconfirm title="确定删除文档?"
                               iconColor="var(--scp-text-color-error)"
                               @confirm="deleteDocAction"
                               confirmButtonType="danger"
                               confirmButtonText='确定'
                               cancelButtonText='取消'>
                  <template #reference>
                    <font-awesome-icon icon="times" style="margin-right: 10px;color: var(--scp-text-color-error);cursor: pointer" title="删除"/>
                  </template>
                </el-popconfirm>
                | </span> 作者: {{ pageCtl.viewDoc.CREATE_BY }} | 创建时间: {{ pageCtl.viewDoc.CREATE_DATE }} | 最后编辑:
                {{ pageCtl.viewDoc.UPDATE_BY || pageCtl.viewDoc.CREATE_BY }} | 编辑时间:
                {{ pageCtl.viewDoc.UPDATE_DATE || pageCtl.viewDoc.CREATE_DATE }}
              </span>
            </h2>
          </div>
        </div>
        <scp-md-preview v-model="pageCtl.viewDoc.CONTENT" :editorId="'editor_' + pageCtl.viewDoc.DOC_ID" :markedHeading="markedHeading"/>
      </div>

      <!-- new doc -->
      <scp-draggable-resizable w="80vw" h="70vh" v-model="pageCtl.visible.newDocDiv" title="New Document" :save="saveNewDocAction"
                               :save-loading="pageCtl.loading.create">
        <template v-slot="{height}">
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="12">
                  <el-input
                      size="default"
                      placeholder="Subject"
                      v-model="pageCtl.newDoc.subject"
                      style="width: var(--scp-input-width) !important;"
                      clearable>
                  </el-input>
                </el-col>
                <el-col :span="5">
                  <el-autocomplete
                      class="inline-input"
                      v-model="pageCtl.newDoc.groups"
                      :maxlength="200"
                      size="default"
                      style="width: var(--scp-input-width) !important;"
                      :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                      placeholder="Group"
                      show-word-limit
                  ></el-autocomplete>
                </el-col>
              </el-row>
            </div>
            <hr style="margin: 10px 0">
            <div>
              <el-row>
                <el-col :span="24" style="padding-right: 15px">
                  <scp-md-editor v-model="pageCtl.newDoc.content" :style="{'height': (height - 150) + 'px'}"/>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </scp-draggable-resizable>

      <!-- modify doc -->
      <scp-draggable-resizable w="80vw" h="70vh" title="Modify Document" v-model="pageCtl.visible.modifyDocDiv" :save="modifyDocAction"
                               :save-loading="pageCtl.loading.modify">
        <template v-slot="{height}">
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="12">
                  <el-input
                      size="default"
                      placeholder="Subject"
                      v-model="pageCtl.modifyDoc.SUBJECT"
                      style="width: var(--scp-input-width) !important;"
                      clearable>
                  </el-input>
                </el-col>
                <el-col :span="5">
                  <el-autocomplete
                      class="inline-input"
                      v-model="pageCtl.modifyDoc.GROUPS"
                      :maxlength="200"
                      size="default"
                      style="width: var(--scp-input-width) !important;"
                      :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                      placeholder="Group"
                      show-word-limit
                  ></el-autocomplete>
                </el-col>
              </el-row>
            </div>
            <hr style="margin: 10px 0">
            <div>
              <el-row>
                <el-col :span="24" style="padding-right: 15px">
                  <scp-md-editor v-model="pageCtl.modifyDoc.CONTENT" :style="{'height': (height - 150) + 'px'}"/>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </scp-draggable-resizable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'

const treeRef = ref()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $copyText: any = inject('$copyText')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $store: any = useStore()

const pageCtl = reactive({
  existsGroup: [],
  visible: {
    newDocDiv: false,
    modifyDocDiv: false
  },
  loading: {
    content: false,
    create: false,
    modify: false
  },
  newDoc: {
    subject: '',
    groups: '',
    content: ''
  },
  modifyDoc: {
    DOC_ID: '',
    CONTENT: '',
    SUBJECT: '',
    GROUPS: ''
  },
  viewDoc: {
    DOC_ID: '',
    CONTENT: '',
    SUBJECT: '',
    GROUPS: '',
    CREATE_BY: '',
    CREATE_DATE: '',
    UPDATE_BY: '',
    UPDATE_DATE: ''
  }
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/system/documentation/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
  }).catch((error) => {
    console.log(error)
  })
}

const markedHeading = (text, level, index) => {
  return `${text}-${level}-${index}`
}

const saveNewDocAction = () => {
  if (pageCtl.newDoc.subject) {
    pageCtl.loading.create = true
    $axios({
      method: 'post',
      url: '/system/documentation/save_new_doc',
      data: pageCtl.newDoc
    }).then(() => {
      treeRef.value.search()
      pageCtl.visible.newDocDiv = false
      $message.success('Documentation Saved.')
      initNewDoc()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.create = false
    })
  } else {
    $message.error('Invalid subject')
  }
}

const showModifyDocWin = () => {
  $axios({
    method: 'post',
    url: '/system/documentation/query_doc',
    data: {
      docid: pageCtl.viewDoc.DOC_ID
    }
  }).then((body) => {
    pageCtl.modifyDoc.DOC_ID = body.DOC_ID
    pageCtl.modifyDoc.CONTENT = body.CONTENT
    pageCtl.modifyDoc.SUBJECT = body.SUBJECT
    pageCtl.modifyDoc.GROUPS = body.GROUPS
    pageCtl.visible.modifyDocDiv = true
  }).catch((error) => {
    console.log(error)
  })
}

const modifyDocAction = () => {
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/system/documentation/modify_doc',
    data: pageCtl.modifyDoc
  }).then(() => {
    $message.success('Documentation Saved.')
    pageCtl.visible.modifyDocDiv = false
    getContentByDocID(pageCtl.viewDoc.DOC_ID)
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

const deleteDocAction = () => {
  $axios({
    method: 'post',
    url: '/system/documentation/delete_doc',
    data: {
      docid: pageCtl.viewDoc.DOC_ID
    }
  }).then(() => {
    $message.success('Documentation Deleted.')
    initViewDoc()
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const nodeClick = (data) => {
  getContentByDocID(data.key)
}

const getContentByDocID = (docid) => {
  pageCtl.loading.content = true
  $axios({
    method: 'post',
    url: '/system/documentation/query_doc_with_function',
    data: {
      docid
    }
  }).then((body) => {
    const keys = Object.keys(pageCtl.viewDoc)
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      pageCtl.viewDoc[key] = body[key] || ''
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.content = false
  })
}

const initViewDoc = () => {
  const keys = Object.keys(pageCtl.viewDoc)
  for (let i = 0; i < keys.length; i++) {
    pageCtl.viewDoc[keys[i]] = ''
  }
}

const initNewDoc = () => {
  const keys = Object.keys(pageCtl.newDoc)
  for (let i = 0; i < keys.length; i++) {
    pageCtl.newDoc[keys[i]] = ''
  }
  localStorage.removeItem('developer-doc.new.content')
}

const shareDoc = () => {
  let url = window.location.href.split('#')[0]
  url += '#/public_documentation?id=' + pageCtl.viewDoc.DOC_ID
  $copyText(url)
  $message.success('分享链接已复制到剪切板')
}

onMounted(() => {
  initPage()

  const newDocContent = localStorage.getItem('developer-doc.new.content')
  if (newDocContent) {
    pageCtl.newDoc.content = newDocContent
  }
})

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 150
})

watch(() => pageCtl.newDoc.content, (newVal) => {
  localStorage.setItem('developer-doc.new.content', newVal)
})
</script>

<style lang="scss">
#scpDssDocumentation {
  .markdown-body {
    font-size: 0.5rem !important;
    border-bottom: 1px solid var(--scp-border-color);

    h2 {
      margin: 5px 0;
    }
  }

  .anchor-active {
    display: inline-block;
  }

  .menu-btn {
    position: fixed;
    right: 1rem;
    top: 4.2rem;
    z-index: 10;
  }

  .menu-body {
    box-shadow: 0 0 1px var(--scp-border-color);
    z-index: 10;
    padding: 5px 0;
    position: fixed;
    right: 10px;
    width: 10rem;
    top: 4.2rem;
    background-color: var(--scp-bg-color);
    overflow: auto;
  }

  .md-editor-catalog-active > span {
    color: inherit !important;
  }

  .md-editor-content .md-editor-input-wrapper {
    overflow: auto;
  }

  .documentation-container {

    .documentation-container-left {
      float: left;
      width: 250px;
    }

    .documentation-container-right {
      float: left;
      width: calc(100% - 280px);
      padding-left: 25px;
      border-left: 1px solid var(--scp-border-color);
    }
  }

  @media print {
    .documentation-container {
      .documentation-container-left {
        display: none;
      }

      .documentation-container-right {
        border-left: none !important;
        width: 100% !important;
        padding-left: 0 !important;

        .documentation-container-right-print-hidden {
          display: none !important;
        }

        .menu-btn {
          display: none !important;
        }
      }
    }
  }
}

</style>
