<template>
  <div class="left-sidebar">
    <div class="widget">
      <div class="widget-body" style="display: flex; justify-content: center;">
        <div style="width: 960px;height: 600px;border:1px solid var(--scp-border-color);padding: 5px;">
          <five-dc-report-virtual-column ref="vcolumnRef"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import FiveDcReportVirtualColumn from '@/components/scp/canvas/FiveDCReportVirtualColumn.vue'
import { ref } from 'vue'

const vcolumnRef = ref()

const test = () => {
  console.log(vcolumnRef.value.getOptions())
}
</script>
