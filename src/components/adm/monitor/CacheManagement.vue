<template>
  <div class="left-sidebar" id="cacheManagement">
    <div class="widget">
      <div class="widget-body">
        <el-container :style="{height: pageCtl.height + 'px'}">
          <el-aside width="380px" style="border-right: 1px solid var(--scp-border-color);height: 100%" class="left-container">
            <scp-tree-menu
                ref="treeRef"
                style="height: 100%"
                url="/system/cache_management/query_key_list"
                node-key="label"
                :default-expanded-keys="['1d', '5m']"
                :node-click="clickNode"
                :node-delete-click="(data)=>deleteCacheByGroup(data)"
                node-delete-confirm-msg="确定删除缓存?"/>
          </el-aside>
          <el-main style="padding: 0 var(--scp-widget-margin)" v-loading="pageCtl.loading" v-show="pageCtl.selectedKey !== ''">
            <h3 style="margin: var(--scp-widget-margin) 0">{{ pageCtl.selectedKey }}
              <span style="margin-left: var(--scp-widget-margin); font-size: 0.45rem;color: var(--scp-text-color-secondary);font-weight: normal;height: 1.5rem;line-height: 1.5rem">
                TTL: {{ pageCtl.content.ttl + ' (' + $toFixed((pageCtl.content.ttl / 60), 1) + 'min)' }} | Size: {{ pageCtl.content.size }} |
                <el-popconfirm title="确定删除缓存?"
                               iconColor="var(--scp-text-color-error)"
                               @confirm="deleteCacheByKey"
                               confirmButtonType="danger"
                               confirmButtonText='确定'
                               cancelButtonText='取消'>
                  <template #reference>
                    <font-awesome-icon icon="times" style="margin-left: 10px;margin-right: 10px;color: var(--scp-text-color-error);cursor: pointer" title="删除"/>
                  </template>
                </el-popconfirm> |
                <font-awesome-icon icon="copy" style="margin-left: 10px;color: var(--scp-text-color-highlight);cursor: pointer" title="复制"
                                   @click="copyContent"/>
              </span>
            </h3>
            <scp-ace-editor v-model="pageCtl.content.text" :readonly="true" :wrap="true" lang="json" style="height: calc(100% - 80px)"/>
          </el-main>
        </el-container>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'
import { useStore } from 'vuex'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $store = useStore()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $toFixed: any = inject('$toFixed')
const $copyText: any = inject('$copyText')
const treeRef = ref()
const pageCtl = reactive({
  height: 500,
  selectedKey: '',
  loading: false,
  content: {
    text: '',
    ttl: 0,
    size: 0
  },
  infoHeight: 800
})

const clickNode = (e) => {
  pageCtl.selectedKey = e.key
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/system/cache_management/query_content_by_key',
    data: {
      key: e.key
    }
  }).then((body) => {
    pageCtl.content = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const search = () => {
  treeRef.value.search()
  pageCtl.selectedKey = ''
  pageCtl.content = {
    text: '',
    ttl: 0,
    size: 0
  }
}

const deleteCacheByKey = () => {
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/system/cache_management/delete_cache_by_key',
    data: {
      key: pageCtl.selectedKey
    }
  }).then(() => {
    search()
    $message.success('Cache deleted')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const deleteCacheByGroup = (data) => {
  const rootKey = getRootKey(data)
  const label = data.label
  const ks = rootKey.split(label)
  const key = ks[0] + label
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/system/cache_management/delete_cache_by_key',
    data: {
      key
    }
  }).then(() => {
    search()
    $message.success('Cache removed')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const getRootKey = (data) => {
  if (data.children && data.children.length > 0) {
    return getRootKey(data.children[0])
  } else {
    return data.key
  }
}

const copyContent = () => {
  $copyText(pageCtl.content.text)
  $message.success('Content has been copied to the clipboard.')
}

onMounted(() => {
  pageCtl.height = document.documentElement.clientHeight - 100
})
</script>
