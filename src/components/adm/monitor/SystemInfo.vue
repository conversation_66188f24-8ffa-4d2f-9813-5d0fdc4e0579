<template>
  <div>
    <div class="left-sidebar">
      <div style="height:10px;"></div>
      <el-button type="primary" size="default" @click="getSystemInfo()" style="margin-left:var(--scp-widget-margin)">
        <font-awesome-icon icon="refresh"/>&nbsp;&nbsp;
        Refresh System Info
      </el-button>

      <el-popconfirm title="确定清空缓存?"
                     iconColor="orange"
                     @confirm="flushCache"
                     confirmButtonType="warning"
                     confirmButtonText='确定'
                     cancelButtonText='取消'
                     style="margin-left:5px">
        <template #reference>
          <el-button size="default" :loading="pageCtl.loading.cache">
            <font-awesome-icon icon="fa-solid fa-wand-magic-sparkles"/>&nbsp;&nbsp;
            Flush Cache
          </el-button>
        </template>
      </el-popconfirm>
      &nbsp;&nbsp;
      <el-popconfirm title="确定重启服务器? 这会导致系统在1分钟内不可用!"
                     iconColor="var(--scp-text-color-error)"
                     @confirm="restartSystem"
                     confirmButtonType="danger"
                     confirmButtonText='确定'
                     cancelButtonText='取消'>
        <template #reference>
          <el-button type="danger" size="default" style="float:right;margin-right:10px;" :loading="pageCtl.loading.restart">
            <font-awesome-icon icon="fa-solid fa-microchip"/>&nbsp;&nbsp;
            Restart System
          </el-button>
        </template>
      </el-popconfirm>

      <div style="padding:var(--scp-widget-margin);">
        <div class="subscript-container" style="height:100%;" v-loading="pageCtl.loading.monitor">
          <el-row class="search-box" style="float: left;width: 500px;z-index: 100;">
            <el-col :span="14">
              <el-date-picker
                  v-model="pageCtl.conditions.dateRange"
                  type="daterange"
                  unlink-panels
                  range-separator="~"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  :clearable="false">
              </el-date-picker>
            </el-col>
            <el-col :span="10">
              <el-select v-model="pageCtl.conditions.serverID" placeholder="Server ID">
                <el-option
                    v-for="(item, index) in pageCtl.serverIdList"
                    :key="index"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <scp-subscript id="CPMM"/>
          <chart ref="monitorInfo" :height="400" :option="_monitorOpt"/>
        </div>
      </div>
      <div style="padding: 0 var(--scp-widget-margin)">
        <scp-ace-editor v-model="pageCtl.systemInfo" :readonly="true" :wrap="true" lang="json" :auto-height="true"/>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, reactive, watch } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $axios: any = inject('$axios')
const $px2Rem: any = inject('$px2Rem')
const $toFixed: any = inject('$toFixed')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const pageCtl = reactive({
  systemInfo: '',
  deviceInfo: {
    CPU: [],
    MEMORY: [],
    ORA_MEMORY: [],
    JAVA_MEMORY: [],
    TIME: [],
    TRAFFIC_IN: [],
    TRAFFIC_OUT: []
  },
  conditions: {
    dateRange: [] as Array<string>,
    serverID: 'scp02'
  },
  dateRange: [] as Array<string>,
  serverIdList: ['scp-landing', 'scp01', 'scp02'],
  loading: {
    default: false,
    monitor: false,
    restart: false,
    cache: false
  }
})

watch(() => pageCtl.conditions.dateRange, () => {
  searchCpuWorkload()
})

watch(() => pageCtl.conditions.serverID, () => {
  searchCpuWorkload()
})

onMounted(() => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  pageCtl.conditions.dateRange = [$dateFormatter(new Date(year, month, day - 1), 'yyyy/MM/dd'), $dateFormatter(today, 'yyyy/MM/dd')]

  getSystemInfo()
})

const getSystemInfo = () => {
  pageCtl.loading.default = true
  $axios({
    method: 'post',
    url: '/jvm'
  }).then((body) => {
    pageCtl.systemInfo = JSON.stringify(body || '', undefined, 2)
    searchCpuWorkload()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.default = false
  })
}

const flushCache = () => {
  pageCtl.loading.cache = true
  $axios({
    method: 'post',
    url: '/flush_cache'
  }).then(() => {
    $message.success('Cache flushed')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.cache = false
  })
}

const restartSystem = () => {
  pageCtl.loading.restart = true
  $axios({
    method: 'post',
    url: '/restart'
  }).then((body) => {
    $message.error(body + '')
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.restart = false
  })
}

const searchCpuWorkload = () => {
  pageCtl.loading.monitor = true
  $axios({
    method: 'post',
    url: '/system/query_cpu_workload',
    data: pageCtl.conditions
  }).then((body) => {
    const info = {
      CPU: [] as Array<any>,
      MEMORY: [] as Array<any>,
      ORA_MEMORY: [] as Array<any>,
      JAVA_MEMORY: [] as Array<any>,
      TIME: [] as Array<any>,
      TRAFFIC_IN: [] as Array<any>,
      TRAFFIC_OUT: [] as Array<any>
    } as any
    if (body) {
      for (let i = 0; i < body.length; i++) {
        info.CPU.push(body[i].CPU)
        info.MEMORY.push(body[i].MEMORY)
        info.ORA_MEMORY.push(body[i].ORA_MEMORY)
        info.JAVA_MEMORY.push(body[i].JAVA_MEMORY)
        info.TIME.push(body[i].TIME)
        info.TRAFFIC_IN.push(body[i].TRAFFIC_IN)
        info.TRAFFIC_OUT.push(body[i].TRAFFIC_OUT)
      }
    }
    pageCtl.deviceInfo = info
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.monitor = false
  })
}

const _monitorOpt = computed(() => {
  let hasOraMemory = false
  for (let i = 0; i < pageCtl.deviceInfo.ORA_MEMORY.length; i++) {
    if (pageCtl.deviceInfo.ORA_MEMORY[i]) {
      hasOraMemory = true
      break
    }
  }

  let hasJavaMemory = false
  for (let i = 0; i < pageCtl.deviceInfo.JAVA_MEMORY.length; i++) {
    if (pageCtl.deviceInfo.JAVA_MEMORY[i]) {
      hasJavaMemory = true
      break
    }
  }

  let hasTraffic = false
  for (let i = 0; i < pageCtl.deviceInfo.TRAFFIC_IN.length; i++) {
    if (pageCtl.deviceInfo.TRAFFIC_IN[i]) {
      hasTraffic = true
      break
    }
  }

  const legend = ['CPU', 'Memory']
  const series = [{
    name: 'CPU',
    data: pageCtl.deviceInfo.CPU,
    type: 'line'
  }, {
    name: 'Memory',
    data: pageCtl.deviceInfo.MEMORY,
    type: 'line',
    markLine: {
      silent: true,
      data: [{
        yAxis: 85
      }],
      lineStyle: {
        type: 'dotted',
        color: '#ff0001'
      },
      label: {
        color: '#ff0001'
      }
    }
  }] as Array<any>

  if (hasOraMemory) {
    legend.push('Memory - Oracle')
    series.push({
      name: 'Memory - Oracle',
      data: pageCtl.deviceInfo.ORA_MEMORY,
      type: 'line'
    })
  }

  if (hasJavaMemory) {
    legend.push('Memory - Java')
    series.push({
      name: 'Memory - Java',
      data: pageCtl.deviceInfo.JAVA_MEMORY,
      type: 'line'
    })
  }

  if (hasTraffic) {
    legend.push('Received', 'Sent')
    series.push({
      yAxisIndex: 1,
      name: 'Received',
      data: pageCtl.deviceInfo.TRAFFIC_IN,
      type: 'line'
    }, {
      yAxisIndex: 1,
      name: 'Sent',
      data: pageCtl.deviceInfo.TRAFFIC_OUT,
      type: 'line'
    })
  }

  return {
    color: [
      '#91cc75',
      '#5470c6',
      '#159e41',
      '#ee6666',
      '#fac858',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc'
    ],
    legend: $legend(),
    grid: $grid({ left: 5, right: 10 }),
    title: {
      text: ' CPU & Memory Monitor'
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        for (const i in params) {
          if (params.hasOwnProperty(i) === false) {
            continue
          }

          let value = params[i].value || 0
          tip.push('<div style="width:9.5rem;">')
          const marker = params[i].marker

          tip.push(marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          if (params[i].seriesName === 'Received' || params[i].seriesName === 'Sent') {
            if (value > 1000) {
              const value2 = $toFixed(value * 120 / 1024 / 1024, 1)
              value = $toFixed(value / 1024, 1)
              tip.push($thousandBitSeparator(value2, 1) + 'GB - ' + $thousandBitSeparator(value, 1) + ' MB/s')
            } else {
              const value2 = $toFixed(value * 120 / 1024, 1)
              tip.push($thousandBitSeparator(value2) + 'MB - ' + $thousandBitSeparator(value) + ' KB/s')
            }
          } else {
            tip.push($shortenNumber(value) + (value ? '%' : ''))
          }
          tip.push('</span></div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    xAxis: {
      type: 'category',
      data: pageCtl.deviceInfo.TIME,
      axisLabel: {
        formatter: function (value) {
          return value.substring(11, 16)
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        scale: true,
        splitLine: {
          show: false
        }
      }, {
        type: 'value',
        scale: true,
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: function (value) {
            return $shortenNumber(value * 1024, 0)
          }
        }
      }
    ],
    series
  }
})
</script>
