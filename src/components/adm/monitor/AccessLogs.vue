<template>
  <div>
    <div class="left-sidebar">
      <div class="widget">
        <div class="widget-body">
          <el-row class="search-box">
            <el-col :span="7">
              <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :loading="pageCtl.loading.filter"
                :options="pageCtl.filterOpts"/>
            </el-col>
            <el-col :span="5">
              <el-date-picker
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                :clearable="false"
                range-separator="to"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                start-placeholder="Start month"
                end-placeholder="End month">
              </el-date-picker>
            </el-col>
            <el-col :span="1">
              <scp-search ref="searchRef" :click-native="search" :data="pageCtl.conditions"/>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="subscript-container">
                <scp-subscript id="RTAT"/>
                <chart ref="report2Ref" :height="400" :option="_report2Opt"/>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="subscript-container">
                <scp-subscript id="RTAD"/>
                <scp-table
                  ref="report1TableRef"
                  :lazy="true"
                  url="/system/access_logs/query_report1"
                  :params="pageCtl.conditions"
                  :columns="pageCtl.report1Columns"
                />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const report2Ref = ref()
const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const searchRef = ref()
const report1TableRef = ref()
const $axios: any = inject('$axios')
const $shortenNumber: any = inject('$shortenNumber')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $dateFormatter: any = inject('$dateFormatter')
const pageCtl = reactive({
  filterOpts: [],
  conditions: {
    dateRange: [] as Array<any>,
    filterList: [] as Array<any>
  },
  loading: {
    filter: false,
    report2: false
  },
  report1Columns: [
    { data: 'MENU_NAME' },
    { data: 'ACCESS_URL' },
    { data: 'OPR_TYPE', title: 'Type' },
    { data: 'REQUEST_LENGTH', title: 'Req.Size', type: 'numeric' },
    { data: 'RESPONSE_LENGTH', title: 'Res.Size', type: 'numeric' },
    { data: 'ACCESS_TIME' },
    { data: 'TIMECOST_IN_MS', title: 'Timecost(ms)', type: 'numeric' },
    { data: 'USER_NAME' },
    { data: 'REQUEST_BODY', title: 'Request', width: '150px' },
    { data: 'RESPONSE_TXT', title: 'Response', width: '150px' },
    { data: 'RESPONSE_CODE', title: 'Res.Code' },
    { data: 'ERROR_MESSAGE', width: '100px' }
  ] as Array<any>,
  report2Data: {
    xAxis: [],
    yAxis1: [],
    yAxis2: [],
    yAxis3: []
  }
})

onMounted(() => {
  initPage()

  report2Ref.value.chart().on('dblclick', (params) => {
    if (params.componentType === 'title') {
      searchReport2()
    }
  })
})

const initPage = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  pageCtl.conditions.dateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]

  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/system/access_logs/init_page'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  searchReport1()
  searchReport2()
}

const searchReport1 = () => {
  report1TableRef.value.search()
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/system/access_logs/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const _report2Opt = computed(() => {
  return {
    title: {
      text: 'Realtime Access Monitor',
      triggerEvent: true
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as Array<string>
        tip.push(params[0].name)
        for (let i = 0; i < params.length; i++) {
          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($thousandBitSeparator(params[i].data) || 0)
          if ((params[i].seriesName === 'Inbound' || params[i].seriesName === 'Outbound') && params[i].data) {
            tip.push(' KB')
          }
          tip.push('</span>')
          tip.push('</div>')
        }
        return tip.join('')
      }
    },
    legend: $legend(),
    grid: $grid({ left: 25, right: 5 }),
    xAxis: [
      {
        type: 'category',
        boundaryGap: true,
        data: pageCtl.report2Data.xAxis
      }
    ],
    yAxis: [
      {
        type: 'value',
        scale: true,
        min: 0,
        offset: 50,
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      },
      {
        type: 'value',
        scale: true,
        min: 0,
        position: 'left',
        splitArea: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      },
      {
        type: 'value',
        scale: true,
        min: 0,
        position: 'right',
        splitArea: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          formatter: (value) => {
            return $shortenNumber(value, 0)
          }
        }
      }
    ],
    series: [
      {
        name: 'Inbound',
        type: 'line',
        data: pageCtl.report2Data.yAxis1,
        itemStyle: {
          normal: {
            lineStyle: {
              width: 1
            }
          },
          emphasis: {
            lineStyle: {
              width: 1
            }
          }
        }
      }, {
        name: 'Outbound',
        type: 'line',
        yAxisIndex: 1,
        data: pageCtl.report2Data.yAxis2,
        itemStyle: {
          normal: {
            lineStyle: {
              width: 1
            }
          },
          emphasis: {
            lineStyle: {
              width: 1
            }
          }
        }
      }, {
        name: 'Response',
        type: 'line',
        yAxisIndex: 2,
        data: pageCtl.report2Data.yAxis3,
        itemStyle: {
          normal: {
            lineStyle: {
              width: 1
            }
          },
          emphasis: {
            lineStyle: {
              width: 1
            }
          }
        }
      }
    ]
  }
})
</script>
