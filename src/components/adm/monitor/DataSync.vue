<template>
  <div class="left-sidebar" id="dataSync">
    <div class="widget">
      <div class="widget-body">
        <el-tabs v-model="pageCtl.activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="重跑任务" name="RERUN_JOB">
            <div>
              <el-row>
                <el-col :span="11">
                  <el-transfer
                      v-loading="pageCtl.loading.report1"
                      filterable
                      :filter-method="report3Filter"
                      :render-content="renderReport1Content"
                      filter-placeholder="Search"
                      @change="report1Change"
                      :titles="['Available Tasks', 'Selected Tasks']"
                      v-model="pageCtl.report1Value"
                      :data="pageCtl.report1Data">
                    <template #right-footer>
                      <el-link class="footer-link" underline="never" @click="showReport1Templates">Templates</el-link>
                    </template>
                  </el-transfer>
                </el-col>
                <el-col :span="13" v-loading="pageCtl.loading.report1Script">
                  <scp-ace-editor v-model="pageCtl.report1Script" lang="json"
                                  style="width:100%;height: 470px;border: 1px dotted var(--scp-border-color);float:right"/>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="24" style="text-align: left">
                  <el-button @click="pageCtl.report1Logs = [pageCtl.emptyContent]" style="margin-right: 20px">Clear Logs</el-button>
                  <el-popconfirm :title="'确定重新执行'+pageCtl.report1Value.length+'个任务? '"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="report1Run"
                                 confirmButtonType="danger"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'>
                    <template #reference>
                      <el-button type="primary" :loading="pageCtl.loading.report1Run" :disabled="pageCtl.report1Script.length === 0">
                        Re-run Tasks({{ pageCtl.report1Value.length }})
                      </el-button>
                    </template>
                  </el-popconfirm>
                  <span v-html="_report1PIDScript"></span>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-divider content-position="left">Execute Logs</el-divider>
                </el-col>
              </el-row>
              <el-row style="margin-top: 20px">
                <el-col :span="24">
                  <pre id="report1Logs" style="height: 600px; overflow: auto">{{ pageCtl.report1Logs.join('') }}</pre>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="任务管理" name="DAILY_JOB" v-loading="pageCtl.loading.report2">
            <el-row>
              <el-col :span="5">
                <el-select v-model="pageCtl.report2Version" style="width: var(--scp-input-width)" :loading="pageCtl.loading.filter">
                  <el-option
                      v-for="item in pageCtl.report2VersionOpts"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input placeholder="Search" v-model="pageCtl.report2Search" style="width: 100%" clearable></el-input>
              </el-col>
              <el-col :span="4" style="padding-left: 10px">
                <scp-search :click-native="searchReport2" :data="{}"/>
                <el-button type="primary" @click="createReport2DetailsWin">
                  <font-awesome-icon :icon="['far', 'pen-to-square']"/>&nbsp;
                  New
                </el-button>
              </el-col>
              <el-col :span="11" style="text-align: right">
                <el-button style="border: none !important">
                  {{ _report2FinishRate }}
                </el-button>
                <el-button :type="convertStatus('not_start')">
                  Not Start({{ pageCtl.report2Status.not_start }})
                </el-button>
                <el-button :type="convertStatus('disable')">
                  Disable({{ pageCtl.report2Status.disable }})
                </el-button>
                <el-button :type="convertStatus('running')">
                  Running({{ pageCtl.report2Status.running }})
                </el-button>
                <el-button :type="convertStatus('success')">
                  Success({{ pageCtl.report2Status.success }})
                </el-button>
                <el-button :type="convertStatus('failed')">
                  Failed({{ pageCtl.report2Status.failed }})
                </el-button>
                <el-button class="fatal-error" v-show="pageCtl.report2Status.error > 0">
                  Fatal Error({{ pageCtl.report2Status.error }})
                </el-button>
              </el-col>
            </el-row>
            <div style="min-height: 400px;">
              <pre style="color:var(--scp-text-color-error);max-height:100px;overflow:auto" v-show="pageCtl.report2Messages.length > 0">{{
                  pageCtl.report2Messages.join('\r\n')
                }}</pre>
              <!-- success: 执行成功, warning: 没有执行, danger: 执行失败, default: 禁用, primary: 正在执行 -->
              <div v-for="(val, key) in _report2Data" :key="key" v-show="val.length > 0">
                <el-divider content-position="left">{{ key }}</el-divider>
                <el-button :title="item['title']" :loading="pageCtl.loading['r2_' + item['row_id']]" style="cursor: pointer" v-for="item in val"
                           :key="item['row_id']"
                           :type="item.status === 'error' ? 'text' : convertStatus(item.status)"
                           :class="item.status === 'error' ? 'fatal-error' : ''"
                           :plain="item.status === 'info'"
                           @click="searchReport2Details(item['row_id'])">
                  {{ item.name }}
                </el-button>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="刷新MV" name="REFRESH_MV">
            <div>
              <el-row>
                <el-col :span="24">
                  <el-transfer
                      v-loading="pageCtl.loading.report3"
                      filterable
                      :filter-method="report3Filter"
                      filter-placeholder="Search"
                      style="width: 900px"
                      target-order="push"
                      :titles="['Available Materialized View', 'Selected Materialized View']"
                      v-model="pageCtl.report3Value"
                      :data="pageCtl.report3Data">
                  </el-transfer>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-popconfirm :title="'确定刷新'+pageCtl.report3Value.length+'个物化视图? 同一时间只能有一个用户执行刷新操作'"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="report3RefreshMV"
                                 confirmButtonType="danger"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'>
                    <template #reference>
                      <el-button type="primary" style="float: left;margin-top: 20px" :loading="pageCtl.loading.report3Refresh"
                                 :disabled="pageCtl.report3Value.length === 0">
                        Refresh MV({{ pageCtl.report3Value.length }})
                      </el-button>
                    </template>
                  </el-popconfirm>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-divider content-position="left">Execute Logs</el-divider>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <pre style="height: 400px; overflow: auto">{{ pageCtl.report3Logs.join('\r\n') }}</pre>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="任务关系" name="JOB_MAP" v-loading="pageCtl.loading.report4" style="overflow: auto">
            <el-row>
              <el-col :span="4">
                <el-input placeholder="Search" v-model="pageCtl.report4Search" style="width: 100%" clearable></el-input>
              </el-col>
              <el-col :span="4" style="padding-left: 10px">
                <scp-search :click-native="searchReport4" :data="{}"/>
              </el-col>
            </el-row>
            <div>
              <chart ref="report4Chart" :style="{width:'1750px',height:'700px'}" :option="pageCtl.report4ChartOpt"/>
            </div>
          </el-tab-pane>
          <el-tab-pane label="执行时序" name="JOB_GANTT" v-loading="pageCtl.loading.report5" style="overflow: auto">
            <el-row>
              <el-col :span="4">
                <el-date-picker
                    style="width: var(--scp-input-width)"
                    v-model="pageCtl.report5Search"
                    align="right"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    type="date"
                    :clearable="false"
                    placeholder="Date">
                </el-date-picker>
              </el-col>
              <el-col :span="4">
                <el-input placeholder="Task Name" v-model="pageCtl.report5Search2" style="width: 100%" clearable
                          @keydown.enter="searchReport5"></el-input>
              </el-col>
              <el-col :span="4" style="padding-left: 10px">
                <scp-search :click-native="searchReport5" :data="{}"/>
              </el-col>
            </el-row>
            <div ref="report5GanttRef" :style="{height: pageCtl.report5Hight}"></div>
          </el-tab-pane>
        </el-tabs>
        <div style="height: 2rem"></div>
      </div>
    </div>

    <!-- modify -->
    <scp-draggable-resizable w="60vw" h="680px" v-model="pageCtl.visible.report2Details" title="Data Sync RectBase"
                             :delete-loading="pageCtl.loading.deleteReport2Details" :delete="deleteReport2Details"
                             delete-confirm-text="确定删除此任务? 该操作无法撤销"
                             :save="modifyReport2Details" :save-loading="pageCtl.loading.modifyReport2Details">
      <template v-slot="{height}">
        <el-row>
          <el-col :span="18" style="margin: 5px">
            <el-input type="text" placeholder="Name" v-model="pageCtl.report2Details.NAME"></el-input>
          </el-col>
          <el-col :span="4" style="margin: 5px">
            <el-switch
                v-model="pageCtl.report2Details.ENABLE"
                active-color="#13ce66"
                inactive-color="#ff4949">
            </el-switch>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 5px;height: calc(100% - 60px)">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.report2Details.CONFIG" lang="json" style="border: 1px dotted var(--scp-border-color);width: 100%;"
                            :style="{height: (height - 170) + 'px'}"/>
          </el-col>
          <el-col :style="{borderLeft:'1px dashed var(--scp-border-color)'}" :span="1">
            <el-tooltip effect="light" content="Format SQL" :show-after="1000">
              <el-tag type="info" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatJSON('report2')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>

    <!-- new -->
    <scp-draggable-resizable w="60vw" h="680px" v-model="pageCtl.visible.report2DetailsNew" title="New Data Sync RectBase"
                             :save-loading="pageCtl.loading.createReport2Details" :save="createReport2Details">
      <template v-slot="{height}">
        <el-row>
          <el-col :span="18" style="margin: 5px">
            <el-input type="text" placeholder="Name" v-model="pageCtl.report2DetailsNew.NAME"></el-input>
          </el-col>
          <el-col :span="4" style="margin: 5px">
            <el-switch
                v-model="pageCtl.report2DetailsNew.ENABLE"
                active-color="#13ce66"
                inactive-color="#ff4949">
            </el-switch>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 5px;height: calc(100% - 60px)">
          <el-col :span="23">
            <scp-ace-editor v-model="pageCtl.report2DetailsNew.CONFIG" lang="json" style="width: 100%;border: 1px dotted var(--scp-border-color)"
                            :style="{height: (height - 170) + 'px'}"/>
          </el-col>
          <el-col :style="{width: '15px', height:'200px', borderLeft:'1px dashed var(--scp-border-color)'}" :span="1">
            <el-tooltip effect="light" content="Format SQL" :show-after="1000">
              <el-tag type="info" style="cursor: pointer;font-size: 12px;padding: 0 9px" @click="formatJSON('report2New')">
                <font-awesome-icon icon="align-left"/>
              </el-tag>
            </el-tooltip>
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>

    <!-- template -->
    <scp-draggable-resizable w="60vw" h="580px" v-model="pageCtl.visible.report1Template" title="Task Template"
                             :save-loading="pageCtl.loading.report1Template">
      <template v-slot="{height}">
        <div style="padding: 5px;">
          <el-tabs v-model="pageCtl.report1Tab">
            <el-tab-pane label="创建模板" name="CREATE">
              <div :style="{height: (height - 150) + 'px'}">
                <el-row>
                  <el-col :span="12">
                    <el-input v-model="pageCtl.report1TemplateCName" placeholder="Template Name" style="width: 100%" clearable/>
                  </el-col>
                  <el-col :span="12" style="padding-left: 8px">
                    <el-button @click="saveReport1Template" :loading="pageCtl.loading.saveReport1Template" type="primary">
                      <font-awesome-icon icon="save"/>&nbsp;
                      Save
                    </el-button>
                  </el-col>
                </el-row>
                <el-row
                    style="margin-top: 5px;padding: 5px;height: calc(100% - 60px);overflow-y: auto; border: 1px dotted var(--scp-border-color);display: block"
                    v-loading="pageCtl.loading.queryReport1Template">
                  <el-button size="small" v-for="(item, index) in pageCtl.report1Value" style="margin-bottom: 8px;margin-right: 8px;"
                             :key="'list_' + index">
                    {{ item }}
                  </el-button>
                </el-row>
              </div>
            </el-tab-pane>
            <el-tab-pane label="管理模板" name="MODIFY">
              <div :style="{height: (height - 150) + 'px'}">
                <el-row>
                  <el-col :span="12">
                    <el-select v-model="pageCtl.report1TemplateID" placeholder="Select a Template" style="width: 100%"
                               clearable filterable allow-create @change="report1TemplateIDChange">
                      <el-option
                          v-for="item in pageCtl.report1TemplateOpts"
                          :key="item.ID"
                          :label="item.NAME"
                          :value="item.ID">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="12" style="padding-left: 8px">
                    <el-popconfirm title="确定删除模板?"
                                   iconColor="var(--scp-text-color-error)"
                                   @confirm="deleteReport1Template"
                                   confirmButtonType="danger"
                                   confirmButtonText='确定'
                                   cancelButtonText='取消'>
                      <template #reference>
                        <el-button>
                          <font-awesome-icon icon="times"/>&nbsp;
                          Delete
                        </el-button>
                      </template>
                    </el-popconfirm>
                    <el-button type="primary" @click="loadReport1Template">
                      <font-awesome-icon icon="check"/>&nbsp;
                      Load
                    </el-button>
                  </el-col>
                </el-row>
                <el-row
                    style="margin-top: 5px;padding: 5px;height: calc(100% - 60px);overflow-y: auto; border: 1px dotted var(--scp-border-color);display: block"
                    v-loading="pageCtl.loading.queryReport1Template">
                  <el-button size="small" v-for="(item, index) in pageCtl.report1TemplateTasks" style="margin-bottom: 8px;margin-right: 8px;"
                             :key="'list_' + index">
                    {{ item }}
                  </el-button>
                </el-row>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import mqtt from 'mqtt'
import { gantt } from 'dhtmlx-gantt' // 引入模块
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { computed, inject, onBeforeUnmount, onMounted, onUpdated, reactive, ref, watch } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'

let client: any = {} // mqtt client
const report5GanttRef = ref()
const $axios: any = inject('$axios')
const $isEmpty: any = inject('$isEmpty')
const $deepClone: any = inject('$deepClone')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')
const $randomString: any = inject('$randomString')
const pageCtl = reactive({
  subscribeTopic: 'scp/dss/ui/data-sync',
  publishTopic: 'scp/dss/scp-landing/execute',
  activeTab: 'DAILY_JOB',
  report1Tab: 'CREATE',
  visible: {
    report1Template: false,
    report2DetailsNew: false,
    report2Details: false
  },
  loading: {
    report1Template: false,
    createReport2Details: false,
    deleteReport2Details: false,
    modifyReport2Details: false,
    filter: false,
    report1: false,
    saveReport1Template: false,
    queryReport1Template: false,
    deleteReport1Template: false,
    report1Script: false,
    report1Run: false,
    report2: false,
    report3: false,
    report4: false,
    report5: false,
    report3Refresh: false
  },
  report1TemplateOpts: [] as Array<any>,
  report1TemplateID: '',
  report1TemplateCName: '',
  report1TemplateTasks: [],
  report1PID: '',
  report1Script: '',
  report1Logs: [] as Array<string>,
  report1Value: [],
  report1Data: [],
  report2Search: '',
  report2Version: '',
  report2VersionOpts: [],
  report2DataRaw: {} as any,
  report2Messages: [],
  report2Details: {
    ROW_ID: '',
    NAME: '',
    CONFIG: '',
    ENABLE: true
  },
  report2DetailsNew: {
    NAME: '',
    CONFIG: '',
    ENABLE: true
  },
  report2Status: {
    not_start: 0,
    success: 0,
    disable: 0,
    failed: 0,
    running: 0,
    error: 0
  },
  report3Data: [],
  report3Value: [],
  report3Logs: [] as Array<string>,
  report4Data: {
    data: [] as Array<any>,
    links: [],
    tips: {}
  },
  report4Search: '',
  report5Search: '',
  report5Search2: '',
  report5Data: {},
  report5Hight: '100px',
  emptyContent: '<!-- EMPTY -->',
  report4ChartOpt: {} as any
})

const initGantt = () => {
  gantt.config.xml_date = '%Y/%m/%d %H:%i:%s'
  gantt.config.scale_unit = 'hour'
  gantt.config.duration_unit = 'minute'
  gantt.config.date_scale = '%H:%i'
  gantt.config.fit_tasks = true
  gantt.config.readonly = true
  gantt.config.task_height = 20
  gantt.config.row_height = 26
  gantt.config.columns = [
    {
      name: 'text',
      label: 'Task Name',
      width: '220',
      template: function (obj) {
        let html = obj.text
        if (obj.text.indexOf('[failed]') !== -1) {
          html = '<span style="color:var(--scp-text-color-error)">' + html + '</span>'
        } else if (obj.text.indexOf('[running]') !== -1) {
          html = '<span style="color:var(--scp-text-color-success)">' + html + '</span>'
        }
        return html
      }
    },
    {
      name: 'duration',
      label: 'Duration',
      align: 'right',
      template: function (obj) {
        return obj.duration + 'min'
      }
    }
  ]
  gantt.templates.task_class = function (start, end, task) {
    if (task.text.indexOf('[failed]') !== -1) {
      return 'failed'
    }
    if (task.text.indexOf('[running]') !== -1) {
      return 'running'
    }
    return 'finish'
  }
}

const connectMqtt = () => {
  client = mqtt.connect('wss://scp-dss.cn.schneider-electric.com:61615/mqtt', {
    clientId: 'scp-ui-data-sync-' + (localStorage.getItem('username') || 'nobody').toLowerCase() + '-' + $randomString(4)
  })
  client.on('connect', e => {
    client.subscribe(pageCtl.subscribeTopic, { qos: 2 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + pageCtl.subscribeTopic)
        pageCtl.report1Logs.push('Server connected, subscribe topic: ' + pageCtl.subscribeTopic + '\r\n')
      }
    })
  })

  client.on('message', (topic, message) => {
    if (pageCtl.report1Logs.length === 1 && pageCtl.report1Logs[0] === pageCtl.emptyContent) {
      pageCtl.report1Logs = []
    }
    if (pageCtl.report1Logs.length > 1000) {
      pageCtl.report1Logs.shift()
    }
    let messageStr = '' + message

    if (messageStr.indexOf('#PID#') !== -1) {
      const msgs = messageStr.split('#PID#')
      pageCtl.report1PID = msgs[0]
      messageStr = msgs[1]
    }
    pageCtl.report1Logs.push(messageStr)

    if (messageStr.indexOf('>>>>>> ') !== -1) {
      pageCtl.loading.report1Run = false
      pageCtl.report1PID = ''
      $message.success(messageStr.split('>>>>>> ')[1])
    }
  })
}

const renderReport1Content: any = (h, option) => {
  const content = option.label
  if (content.indexOf('[failed]') !== -1) {
    return h('span', { style: 'color:var(--scp-text-color-error)' }, option.label)
  } else if (content.indexOf('[running]') !== -1 || content.indexOf('[not start]') !== -1) {
    return h('span', { style: 'color:var(--scp-bg-color-warning)' }, option.label)
  } else if (content.indexOf('[success]') !== -1) {
    return h('span', { style: 'color:var(--scp-text-color-highlight)' }, option.label)
  } else {
    return h('span', {}, option.label)
  }
}

const report1Change = () => {
  pageCtl.loading.report1Script = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report1_change',
    data: {
      selected: pageCtl.report1Value
    }
  }).then((body) => {
    pageCtl.report1Script = body
  }).finally(() => {
    pageCtl.loading.report1Script = false
  })
}

const report1Run = () => {
  pageCtl.loading.report1Run = true
  client.publish(pageCtl.publishTopic, pageCtl.report1Script)
  pageCtl.report1Logs.push('请求已发送, 如果确认无任务执行, 并长时间未得到回复, 请重启计划任务DSS_CPU_Monitor\r\n')
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  pageCtl.report1Script = ''
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report1'
  }).then((body) => {
    pageCtl.report1Data = body
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const loadReport1TemplateOpts = () => {
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report1_template_opts'
  }).then((body) => {
    pageCtl.report1TemplateOpts = body
  })
}

const showReport1Templates = () => {
  pageCtl.report1Tab = pageCtl.report1Value.length === 0 ? 'MODIFY' : 'CREATE'
  pageCtl.report1TemplateID = ''
  pageCtl.report1TemplateCName = ''
  pageCtl.report1TemplateTasks = []
  pageCtl.visible.report1Template = true
}

const loadReport1Template = () => {
  if (pageCtl.report1TemplateTasks.length === 0) {
    $message.error('Please select a template.')
    return
  }
  const invalid = []
  const valid = []
  const allTasks = pageCtl.report1Data.map(e => e.key)
  for (let i = 0; i < pageCtl.report1TemplateTasks.length; i++) {
    const task = pageCtl.report1TemplateTasks[i]
    if (allTasks.indexOf(task) === -1) {
      invalid.push(task)
    } else {
      valid.push(task)
    }
  }
  if (invalid.length > 0) {
    $message.error('发现过期任务 [' + invalid.join(', ') + '], 请确认后执行!')
  }
  pageCtl.report1Value = valid
  pageCtl.visible.report1Template = false
  report1Change()
}

const deleteReport1Template = () => {
  if (pageCtl.report1TemplateID === '') {
    $message.error('Please select a template.')
    return
  }
  pageCtl.loading.deleteReport1Template = true
  $axios({
    method: 'post',
    url: '/system/data_sync/delete_report1_template',
    data: {
      id: pageCtl.report1TemplateID
    }
  }).then(() => {
    $message.success('Template deleted.')
    loadReport1TemplateOpts()
    pageCtl.report1TemplateID = ''
    pageCtl.visible.report1Template = false
  }).finally(() => {
    pageCtl.loading.deleteReport1Template = false
  })
}

const saveReport1Template = () => {
  if (pageCtl.report1TemplateCName === '') {
    $message.error('Invalid template name!')
    return
  }
  if (pageCtl.report1Value.length === 0) {
    $message.error('One or more tasks should be selected.')
    return
  }
  pageCtl.loading.saveReport1Template = true
  $axios({
    method: 'post',
    url: '/system/data_sync/save_report1_template',
    data: {
      name: pageCtl.report1TemplateCName,
      tasks: JSON.stringify(pageCtl.report1Value)
    }
  }).then(() => {
    $message.success('Template [' + pageCtl.report1TemplateCName + '] saved.')
    loadReport1TemplateOpts()
    pageCtl.visible.report1Template = false
  }).finally(() => {
    pageCtl.loading.saveReport1Template = false
  })
}

const report1TemplateIDChange = () => {
  if (pageCtl.report1TemplateID === '') {
    pageCtl.report1TemplateTasks = []
    return
  }
  pageCtl.loading.queryReport1Template = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report1_template',
    data: {
      id: pageCtl.report1TemplateID
    }
  }).then((body) => {
    pageCtl.report1TemplateTasks = JSON.parse(body || '[]')
  }).finally(() => {
    pageCtl.loading.queryReport1Template = false
  })
}

const searchReport2Version = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report2_version'
  }).then((body) => {
    pageCtl.report2VersionOpts = body
    if (pageCtl.report2VersionOpts.length > 0) {
      pageCtl.report2Version = pageCtl.report2VersionOpts[0]
    }
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report2',
    data: {
      version: pageCtl.report2Version
    }
  }).then((body) => {
    const data = body.data
    pageCtl.report2DataRaw = data
    pageCtl.report2Messages = body.message
    // init loading
    const loading = $deepClone(pageCtl.loading)
    const status = {
      not_start: 0,
      success: 0,
      disable: 0,
      failed: 0,
      running: 0,
      error: 0
    }
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const details = data[key]
        for (let i = 0; i < details.length; i++) {
          loading['r2_' + details[i].row_id] = false
          switch (details[i].status) {
            case 'not_start':
              status.not_start += 1
              break
            case 'failed':
              status.failed += 1
              break
            case 'running':
              status.running += 1
              break
            case 'success':
              status.success += 1
              break
            case 'disable':
              status.disable += 1
              break
            case 'error':
              status.error += 1
              break
          }
        }
      }
    }
    pageCtl.loading = loading
    pageCtl.report2Status = status
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report3'
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report4'
  }).then((body) => {
    pageCtl.report4Data = body
    refreshReport4Opts()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const searchReport5 = () => {
  pageCtl.loading.report5 = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report5',
    data: {
      date: pageCtl.report5Search,
      key: pageCtl.report5Search2
    }
  }).then((body) => {
    const data = [] as Array<any>
    for (let i = 0; i < body.length; i++) {
      const e = body[i]
      data.push({
        id: (i + 1),
        text: e.KEY,
        start_date: e.START_DATE,
        duration: e.DURATION
      })
    }

    gantt.init(report5GanttRef.value)
    gantt.clearAll()
    gantt.parse({
      data
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report5 = false
  })
}

const searchReport2Details = (key) => {
  pageCtl.loading['r2_' + key] = true
  $axios({
    method: 'post',
    url: '/system/data_sync/query_report2_details',
    data: {
      key
    }
  }).then((body) => {
    pageCtl.report2Details = body
    pageCtl.visible.report2Details = true
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading['r2_' + key] = false
  })
}

const modifyReport2Details = () => {
  if (pageCtl.report2Details.NAME === '' || pageCtl.report2Details.CONFIG === '') {
    $message.error('Job name or config cannnot be empty!')
    return
  }
  pageCtl.loading.modifyReport2Details = true
  $axios({
    method: 'post',
    url: '/system/data_sync/modify_report2_details',
    data: pageCtl.report2Details
  }).then((body) => {
    if (body) {
      $message.error(body + '')
      return
    }
    pageCtl.visible.report2Details = false
    $message.success('Changes Saved')
    searchReport2()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modifyReport2Details = false
  })
}

const deleteReport2Details = () => {
  pageCtl.loading.deleteReport2Details = true
  $axios({
    method: 'post',
    url: '/system/data_sync/delete_report2_details',
    data: pageCtl.report2Details
  }).then(function () {
    pageCtl.visible.report2Details = false
    $message.success('Job Deleted!')
    searchReport2()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.deleteReport2Details = false
  })
}

const createReport2DetailsWin = () => {
  pageCtl.report2DetailsNew = {
    NAME: '',
    CONFIG: '',
    ENABLE: true
  }
  pageCtl.visible.report2DetailsNew = true
}

const createReport2Details = () => {
  if (pageCtl.report2DetailsNew.NAME === '' || pageCtl.report2DetailsNew.CONFIG === '') {
    $message.error('Job name or config cannnot be empty!')
    return
  }
  pageCtl.loading.createReport2Details = true
  $axios({
    method: 'post',
    url: '/system/data_sync/create_report2_details',
    data: pageCtl.report2DetailsNew
  }).then((body) => {
    if (body) {
      $message.error(body + '')
      return
    }
    pageCtl.visible.report2DetailsNew = false
    $message.success('Job Saved')
    searchReport2()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.createReport2Details = false
  })
}

const handleTabClick = (tab) => {
  if (tab.paneName === 'RERUN_JOB') {
    searchReport1()
  } else if (tab.paneName === 'DAILY_JOB') {
    searchReport2()
  } else if (tab.paneName === 'REFRESH_MV') {
    searchReport3()
  } else if (tab.paneName === 'JOB_MAP') {
    searchReport4()
  } else if (tab.paneName === 'JOB_GANTT') {
    searchReport5()
  }
}

const formatJSON = (key) => {
  let json = ''
  if (key === 'report2') {
    json = pageCtl.report2Details.CONFIG
  } else if (key === 'report2New') {
    json = pageCtl.report2DetailsNew.CONFIG
  }
  $axios({
    method: 'post',
    url: '/system/data_sync/format_json',
    data: {
      json
    }
  }).then((body) => {
    if (key === 'report2') {
      pageCtl.report2Details.CONFIG = body
    } else if (key === 'report2New') {
      pageCtl.report2DetailsNew.CONFIG = body
    }
  }).catch((error) => {
    console.log(error)
  })
}

const report3Filter = (query, item) => {
  return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
}

const report3RefreshMV = async () => {
  pageCtl.loading.report3Refresh = true
  pageCtl.report3Logs = []
  const total = pageCtl.report3Value.length
  pageCtl.report3Logs.push($dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss') + ' - 开始执行视图刷新, 共' + total + '个')
  const start = new Date().getTime()
  for (let i = 0; i < total; i++) {
    const mv = pageCtl.report3Value[i]
    let success = true
    pageCtl.report3Logs.push($dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss') + ' - refreshing ' + mv + ' (' + (i + 1) + '/' + total + ')...')
    await $axios({
      method: 'post',
      url: '/system/data_sync/report3_refresh_mv',
      data: {
        mv
      }
    }).then((body) => {
      if (typeof body === 'string') {
        success = false
        pageCtl.report3Logs.push(body)
        pageCtl.report3Logs.push($dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss') + ' - 任务中断')
      } else {
        pageCtl.report3Logs.push(...body)
        pageCtl.report3Logs.push('------------------------------------------')
      }
    }).catch((error) => {
      console.log(error)
    })

    // @ts-ignore
    if (success === false) {
      break
    }
  }
  pageCtl.report3Logs.push($dateFormatter(new Date(), 'yyyy/MM/dd hh:mm:ss') + ' - 任务执行完成, 共花费' + $thousandBitSeparator(((new Date().getTime() - start) / 1000).toFixed(0)) + '秒')
  pageCtl.loading.report3Refresh = false
}

// 遍历父级
const searchTreeLeft = (links, items) => {
  let result = $deepClone(items)
  // 遍历检查, items里面, 是否有target角色的对象, 如果有, 则找到对应的source
  let hasFind = false
  for (let i = 0; i < links.length; i++) {
    const target = links[i].target.toLowerCase()
    const source = links[i].source.toLowerCase()
    if (items.indexOf(target) !== -1 && items.indexOf(source) === -1) {
      result.push(source)
      hasFind = true
    }
  }

  if (hasFind === true) {
    result = searchTreeLeft(links, result)
  }

  return result
}

// 遍历子项
const searchTreeRight = (links, items) => {
  let result = $deepClone(items)
  // 遍历检查, items里面, 是否有source角色的对象, 如果有, 则找到对应的target
  let hasFind = false
  for (let i = 0; i < links.length; i++) {
    const target = links[i].target.toLowerCase()
    const source = links[i].source.toLowerCase()
    if (items.indexOf(source) !== -1 && items.indexOf(target) === -1) {
      result.push(target)
      hasFind = true
    }
  }

  if (hasFind === true) {
    result = searchTreeRight(links, result)
  }

  return result
}

const convertStatus = (code) => {
  switch (code) {
    case 'not_start':
      return 'default'
    case 'failed':
      return 'danger'
    case 'running':
      return 'success'
    case 'success':
      return 'primary'
    case 'disable':
      return 'info'
  }
  return 'text'
}

const refreshReport4Opts = () => {
  const tips = pageCtl.report4Data.tips

  // 根据link从node里面选择符合筛选项的node
  let nodeNames = [] as Array<any>
  const searchItem = pageCtl.report4Search.toLowerCase()
  if (searchItem !== '') {
    // 复制对象, 因为searchTree方法会对数据进行修改
    const links = $deepClone(pageCtl.report4Data.links)
    const items = $deepClone(pageCtl.report4Data.data.filter(e => pageCtl.report4Search === '' || e.name.toLowerCase().indexOf(searchItem) !== -1).map(e => e.name.toLowerCase()))
    const lefts = searchTreeLeft(links, items)
    const rights = searchTreeRight(links, items)
    nodeNames.push(...lefts)
    nodeNames.push(...rights)
    nodeNames = Array.from(new Set(nodeNames))
  }

  pageCtl.report4ChartOpt = {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: function (params) {
        const tip = [] as Array<string>
        const name = params.name as string
        const title = (tips[name] || {}).title || ''
        tip.push(name)
        tip.push('<br>')
        tip.push(title.replace(/\n/g, '<br>'))
        return tip.join('')
      }
    },
    series: [
      {
        type: 'sankey',
        left: 20.0,
        top: 20.0,
        right: 180.0,
        bottom: 25.0,
        data: pageCtl.report4Data.data.filter(e => pageCtl.report4Search === '' || nodeNames.indexOf(e.name.toLowerCase()) !== -1).map(e => {
          if (searchItem !== '' && e.name.indexOf(searchItem) !== -1) {
            e.itemStyle.borderColor = '#FFB400'
            e.itemStyle.borderWidth = 2
          } else {
            e.itemStyle.borderColor = e.itemStyle.borderColorOrg
            e.itemStyle.borderWidth = 1
          }
          return e
        }),
        links: pageCtl.report4Data.links,
        lineStyle: {
          color: 'source',
          curveness: 0.5
        },
        itemStyle: {
          borderColor: 'rgba(0,0,0,0)'
        },
        label: {
          fontSize: 8
        }
      }]
  }
}

onMounted(() => {
  initGantt()
  pageCtl.report5Search = $dateFormatter(new Date(), 'yyyy/MM/dd')
  pageCtl.report5Hight = (document.documentElement.clientHeight - 240) + 'px'
  connectMqtt()
  loadReport1TemplateOpts()
  searchReport2Version()

  if (pageCtl.activeTab === 'RERUN_JOB') {
    searchReport1()
  } else if (pageCtl.activeTab === 'DAILY_JOB') {
    if (pageCtl.report2Version !== '') {
      searchReport2()
    } else {
      searchReport2Version()
    }
  } else if (pageCtl.activeTab === 'REFRESH_MV') {
    searchReport3()
  } else if (pageCtl.activeTab === 'JOB_MAP') {
    searchReport4()
  } else if (pageCtl.activeTab === 'JOB_GANTT') {
    searchReport5()
  }
})

const _report1PIDScript = computed(() => {
  return $isEmpty(pageCtl.report1PID) ? '' : '任务正在执行, 若要终止, 请至服务器中执行命令 <i style="color:deepskyblue">taskkill /F /pid ' + pageCtl.report1PID + ' /T</i>'
})

const _report2Data = computed(() => {
  if (pageCtl.report2Search) {
    const result = {} as any
    for (const key in pageCtl.report2DataRaw) {
      const details = pageCtl.report2DataRaw[key]
      const searched = [] as Array<any>
      for (let i = 0; i < details.length; i++) {
        if (details[i].name.toLowerCase().indexOf(pageCtl.report2Search.toLowerCase()) !== -1) {
          searched.push(details[i])
        }
      }
      if (searched.length > 0) {
        result[key] = searched
      }
    }
    return result
  } else {
    return pageCtl.report2DataRaw
  }
})

const _report2FinishRate = computed(() => {
  let total = 0
  let finish = 0
  total += pageCtl.report2Status.not_start
  total += pageCtl.report2Status.success
  total += pageCtl.report2Status.failed
  total += pageCtl.report2Status.running

  finish += pageCtl.report2Status.success
  finish += pageCtl.report2Status.failed
  if (total === 0 || finish === 0) {
    return finish + ' / ' + total + ' (0)'
  } else {
    return finish + ' / ' + total + ' (' + (finish / total * 100).toFixed(1) + '%)'
  }
})

watch(() => pageCtl.report2Version, () => {
  searchReport2()
})

onUpdated(() => {
  const report1Logs = document.getElementById('report1Logs') as HTMLPreElement
  // 900 是1.5屏日志的高度, 也就是当用户滚动了0.5屏幕的高度时, 日志将不再自动滚动
  if (report1Logs.scrollTop === 0 || report1Logs.scrollHeight - report1Logs.scrollTop < 900) {
    report1Logs.scrollTo({ top: report1Logs.scrollHeight, behavior: 'smooth' })
  }
})

const closeMqttClient = () => {
  try {
    client.end()
  } catch (e) {

  }
}

onBeforeRouteLeave((to, from, next) => {
  closeMqttClient()
  next()
})

onBeforeUnmount(() => {
  closeMqttClient()
})

window.onbeforeunload = () => {
  closeMqttClient()
}

</script>
<style lang="scss">
#dataSync {
  .fatal-error {
    background-color: rebeccapurple !important;
    border-color: rebeccapurple !important;
    color: #fff !important;
  }

  .fatal-error:hover {
    background-color: mediumpurple !important;
    border-color: mediumpurple !important;
    color: #fff !important;
  }

  .el-button--small {
    margin-left: 0 !important;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .el-divider__text {
    font-size: 0.5rem !important;
    background-color: var(--scp-bg-color) !important;
  }

  .el-transfer-panel {
    width: calc(48% - 80px) !important;
  }

  .el-transfer-panel__body.is-with-footer {
    height: 416px !important;
  }

  .el-transfer-panel__footer {
    height: 24px !important;
    text-align: right;
    padding-right: 10px;
    border-top: 0;

    .footer-link {
      font-style: italic;
      font-size: 0.4rem;
      color: var(--scp-text-color-secondary);
    }

    .footer-link:hover {
      color: var(--scp-text-color-highlight);
    }
  }

  .el-transfer-panel__body {
    height: 440px !important;
  }

  .el-transfer-panel__list.is-filterable {
    height: 350px !important;
  }

  .gantt_cell {
    font-size: 0.5rem;
    color: var(--scp-text-color-primary)
  }

  .gantt_row, .gantt_task_row, .gantt_grid_scale, .gantt_task_scale, .gantt_task_vscroll {
    background-color: var(--scp-bg-color) !important;
  }

  .gantt_row, .gantt_task_row {
    border-bottom: 1px solid var(--scp-border-color-lighter) !important;
  }

  .gantt_task_cell {
    border-right: 1px solid var(--scp-border-color-lighter) !important;
  }
}
</style>
