<template>
  <div class="left-sidebar" id="changeLog">
    <div class="widget">
      <div class="widget-body">
        <el-row :gutter="30">
          <el-col :span="4" style="text-align: right;padding-bottom: 4px;">
            <el-button type="primary" style="font-size: 0.4rem;" @click="newChangeLog"
                       v-show="_showNewBtn">New
            </el-button>
          </el-col>
          <el-col :span="20" style="border-left: solid 1px var(--scp-border-color);height:24px;"></el-col>
        </el-row>
        <el-row :gutter="30" v-for="(item,index) in pageCtl.changeLogs" :key="index"
                :style="index === 0? {backgroundColor: 'var(--scp-bg-color-fill-lighter)'} :{}">
          <el-col :span="4">
            <div class="time-stamp">
              <i class="el-icon-price-tag" style="transform: rotate(-30deg);"></i>
              <span>v{{ item.version }}</span>
              <br>
              <i class="el-icon-link" style="transform: rotate(45deg)"></i>
              <span style="font-size: 0.4rem">{{ item.submit }}</span>
            </div>
          </el-col>
          <el-col :span="20" style="border-left: solid 1px var(--scp-border-color);padding-bottom: 15px">
            <div class="log-details" :style="index > 0 ? {borderTop: '1px dashed var(--scp-border-color)'} : {}">
              <div class="log-label">{{ item.version }}</div>
              <div class="log-message">Summary: {{ item.summary }}</div>
              <ul v-for="(log,index1) in item['logs']" :key="index1">
                <li style="padding: 4px 0;font-weight: bold;">{{ log.category }}</li>
                <li>
                  <ul style="list-style: circle; padding-left: 20px;">
                    <li v-for="(list,index3) in log.contents" :key="index3">{{ list }}</li>
                  </ul>
                </li>
              </ul>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="4">
            <div class="load-log" @click="moreLogs" style="padding-top:10px;">
              <i v-show="!pageCtl.loading.more">
                <font-awesome-icon :icon="['fas', 'angles-down']"/>
              </i>
              <i v-show="pageCtl.loading.more">
                <font-awesome-icon :icon="['fas', 'spinner']" spin/>
              </i>
            </div>
          </el-col>
          <el-col :span="20" style="border-left: solid 1px var(--scp-border-color)">
            <div class="log-details">
              <p></p>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!--new changelog-->
    <scp-draggable-resizable w="70vw" h="75vh" title="New Changelog"
                             :save="saveChange" :save-loading="pageCtl.loading.saveNewChangeLog"
                             v-model="pageCtl.newChangeLogVisible">
      <template v-slot>
        <div class="tip-contet">
          <div class="log-input">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-select v-model="pageCtl.newLog.releasedBy" @change="getReleasedName" style="width: 100%">
                  <el-option v-for="menu in pageCtl.maintainers" :key="menu.SESA" :label="menu.NAME"
                             :value="menu.SESA"/>
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-autocomplete
                    class="inline-input"
                    style="width: 100%"
                    clearable
                    v-model="pageCtl.newLog.releasedTime"
                    :fetch-suggestions="releasedTimeSuggestion"
                    placeholder="Release Time"
                ></el-autocomplete>
              </el-col>
              <el-col :span="6">
                <el-input
                    v-model="pageCtl.newLog.version"
                    placeholder="Version">
                </el-input>
              </el-col>
            </el-row>
          </div>
          <div class="log-content" v-for="(item,index) in pageCtl.newLog.logDetails" :key="index">
            <div class="log-title">
              <el-select v-model="pageCtl.newLog.logDetails[index].category" class="group-select"
                         placeholder="Category">
                <el-option
                    v-for="category in pageCtl.categoryOption" :key="category" :label="category" :value="category">
                </el-option>
              </el-select>
              <div class="pull-right" @click="delType(index)" style="color:var(--scp-text-color-secondary);cursor: pointer" v-show="index > 0">
                <font-awesome-icon icon="times"/>
              </div>
            </div>
            <ul>
              <li class="log-list" v-for="(details,index1) in item.details" :key="index1">
                <el-row style="text-align: center">
                  <el-col :span="16">
                    <el-input v-model="details.content" placeholder="Change Content"></el-input>
                  </el-col>
                  <el-col :span="6">
                    <el-select v-model="details.raisedBy" filterable placeholder="Raised By"
                               style="width: var(--scp-input-width)">
                      <el-option
                          v-for="raisedBy in pageCtl.raisedByOption" :key="raisedBy['SESA_CODE']"
                          :label="raisedBy['USER_NAME']"
                          :value="raisedBy['SESA_CODE']"/>
                    </el-select>
                  </el-col>
                  <el-col :span="2">
                    <div @click="delLine(index,index1)" style="color: var(--scp-text-color-error);cursor: pointer" v-show="index1 > 0">
                      <font-awesome-icon icon="minus"/>
                    </div>
                  </el-col>
                </el-row>
              </li>
              <li class="log-list">
                <el-row style="text-align: center">
                  <el-col :span="14" style="opacity: 0">1</el-col>
                  <el-col :span="8" style="opacity: 0">1</el-col>
                  <el-col :span="2">
                    <div @click="newLine(index)" style="color:var(--scp-text-color-highlight);cursor: pointer">
                      <font-awesome-icon icon="plus"/>
                    </div>
                  </el-col>
                </el-row>
              </li>
            </ul>
          </div>
          <el-row style="text-align: center">
            <el-col :span="22">&nbsp;</el-col>
            <el-col :span="2">
              <div class="pull-right" @click="newType"
                   style="color:var(--scp-text-color-highlight);cursor: pointer;margin-top :10px;margin-right: 10px">
                <font-awesome-icon icon="plus"/>
              </div>
            </el-col>
          </el-row>
        </div>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive } from 'vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')

const pageCtl = reactive({
  maintainers: [{ SESA: 'SESA513408', NAME: 'Eric Zhang' }, { SESA: 'SESA570303', NAME: 'Chuck NI' }, {
    SESA: 'SESA97925',
    NAME: 'Lin BAI'
  }],
  showing: 0,
  loading: {
    more: false,
    saveNewChangeLog: false
  } as any,
  changeLogs: [] as any,
  newChangeLogVisible: false,
  categoryOption: [],
  raisedByOption: [],
  suggestReleaseTime: [],
  newLog: {} as any
})

const _showNewBtn = computed(() => {
  for (let i = 0; i < pageCtl.maintainers.length; i++) {
    if (pageCtl.maintainers[i].SESA === localStorage.getItem('username')) {
      return true
    }
  }
  return false
})

const loadLog = (offset, next) => {
  pageCtl.loading.more = true
  $axios({
    method: 'post',
    url: '/system/query_change_log',
    data: {
      offset,
      next
    }
  }).then((body) => {
    pageCtl.changeLogs.push(...body)
    pageCtl.showing = offset + next
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.more = false
  })
}
const moreLogs = () => {
  loadLog(pageCtl.showing, 1)
}
const saveChange = () => {
  // check input validate
  pageCtl.loading.saveNewChangeLog = true
  if (!pageCtl.newLog.version) {
    $message.error('Please confirm version')
    pageCtl.loading.saveNewChangeLog = false
    return
  }
  if (!pageCtl.newLog.releasedTime) {
    $message.error('Please confirm released time')
    pageCtl.loading.saveNewChangeLog = false
    return
  }
  for (let i = 0; i < pageCtl.newLog.logDetails.length; i++) {
    const obj = pageCtl.newLog.logDetails[i]
    if (!obj.category) {
      $message.error('Please confirm category')
      pageCtl.loading.saveNewChangeLog = false
      return
    } else {
      for (let j = 0; j < obj.details.length; j++) {
        if (!obj.details[j].content) {
          $message.error('Please confirm change content')
          pageCtl.loading.saveNewChangeLog = false
          return
        }
      }
    }
  }

  $axios({
    method: 'post',
    url: '/system/save_changelog',
    data: pageCtl.newLog
  }).then(() => {
    pageCtl.newChangeLogVisible = false
    pageCtl.changeLogs = []
    loadLog(0, 4)
    initNewForm()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.saveNewChangeLog = false
  })
}
const newChangeLog = () => {
  pageCtl.newChangeLogVisible = true
}
const initNewForm = () => {
  pageCtl.newLog = {
    releasedBy: '',
    releasedName: '',
    releasedTime: '',
    commits: [],
    version: '',
    logDetails: [
      {
        category: '',
        details: [
          {
            content: '',
            raisedBy: ''
          }
        ]
      }
    ]
  }
  pageCtl.loading.initNew = true
  $axios({
    method: 'post',
    url: '/system/init_changelog_page',
    data: {
      maintainers: pageCtl.maintainers.map(e => e.SESA)
    }
  }).then((body) => {
    pageCtl.suggestReleaseTime = body.releaseTime
    pageCtl.categoryOption = body.categories
    pageCtl.raisedByOption = body.raisedBy
    pageCtl.newLog.releasedTime = pageCtl.suggestReleaseTime[0]
    pageCtl.newLog.releasedBy = localStorage.getItem('username')
    getReleasedName(localStorage.getItem('username'))
    pageCtl.newLog.version = body.version
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.initNew = false
  })
}
const newType = () => {
  pageCtl.newLog.logDetails.push(
    {
      category: '',
      details: [
        {
          content: '',
          raisedBy: ''
        }
      ]
    }
  )
}
const delType = (index) => {
  // e.currentTarget.parentElement.remove() //dom中删除父级元素，数组中内容不变
  pageCtl.newLog.logDetails.splice(index, 1) // 数组中删除内容
}
const newLine = (index) => {
  pageCtl.newLog.logDetails[index].details.push(
    {
      content: '',
      raisedBy: ''
    }
  )
}
const delLine = (index, index1) => {
  pageCtl.newLog.logDetails[index].details.splice(index1, 1)
}
const releasedTimeSuggestion = (queryString, cb) => {
  const restaurants = pageCtl.suggestReleaseTime.map(e => {
    return { value: e }
  })
  const results = queryString ? restaurants.filter(createFilter(queryString)) : restaurants
  cb(results)
}
const createFilter = (queryString) => {
  return (restaurant) => {
    return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
  }
}
const getReleasedName = (releasedBy) => {
  let match = false
  for (let i = 0; i < pageCtl.maintainers.length; i++) {
    if (pageCtl.maintainers[i].SESA === releasedBy) {
      match = true
      pageCtl.newLog.releasedName = pageCtl.maintainers[i].NAME
    }
  }
  if (!match) {
    pageCtl.newLog.releasedName = releasedBy
  }
}

onMounted(() => {
  loadLog(0, 4)
  initNewForm()
})

</script>

<style lang="scss">
#changeLog {
  .time-stamp {
    text-align: right;
    padding-top: 10px;

    &:hover {
      cursor: pointer;
    }
  }

  .log-details {
    padding-top: 10px;

    .log-label {
      font-size: 0.65rem;
      color: var(--scp-text-color-highlight);
    }

    .log-message {
      font-size: 0.45rem;
    }
  }

  .load-log {
    text-align: right;

    i {
      transform: rotate(-90deg);
    }
  }

  .tip-contet {
    box-sizing: border-box;
    padding: 10px;
    height: calc(100% - 10px);
    text-align: -webkit-center;
    overflow: auto;

    .log-input {
      box-sizing: border-box;

      .el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 100% !important;
      }

      .el-select.el-select--medium {
        width: 100% !important;
      }
    }

    .log-content {
      min-height: 100px;
      text-align: left;
      margin-top: 10px;
      border: solid 1px var(--scp-border-color-lighter);

      .log-title {
        background-color: var(--scp-bg-color-fill);
        padding: 4px 10px 6px 10px;

        .group-select {
          width: 200px;
        }
      }

      ul {
        padding: 8px;
      }

      .log-list {
        padding-top: 8px;
      }
    }
  }
}
</style>
