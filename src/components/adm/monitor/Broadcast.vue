<template>
  <div class="left-sidebar" id="broadcast">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-filter v-model="pageCtl.conditions.$scpFilter" :cascader-base-opts="pageCtl.filterOpts" :filter-base="['SY_DIALOG_CONFIG', 'SY_DIALOG_AUTH']"
                        :after-apply="searchBroadcast"/>
          </el-col>
          <el-col :span="1">
            <scp-search ref="searchRef" :click-native="searchBroadcast"/>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <el-table v-loading="pageCtl.tableLoading" :data="pageCtl.tableData" style="width: 100%">
            <el-table-column sortable :show-overflow-tooltip="true" fixed prop="pageName" label="Page Name" :min-width="100"/>
            <el-table-column sortable :show-overflow-tooltip="true" fixed prop="remindSubject" label="Remind Subject" :min-width="200"/>
            <el-table-column sortable prop="frequency" label="Frequency"/>
            <el-table-column sortable prop="remindCount" label="Remind Count"/>
            <el-table-column prop="description" label="Description" :min-width="250">
              <template #default="scope">
                <el-tooltip class="item" :show-after=1000 effect="light" placement="left-start">
                  <template #content>
                    <div class="html-content" v-html="scope.row.description"></div>
                  </template>
                  <div class="description-cell">
                    {{ scope.row.description }}
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column sortable prop="validFrom" label="Valid From" :min-width="100">
              <template v-slot="scope">
                <span>{{ formatDate(scope.row.validFrom) }}</span>
              </template>
            </el-table-column>
            <el-table-column sortable prop="validTo" label="Valid To" :min-width="100">
              <template v-slot="scope">
                <span>{{ formatDate(scope.row.validTo) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="Operations" :min-width="50" align="center">
              <template #default="scope">
<!--                <el-popover placement="left-start" :width="400" trigger="click">-->
<!--                  <template #reference>-->
<!--                    <el-button link type="primary" size="small" @click="handleClick(scope.row)">Auth-->
<!--                    </el-button>-->
<!--                  </template>-->
<!--                  <el-input-->
<!--                      v-model="pageCtl.details.filterText"-->
<!--                      style="width: 240px"-->
<!--                      placeholder="Filter keyword"-->
<!--                  />-->
<!--                  <el-tree-->
<!--                      ref="treeRef"-->
<!--                      style="max-width: 600px"-->
<!--                      :data="_detailsData"-->
<!--                      :props="pageCtl.details.defaultProps"-->
<!--                      default-expand-all-->
<!--                      :filter-node-method="filterDetailsNode"-->
<!--                  />-->
<!--                </el-popover>-->
                <el-button link type="primary" @click="editBroadcast(scope.row)" size="small">
                  <font-awesome-icon icon="edit"/>
                </el-button>
                <el-popconfirm title="Are you sure to delete this broadcast?" @confirm="deleteBroadcast(scope.row)">
                  <template #reference>
                    <el-button link type="danger" size="small">
                      <font-awesome-icon icon="trash"/>
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
              size="small"
              style="margin:10px 5px 0 10px"
              v-model:current-page="pageCtl.conditions.page.currentPage"
              v-model:page-size="pageCtl.conditions.page.length"
              :page-sizes="[5, 10, 20, 30, 50, 100 ]"
              layout="total, sizes, ->, prev, pager, next"
              :total="pageCtl.conditions.page.total"
              :background="true"
              @size-change="searchBroadcast"
              @current-change="searchBroadcast"
          />
        </div>
      </div>
    </div>
    <scp-broadcast-create v-model="pageCtl.visible.maintainRecords" :rule-form="ruleForm"></scp-broadcast-create>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import ScpFilter from '@/components/starter/components/Filter.vue'
import ScpBroadcastCreate from '@/components/starter/components/BroadcastCreate.vue'

const $axios: any = inject('$axios')
const treeRef = ref()
const searchRef = ref()

interface RuleForm {
  url: string
  pageName: string
  remindSubject: string
  validFrom: string
  validTo: string
  frequency: string
  remindCount: number
  description: string
  specificTarget: []
}

const ruleForm = reactive<RuleForm>({
  url: '',
  pageName: '',
  remindSubject: '',
  validFrom: '',
  validTo: '',
  frequency: '',
  remindCount: 0,
  description: '',
  specificTarget: []
})

const pageCtl = reactive({
  filterOpts: [],
  tableData: [],
  tableLoading: false,
  details: {
    defaultProps: {
      children: 'children',
      label: 'label'
    },
    filterText: '',
    detailsKey: '',
    detailsData: {}
  },
  visible: {
    details: true,
    maintainRecords: false
  },
  conditions: {
    $scpFilter: {
      cascader: [],
      filter: []
    },
    page: {
      currentPage: 1,
      pagging: true,
      start: 0,
      length: 10,
      total: 0
    }
  }
})

onMounted(() => {
  queryBroadcastCascader()
})

const queryBroadcastCascader = () => {
  $axios({
    method: 'post',
    url: '/system/broadcast/query_broadcast_cascader'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
  })
}

const searchBroadcast = () => {
  pageCtl.conditions.page.start = (pageCtl.conditions.page.currentPage - 1) * pageCtl.conditions.page.length
  queryBroadcastData()
}

const formatDate = (date) => {
  return date.toLocaleString('en-US', { hour12: false })
}

const _detailsData = computed(() => {
  const sourceIndex = pageCtl.tableData.findIndex((value: any) => {
    return value.url + value.remindSubject === pageCtl.details.detailsKey
  })
  let data: Object = pageCtl.tableData[sourceIndex]
  const result = [] as any
  if (data && data.hasOwnProperty('authInfo')) {
    data = data.authInfo
    let idCounter = 1

    for (const [key, values] of Object.entries(data)) {
      const children = values.map(value => ({
        id: idCounter++,
        label: value
      }))

      result.push({
        id: idCounter++,
        label: key,
        children
      })
    }
  }
  return result
})

const queryBroadcastData = () => {
  pageCtl.tableLoading = true
  $axios({
    method: 'post',
    url: '/system/broadcast/query_broadcast_config',
    data: pageCtl.conditions
  }).then((response) => {
    pageCtl.tableData = response.data
    pageCtl.conditions.page.total = response.total
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.tableLoading = false
  })
}

const deleteBroadcast = (row) => {
  ruleForm.url = row.url
  ruleForm.remindSubject = row.remindSubject
  $axios({
    method: 'post',
    url: 'system/broadcast/delete_broadcast',
    data: ruleForm
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    queryBroadcastData()
  })
}

watch(() => pageCtl.details.filterText, (val) => {
  treeRef.value!.filter(val)
})

watch(() => pageCtl.visible.maintainRecords, () => {
  if (!pageCtl.visible.maintainRecords) {
    searchBroadcast()
  }
})

const handleClick = (row) => {
  pageCtl.details.detailsKey = row.url + row.remindSubject
}

const filterDetailsNode = (value: string, data) => {
  if (!value) return true
  return data.label.includes(value)
}

const editBroadcast = (row) => {
  const specificTarget = [] as any
  for (const [key, value] of Object.entries(row.authInfo)) {
    if (Array.isArray(value)) {
      for (const item of value) {
        specificTarget.push([key, item])
      }
    }
  }
  pageCtl.visible.maintainRecords = true
  ruleForm.url = [row.url]
  ruleForm.validFrom = row.validFrom
  ruleForm.validTo = row.validTo
  ruleForm.remindSubject = row.remindSubject
  ruleForm.remindCount = row.remindCount
  ruleForm.frequency = row.frequency
  ruleForm.description = row.description
  ruleForm.specificTarget = specificTarget
}
</script>
<style lang="scss">
#broadcast {
  .description-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .el-table {
    .sort-caret {
      border-top-width: 3px;
      border-bottom-width: 3px;
      border-left-width: 3px;
      border-right-width: 3px;
      left: 3px;
    }
    .caret-wrapper {
      height: 6px;
    }
    .el-table__row {
      height: 35px !important;
    }
  }
}
</style>

<style scoped>
.html-content {
  max-width: 900px;
  max-height: 600px;
  overflow-y: auto;
}
.html-content >>> img {
  max-width: 800px;
  height: auto;
}
</style>
