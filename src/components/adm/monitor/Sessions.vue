<template>
  <div>
    <div class="left-sidebar">
      <div class="widget">
        <div class="widget-body">
          <scp-table
            ref="sessionTableRef"
            :columns="pageCtl.sessionColumns"
            :paggingSettingEnable="false"
            :pagging="false"
            :filters="false"
            :columnSorting="false"
            :contextMenuItems="pageCtl.contextMenuItems"
            :afterSelect="afterSelect"
            url="/system/query_login_sessions"/>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { inject, reactive, ref } from 'vue'
const $axios : any = inject('$axios')
const $message : any = inject('$message')
const sessionTableRef = ref()

const clearAllSession = () => {
  clearSession('all')
}

const clearSession = (type) => {
  if (confirm(type === 'all' ? 'Remove all sessions?' : 'Remove selected session?')) {
    $axios({
      method: 'post',
      url: '/system/clear_login_sessions',
      data: {
        key: type === 'all' ? 'all' : pageCtl.selectedRow.key
      }
    }).then(function () {
      sessionTableRef.value.search()
      $message.success('Session removed')
    }).catch((error) => {
      console.log(error)
    })
  }
}

const pageCtl = reactive({
  sessionColumns: [{
    data: 'userid',
    title: 'SESA No'
  }, {
    data: 'username',
    title: 'User Name'
  }, {
    data: 'loginIP',
    title: 'Login IP'
  }, {
    data: 'lastActiveTime',
    title: 'Last Active Time'
  }, {
    data: 'expiringTime',
    title: 'Expired Time'
  }, {
    data: 'idleTime',
    title: 'Idle Time'
  }, {
    data: 'location',
    title: 'Login Location'
  }],
  contextMenuItems: {
    view_split: { name: '---------' },
    remove_all_session: {
      name: 'Remove all sessions',
      callback: clearAllSession
    },
    remove_selected_session: {
      name: 'Remove selected session',
      callback: clearSession
    }
  },
  selectedRow: {} as any
})

const afterSelect = (r) => {
  pageCtl.selectedRow = r
}
</script>
