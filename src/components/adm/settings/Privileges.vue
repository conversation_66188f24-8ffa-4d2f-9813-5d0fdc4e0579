<template>
  <div>
    <div class="left-sidebar">
      <div class="widget">
        <div class="widget-body" style="min-height:350px">
          <div class="user-list">
            <el-row class="search-input search-user" style="margin-bottom:var(--scp-widget-margin)">
              <el-col :span="16">
                <el-input v-model="pageCtl.userParams.keywords" placeholder="Keywords" clearable size="default" @keyup.enter="userSearch"></el-input>
              </el-col>
              <el-col :span="8">
                <el-button @click="userSearch" size="default">
                  <font-awesome-icon icon="search"/>
                </el-button>
              </el-col>
            </el-row>
            <scp-table
                ref="userTableRef"
                :columns="pageCtl.userColumns"
                :params="pageCtl.userParams"
                url="/system/query_users_lite"
                :afterSelect="afterUserSelect"/>
          </div>
          <div class="modify-box" v-show="pageCtl.menuBoxShow">
            <div class="left-box">
              <el-row class="search-input search-menu">
                <el-col :span="16">
                  <el-input v-model="pageCtl.menuParams.keywords" placeholder="Keywords" clearable size="default" @keyup.enter="menuSearch"></el-input>
                </el-col>
                <el-col :span="7">
                  <el-button @click="menuSearch" size="default">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <scp-table
                  ref="menuTableRef"
                  :columns="pageCtl.menuColumns"
                  :params="pageCtl.menuParams"
                  url="/system/query_menu_lite"
                  :afterSelect="afterMenuSelect"/>
            </div>
            <div class="right-box" v-loading="pageCtl.loading">
              <div v-show="pageCtl.detailBoxShow">
                <el-row style="margin-bottom:var(--scp-widget-margin)">
                  <el-col :span="24">
                    <h4 style="margin-bottom: 0">{{ pageCtl.selectedUser }}</h4>
                  </el-col>
                </el-row>
                <hr style="margin-top:5px">
                <h4>{{ pageCtl.selectedMenu }}</h4>
                <el-checkbox v-model="pageCtl.accessible">Accessibale.</el-checkbox>
                <span> If unchecked, the corresponding menu will not be accessible.</span>
                <hr>
                <h4>Auth Setting</h4>
                <el-input size="default" v-model="pageCtl.authDetails"></el-input>
                <p>For more authorization settings.</p>
                <hr>
                <el-button class="pull-right" type="primary" @click="save()" size="default">Save</el-button>
              </div>
            </div>
          </div>
          <div class="clearfix"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue'

const menuTableRef = ref()
const userTableRef = ref()
const $axios :any = inject('$axios')
const $message: any = inject('$message')
const pageCtl = reactive({
  selectedUser: '',
  selectedMenu: '',
  userParams: {
    keywords: ''
  },
  userColumns: [{
    data: 'SESA_CODE',
    title: 'SESA No'
  }, {
    data: 'USER_NAME',
    title: 'User Name'
  }, {
    data: 'ENTITY_NAME',
    title: 'Entity'
  }],
  // modify box
  menuBoxShow: false,
  menuParams: {
    keywords: ''
  },
  menuColumns: [{
    data: 'MENU_CODE',
    title: 'Code'
  }, {
    data: 'NAME',
    title: 'Menu Name'
  }, {
    data: 'PARENT_NAME',
    title: 'Parent'
  }, {
    data: 'CLASSIFICATION',
    title: 'Class'
  }, {
    data: 'MENU_TYPE',
    title: 'Type'
  }, {
    data: 'URL'
  }],
  detailBoxShow: false,
  accessible: false,
  authDetails: '',
  authDetailsParams: {
    menuCode: '',
    userCode: '',
    classification: '',
    accessible: false,
    authDetails: ''
  },
  loading: false
})

const afterUserSelect = (row) => {
  pageCtl.menuBoxShow = true
  pageCtl.detailBoxShow = false
  menuTableRef.value.search()
  pageCtl.authDetailsParams.userCode = row.SESA_CODE
  pageCtl.selectedUser = row.USER_NAME + ' [' + row.SESA_CODE + ']'
}
const userSearch = () => {
  userTableRef.value.search()
}
const afterMenuSelect = (row) => {
  if (row.MENU_TYPE === 'component') {
    pageCtl.selectedMenu = row.NAME + ' [' + row.MENU_TYPE + ']'
  } else {
    pageCtl.selectedMenu = row.URL + ' [' + row.MENU_TYPE + ']'
  }
  pageCtl.authDetailsParams.menuCode = row.MENU_CODE
  pageCtl.authDetailsParams.classification = row.CLASSIFICATION

  pageCtl.loading = true
  pageCtl.detailBoxShow = true
  const that = this
  $axios({
    method: 'post',
    url: '/system/query_auth_details',
    data: pageCtl.authDetailsParams
  }).then((body) => {
    if (body) {
      pageCtl.accessible = body.ACCESSIBLE === 'true'
      pageCtl.authDetails = body.AUTH_DETAILS
    } else {
      pageCtl.accessible = row.CLASSIFICATION === 'public'
      pageCtl.authDetails = ''
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const menuSearch = () => {
  menuTableRef.value.search()
}

const save = () => {
  pageCtl.authDetailsParams.accessible = pageCtl.accessible
  pageCtl.authDetailsParams.authDetails = pageCtl.authDetails
  $axios({
    method: 'post',
    url: '/system/save_auth_details',
    data: pageCtl.authDetailsParams
  }).then(function () {
    $message.success('Privileges saved')
  }).catch((error) => {
    console.log(error)
  })
}

</script>

<style scoped>
.user-list {
  width: 501px;
  box-sizing: border-box;
  padding-right: 15px;
  float: left;
}

.modify-box {
  width: calc(100% - 501px);
  float: left;
  box-sizing: border-box;
  padding-left: 15px;
  border-left: 1px solid var(--scp-border-color-lighter);
  display: flex;
}

.left-box {
  flex: 0 1 60%;
  padding-right: 15px;
}

.right-box {
  flex: 0 1 40%;
  padding-left: 15px;
  border-left: 1px solid var(--scp-border-color-lighter);
}

.search-input {
  margin-bottom: var(--scp-widget-margin);
}

.search-input div {
  padding-right: var(--scp-widget-margin)
}

.search-input div:last-child {
  padding-right: 0
}

@media screen and (max-width: 1300px) {
  .user-list, .modify-box {
    width: 100%;
    padding-right: 0;
    padding-left: 0;
    border-left: none;
    float: none;
  }

  .modify-box {
    margin-top: var(--scp-widget-margin);
    padding-top: var(--scp-widget-margin);
    border-top: 1px solid var(--scp-border-color-lighter);
  }
}
</style>
