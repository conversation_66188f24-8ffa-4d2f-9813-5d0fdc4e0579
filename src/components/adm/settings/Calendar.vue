<template>
  <div>
    <v-contextmenu ref="contextmenu1" class="calendar-item-box">
      <v-contextmenu-item @click="chooseWorkday" value="Workday" data-value="Workday" class="calendar-item">
        <font-awesome-icon icon="check" class="hover-icon"/>
        Workday
      </v-contextmenu-item>
      <v-contextmenu-item @click="chooseWeekend" value="Weekend" data-value="Weekend" class="calendar-item" style="background-color: rgba(255, 217, 128, 0.5);">
        <font-awesome-icon icon="check" class="hover-icon"/>
        Weekend / Holiday
      </v-contextmenu-item>
    </v-contextmenu>

    <scp-draggable-resizable w="480px" h="160px" title="New Template Name"
                             :save="saveNewTemplate"
                             v-model="pageCtl.dialogVisible">
      <template v-slot>
        <el-row style="padding: var(--scp-widget-margin)">
          <el-col :span="6">
            Template Name
          </el-col>
          <el-col :span="16">
            <el-autocomplete
                class="inline-input"
                style="width: 100%"
                v-model="pageCtl.templateName"
                :maxlength="30"
                :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.options, query, cb)"
                show-word-limit
            ></el-autocomplete>
          </el-col>
          <el-col :span="24" class="input-tips" style="padding: 0;margin-top: 5px;">
            复制 <b>{{ pageCtl.name }}@{{ pageCtl.year }}</b> 到新的日历
          </el-col>
        </el-row>
      </template>
    </scp-draggable-resizable>

    <div class="left-sidebar">
      <div class="widget">
        <div class="widget-body" v-loading="pageCtl.loading">
          <el-row style="margin-bottom: var(--scp-widget-margin)">
            <el-col :span="12">
              <el-button @click="prevYear">
                <font-awesome-icon icon="angle-left"/>
              </el-button>
              <span @click="loadCalendar" style="cursor: pointer;margin-left: 8px">{{ pageCtl.year }}</span>
              <el-button @click="nextYear" value="year" style="margin-left: 8px">
                <font-awesome-icon icon="angle-right"/>
              </el-button>
              <el-popconfirm :title="'Do you want to delete calendar ' + pageCtl.year + ' of ' + pageCtl.name + '?'"
                             iconColor="orange"
                             @confirm="deleteTemplate"
                             confirmButtonType="warning"
                             confirmButtonText='确定'
                             cancelButtonText='取消'
                             :width="350">
                <template #reference>
                    <span>
                      <el-button type="danger" style="margin-left:8px;" v-if="pageCtl.name !== 'National Holidays'">
                        <font-awesome-icon icon="times"/>
                        &nbsp;Delete
                      </el-button>&nbsp;&nbsp;
                    </span>
                </template>
              </el-popconfirm>
              <el-button type="primary" style="margin-right: 8px" @click="pageCtl.dialogVisible = true">
                <font-awesome-icon icon="save"/>&nbsp;
                Copy Current Calendar
              </el-button>
              <el-select v-model="pageCtl.name" filterable placeholder="Selected Calendar" style="width:200px;">
                <el-option
                    v-for="item in pageCtl.options"
                    :key="item"
                    :label="item"
                    :value="item">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="12" style="display: flex;justify-content: flex-end;align-content: center">
              <el-radio-group v-model="pageCtl.detailType" size="small">
                <el-radio-button key="week" value="week">DSS Week Calendar</el-radio-button>
                <el-radio-button key="lunar" value="lunar">Lunar Calendar</el-radio-button>
              </el-radio-group>
              <div style="margin-left: 8px;line-height: 26px;">Workday：</div>
              <div style="margin-left: 8px;background: #fff !important" class="workday"></div>
              <div style="margin-left: 8px;line-height: 26px;">Weekend / Holiday：</div>
              <div style="margin-left: 8px;" class="weekend"></div>
            </el-col>
          </el-row>

          <div class="monthly_box">
            <div v-if="pageCtl.calendar.length > 1" style="width: 100%;display: flex;flex-wrap: wrap;justify-content: space-between">
              <div class="monthly" style="position: relative;" v-for="(month,index) in pageCtl.calendar" :key="index">
                <font-awesome-layers style="
                    font-size: 6rem;
                    font-weight: bold;
                    color: rgba(0,0,0,0.1);
                    position: absolute;
                    user-select: none;
                    left: calc(100% / 2 - 3rem);
                    top: calc(50% - 4rem);
                    pointer-events: none">
                  <font-awesome-layers-text :value="index + 1"/>
                </font-awesome-layers>
                <table class="table table-bordered" style="border-top:0;border-left:0;">
                  <thead>
                  <tr>
                    <th v-for="(title,index) in pageCtl.days" :key="index">{{ title }}</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(week,index) in month" :key="index">
                    <td v-for="(day,index) in week" v-contextmenu:contextmenu1 @contextmenu.prevent="getSelectedDay" v-bind:data-fullday="day.text" :key="index"
                        v-bind:class="day.d ? (day.wd==='0'?'weekend':'workday'):''" :style="{color: day.text === $dateFormatter(new Date(), 'yyyy/MM/dd')  ? 'var(--scp-text-color-success)': 'inherit',
                                                                                              border: day.text === $dateFormatter(new Date(), 'yyyy/MM/dd')  ? '2px solid var(--scp-text-color-success)': ''}">
                      <div v-if="day.d">
                        {{ day.d }}
                        <div v-show="pageCtl.detailType === 'lunar'">
                          <p v-if="pageCtl.lunar_festival.get(day.lm + '_' + day.ld)">{{ pageCtl.lunar_festival.get(day.lm + '_' + day.ld) }}</p>
                          <p v-else-if="pageCtl.solar_festival.get(day.m + '_' + day.d)">{{ pageCtl.solar_festival.get(day.m + '_' + day.d) }}</p>
                          <p v-else>{{ pageCtl.lunar_map.get(day.ld) }}</p>
                        </div>
                        <p v-show="pageCtl.detailType === 'week'">W{{ day.wk }}</p>
                      </div>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else><h1 class="empty-calendar">No Calendar Defined</h1></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, onMounted, reactive, watch } from 'vue'

const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')

const pageCtl = reactive({
  detailType: 'lunar',
  days: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  solar_festival: new Map([
    ['1_1', '元旦'],
    ['3_8', '妇女节'],
    ['3_12', '植树节'],
    ['4_1', '愚人节'],
    ['4_5', '清明节'],
    ['5_1', '劳动节'],
    ['5_4', '青年节'],
    ['6_1', '儿童节'],
    ['7_1', '建党节'],
    ['8_1', '建军节'],
    ['9_10', '教师节'],
    ['10_1', '国庆节'],
    ['10_31', '万圣节'],
    ['12_13', '公祭日'],
    ['12_25', '圣诞节']
  ]),

  lunar_festival: new Map([
    ['12_8', '腊八'],
    ['12_23', '小年'],
    ['12_30', '除夕'],
    ['1_1', '春节'],
    ['1_15', '元宵节'],
    ['2_2', '龙抬头'],
    ['5_5', '端午节'],
    ['7_15', '中元节'],
    ['8_15', '中秋节'],
    ['9_9', '重阳节']
  ]),

  lunar_map: new Map([
    ['1', '初一'],
    ['2', '初二'],
    ['3', '初三'],
    ['4', '初四'],
    ['5', '初五'],
    ['6', '初六'],
    ['7', '初七'],
    ['8', '初八'],
    ['9', '初九'],
    ['10', '初十'],
    ['11', '十一'],
    ['12', '十二'],
    ['13', '十三'],
    ['14', '十四'],
    ['15', '十五'],
    ['16', '十六'],
    ['17', '十七'],
    ['18', '十八'],
    ['19', '十九'],
    ['20', '二十'],
    ['21', '廿一'],
    ['22', '廿二'],
    ['23', '廿三'],
    ['24', '廿四'],
    ['25', '廿五'],
    ['26', '廿六'],
    ['27', '廿七'],
    ['28', '廿八'],
    ['29', '廿九'],
    ['30', '三十']
  ]),
  calendar: [] as Array<any>,
  year: new Date().getFullYear(),
  // select
  options: [],
  name: 'National Holidays',
  loading: false,
  dialogVisible: false,
  templateName: '',
  selectedDay: {} as HTMLTableColElement
})

watch(() => pageCtl.name, () => {
  loadCalendar()
})
const loadCalendar = () => {
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/system/query_calendar',
    data: {
      name: pageCtl.name,
      year: pageCtl.year
    }
  }).then((body) => {
    pageCtl.calendar = body as Array<any>
    for (let i = 0; i < 12; i++) {
      if (pageCtl.calendar[i][0][0]) {
        switch (pageCtl.calendar[i][0][0].w) {
          case 'Sun':
            pageCtl.calendar[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Sat':
            pageCtl.calendar[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Fri':
            pageCtl.calendar[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Thu':
            pageCtl.calendar[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Wed':
            pageCtl.calendar[i][0].unshift('')
            // eslint-disable-next-line no-fallthrough
          case 'Tue':
            pageCtl.calendar[i][0].unshift('')
        }
      } else {
        break
      }
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const loadCalendarTemplate = () => {
  $axios({
    method: 'post',
    url: '/system/query_calendar_template'
  }).then((body) => {
    pageCtl.options = body
  }).catch((error) => {
    console.log(error)
  })
}

const prevYear = () => {
  pageCtl.year--
  loadCalendar()
}

const nextYear = () => {
  pageCtl.year++
  loadCalendar()
}

const cancelNewTemplate = () => {
  pageCtl.dialogVisible = false
  pageCtl.templateName = ''
}

const deleteTemplate = () => {
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/system/delete_calendar_template',
    data: {
      name: pageCtl.name,
      year: pageCtl.year
    }
  }).then((body) => {
    pageCtl.name = 'National Holidays'
    loadCalendarTemplate()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const saveNewTemplate = () => {
  $axios({
    method: 'post',
    url: '/system/save_calendar_template',
    data: {
      name: pageCtl.name,
      year: pageCtl.year,
      templateName: pageCtl.templateName
    }
  }).then((body) => {
    pageCtl.dialogVisible = false
    pageCtl.name = pageCtl.templateName
    loadCalendarTemplate()
    pageCtl.templateName = ''
  }).catch((error) => {
    console.log(error)
    pageCtl.dialogVisible = false
  })
}

const getSelectedDay = (e) => {
  let target = e.target
  for (let i = 0; i < 10; i++) {
    if (target.dataset.fullday) {
      break
    } else {
      target = target.parentNode
    }
  }
  pageCtl.selectedDay = target
}

const chooseWorkday = () => {
  if (pageCtl.selectedDay.getAttribute('class') !== 'workday') {
    updateDayType(pageCtl.selectedDay.dataset.fullday, 1)
    pageCtl.selectedDay.setAttribute('class', 'workday')
  }
}

const chooseWeekend = () => {
  if (pageCtl.selectedDay.getAttribute('class') !== 'weekend') {
    updateDayType(pageCtl.selectedDay.dataset.fullday, 0)
    pageCtl.selectedDay.setAttribute('class', 'weekend')
  }
}

const updateDayType = (text, workingDay) => {
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/system/update_calendar',
    data: {
      name: pageCtl.name,
      text,
      workingDay
    }
  }).then((body) => {
    // DO NOTHING
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

onMounted(() => {
  loadCalendarTemplate()
  loadCalendar()
})

</script>

<style lang="scss" scoped>
.calendar-item-box {
  padding: 0
}

.calendar-item {
  line-height: 1rem !important;
  padding-right: 0.833rem !important;
  font-size: 0.5rem !important;
}

.hover-icon {
  opacity: 0
}

.calendar-item:hover .hover-icon {
  opacity: 1
}

.workday {
  width: 0.75rem;
  height: 0.75rem;
  background: #fff;
  margin-top: 3px;
  border: 1px solid var(--scp-border-color-lighter);
  box-shadow: 1px 1px 2px var(--scp-border-color-lighter);
}

.weekend {
  width: 0.75rem;
  height: 0.75rem;
  background: #ffd980;
  margin-top: 3px;
  border: 1px solid var(--scp-border-color-lighter);
  box-shadow: 1px 1px 2px var(--scp-border-color-lighter);
}

.monthly_box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-items: stretch
}

.monthly_box .monthly {
  width: calc(100% / 4 - 8px);
  background: no-repeat center;
  box-sizing: border-box;
  min-height: 400px;
}

.monthly_box .monthly table {
  width: 100%;
  text-align: center;
}

.monthly_box .monthly table tr th, .monthly_box .monthly table tr td {
  text-align: center;
  line-height: 1.42857143;
  background-color: rgba(240, 240, 240, 0.6);
  vertical-align: middle;
  border-bottom: 1px solid var(--scp-border-color-lighter);
}

.monthly_box .monthly table tr td {
  font-size: 0.625rem;
  padding: 5px !important;
  width: 1.333rem;
  height: 1.75rem;
}

.monthly_box .monthly table tr td:hover {
  cursor: pointer
}

.monthly_box .monthly table tr td.weekend {
  background-color: rgba(255, 217, 128, 0.5) !important;
}

.monthly_box .monthly table tr td.workday {
  background-color: rgba(255, 255, 255, 0) !important;
}

.monthly_box .monthly table tr td p {
  margin-bottom: 0;
  font-size: 0.333rem;
}

.fa-layers {
  color: rgba(151, 159, 158, 0.3) !important;
}

.empty-calendar {
  color: var(--scp-text-color-lighter);
  height: 12.5rem;
  line-height: 12.5rem;
  font-size: 2rem;
}
</style>
