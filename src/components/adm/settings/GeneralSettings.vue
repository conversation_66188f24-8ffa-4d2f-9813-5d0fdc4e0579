<template>
    <div class="left-sidebar">
        <div class="widget" style="width:500px">
            <div class="widget-header">
                <div class="title">ADF & ADU
                </div>
            </div>
            <div class="widget-body">
                <h4 style="padding-left:10px;border-bottom:dashed 1px var(--scp-border-color)">{{ new Date().getFullYear() - 1 }}</h4>
                <el-checkbox v-for="month in _lastYear" :key="month.value" v-model="pageCtl.selectMonthMap[month.value]">{{ month.label }}</el-checkbox>
                <h4 style="padding-left:10px;border-bottom:dashed 1px var(--scp-border-color)">{{ new Date().getFullYear() }}</h4>
                <el-checkbox v-for="month in _thisYear" :key="month.value" v-model="pageCtl.selectMonthMap[month.value]">{{ month.label }}</el-checkbox>
            </div>
            <div class="widget-body" style="text-align: right">
                <el-button type="primary" size="small" style="margin-left:10px;margin-bottom: 10px" @click="saveCalcMonth">
                    <font-awesome-icon icon="save"/> &nbsp;
                    Save
                </el-button>
            </div>

        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive } from 'vue'

const $message: any = inject('$message')
const $axios: any = inject('$axios')

const pageCtl = reactive({
  months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  selectMonthMap: {}
})

const _selectedMonth = computed(() => {
  const month = [] as Array<string>
  for (const k in pageCtl.selectMonthMap) {
    if (pageCtl.selectMonthMap.hasOwnProperty(k)) {
      if (pageCtl.selectMonthMap[k] === true) {
        month.push(k)
      }
    }
  }
  return month
})

const _thisYear = computed(() => {
  const year = new Date().getFullYear()
  const result = [] as Array<any>
  for (let i = 1; i <= pageCtl.months.length; i++) {
    if (i > new Date().getMonth()) {
      break
    }
    result.push({
      label: pageCtl.months[i - 1],
      value: year + ((i < 10) ? '0' + i : '' + i)
    })
  }
  return result
})

const _lastYear = computed(() => {
  const year = new Date().getFullYear() - 1
  const result = [] as Array<any>
  for (let i = 1; i <= pageCtl.months.length; i++) {
    result.push({
      label: pageCtl.months[i - 1],
      value: year + ((i < 10) ? '0' + i : '' + i)
    })
  }
  return result
})

const saveCalcMonth = () => {
  if (_selectedMonth.value.length === 0) {
    $message.error('Please choose at least one month')
  } else {
    $axios({
      method: 'post',
      url: '/system/save_base_month',
      data: {
        list: _selectedMonth.value
      }
    }).then(function () {
      $message.success('Data saved')
    }).catch((error) => {
      console.log(error)
    })
  }
}

const initSelectedMonth = () => {
  $axios({
    method: 'post',
    url: '/system/query_base_month'
  }).then((body) => {
    const so = {}
    if (body) {
      for (let i = 0; i < body.length; i++) {
        so[body[i]] = true
      }
    }
    pageCtl.selectMonthMap = so
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  initSelectedMonth()
})
</script>
