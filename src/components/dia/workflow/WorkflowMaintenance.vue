<template>
  <div class="left-sidebar" id="workflowMaintenance">
    <div class="widget">
      <div class="widget-body" style="height: 100%">
        <el-container style="height: calc(100% - 10px)">
          <el-aside width="280px" style="border-right: 1px solid var(--scp-border-color);" class="left-container">
            <scp-tree-menu
                ref="treeRef"
                url="/diagnosis/workflow/maintenance/query_workflow_list"
                :new-click="showNewWorkflow"
                :node-click="clickNode"
            ></scp-tree-menu>
          </el-aside>
          <el-main style="padding: 0 10px">
            <h1 v-show="!pageCtl.selectdID" style="text-align: center; color: var(--scp-text-color-lighter);height: 300px;line-height: 300px;font-size: 1.5rem">
              Select or Create a Workflow
            </h1>
            <el-row v-show="!!pageCtl.selectdID" v-loading="pageCtl.loading.search">
              <el-col :span="24">
                <div v-show="!!pageCtl.selectedWorkflowInfo['USER_NAME']" v-loading="pageCtl.loading.search">
                  <h3 style="margin: 5px 35px 0 0;display: inline;">
                    {{ pageCtl.selectedWorkflowInfo['NAME'] }}
                    <span class="maintain-by"> by {{ pageCtl.selectedWorkflowInfo['USER_NAME'] }}({{ pageCtl.selectedWorkflowInfo['SESA_CODE'] }})</span>
                  </h3>
                  <el-popconfirm title="确定执行当前工作流? 这可能需要一点时间, 并且同时只允许一个工作流测试"
                                 @confirm="runTest"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'
                                 style="margin-left:15px">
                    <template #reference>
                      <el-button :loading="pageCtl.loading.test">
                        <font-awesome-icon icon="play"/> &nbsp;
                        Test
                      </el-button>
                    </template>
                  </el-popconfirm>
                  <el-button size="small" @click="showEditParameter">
                    <font-awesome-icon icon="indent"/> &nbsp;
                    Global Variant
                  </el-button>
                  <el-button size="small" @click="showEditWorkflow">
                    <font-awesome-icon icon="edit"/> &nbsp;
                    Edit
                  </el-button>
                  <el-button size="small" @click="saveWorkflow" type="primary" :loading="pageCtl.loading.save">
                    <font-awesome-icon icon="check"/> &nbsp;
                    Save
                  </el-button>
                  <el-popconfirm title="确定删除当前工作流? 如果当前工作流已经发布, 与此工作流相关的任务将无法正常工作"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="deleteWorkflow"
                                 confirmButtonType="danger"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'
                                 style="margin-left:15px">
                    <template #reference>
                      <el-button type="danger" size="small" :loading="pageCtl.loading.delete">
                        <font-awesome-icon icon="times"/> &nbsp;
                        Delete
                      </el-button>
                    </template>
                  </el-popconfirm>
                </div>
                <div class="design-panel">
                  <logicflow ref="logicflowRef" :style="{height: (pageCtl.clientHeight - 150) + 'px'}"/>
                  <div id="container" class="container"></div>
                </div>
              </el-col>
            </el-row>
          </el-main>
        </el-container>
      </div>
    </div>

    <!-- new workflow -->
    <scp-draggable-resizable w="600px" h="200px" v-model="pageCtl.visible.newWorkflow" title="New Workflow" :save="createWorkflow" save-text="Create">
      <el-form ref="newWorkflowForm" :model="pageCtl.newWorkflowForm" label-width="80px" style="padding:20px 15px 5px 5px">
        <el-form-item label="Name" required prop="name">
          <el-row style="width: 100%">
            <el-col :span="22">
              <el-input v-model="pageCtl.newWorkflowForm.name" placeholder="Name" style="width: 100%"></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="Group" required prop="groups">
          <el-row style="width: 100%">
            <el-col :span="16">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.newWorkflowForm.groups"
                  style="width: 100%"
                  :maxlength="30"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </scp-draggable-resizable>

    <!-- modify workflow -->
    <scp-draggable-resizable w="600px" h="260px" v-model="pageCtl.visible.editWorkflow" title="Edit Workflow" :save="modifyWorkflow">
      <el-form ref="editWorkflowForm" :model="pageCtl.editWorkflowForm" label-width="80px" style="padding:20px 15px 5px 5px">
        <el-form-item label="Name" required prop="name">
          <el-row style="width: 100%">
            <el-col :span="22">
              <el-input v-model="pageCtl.editWorkflowForm.name" placeholder="Name" style="width: 100%"></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="Group" required prop="groups">
          <el-row style="width: 100%">
            <el-col :span="16">
              <el-autocomplete
                  class="inline-input"
                  v-model="pageCtl.editWorkflowForm.groups"
                  style="width: 100%"
                  :maxlength="30"
                  :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                  placeholder="Group"
                  show-word-limit
              ></el-autocomplete>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="Owner" prop="owner" v-if="pageCtl.authType === 'ADMIN'">
          <el-row style="width: 100%">
            <el-col :span="16">
              <el-select v-model="pageCtl.editWorkflowForm.owner" placeholder="Owner" filterable>
                <el-option
                    v-for="item in pageCtl.ownerOpts"
                    :label="item.LABEL"
                    :value="item.VAL"
                    :key="item.VAL"/>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="600px" h="350px" v-model="pageCtl.visible.parameter" title="Edit Parameter" :save="saveParameter">
      <template v-slot="{height}">
        <scp-ace-editor v-model="pageCtl.selectedWorkflowInfo.PARAMS" lang="json"
                        :style="{width: '100%', height: (height - 120) + 'px'}" style="border: 1px solid var(--scp-border-color-lighter);"/>
      </template>
    </scp-draggable-resizable>

    <scp-draggable-resizable w="1024px" h="550px" v-model="pageCtl.visible.testlogs" title="Execute Logs">
      <template v-slot="{height}">
        <pre id="testLogsPre" :style="{height:( height - 100) + 'px'}" style="margin: 2px 0 0 2px;;overflow: auto;">{{ pageCtl.testLogs.join('\r\n') }}</pre>
      </template>
    </scp-draggable-resizable>

    <table-input-component ref="tableInputComponentRef"/>
    <file-output-component ref="fileOutputComponentRef"/>
    <mail-output-component ref="mailOutputComponentRef"/>
    <log-output-component ref="logOutputComponentRef"/>
    <table-output-component ref="tableOutputComponentRef"/>
    <grouping-component ref="groupingComponentRef"/>
    <stop-if-component ref="stopIfComponentRef"/>
    <switch-case-component ref="switchCaseComponentRef"/>
    <make-value-component ref="makeValueComponentRef"/>
    <linear-regression-component ref="linearRegressionComponentRef"/>
    <call-proc-component ref="callProcComponentRef"/>
    <priority-component ref="priorityComponentRef"/>
    <restful-client-component ref="restfulClientComponent"/>
    <java-client-component ref="javaClientComponent"/>
    <make-text-component ref="makeTextComponent"/>
    <call-workflow-component ref="callWorkflowComponent"/>
    <copy-file-component ref="copyFileComponent"/>
  </div>
</template>

<script lang="ts" setup>
import mqtt from 'mqtt'
import Logicflow from '@/components/starter/components/Logicflow.vue'
import wfconfig from '@/assets/js/logicflow/workflow/config.js'
import { isJSONString } from '@/assets/js/logicflow/global'

import RestfulClientComponent from './maintenance/RestfulClient.vue'
import TableInputComponent from './maintenance/TableInput.vue'
import FileOutputComponent from './maintenance/FileOutput.vue'
import MailOutputComponent from './maintenance/MailOutput.vue'
import LogOutputComponent from './maintenance/LogOutput.vue'
import TableOutputComponent from './maintenance/TableOutput.vue'
import GroupingComponent from './maintenance/Grouping.vue'
import StopIfComponent from './maintenance/StopIf.vue'
import SwitchCaseComponent from './maintenance/SwitchCase.vue'
import MakeValueComponent from './maintenance/MakeValue.vue'
import MakeTextComponent from './maintenance/MakeText.vue'
import LinearRegressionComponent from './maintenance/LinearRegression.vue'
import CallProcComponent from './maintenance/CallProc.vue'
import PriorityComponent from './maintenance/Priority.vue'
import JavaClientComponent from './maintenance/JavaClient.vue'
import CallWorkflowComponent from './maintenance/CallWorkflow.vue'
import CopyFileComponent from '@/components/dia/workflow/maintenance/CopyFile.vue'

import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { inject, onBeforeMount, onMounted, reactive, nextTick, ref, provide, onUpdated } from 'vue'

const treeRef = ref()
const logicflowRef = ref()

const tableInputComponentRef = ref()
const fileOutputComponentRef = ref()
const mailOutputComponentRef = ref()
const logOutputComponentRef = ref()
const tableOutputComponentRef = ref()
const groupingComponentRef = ref()
const stopIfComponentRef = ref()
const switchCaseComponentRef = ref()
const makeValueComponentRef = ref()
const makeTextComponent = ref()
const linearRegressionComponentRef = ref()
const callProcComponentRef = ref()
const priorityComponentRef = ref()
const restfulClientComponent = ref()
const javaClientComponent = ref()
const callWorkflowComponent = ref()
const copyFileComponent = ref()

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $randomString: any = inject('$randomString')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const pageCtl = reactive({
  subscribeTopic: 'scp/dss/ui/workflow-maintenance/' + (localStorage.getItem('username') || '').toLowerCase(),
  client: {} as any,
  lf: {} as any,
  selectdID: '',
  existsGroup: [],
  ownerOpts: [],
  authType: '',
  loading: {
    test: false,
    search: false,
    save: false,
    delete: false
  },
  visible: {
    newWorkflow: false,
    editWorkflow: false,
    parameter: false,
    testlogs: false
  },
  newWorkflowForm: {
    name: '',
    groups: ''
  },
  editWorkflowForm: {
    id: '',
    name: '',
    groups: '',
    owner: ''
  },
  selectedWorkflowInfo: {
    ID: '',
    NAME: '',
    USER_NAME: '',
    SESA_CODE: '',
    GROUPS: '',
    PARAMS: '{}'
  },
  clientHeight: 0,
  testLogs: [] as Array<string>
})

const clickNode = (e) => {
  pageCtl.selectdID = e.key
  pageCtl.visible.editWorkflow = false
  clickNodeAction()
}

const clickNodeAction = () => {
  pageCtl.loading.search = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_info_by_id',
    data: {
      id: pageCtl.selectdID
    }
  }).then(body => {
    pageCtl.selectedWorkflowInfo = body.info
    nextTick(() => {
      initGraph()
      pageCtl.lf.render(body.config)
    })
  }).catch(err => {
    console.warn(err)
  }).finally(() => {
    pageCtl.loading.search = false
  })
}

const showNewWorkflow = () => {
  pageCtl.visible.newWorkflow = true
}

const showEditWorkflow = () => {
  pageCtl.editWorkflowForm.id = pageCtl.selectedWorkflowInfo.ID
  pageCtl.editWorkflowForm.name = pageCtl.selectedWorkflowInfo.NAME
  pageCtl.editWorkflowForm.groups = pageCtl.selectedWorkflowInfo.GROUPS
  pageCtl.editWorkflowForm.owner = pageCtl.selectedWorkflowInfo.SESA_CODE
  pageCtl.visible.editWorkflow = true
}

const showEditParameter = () => {
  pageCtl.visible.parameter = true
}

const initPage = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/init_page'
  }).then((body) => {
    pageCtl.authType = body.authType || ''
    pageCtl.ownerOpts = body.ownerOpts || []
    pageCtl.existsGroup = body.existsGroup || []
  }).catch((error) => {
    console.log(error)
  })
}

const saveProps = (id, props) => {
  pageCtl.lf.setProperties(id, props)
}

provide('WorkflowMaintenance.saveProps', saveProps)

const createWorkflow = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/create_workflow',
    data: pageCtl.newWorkflowForm
  }).then(() => {
    initPage()
    pageCtl.visible.newWorkflow = false
    $message.success('Workflow Created!')
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const modifyWorkflow = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/modify_workflow_name',
    data: pageCtl.editWorkflowForm
  }).then(() => {
    initPage()
    pageCtl.visible.editWorkflow = false
    $message.success('Workflow Modified!')
    treeRef.value.search()
    clickNodeAction()
  }).catch((error) => {
    console.log(error)
  })
}

const saveWorkflow = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/modify_workflow',
    data: {
      id: pageCtl.selectdID,
      params: pageCtl.selectedWorkflowInfo.PARAMS,
      config: JSON.stringify(pageCtl.lf.getGraphData())
    }
  }).then(() => {
    $message.success('Workflow Saved!')
  }).catch((error) => {
    console.log(error)
  })
}

const deleteWorkflow = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/delete_workflow',
    data: {
      id: pageCtl.selectdID
    }
  }).then(() => {
    pageCtl.selectdID = ''
    initPage()
    treeRef.value.search()
    $message.success('Workflow Deleted!')
  }).catch((error) => {
    console.log(error)
  })
}

const runTest = () => {
  pageCtl.testLogs = []
  pageCtl.visible.testlogs = true
  pageCtl.loading.test = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/run_test',
    data: {
      id: pageCtl.selectdID,
      config: JSON.stringify(pageCtl.lf.getGraphData()),
      params: pageCtl.selectedWorkflowInfo.PARAMS,
      topic: pageCtl.subscribeTopic
    }
  }).then((body) => {
    if (body + '' === '-1') {
      $message.error('当前有测试任务正在执行, 请稍后再试!')
    } else {
      $message.success('Job executed')
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.test = false
  })
}

const saveParameter = () => {
  const params = pageCtl.selectedWorkflowInfo.PARAMS
  // 判断用户填写的PARAM是否合法
  if (isJSONString(params)) {
    const paramsObj = JSON.parse(params)
    const keys = Object.keys(paramsObj)
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      if (typeof paramsObj[key] === 'object') {
        if (Array.isArray(paramsObj[key])) {
          if (paramsObj[key].length > 0) {
            const obj0 = paramsObj[key][0]
            if (typeof obj0 !== 'object' || Array.isArray(obj0) === true) {
              $message.error('Invalid global variant, key=' + key + '. Ignored.')
            }
          }
        }
      }
    }
  } else {
    $message.error('Global variant cannot be resolved in JSON. Ignored.')
  }
  pageCtl.visible.parameter = false
}

const getUserParameter = () => {
  return pageCtl.selectedWorkflowInfo.PARAMS
}

const initGraph = () => {
  if (Object.keys(pageCtl.lf).length === 0) {
    // 生成logicflow
    pageCtl.lf = logicflowRef.value.create(wfconfig.nodeMap)
    pageCtl.lf.render({})

    // 双击节点事件
    pageCtl.lf.on('node:dbclick', (eventObject) => {
      const data = eventObject.data
      const graph = pageCtl.lf.getGraphData()
      graph['workflow-id'] = pageCtl.selectdID
      switch (data.type) {
        case 'table-input':
          tableInputComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'make-value':
          makeValueComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'make-text':
          makeTextComponent.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'grouping':
          groupingComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'mail-output':
          mailOutputComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'file-output':
          fileOutputComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'log-output':
          logOutputComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'table-output':
          tableOutputComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'stop-if':
          stopIfComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'switch-case':
          switchCaseComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'linear-regression':
          linearRegressionComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'call-proc':
          callProcComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'priority':
          priorityComponentRef.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'restful-client':
          restfulClientComponent.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'java-client':
          javaClientComponent.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'call-workflow':
          callWorkflowComponent.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
        case 'copy-file':
          copyFileComponent.value.showWin(data.id, data.properties, graph, getUserParameter())
          break
      }
    })

    // 添加边时, 系统校验变连接是否正确
    pageCtl.lf.on('edge:add', (data) => {
      const edge = data.data
      const nodeMap = {}
      const nodes = pageCtl.lf.getGraphData().nodes
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i]
        nodeMap[node.id] = node.type
      }
      const targetNodeId = edge.targetNodeId

      const targetType = nodeMap[targetNodeId].split('-')[0]
      if (targetType === 'priority') {
        nextTick(() => {
          setTimeout(() => {
            $message.error('Priority只可以作为起始节点')
            pageCtl.lf.undo()
          }, 200)
        })
      }
    })
  }
}

const connectMqtt = () => {
  pageCtl.client = mqtt.connect('wss://scp-dss.cn.schneider-electric.com:61615/mqtt', {
    clientId: 'scp-ui-workflow-maintenance-' + (localStorage.getItem('username') || '').toLowerCase() + '-' + $randomString(4)
  })
  pageCtl.client.on('connect', e => {
    pageCtl.client.subscribe(pageCtl.subscribeTopic, { qos: 2 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + pageCtl.subscribeTopic)
        pageCtl.testLogs.push('Server connected, subscribe topic: ' + pageCtl.subscribeTopic + '\r\n')
      }
    })
  })

  pageCtl.client.on('message', (topic, message) => {
    pageCtl.testLogs.push('' + message)
  })
}

onUpdated(() => {
  const testLogsPre = document.getElementById('testLogsPre') as HTMLPreElement
  // 900 是1.5屏日志的高度, 也就是当用户滚动了0.5屏幕的高度时, 日志将不再自动滚动
  if (testLogsPre.scrollTop === 0 || testLogsPre.scrollHeight - testLogsPre.scrollTop < 900) {
    testLogsPre.scrollTo({ top: testLogsPre.scrollHeight, behavior: 'smooth' })
  }
})

onBeforeMount(() => {
  pageCtl.clientHeight = document.documentElement.clientHeight
})

onMounted(() => {
  connectMqtt()
  initPage()
})

</script>
<style lang="scss">
#workflowMaintenance {
  height: calc(100% - 25px);

  .widget {
    height: 100%;
  }

  .widget-body {
    height: 100%;
  }

  .maintain-by {
    font-size: 10px;
    font-weight: normal;
    color: var(--scp-text-color-secondary);
    font-style: italic;
  }

  .design-panel {
    position: relative;
    margin-top: 10px;
  }
}
</style>
