<template>
  <div class="left-sidebar" id="workflowDistribution">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-input placeholder="Filter" v-model="pageCtl.filter" style="width: calc(100% - 10px) !important;" clearable/>
          </el-col>
          <el-col :span="19">
            <scp-search :click-native="search"/>
            <el-button type="primary" @click="showNewDistributionWin" style="margin-left: 10px">
              <font-awesome-icon icon="edit"/> &nbsp;
              New Distribution
            </el-button>
          </el-col>
        </el-row>
        <el-collapse v-model="pageCtl.activeTabs" style="margin-top: 20px">
          <el-collapse-item v-show="value.length > 0" v-for="(value, key) in _distributionList" :key="key" :title="key + ' (' + value.length + ')'" :name="key">
            <div class="workflow-box">
              <div v-for="(item, index) in value" :key="index" class="workflow-card" @dblclick="()=>showModifyDistributionWin(item.ID)">
                <el-card shadow="hover" style="position: relative;" :class="item.STATUS === 'Y' ? 'card-active' : 'card-inactive'">
                  <div>{{ item.NAME || '&nbsp;' }}</div>
                  <div class="wf-content">{{ item.LAST_DIS_CNT }}</div>
                  <div style="bottom: 0.5rem;" class="wf-tips">
                    <div class="wf-tips-left">{{ $isEmpty(item.TIME_COST_IN_MIN) ? '- ' : item.TIME_COST_IN_MIN }}mins</div>
                    <div class="wf-tips-right">{{ item.MONTH + ' ' + item.DAY + ' ' + item.HOUR }}</div>
                  </div>
                  <div style="bottom: 0;" class="wf-tips">
                    <div class="wf-tips-left">{{ item.LAST_EXEC_START_TIME || 'Not Started' }}</div>
                    <div class="wf-tips-right">{{ item.USERNAME }}</div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <scp-draggable-resizable w="1024px" h="340px" v-model="pageCtl.visible.newDistribution" title="New Distribution"
                             :save-loading="pageCtl.loading.save" :save="saveDistribution">
      <div style="padding: 10px; height: calc(100% - 20px); overflow: auto;">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="12">
            <el-input v-model="pageCtl.newForm.name" style="width: 100%" placeholder="Name"/>
          </el-col>
          <el-col :span="2" class="content" style="text-align: right" title="Enable or Not">
            <el-switch
                v-model="pageCtl.newForm.enable"
                active-value="Y"
                inactive-value="N">
            </el-switch>
          </el-col>
          <el-col :span="10" class="input-tips">
            给当前任务创建一个名字
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-select v-model="pageCtl.newForm.wfID" style="width: 100%" filterable placeholder="Select a workflow">
              <el-option
                  v-for="(item, index) in pageCtl.workflowOpts"
                  :key="index"
                  :label="item['GROUPS'] + '>' + item['NAME']"
                  :value="item['ID']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="10" class="input-tips">
            选择一个需要分发的Workflow
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="10">
            <el-date-picker
                v-model="pageCtl.newForm.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                style="width: calc(100% - 35px);"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="14" class="input-tips">
            任务有效日期, 超出时间范围的任务将不会执行
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="4" class="content">
            <el-select v-model="pageCtl.newForm.month" placeholder="Month" filterable collapse-tags multiple style="width: calc(100% - 10px);">
              <el-option
                  v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.newForm.dayType" placeholder="Day Type" class="search-group-left" @change="pageCtl.newForm.day = []"
                       style="width: 100%;">
              <el-option
                  v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK', 'WORKING_DAY']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="content">
            <el-select v-model="pageCtl.newForm.day" :placeholder="pageCtl.newForm.dayType" filterable collapse-tags multiple class="search-group-right"
                       style="width: calc(100% - 10px);">
              <el-option
                  v-for="item in _dayRangeNew"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="content" style="text-align: right">
            <el-select v-model="pageCtl.newForm.hour" placeholder="Hour" filterable collapse-tags multiple style="width: 100%;">
              <el-option
                  v-for="item in _hourRange"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" class="input-tips">
            你希望这个工作流, 在什么时间段执行
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>

    <!-- modify -->
    <scp-draggable-resizable w="1024px" h="650px" v-model="pageCtl.visible.modifyDistribution" title="Modify Distribution" :delete="deleteDistribution"
                             :save="modifyDistribution" :save-loading="pageCtl.loading.modify">
      <div style="padding: 10px; height: calc(100% - 20px); overflow: auto;">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="12">
            <el-input v-model="pageCtl.modifyForm.name" style="width: 100%" placeholder="Name"/>
          </el-col>
          <el-col :span="2" class="content" style="text-align: right" title="Enable or Not">
            <el-switch
                v-model="pageCtl.modifyForm.enable"
                active-value="Y"
                inactive-value="N">
            </el-switch>
          </el-col>
          <el-col :span="10" class="input-tips">
            给当前任务创建一个名字
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-select v-model="pageCtl.modifyForm.wfID" style="width: 100%" filterable placeholder="Select a workflow">
              <el-option
                  v-for="(item, index) in pageCtl.workflowOpts"
                  :key="index"
                  :label="item['GROUPS'] + '>' + item['NAME']"
                  :value="item['ID']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="10" class="input-tips">
            选择一个需要分发的Workflow
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="10">
            <el-date-picker
                v-model="pageCtl.modifyForm.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                style="width: calc(100% - 35px)"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="14" class="input-tips">
            任务有效日期, 超出时间范围的任务将不会执行
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="4" class="content">
            <el-select v-model="pageCtl.modifyForm.month" placeholder="Month" filterable collapse-tags multiple style="width: calc(100% - 10px);">
              <el-option
                  v-for="item in ['*', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.modifyForm.dayType" placeholder="Day Type" class="search-group-left" @change="pageCtl.modifyForm.day = []"
                       style="width: 100%;">
              <el-option
                  v-for="item in ['DAY_OF_MONTH', 'DAY_OF_WEEK', 'WORKING_DAY']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="content">
            <el-select v-model="pageCtl.modifyForm.day" :placeholder="pageCtl.modifyForm.dayType" filterable collapse-tags multiple
                       class="search-group-right"
                       style="width: calc(100% - 10px);">
              <el-option
                  v-for="item in _dayRangeModify"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="content" style="text-align: right">
            <el-select v-model="pageCtl.modifyForm.hour" placeholder="Hour" filterable collapse-tags multiple style="width: 100%;">
              <el-option
                  v-for="item in _hourRange"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" class="input-tips">
            你希望这个工作流, 在什么时间段执行
          </el-col>
        </el-row>
        <hr>
        <el-row>
          <el-col :span="24" style="width: calc(100% - 20px)">
            <scp-table
                url="/diagnosis/workflow/distribution/query_report1_details"
                :params="{disID: pageCtl.selectedDisID}"
                ref="report1DetailsTableRef"
                :lazy="true"
                :columns="pageCtl.report1DetailsColumn"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

interface Distribution {
  LAST_EXEC_START_TIME: string,
  MONTH: string,
  STATUS: string,
  GROUPS: string,
  HOUR: string,
  LAST_DIS_CNT: number,
  TIME_COST_IN_MIN: number,
  USERNAME: string,
  ID: string,
  DAY_TYPE: string,
  DAY: string,
  NAME: string
}

const $axios: any = inject('$axios')
const $isEmpty: any = inject('$isEmpty')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')
const report1DetailsTableRef = ref()
const pageCtl = reactive({
  activeTabs: [] as Array<string>,
  filter: '',
  workflowOpts: '',
  distributionList: {} as Map<string, Distribution>,
  newForm: {
    enable: 'Y',
    name: '',
    wfID: '',
    priority: '',
    dateRange: [] as Array<any>,
    month: [],
    dayType: 'DAY_OF_MONTH',
    day: [],
    hour: []
  },
  selectedDisID: '',
  modifyForm: {
    id: '',
    enable: 'Y',
    name: '',
    wfID: '',
    dateRange: [] as Array<string>,
    month: [],
    dayType: 'DAY_OF_MONTH',
    day: [],
    hour: []
  },
  visible: {
    newDistribution: false,
    modifyDistribution: false
  },
  loading: {
    filter: false,
    save: false,
    modify: false,
    report1: false
  },
  report1DetailsColumn: [
    { data: 'START_TIME', width: 130 },
    { data: 'END_TIME', width: 130 },
    { data: 'TIME_COST_IN_SECOND', title: 'Timecost(sec)', type: 'numeric', precision: 3 },
    { data: 'LINES_READ', type: 'numeric' },
    { data: 'LINES_WRITTEN', type: 'numeric' },
    { data: 'MAIL_SENT', type: 'numeric' },
    { data: 'FILE_CREATED', type: 'numeric' },
    { data: 'STATUS' },
    { data: 'MESSAGE', width: 150 }
  ]
})

const initPage = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/init_page'
  }).then((body) => {
    pageCtl.workflowOpts = body.workflowOpts
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const search = () => {
  searchReport1()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/query_report1'
  }).then((body) => {
    pageCtl.distributionList = body
    if (Object.keys(pageCtl.distributionList).length > 0) {
      pageCtl.activeTabs = [Object.keys(pageCtl.distributionList)[0]]
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const showNewDistributionWin = () => {
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/query_workflow'
  }).then((body) => {
    pageCtl.workflowOpts = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
  pageCtl.visible.newDistribution = true
}

const saveDistribution = () => {
  pageCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/save_distribution',
    data: pageCtl.newForm
  }).then(() => {
    pageCtl.visible.newDistribution = false
    $message.success('保存成功')
    searchReport1()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.save = false
  })
}

const showModifyDistributionWin = (id) => {
  pageCtl.selectedDisID = id
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/query_distribution_by_id',
    data: {
      id
    }
  }).then((body) => {
    pageCtl.modifyForm = {
      id,
      enable: body.STATUS,
      name: body.NAME,
      wfID: body.WORKFLOW_ID,
      dateRange: [body.START_DATE, body.END_DATE],
      month: JSON.parse(body.MONTH),
      dayType: body.DAY_TYPE,
      day: JSON.parse(body.DAY),
      hour: JSON.parse(body.HOUR)
    }
    pageCtl.visible.modifyDistribution = true
    nextTick(() => {
      report1DetailsTableRef.value.search()
    })
  }).catch((error) => {
    console.log(error)
  })
}

const modifyDistribution = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/modify_distribution',
    data: pageCtl.modifyForm
  }).then(() => {
    pageCtl.visible.modifyDistribution = false
    $message.success('修改成功')
    searchReport1()
  }).catch((error) => {
    console.log(error)
  })
}

const deleteDistribution = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/distribution/delete_distribution_by_id',
    data: {
      id: pageCtl.modifyForm.id
    }
  }).then(() => {
    pageCtl.visible.modifyDistribution = false
    $message.success('Task deleted')
    searchReport1()
  }).catch((error) => {
    console.log(error)
  })
}

const _distributionList = computed(() => {
  if (pageCtl.filter) {
    const result = {} as Map<string, Distribution>
    for (const key in pageCtl.distributionList) {
      if (pageCtl.distributionList.hasOwnProperty(key)) {
        const list = pageCtl.distributionList[key].filter(e => (e.NAME || '').toUpperCase().indexOf(pageCtl.filter.toUpperCase()) !== -1)
        if (list.length > 0) {
          result[key] = list
        }
      }
    }
    return result
  } else {
    return pageCtl.distributionList
  }
})

const _dayRangeModify = computed(() => {
  const result = ['*']
  if (pageCtl.modifyForm.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.modifyForm.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.modifyForm.dayType === 'WORKING_DAY') {
    return []
  }
  return result
})

const _dayRangeNew = computed(() => {
  const result = ['*']
  if (pageCtl.newForm.dayType === 'DAY_OF_MONTH') {
    for (let i = 1; i <= 31; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.newForm.dayType === 'DAY_OF_WEEK') {
    for (let i = 1; i <= 7; i++) {
      result.push(i + '')
    }
  } else if (pageCtl.newForm.dayType === 'WORKING_DAY') {
    return []
  }
  return result
})

const _hourRange = computed(() => {
  const result = [] as Array<string>
  for (let i = 7; i <= 23; i++) {
    result.push(i + '')
  }
  return result
})

onMounted(() => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  pageCtl.newForm.dateRange = [$dateFormatter(new Date(year, month, day), 'yyyy/MM/dd'), $dateFormatter(new Date(year, month + 24, day), 'yyyy/MM/dd')]

  initPage()
  search()
})
</script>

<style lang="scss">
#workflowDistribution {
  .workflow-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    user-select: none;

    .workflow-card {
      height: 5rem;
      width: 8rem;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;

      .card-inactive {
        border: 1px solid var(--scp-border-color-lighter);
        color: var(--scp-bg-color-fill);
      }

      .card-active {
        color: var(--scp-text-color-primary);
      }

      .el-card {
        width: 100%;
        height: 100%;
        cursor: pointer;

        .el-card__body {
          height: 100%;
          padding: 0.1rem 0.2rem 0.1rem 0.2rem;
          font-size: 0.4rem;

          .wf-content {
            font-size: 1.5rem;
            text-align: center;
            padding-top: 0.4rem;
          }

          .wf-tips {
            position: absolute;
            width: calc(100% - 10px);

            .wf-tips-left {
              float: left;
            }

            .wf-tips-right {
              float: right;
            }
          }
        }
      }
    }
  }
}
</style>
