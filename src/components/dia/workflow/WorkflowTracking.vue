<template>
  <div class="left-sidebar" id="workflowTracking">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <el-date-picker
              @change="search"
              style="width: var(--scp-input-width)"
              v-model="pageCtl.conditions.date"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              type="date"
              :clearable="false"
              placeholder="Date">
            </el-date-picker>
          </el-col>
          <el-col :span="19">
            <scp-search :click-native="search"/>
          </el-col>
        </el-row>
        <div class="wf-widget">
          <el-card shadow="hover" v-loading="pageCtl.loading.report1" v-for="(val, key) in pageCtl.report1Data" :key="key">
            <h4>{{ $isEmpty(val.value) ? '--' : val.value }}</h4>
            <h6>{{ val.label }}</h6>
          </el-card>
        </div>
        <el-row>
          <el-col :span="8">
            <div class="subscript-container subscript-container-left">
              <scp-subscript id="WTDP"/>
              <el-select v-model="pageCtl.conditions.report2Type" class="report2Select" @change="searchReport2">
                <el-option
                  v-for="item in [{value: 'BY_DIS', label: 'By Dis.'},{value: 'BY_TIME', label: 'By Time'}]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <chart ref="report2" :height="370" :option="_report2Opt" v-loading="pageCtl.loading.report2"/>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="subscript-container subscript-container-right">
              <scp-subscript id="WTDT"/>
              <chart ref="report3" :height="370" :option="_report3Opt" v-loading="pageCtl.loading.report3"/>
            </div>
          </el-col>
        </el-row>
        <div class="subscript-container">
          <scp-subscript id="WTTL"/>
          <chart :height="150" :option="_report4Opt" v-loading="pageCtl.loading.report4"/>
        </div>
        <el-row>
          <el-col :span="16">
            <div class="subscript-container subscript-container-left">
              <scp-subscript id="WTDL"/>
              <scp-table
                :lazy="true"
                :params="pageCtl.conditions"
                :max-height="350"
                url="/diagnosis/workflow/tracking/query_report5"
                ref="report5Ref"
                :columns="_report5Columns"/>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="subscript-container subscript-container-right">
              <scp-subscript id="WTTU"/>
              <chart :style="316" :option="_report6Opt" v-loading="pageCtl.loading.report6"/>
            </div>
          </el-col>
        </el-row>
        <div style="height: 30px"/>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref } from 'vue'

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const report5Ref = ref()
const $axios : any = inject('$axios')
const $isEmpty : any = inject('$isEmpty')
const $dateFormatter : any = inject('$dateFormatter')
const $thousandBitSeparator : any = inject('$thousandBitSeparator')
const pageCtl = reactive({
  conditions: {
    date: '',
    report2Type: 'BY_DIS'
  },
  report1Data: {
    activeWorkflowCnt: {
      label: 'ACTIVE WORKFLOW',
      value: 8
    },
    distributionsCnt: {
      label: 'DISTRIBUTIONS',
      value: 72
    },
    timecost: {
      label: 'TIME COST(MIN)',
      value: 5.6
    },
    timecostPerTime: {
      label: 'TIME COST PER DIS.(SEC)',
      value: 5.6
    },
    keyUserCnt: {
      label: 'KEY USER',
      value: 3
    },
    accumulatedDistributionsCnt: {
      label: 'ACCUMULATED DIS.',
      value: 3
    }
  },
  report2Data: [],
  report3Data: {
    xAxis: [],
    yAxis1: [],
    yAxis2: []
  },
  report4Data: [],
  report6Data: {
    yAxis: [],
    yAxisTips: [],
    xAxis: []
  },
  loading: {
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    report6: false
  }
})

const search = () => {
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
  searchReport5()
  searchReport6()
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/tracking/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data.activeWorkflowCnt.value = body.ACTIVE_WORKFLOW_CNT
    pageCtl.report1Data.distributionsCnt.value = body.DISTRIBUTIONS_CNT
    pageCtl.report1Data.timecost.value = body.TIMECOST
    pageCtl.report1Data.timecostPerTime.value = body.TIMECOST_PER_TIME
    pageCtl.report1Data.keyUserCnt.value = body.KEY_USER_CNT
    pageCtl.report1Data.accumulatedDistributionsCnt.value = body.ACCUMULATED_DISTRIBUTIONS_CNT
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/tracking/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/tracking/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/tracking/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const searchReport5 = () => {
  report5Ref.value.search()
}

const searchReport6 = () => {
  pageCtl.loading.report6 = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/tracking/query_report6',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report6Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report6 = false
  })
}

const _report2Opt = computed(() => {
  return {
    title: {
      text: pageCtl.conditions.report2Type === 'BY_DIS' ? 'Distributions' : 'Time Cost'
    },
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const tip = [] as Array<string>
        tip.push('<div>')
        tip.push(params.marker)
        tip.push(params.name)
        tip.push('&nbsp;&nbsp;&nbsp;&nbsp;')
        tip.push('<span style="float: right">')
        tip.push($thousandBitSeparator(params.value) || 0)
        if (pageCtl.conditions.report2Type === 'BY_TIME') {
          tip.push('min(s)')
        }
        tip.push(' (' + params.percent + '%)')
        tip.push('</span>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend(),
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '40',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pageCtl.report2Data
      }
    ]
  }
})

const _report3Opt = computed(() => {
  return {
    title: {
      text: 'Distribution Trend in Latest 30 Working Days'
    },
    legend: $legend(),
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as Array<string>
        tip.push(params[0].name)
        for (let i = 0; i < params.length; i++) {
          tip.push('<div style="width:7.5rem;">')
          tip.push(params[i].marker)
          tip.push(params[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($thousandBitSeparator(params[i].data) || 0)
          if (params[i].seriesName === 'Timecost' && params[i].data) {
            tip.push(' min(s)')
          }
          tip.push('</span>')
          tip.push('</div>')
        }
        return tip.join('')
      }
    },
    xAxis: {
      type: 'category',
      data: pageCtl.report3Data.xAxis
    },
    grid: $grid(),
    yAxis: [{
      type: 'value'
    }, {
      type: 'value',
      splitLine: {
        show: false
      },
      splitArea: {
        show: false
      }
    }],
    series: [
      {
        name: 'Dis.Count',
        data: pageCtl.report3Data.yAxis1,
        type: 'line'
      },
      {
        name: 'Timecost',
        data: pageCtl.report3Data.yAxis2,
        type: 'line',
        yAxisIndex: 1
      }
    ]
  }
})

const _report4Opt = computed(() => {
  return {
    color: ['#fc8452'],
    toolbox: $toolbox({ opts: ['no-details'] }),
    tooltip: {
      position: 'top',
      formatter: function (params) {
        const data = params.data
        const tip = [] as Array<string>
        tip.push('<div style="width:7rem;">')
        tip.push(params.marker)
        tip.push(data[0])
        tip.push('<br>')
        tip.push('Dis.Count')
        tip.push('<span style="font-size: 90%;float: right">')
        tip.push(data[1])
        tip.push('</span>')
        tip.push('<br>')
        tip.push('Peak(%)')
        tip.push('<span style="font-size: 90%;float: right">')
        tip.push((data[2] * 100).toFixed(1) + '%')
        tip.push('</span></div>')
        return tip.join('')
      }
    },
    singleAxis: [{
      left: 0,
      right: 0,
      bottom: 20,
      type: 'category',
      boundaryGap: false,
      splitLine: { show: false },
      data: ['', '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '']
    }],
    series: [{
      singleAxisIndex: 0,
      coordinateSystem: 'singleAxis',
      type: 'scatter',
      data: pageCtl.report4Data,
      symbolSize: function (dataItem) {
        return dataItem[2] * 100
      }
    }]
  }
})

const _report5Columns = computed(() => {
  return [
    { data: 'DIS_NAME', title: 'Dis.Name' },
    { data: 'EXEC_TIME', title: 'Exec Time' },
    { data: 'INVOLVED_USER', type: 'numeric' },
    { data: 'TIME_COST_IN_MIN', type: 'numeric' },
    { data: 'TEMPLATE' },
    { data: 'KEY_USER' }
  ]
})

const _report6Opt = computed(() => {
  return {
    title: {
      text: 'Top 15 Distributions'
    },
    toolbox: $toolbox({ opts: ['line', 'no-details'] }),
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const tip = [] as Array<string>
        tip.push(params[0].name)
        tip.push('&nbsp;-&nbsp;')
        tip.push(pageCtl.report6Data.yAxisTips[params[0].dataIndex])
        for (let i = 0; i < params.length; i++) {
          tip.push('<div style="width:9.5rem;">')
          tip.push(params[i].marker)
          tip.push('Dis.Count')
          tip.push('<span style="float: right">')
          tip.push($thousandBitSeparator(params[i].data) || 0)
          if (params[i].seriesName === 'Timecost' && params[i].data) {
            tip.push(' min(s)')
          }
          tip.push('</span>')
          tip.push('</div>')
        }
        return tip.join('')
      }
    },
    grid: $grid({ bottom: 10 }),
    xAxis: {
      type: 'value',
      splitLine: { show: false }
    },
    yAxis: {
      type: 'category',
      data: pageCtl.report6Data.yAxis,
      axisLabel: {
        show: true
      }
    },
    series: {
      type: 'bar',
      data: pageCtl.report6Data.xAxis,
      label: {
        show: true,
        position: 'right',
        valueAnimation: true
      }
    }
  }
})

onMounted(() => {
  pageCtl.conditions.date = $dateFormatter(new Date(), 'yyyy/MM/dd')
  search()
})
</script>
<style lang="scss">
#workflowTracking {
  .wf-widget {
    margin-bottom: var(--scp-widget-margin);
    display: flex;
    justify-content: space-between;

    .el-card {
      width: calc(100% / 6 - var(--scp-widget-margin));
      background-color: transparent !important;
      border: 1px solid var(--scp-border-color) !important;
      border-radius: 0 !important;

      .el-card__body {
        padding: 0.85rem !important;
        text-align: center !important;

        h4 {
          color: var(--scp-text-color-primary) !important;
          font-size: 0.833rem !important;
          margin-top: 5px !important;
          margin-bottom: 5px !important;
        }

        h6 {
          font-weight: 500 !important;
          font-size: 0.417rem !important;
          text-transform: uppercase !important;
          color: var(--scp-text-color-secondary) !important;
          margin: 0 !important;
        }

      }
    }
  }

  .report2Select {
    float: right;
    margin-top: 2px;
    margin-right: 52px;
    width: 120px;
    z-index: 1000;

    .el-input.el-input--small.el-input--suffix {
      z-index: 600;
    }
  }
}
</style>
