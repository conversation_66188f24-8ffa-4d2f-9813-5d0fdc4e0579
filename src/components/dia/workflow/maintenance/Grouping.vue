<template>
  <div id="GroupingComponent">
    <scp-draggable-resizable w="600px" h="180px" v-model="pageCtl.visible" :title="'Grouping - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="16">
            <el-select v-model="pageCtl.props.groupby" placeholder="Grouping Fields"
                       collapse-tags filterable clearable multiple style="width: 100%">
              <el-option
                  v-for="item in pageCtl.input.lists"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" class="input-tips">需要Grouping的字段</el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, ListVarient, SingleVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  props: {
    groupby: [],
    _output: {
      singles: [],
      lists: [] as Array<ListVarient>
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}
const saveProps = () => {
  const lists = [] as Array<ListVarient>
  const singles = [] as Array<SingleVarient>
  // 先复制前序的所有lists
  for (let i = 0; i < pageCtl.input.lists.length; i++) {
    const value = pageCtl.input.lists[i]
    lists.push({
      label: value.label,
      value: value.value,
      title: value.title
    })
  }
  // 再加上当前节点额外的输出
  for (let i = 0; i < pageCtl.props.groupby.length; i++) {
    const label = pageCtl.props.groupby[i] + '_GROUPING'
    singles.push({
      type: 'grouping',
      label,
      value: pageCtl.props.groupby[i],
      title: 'Grouping Field: ' + pageCtl.props.groupby[i]
    })
  }
  pageCtl.props._output.lists = lists
  pageCtl.props._output.singles = singles
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const _tips = computed(() => {
  return [
    '- 本节点会将Table Value进行分组, 然后分批执行',
    '- 也就是说, 如果分了N组, 那么后续所有节点都会执行N次',
    '- 被分组后的字段可以当做Single Value使用',
    '- Grouping会导致多分支路径执行异常, 使用Grouping的时候建议不要出现多路径配置'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'GroupingComponent'
}
</script>
