<template>
  <div id="MakeValueComponent">
    <scp-draggable-resizable w="650px" h="160px" v-model="pageCtl.visible" :tips="_tips" :title="'Make Value - ' + pageCtl.id" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="8">
            <el-input v-model="pageCtl.props.key" type="text" placeholder="Name" style="width: var(--scp-input-width)"/>
          </el-col>
          <el-col :span="8">
            <el-select v-model="pageCtl.props.value" placeholder="Field" style="width: 100%" clearable filterable allow-create>
              <el-option
                  v-for="item in pageCtl.valueOpts"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" class="input-tips">Name不要与其他变量名冲突</el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, SingleVarient } from '@/assets/js/logicflow/global'
import { inject, reactive, computed } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  valueOpts: [{ value: '_DAY' }, { value: '_DAYTIME' }, { value: '_UUID8' }, { value: '_UUID16' }],
  props: {
    key: '',
    value: '',
    _output: {
      singles: [] as Array<SingleVarient>,
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}
const saveProps = () => {
  const key = '_' + pageCtl.props.key
  pageCtl.props.value = pageCtl.props.value.trim()
  pageCtl.props._output.singles = [{
    type: 'make-value',
    value: pageCtl.props.value,
    label: key,
    title: 'Make Value: ' + pageCtl.props.value
  }]
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const _tips = computed(() => {
  return [
    '- 创建一个变量, 这个变量可以供其他节点使用',
    '- 避免了某一个节点生成随机数, 其下一个节点无法获取该随机数的问题'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'MakeValueComponent'
}
</script>
