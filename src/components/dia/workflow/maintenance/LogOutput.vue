<template>
  <div id="LogOutputComponent">
    <scp-draggable-resizable w="800px" h="550px" v-model="pageCtl.visible" :title="'Write to Log - ' + pageCtl.id" :save="saveProps" :tips="_tips">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="4">
            <el-select v-model="pageCtl.props.level" filterable placeholder="Selected Calendar" style="width:100%;">
              <el-option
                  v-for="item in ['DEBUG', 'INFO', 'WARN', 'ERROR']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="20" class="input-tips">Log Level</el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;height: 190px">
          <el-col :span="22">
            <el-input
                type="textarea"
                :rows="10"
                resize="none"
                placeholder="请输入日志内容"
                v-model="pageCtl.props.template">
            </el-input>
          </el-col>
          <el-col :span="2" class="input-tips">Log</el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="22">
            <varient :options="pageCtl.input"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import Varient from './Varient.vue'
import { getPreVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  props: {
    level: 'DEBUG',
    template: '',
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}
const saveProps = () => {
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const _tips = computed(() => {
  return [
    '- 本节点会输出日志, 主要用于构建Workflow时, 调试使用',
    '- 日志可以直接输出Table Value, 如{MATERIAL}',
    '- 本节点不会输出任何参数'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'LogOutputComponent'
}
</script>

<style lang="scss">
#LogOutputComponent {
}
</style>
