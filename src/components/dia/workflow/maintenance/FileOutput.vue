<template>
  <div id="SaveFileComponent">
    <scp-draggable-resizable w="65vw" h="60vh" v-model="pageCtl.visible" :title="'Save File - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 40px); overflow: auto;">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="20">
            <el-input v-model.trim="pageCtl.props.filename" placeholder="Filename"/>
          </el-col>
          <el-col :span="4" class="input-tips">
            Filename
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="20">
            <div class="output-fields">
              <el-tag
                  v-for="(field, index) in pageCtl.props.outputFields"
                  :key="'f_'+ index"
                  closable
                  effect="dark"
                  :disable-transitions="true"
                  @close="handleClose(field)"
                  style="margin-right: 3px"
                  type="primary">
                {{ field }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="4" class="input-tips">
            Output fields
            <br/>
            <el-link type="primary" style="margin-top: 10px;" @click="addAllFields">
              <font-awesome-icon icon="plus"/> &nbsp;
              add all fields
            </el-link>
            <br/>
            <el-link type="danger" style="margin-top: 10px;" @click="removeAllFields">
              <font-awesome-icon icon="minus"/> &nbsp;
              remove all fields
            </el-link>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="20">
            <varient :options="pageCtl.input" :varient-click="varientClick"
                     :inactive-lists="pageCtl.props.outputFields"
                     :inactive-singles="pageCtl.props.outputFields"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, SingleVarient, ListVarient } from '@/assets/js/logicflow/global'
import Varient from './Varient'
import { reactive, computed, inject } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  props: {
    filename: '',
    filekey: '', // 供后台使用
    outputFields: [] as Array<string>,
    _output: {
      singles: [] as Array<SingleVarient>,
      lists: [] as Array<ListVarient>
    }
  },
  input: {
    singles: [] as Array<SingleVarient>,
    lists: [] as Array<ListVarient>
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  if (pageCtl.props.filename === '') {
    pageCtl.props.filename = id + '.xlsx'
  }
  // 如果outputFields中的字段不存在pageCtl.input.single和pageCtl.input.list中, 移除
  const deleteFields = [] as Array<string>
  for (let i = 0; i < pageCtl.props.outputFields.length; i++) {
    const field = pageCtl.props.outputFields[i]
    if (pageCtl.input.singles.map(e => e.value).indexOf(field) === -1 && pageCtl.input.lists.map(e => e.value).indexOf(field) === -1) {
      deleteFields.push(field)
    }
  }
  for (let i = 0; i < deleteFields.length; i++) {
    handleClose(deleteFields[i])
  }
  pageCtl.visible = true
}
const varientClick = (name) => {
  if (pageCtl.props.outputFields.indexOf(name) === -1) {
    pageCtl.props.outputFields.push(name)
  } else {
    handleClose(name)
  }
}
const saveProps = () => {
  const label = '_FILENAME_' + pageCtl.id.substring(pageCtl.id.length - 4, pageCtl.id.length).toUpperCase()
  if (pageCtl.props.filename === '') {
    pageCtl.props.filename = pageCtl.id + '.xlsx'
  } else if (pageCtl.props.filename.endsWith('.xlsx') === false) {
    pageCtl.props.filename = pageCtl.props.filename + '.xlsx'
  }
  pageCtl.props._output.singles = [{
    type: 'file-output',
    value: pageCtl.props.filename,
    label,
    title: 'Save File: ' + pageCtl.props.filename
  }]
  pageCtl.props.filekey = label
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}
const handleClose = (field) => {
  pageCtl.props.outputFields.splice(pageCtl.props.outputFields.indexOf(field), 1)
}
const addAllFields = () => {
  pageCtl.props.outputFields = [...pageCtl.input.lists.map(e => e.value)]
}
const removeAllFields = () => {
  pageCtl.props.outputFields = []
}

const _tips = computed(() => {
  return [
    '- 本节点会在服务器上生成一个临时文件, 并且这个文件会在任务执行完成后删除',
    '- 后续的节点将会获得一个名为<b>_FILENAME_ + 4位随机数</b>的参数, 返回的是文件在服务器的相对路径'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'SaveFileComponent'
}
</script>

<style lang="scss">
#SaveFileComponent {
  .output-fields {
    width: 100%;
    height: 160px;
    border: 1px solid var(--scp-border-color-lighter);
    border-radius: 2px;
    table-layout: fixed;
    word-break: break-all;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 2px;

    .el-tag--small {
      height: 20px !important;
      line-height: 18px !important;
      user-select: none;
      margin-bottom: 3px;
    }

    .el-tag {
      font-size: 10px !important;
    }
  }
}
</style>
