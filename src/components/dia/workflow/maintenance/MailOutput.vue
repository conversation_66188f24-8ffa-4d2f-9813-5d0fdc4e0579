<template>
  <div id="SendMailComponent">
    <scp-draggable-resizable w="1024px" h="700px" v-model="pageCtl.visible" :title="'Send Mail - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px); overflow: auto;">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="16">
            <el-input v-model="pageCtl.props.subject" placeholder="Subject"/>
          </el-col>
          <el-col :span="4" class="input-tips" style="text-align: left">
            <el-select v-model="pageCtl.props.method" style="width: 100%" placeholder="发件方式">
              <el-option
                  v-for="item in [{value: 'NONE', label: '不发送'}, {value: 'MESSAGE', label: '站内信'},
                  {value: 'MAIL', label: '内部邮件'}, {value: 'MESSAGE_MAIL', label: '站内信&内部邮件'}, {value: 'APPOINTMENT', label: '会议邀请'}]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="input-tips">
            主题 & 发件方式
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;" v-if="pageCtl.props.method === 'APPOINTMENT'">
          <el-col :span="20" style="display: flex; justify-content: space-between;align-items: center;">
            <el-select v-model="pageCtl.props.appointment_date" style="width: 45%" placeholder="Appointment Date(YYYY/MM/DD)" allow-create filterable>
              <el-option-group
                  v-for="group in _date"
                  :key="group.label"
                  :label="group.label">
                <el-option
                    v-for="item2 in group.options"
                    :key="item2"
                    :label="item2"
                    :value="item2">
                </el-option>
              </el-option-group>
            </el-select>
            &nbsp;
            <el-select v-model="pageCtl.props.appointment_start" style="width: 25%" placeholder="Start Date" @change="startChange">
              <el-option-group
                  v-for="group in _time"
                  :key="group.label"
                  :label="group.label">
                <el-option
                    v-for="item2 in group.options"
                    :key="item2"
                    :label="item2"
                    :value="item2">
                </el-option>
              </el-option-group>
            </el-select>
            <font-awesome-icon icon="minus"/>
            <el-select v-model="pageCtl.props.appointment_end" style="width: 25%" placeholder="End Date">
              <el-option-group
                  v-for="group in _time"
                  :key="group.label"
                  :label="group.label">
                <el-option
                    v-for="item2 in group.options"
                    :key="item2"
                    :label="item2"
                    :value="item2">
                </el-option>
              </el-option-group>
            </el-select>
          </el-col>
          <el-col :span="4" class="input-tips">
            会议开始/结束时间
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="20">
            <el-select v-model="pageCtl.props.to" style="width: 100%" multiple filterable collapse-tags allow-create placeholder="To">
              <el-option
                  v-for="(item, index) in pageCtl.mailList"
                  :key="index"
                  :label="item['USER_NAME'] + ' [' + item['EMAIL'] +']'"
                  :value="item['EMAIL']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="input-tips">
            收件人
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="20">
            <el-select v-model="pageCtl.props.cc" style="width: 100%" multiple filterable collapse-tags allow-create placeholder="CC">
              <el-option
                  v-for="(item, index) in pageCtl.mailList"
                  :key="index"
                  :label="item['USER_NAME'] + ' [' + item['EMAIL'] +']'"
                  :value="item['EMAIL']">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" class="input-tips">
            抄送人
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="16">
            <el-select v-model="pageCtl.props.attachement" style="width: 100%" multiple filterable placeholder="Attachement">
              <el-option
                  v-for="(item, index) in _attachements"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="8" class="input-tips">
            因邮件服务限制, 过大的附件将会导致任务失败
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="20">
            <scp-ace-editor v-model="pageCtl.props.body" lang="html" style="border: 1px dotted var(--scp-border-color);width: 100%; height: 250px"/>
          </el-col>
          <el-col :span="4" class="input-tips">
            邮件正文
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="20">
            <varient :options="pageCtl.input"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import Varient from './Varient.vue'
import { getPreVarient, SingleVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive, watch } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  mailList: [],
  props: {
    subject: '',
    method: 'MESSAGE',
    to: [],
    cc: [],
    body: '',
    attachement: [],
    appointment_date: '',
    appointment_start: '',
    appointment_end: '',
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [] as Array<SingleVarient>,
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true

  if (pageCtl.mailList.length === 0) {
    $axios({
      method: 'post',
      url: '/diagnosis/workflow/maintenance/query_user_mail'
    }).then((body) => {
      pageCtl.mailList = body
    }).catch((error) => {
      console.log(error)
    })
  }

  if (!pageCtl.props.appointment_start) {
    // 生成会议时间
    const now = new Date()
    now.setTime(Math.ceil(now.getTime() / 900000) * 900000)
    const hour = now.getHours()
    const minute = now.getMinutes()
    pageCtl.props.appointment_date = $dateFormatter(now, 'yyyy/MM/dd')
    pageCtl.props.appointment_start = (hour < 10 ? '0' + hour : hour) + ':' + (minute < 10 ? '0' + minute : minute)

    startChange()
  }
}
const saveProps = () => {
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const startChange = () => {
  const times = _time.value[0].options as any
  let index = times.indexOf(pageCtl.props.appointment_start)
  if (index !== -1) {
    index = Math.min(index + 2, times.length - 1)
    pageCtl.props.appointment_end = times[index]
  } else {
    pageCtl.props.appointment_end = pageCtl.props.appointment_start
  }
}

const _attachements = computed(() => {
  const result = [] as Array<any>
  for (let i = 0; i < pageCtl.input.singles.length; i++) {
    const value = pageCtl.input.singles[i].value
    const type = pageCtl.input.singles[i].type
    if (value && type && type === 'file-output') {
      result.push({
        value: pageCtl.input.singles[i].label,
        label: value
      })
    }
  }
  return result
})

const _date = computed(() => {
  const result = []
  const date = new Date()
  result.push($dateFormatter(date, 'yyyy/MM/dd'))
  for (let i = 0; i < 60; i++) {
    date.setTime(date.getTime() + 3600000 * 24)
    result.push($dateFormatter(date, 'yyyy/MM/dd'))
  }
  return [{
    label: 'Time',
    options: result
  }, {
    label: '单值变量',
    options: pageCtl.input.singles.map(e => e.value)
  }]
})

const _time = computed(() => {
  const result = []
  for (let h = 0; h <= 23; h++) {
    for (let m = 0; m <= 45; m = m + 15) {
      result.push((h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m))
    }
  }
  return [{
    label: 'Time',
    options: result
  }, {
    label: '单值变量',
    options: pageCtl.input.singles.map(e => e.value)
  }]
})

const _tips = computed(() => {
  return [
    '- 本节点会在以DSS的身份发送一封邮件或者以System身份发送一份站内信',
    '- 主题不支持Table Value参数',
    '- 发送邮件会自动统计收件人作为被分发人并且将这些信息记录下来, 如果仅希望记录被分发人而不想发送邮件或站内信, 可以将发送方式设置为[不发送]状态',
    '- 手动创建的邮箱地址, 不会发送站内信',
    '- 邮件正文如果需要Table Value参数, 请使用[[]]明确标识需要循环的内容' +
    '<br/>比如<br/>' +
    '[[&lt;b&gt;{MATERIAL}&lt;/b&gt;]]' +
    '<br/>或<br/>' +
    '&lt;table&gt;<br/>' +
    '&nbsp;&nbsp;&nbsp;&nbsp;[[&lt;tr&gt;<br/>' +
    '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;td&gt;{MATERIAL}&lt;/td&gt;<br/>' +
    '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;td&gt;{PLANT_CODE}&lt;/td&gt;<br/>' +
    '&nbsp;&nbsp;&nbsp;&nbsp;&lt;/tr&gt;]]<br/>' +
    '&lt;/table&gt;',
    '- 为了防止大邮件, 在使用Table Value生成邮件正文时, 最大支持输出2000次循环'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'SendMailComponent'
}
</script>

<style lang="scss">
#SendMailComponent {
}
</style>
