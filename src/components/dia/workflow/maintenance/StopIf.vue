<template>
  <div id="StopIfComponent">
    <scp-draggable-resizable w="1024px" h="600px" v-model="pageCtl.visible" :title="'Stop If - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="6" class="input-tips" style="font-weight: bold">以下条件均成立时</el-col>
        </el-row>
        <hr style="margin-top:5px;margin-bottom: 10px;">
        <el-row style="margin-bottom: 10px" v-for="(element, elIndex) in pageCtl.props.conditions" :key="elIndex">
          <el-col :span="7">
            <el-select v-model="element.field" placeholder="Field" style="width: 100%" clearable filterable class="search-group-left">
              <el-option-group
                  v-for="group in pageCtl.fieldOpts"
                  :key="group.label"
                  :label="group.label">
                <el-option
                    v-for="item2 in group.options"
                    :key="item2.value"
                    :label="item2.value"
                    :value="item2.value">
                </el-option>
              </el-option-group>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="element.props" placeholder="Props" filterable style="width: var(--scp-input-width)" class="search-group-right">
              <el-option
                  v-for="item in ['.toString()', '.toNumber()', '.toInteger()', '.size()']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="element.operator" placeholder="Operator" filterable clearable style="width: var(--scp-input-width)">
              <el-option
                  v-for="item in operatorOpts(element.props)"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-input v-model="element.value" type="text" placeholder="Value"
                      style="width: var(--scp-input-width)" :disabled="element.operator === 'IS EMPTY'"/>
          </el-col>
          <el-col :span="2">
            <el-tag type="info" style="cursor: pointer;font-size: 10px" @click="newLine(index)" v-show="elIndex===0">
              <font-awesome-icon icon="plus"/>
            </el-tag>
            <el-tag type="info" style="cursor: pointer;font-size: 10px" @click="delLine(elIndex)" v-show="elIndex>0">
              <font-awesome-icon icon="minus"/>
            </el-tag>
          </el-col>
        </el-row>
        <hr style="margin-top:10px;margin-bottom: 5px;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="6" class="input-tips" style="font-weight: bold">当前工作流终止</el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { inject, reactive, computed } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  fieldOpts: [] as Array<any>,
  props: {
    conditions: [] as Array<any>,
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true

  pageCtl.fieldOpts = [{
    label: '列表变量',
    options: pageCtl.input.lists
  }, {
    label: '单值变量',
    options: pageCtl.input.singles
  }]
}
const saveProps = () => {
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}
const newLine = () => {
  pageCtl.props.conditions.push(
    {
      field: '',
      props: '.toString()',
      operator: '',
      value: ''
    }
  )
}
const delLine = (index) => {
  pageCtl.props.conditions.splice(index, 1)
}
const operatorOpts = (props) => {
  switch (props) {
    case '.toString()':
      return ['=', '<>', 'IS EMPTY']
    case '.toNumber()':
    case '.toInteger()':
    case '.size()':
      return ['=', '<>', '>', '<', '>=', '<=']
  }
  return ['=', '<>', '>', '<', '>=', '<=', 'IS EMPTY']
}

const _tips = computed(() => {
  return [
    '- 根据Variant判断当前任务链是否需要终止',
    '- 比如某个指标正常, 当前任务就不需要继续执行了, 可以使用这个组件',
    ' ',
    '如果选择单值变量:',
    '- toString(): 转换为字符串',
    '- toNumber(): 转换为双精度浮点数, 如果不是合法数字, 整列表达式返回false',
    '- toInteger(): 转换为整数, 如果不是合法数字, 整列表达式返回false',
    '- size(): 先将该变量转换为字符串, 再输出字符串的长度',
    ' ',
    '如果选择列表变量',
    '- toString(): 取列表中的第一行的该列值, 转换为字符串',
    '- toNumber(): 取列表中的第一行的该列值, 转换为双精度浮点数, 如果不是合法数字, 整列表达式返回false',
    '- toInteger(): 取列表中的第一行的该列值, 转换为整数, 如果不是合法数字, 整列表达式返回false',
    '- size(): 输出列表行数',
    ' ',
    'toString()进行"&gt;"或"&lt;"操作时, 将按照字母序列排序',
    'toNumber()进行"="操作时, 因为计算机存储原因, 两数差绝对值在0.000001以内,可以认为相等'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>
<script lang="ts">

export default {
  name: 'StopIfComponent'
}
</script>

<style lang="scss">
#StopIfComponent {
}
</style>
