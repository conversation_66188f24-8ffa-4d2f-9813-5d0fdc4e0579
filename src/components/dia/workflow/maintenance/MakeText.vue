<template>
  <div id="MakeTextComponent">
    <scp-draggable-resizable w="1024px" h="600px" v-model="pageCtl.visible" :tips="_tips" :title="'Make Text - ' + pageCtl.id" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="8">
            <el-input v-model="pageCtl.props.key" type="text" placeholder="Name" style="width: var(--scp-input-width)"/>
          </el-col>
          <el-col :span="8" class="input-tips">Name不要与其他变量名冲突</el-col>
          <el-col :span="8" style="text-align: right">
            <span class="input-tips" style="font-style: normal;margin-right: 10px">Render</span>
            <el-select v-model="pageCtl.props.render" placeholder="Render" filterable style="width: 120px">
              <el-option
                  v-for="item in ['plain_text', 'sql', 'java', 'javascript', 'json', 'html']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row style="height: calc(100% - 160px)">
          <el-col :span="24" style="height:100%">
            <scp-ace-editor v-model="pageCtl.props.value" :lang="pageCtl.props.render" style="width:100%;height: 100%;border: 1px dotted var(--scp-border-color);float:right"/>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <el-col :span="24">
            <varient :options="pageCtl.input"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, SingleVarient } from '@/assets/js/logicflow/global'
import { inject, reactive, computed } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import Varient from '@/components/dia/workflow/maintenance/Varient.vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  props: {
    key: '',
    value: '',
    render: 'plain_text',
    _output: {
      singles: [] as Array<SingleVarient>,
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}
const saveProps = () => {
  const key = '_' + pageCtl.props.key
  pageCtl.props._output.singles = [{
    type: 'make-text',
    value: pageCtl.props.value,
    label: key,
    title: 'Make Text: ' + pageCtl.props.value
  }]
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const _tips = computed(() => {
  return [
    '- 创建一个长文本变量, 这个变量可以供其他节点使用',
    '- 短变量请使用Make Value'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'MakeTextComponent'
}
</script>
