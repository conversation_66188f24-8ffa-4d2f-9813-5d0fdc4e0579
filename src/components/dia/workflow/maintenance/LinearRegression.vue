<template>
  <div id="LinearRegressionComponent">
    <scp-draggable-resizable w="800px" h="300px" v-model="pageCtl.visible" :title="'Linear Regression -' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="10">
            <el-select v-model="pageCtl.props.xAxis" filterable placeholder="xAxis" style="width: 100%;">
              <el-option-group
                  v-for="group in _xAxisOpts"
                  :key="group.label"
                  :label="group.label">
                <el-option
                    v-for="item2 in group.options"
                    :key="item2.value"
                    :label="item2.value"
                    :value="item2.value">
                </el-option>
              </el-option-group>
            </el-select>
          </el-col>
          <el-col :span="14" class="input-tips">选择时间序列字段, 系统在计算时会先按照升序排列(A->Z)后再计算</el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="10">
            <el-select v-model="pageCtl.props.yAxis" filterable placeholder="yAxis" style="width: 100%;">
              <el-option
                  v-for="item in pageCtl.input.lists"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="14" class="input-tips">选择线性回归变量对应的字段名, 需要为数字格式, 非数字将按0处理</el-col>
        </el-row>
        <br/>
        <el-row>
          <el-col :span="24">
            <span class="input-tips" style="padding-left: 0">线性回归表达式&nbsp;f(x)=kx + b&nbsp;, 当f(x)落在&nbsp;</span>
            <el-input-number v-model="pageCtl.props.thresholdFrom" style="width: 100px"/>
            <span class="input-tips" style="padding-left: 0">%至</span>&nbsp;
            <el-input-number v-model="pageCtl.props.thresholdTo" style="width: 100px"/>
            <span class="input-tips" style="padding-left: 0">%&nbsp;之&nbsp;&nbsp;</span>
            <el-select v-model="pageCtl.props.outputPolicy" style="width: 80px">
              <el-option label="内" value="IN"/>
              <el-option label="外" value="OUT"/>
            </el-select>
            <span class="input-tips">时, 输出数据行, 其他行将会被拦截</span>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  props: {
    xAxis: '',
    yAxis: '',
    thresholdFrom: -100,
    thresholdTo: 100,
    outputPolicy: 'IN',
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}

const _tips = computed(() => {
  return [
    '- 本节点会计算给定列x在时间序列y上的一次线性回归, 并且输出偏差较大的记录行或者符合线性回归的行',
    '- 如果没有时间序列字段, 可以使用_AUTO_SEQUENCE来代替, 系统将按照1, 2, 3, 4...作为时间序列进行计算',
    '- 如果使用_AUTO_SEQUENCE, 请先对结果集进行排序'
  ].join('<br>')
})

const _xAxisOpts = computed(() => {
  return [{
    label: '列表变量',
    options: pageCtl.input.lists
  }, {
    label: '系统参数',
    options: [{
      value: '_AUTO_SEQUENCE',
      label: '_AUTO_SEQUENCE',
      title: '_AUTO_SEQUENCE'
    }]
  }]
})

const saveProps = () => {
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'LinearRegressionComponent'
}
</script>
