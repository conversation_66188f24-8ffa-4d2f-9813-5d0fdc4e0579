<template>
  <div id="CallWorkflowComponent">
    <scp-draggable-resizable w="650px" h="200px" v-model="pageCtl.visible" :title="'Call Workflow - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="18">
            <el-select v-model="pageCtl.props.targetWorkflow" :placeholder="pageCtl.loading.init?'Loading...': 'Target Workflow'" filterable clearable
                       style="width: 100%" :loading="pageCtl.loading.init">
              <el-option
                  v-for="item in pageCtl.workflowList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5" class="input-tips">Target Workflow</el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'

const $axios: any = inject('$axios')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')

const pageCtl = reactive({
  visible: false,
  id: '',
  workflowList: [] as Array<any>,
  loading: {
    init: false
  },
  props: {
    targetWorkflow: '',
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [] as Array<any>,
    lists: [] as Array<any>
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true

  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_workflow_opts'
  }).then((body) => {
    pageCtl.workflowList = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}
const saveProps = () => {
  pageCtl.visible = false
  $saveProps(pageCtl.id, pageCtl.props)
}

const _tips = computed(() => {
  return [
    '- 目前Workflow调用颗粒度是精确到小时, 这就导致我们无法控制同一小时内的Workflow执行顺序',
    '- 所以我们设计了Call Workflow, 让用户可以在一个Workflow调用其他的Workflow',
    '- 如果一个Workflow过于复杂, 使用Priority组件来控制调用优先级就不再合理, 这时候就可以用Call Workflow来实现'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'CallWorkflowComponent'
}
</script>
