<template>
  <div id="TableOutputComponent">
    <scp-draggable-resizable w="800px" h="600px" v-model="pageCtl.visible" :title="'Table Output - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="12">
            <el-select v-model="pageCtl.props.targetTable" :placeholder="pageCtl.loading.init? 'Loading...': 'Target Table'" filterable clearable
                       style="width: 100%" @change="changeTable" :loading="pageCtl.loading.init">
              <el-option
                  v-for="item in pageCtl.tableList"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5" class="input-tips">
            <el-tooltip class="item" effect="light" placement="top" :show-after="500" content="Refresh Metadata">
              <font-awesome-icon icon="refresh" @click="refreshTable" :spin="pageCtl.loading.query"
                                 style="color: var(--scp-text-color-highlight);cursor: pointer;"/>
            </el-tooltip>
            &nbsp;Target Table
          </el-col>
        </el-row>
        <hr/>
        <div style="min-height: 48px" v-loading="pageCtl.loading.query">
          <el-row style="margin-bottom: 10px" v-for="(item, index) in pageCtl.props.tableColumns" :key="index">
            <el-col :span="18">
              <span>{{ index + 1 }}. </span>
              <span>{{ item.COLUMN_NAME }}</span>
              <div style="width: calc(100% - 200px); text-align: right;float: right">
                <el-select v-model="item.BINDING" placeholder="Stream Field" style="width: 100%" clearable allow-create filterable>
                  <el-option-group
                      v-for="group in columnOptions(item.COLUMN_NAME)"
                      :key="group.label"
                      :label="group.label">
                    <el-option
                        v-for="item2 in group.options"
                        :key="item2.label"
                        :label="item2.label"
                        :value="item2.label">
                    </el-option>
                  </el-option-group>
                </el-select>
              </div>
            </el-col>
            <el-col :span="6" class="input-tips">{{ item.DATA_TYPE }}({{ item.DATA_LENGTH }})</el-col>
          </el-row>
        </div>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')

interface TableColumn {
  BINDING: string,
  COLUMN_NAME: string,
  DATA_TYPE: string,
  DATA_LENGTH: string
}

const pageCtl = reactive({
  visible: false,
  id: '',
  tableList: [],
  columnDefaultValues: {},
  loading: {
    init: false,
    query: false
  },
  props: {
    targetTable: '',
    tableColumns: [] as Array<TableColumn>,
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
  queryTableDefaultValue()

  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_table_output_list'
  }).then((body) => {
    pageCtl.tableList = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}
const saveProps = () => {
  pageCtl.visible = false
  $saveProps(pageCtl.id, pageCtl.props)
}
const changeTable = () => {
  if (pageCtl.props.targetTable) {
    pageCtl.loading.query = true
    pageCtl.props.tableColumns = []
    $axios({
      method: 'post',
      url: '/diagnosis/workflow/maintenance/query_table_columns',
      data: {
        table: pageCtl.props.targetTable
      }
    }).then((body) => {
      pageCtl.props.tableColumns = body
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.query = false
    })
    queryTableDefaultValue()
  } else {
    pageCtl.props.tableColumns = []
  }
}

const refreshTable = () => {
  if (pageCtl.props.targetTable) {
    pageCtl.loading.query = true
    $axios({
      method: 'post',
      url: '/diagnosis/workflow/maintenance/query_table_columns',
      data: {
        table: pageCtl.props.targetTable
      }
    }).then((body) => {
      // 先将body中有的, tableColumns中没有的加进去
      const bs = body.map(e => e.COLUMN_NAME)
      const ts = pageCtl.props.tableColumns.map(e => e.COLUMN_NAME)
      for (let i = 0; i < body.length; i++) {
        const b = body[i]
        const columnName = b.COLUMN_NAME
        if (ts.indexOf(columnName) === -1) {
          pageCtl.props.tableColumns.push(b)
        }
      }

      // 再删除tableColumns中有, body中没有的
      for (let i = pageCtl.props.tableColumns.length - 1; i >= 0; i--) {
        const t = pageCtl.props.tableColumns[i]
        const columnName = t.COLUMN_NAME
        if (bs.indexOf(columnName) === -1) {
          pageCtl.props.tableColumns.splice(i, 1)
        }
      }
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.query = false
    })
    queryTableDefaultValue()
  } else {
    pageCtl.props.tableColumns = []
  }
}

const queryTableDefaultValue = () => {
  if (pageCtl.props.targetTable) {
    $axios({
      method: 'post',
      url: '/diagnosis/workflow/maintenance/query_table_default_value',
      data: {
        table: pageCtl.props.targetTable
      }
    }).then((body) => {
      pageCtl.columnDefaultValues = body
    }).catch((error) => {
      console.log(error)
    })
  } else {
    pageCtl.props.tableColumns = []
  }
}
const columnOptions = (columnName) => {
  const opts = [] as Array<any>
  const defaultValues = pageCtl.columnDefaultValues[columnName]
  if (defaultValues && defaultValues.length > 0) {
    const op = [] as Array<any>
    for (let i = 0; i < defaultValues.length; i++) {
      op.push({
        label: defaultValues[i],
        value: defaultValues[i],
        title: defaultValues[i]
      })
    }
    opts.push({
      label: '可用值',
      options: op
    })
  } else {
    const grouping = pageCtl.input.singles.filter(e => e.type === 'grouping')
    opts.push({
      label: '列表变量',
      options: pageCtl.input.lists
    }, {
      label: '单值变量',
      options: pageCtl.input.singles.filter(e => e.type !== 'grouping')
    })
    if (grouping.length > 0) {
      opts.push({
        label: '分组变量',
        options: grouping
      })
    }
    opts.push({
      label: '数据库函数',
      options: [{ value: '_SYSDATE', label: '_SYSDATE' }, { value: '_UUID8', label: '_UUID8' }, { value: '_UUID16', label: '_UUID16' }]
    })
  }
  return opts
}

const _tips = computed(() => {
  return [
    '- 本节点会将前序步骤的输出结果写入数据库',
    '- 日期类型的字段, 请使用YYYY/MM/DD格式的字符串插入'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'TableOutputComponent'
}
</script>

<style lang="scss">
#TableOutputComponent {
}
</style>
