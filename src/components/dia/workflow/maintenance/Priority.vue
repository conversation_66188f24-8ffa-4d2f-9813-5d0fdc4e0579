<template>
  <div id="PriorityComponent">
    <scp-draggable-resizable w="650px" h="160px" v-model="pageCtl.visible" :save="saveProps" :title="'Priority - ' + pageCtl.id" :tips="_tips">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="6">
            <el-input-number v-model="pageCtl.props.priority" style="width: 100%" :min="0" :max="100" :step="1"/>
          </el-col>
          <el-col :span="18" class="input-tips">执行树的优先级, 越大执行越靠前</el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { inject, reactive, computed } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  props: {
    priority: 0,
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}

const saveProps = () => {
  pageCtl.visible = false
  $saveProps(pageCtl.id, pageCtl.props)
}

const _tips = computed(() => {
  return [
    '- 当一个任务包含多个独立的执行树时, 需要用这个组件来决定每棵树的执行顺序',
    '- 优先级越大, 执行越靠前',
    '- 未设置优先级的执行树时, 按照优先级为0执行, 优先级相同时, 随机顺序执行(由哈希表特性决定)',
    '- 此节点只可以作为开始节点使用, 如果作为并行开始节点, 可能会无法正确识别优先级'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'PriorityComponent'
}
</script>
