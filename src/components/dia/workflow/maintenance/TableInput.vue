<template>
  <div id="TableInputComponent">
    <scp-draggable-resizable w="1024px" h="600px" v-model="pageCtl.visible" :title="'Table Input - ' + pageCtl.id" :tips="_tips" :save="saveProps"
                             :draft="formatSQL" :draft-loading="pageCtl.formating" draft-text="Format" :save-loading="pageCtl.saving">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="22" v-loading="pageCtl.formating">
            <scp-ace-editor v-model="pageCtl.props.sql" :editor-init="editorInit" lang="sql" :options="pageCtl.aceOptions"
                            style="width: 100%; height:350px;border:1px solid var(--scp-border-color)"/>
          </el-col>
          <el-col :span="2" class="input-tips">SQL</el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="22">
            <varient :options="pageCtl.input"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, ListVarient } from '@/assets/js/logicflow/global'
import keywords from '@/assets/js/oracle-keywords.js'
import Varient from './Varient.vue'
import { inject, reactive, computed } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl: any = reactive({
  visible: false,
  formating: false,
  saving: false,
  id: '',
  aceOptions: {
    enableLiveAutocompletion: true
  },
  completions: [],
  props: {
    sql: '',
    _output: {
      singles: [],
      lists: [] as Array<ListVarient>
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

// 如果以后需要将workflow剥离出来, 这个地方需要改一下
const showWin = (id, props, graph, params) => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_table_conf'
  }).then((body) => {
    // 加载数据库配置
    const tables = body.tables
    const cols = body.cols
    const completions = [] as Array<any>

    for (const i in tables) {
      completions.push({ caption: tables[i], meta: 'table', value: 'SCPA.' + tables[i] })
    }

    for (const i in cols) {
      completions.push({ caption: cols[i], meta: 'column', value: cols[i] })
    }

    const keys = Object.keys(keywords)
    for (const index in keys) {
      const key = keys[index]
      const list = keywords[key]
      for (const i in list) {
        const value = list[i]
        completions.push({ caption: value, meta: key, value })
      }
    }
    completions.sort((e1, e2) => e1.caption.localeCompare(e2.caption))
    pageCtl.completions = completions

    // 从前序步骤获取所有可用的参数, 保存在_input对象中
    pageCtl.input = getPreVarient(id, graph, params)
    pageCtl.id = id
    pageCtl.props = props
    pageCtl.visible = true
  }).catch((error) => {
    console.log(error)
  })
}

const formatSQL = () => {
  pageCtl.formating = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/format_sql',
    data: {
      scripts: pageCtl.props.sql
    }
  }).then((body) => {
    if (body) {
      pageCtl.props.sql = body
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.formating = false
  })
}

const saveProps = () => {
  pageCtl.saving = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_columns_by_sql',
    data: {
      sql: pageCtl.props.sql,
      singles: pageCtl.input.singles
    }
  }).then((body) => {
    if (typeof body === 'string') {
      $message.error(body + '')
    } else {
      const lists = [] as Array<ListVarient>
      for (let i = 0; i < body.length; i++) {
        lists.push({
          value: body[i],
          label: body[i],
          title: 'Table Input: ' + body[i]
        })
      }
      pageCtl.props._output.lists = lists
      $saveProps(pageCtl.id, pageCtl.props)
      pageCtl.visible = false
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.saving = false
  })
}

const setCompletions = (editor, session, pos, prefix, callback) => {
  // 这里只是举例子，具体实现要看自己需求
  if (prefix.length === 0) {
    return callback(null, [])
  } else {
    return callback(null, pageCtl.completions)
  }
}

const editorInit = (editor) => {
  // 加入自定义语法提示
  editor.completers = [{
    getCompletions: function (editor, session, pos, prefix, callback) {
      setCompletions(editor, session, pos, prefix, callback)
    }
  }]
}

const _tips = computed(() => {
  return [
    '- 本节点会从DSS数据库中获取一个表格, 所有输出字段都会保存在<b>Table Value</b>参数集',
    '- Table Input节点会清空此步骤之前所有的Table Value参数'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'TableInputComponent'
}
</script>

<style lang="scss">
#TableInputComponent {
}
</style>
