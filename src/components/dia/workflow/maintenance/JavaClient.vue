<template>
  <div id="JavaClientComponent">
    <scp-draggable-resizable w="1280px" h="700px" v-model="pageCtl.visible" :title="'Java Client - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="22" v-loading="pageCtl.formating">
            <scp-ace-editor v-model="pageCtl.props.java" :editor-init="editorInit" lang="java" :options="pageCtl.aceOptions"
                            style="width: 100%; height:450px;border:1px solid var(--scp-border-color)"/>
          </el-col>
          <el-col :span="2" class="input-tips">Java Code</el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="22">
            <varient :options="pageCtl.input" :copy-as-placeholder="false"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, ListVarient } from '@/assets/js/logicflow/global'
import keywords from '@/assets/js/java-keywords.js'
import Varient from './Varient.vue'
import { inject, reactive, computed } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl: any = reactive({
  visible: false,
  id: '',
  aceOptions: {
    enableLiveAutocompletion: true
  },
  completions: [],
  props: {
    java: '',
    _output: {
      singles: [],
      lists: [] as Array<ListVarient>
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

// 如果以后需要将workflow剥离出来, 这个地方需要改一下
const showWin = (id, props, graph, params) => {
  const completions = [] as Array<any>

  const keys = Object.keys(keywords)
  for (const index in keys) {
    const key = keys[index]
    const list = keywords[key]
    for (const i in list) {
      const value = list[i]
      completions.push({ caption: value, meta: key, value })
    }
  }
  completions.sort((e1, e2) => e1.caption.localeCompare(e2.caption))
  pageCtl.completions = completions

  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}

const saveProps = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_java_client_output',
    data: {
      java: pageCtl.props.java
    }
  }).then((body) => {
    if (typeof body === 'string') {
      $message.error(body + '')
    } else {
      const lists = [] as Array<ListVarient>

      if (body.lists) {
        for (let i = 0; i < body.lists.length; i++) {
          lists.push({
            value: body.lists[i],
            label: body.lists[i],
            title: 'JavaClient: ' + body.lists[i]
          })
        }
        pageCtl.props._output.lists = lists
      }

      if (body.singles) {
        for (let i = 0; i < body.singles.length; i++) {
          pageCtl.props._output.singles = [{
            type: 'java-client',
            value: body.singles[i],
            label: body.singles[i],
            title: 'JavaClient: ' + body.singles[i]
          }]
        }
      }

      $saveProps(pageCtl.id, pageCtl.props)
      pageCtl.visible = false
    }
  }).catch((error) => {
    console.log(error)
  })
}

const setCompletions = (editor, session, pos, prefix, callback) => {
  // 这里只是举例子，具体实现要看自己需求
  if (prefix.length === 0) {
    return callback(null, [])
  } else {
    return callback(null, pageCtl.completions)
  }
}

const editorInit = (editor) => {
  // 加入自定义语法提示
  editor.completers = [{
    getCompletions: function (editor, session, pos, prefix, callback) {
      setCompletions(editor, session, pos, prefix, callback)
    }
  }]
}

const _tips = computed(() => {
  return [
    '- 本节点会基于JDK17执行一段java代码',
    '- 具体可以参考注释'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'JavaClientComponent'
}
</script>

<style lang="scss">
#JavaClientComponent {
}
</style>
