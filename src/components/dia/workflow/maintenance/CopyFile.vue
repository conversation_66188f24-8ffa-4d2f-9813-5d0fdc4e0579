<template>
  <div id="CopyFileComponent">
    <scp-draggable-resizable w="620px" h="420px" v-model="pageCtl.visible" :title="'Copy File - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 40px); overflow: auto;">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-radio-group v-model="pageCtl.props.type" size="small">
              <el-radio-button value="smb">Windows Shared Disk</el-radio-button>
              <el-radio-button value="sftp" :disabled="true">SFTP</el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="10" class="input-tips">
            复制文件的方式
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-select v-model="pageCtl.props.files" style="width: 100%" multiple filterable placeholder="Files">
              <el-option
                  v-for="(item, index) in _attachements"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="10" class="input-tips">
            需要复制的文件
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-input v-model.trim="pageCtl.props.path" placeholder="Shared Path"/>
          </el-col>
          <el-col :span="10" class="input-tips">
            需要复制到的共享盘路径, 必须以<b> \\ </b>开头
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-select v-model="pageCtl.props.rename" style="width: 100%" placeholder="Rename">
              <el-option label="None" value="none"/>
              <el-option label="Add Timestamp" value="timestamp"/>
            </el-select>
          </el-col>
          <el-col :span="10" class="input-tips">
            复制到新目录, 是否要添加后缀
          </el-col>
        </el-row>
        <el-divider content-position="left">认证信息</el-divider>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="8">
            <el-select v-model="pageCtl.props.domain" style="width: 100%" placeholder="Domain">
              <el-option label="APA" value="APA"/>
              <el-option label="Local Domain" value="local"/>
            </el-select>
          </el-col>
          <el-col :span="16" class="input-tips">
            使用域账户的时候选择APA, 本地账户选择Local Domain
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-input v-model.trim="pageCtl.props.username" placeholder="Username"/>
          </el-col>
          <el-col :span="10" class="input-tips">
            用户名
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="14">
            <el-input v-model.trim="pageCtl.props.password" placeholder="Password" type="password" autocomplete="new-password"/>
          </el-col>
          <el-col :span="10" class="input-tips">
            <el-button type="primary" @click="testConnection" :loading="pageCtl.loading">
              <font-awesome-icon icon="link"/>
              &nbsp;Test Connection
            </el-button>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient, SingleVarient, ListVarient } from '@/assets/js/logicflow/global'
import { reactive, computed, inject } from 'vue'

const $message: any = inject('$message')
const $axios: any = inject('$axios')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  loading: false,
  id: '',
  props: {
    type: 'smb',
    path: '',
    rename: 'none',
    username: '',
    password: '',
    domain: 'APA',
    files: [],
    _output: {
      singles: [] as Array<SingleVarient>,
      lists: [] as Array<ListVarient>
    }
  },
  input: {
    singles: [] as Array<SingleVarient>,
    lists: [] as Array<ListVarient>
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}

const saveProps = () => {
  if (!pageCtl.props.path.startsWith('\\\\')) {
    $message.error('共享盘路径应该以\\\\开头, 比如\\\\192.168.1.1\\shared')
    return
  } else if (pageCtl.props.files.length === 0) {
    $message.error('请选择一个文件')
    return
  } else if (pageCtl.props.username === '') {
    $message.error('请输入用户名')
    return
  } else if (pageCtl.props.password === '') {
    $message.error('请输入密码')
    return
  }
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const testConnection = () => {
  if (!pageCtl.props.path.startsWith('\\\\')) {
    $message.error('共享盘路径应该以\\\\开头, 比如\\\\192.168.1.1\\shared')
    return
  } else if (pageCtl.props.username === '') {
    $message.error('请输入用户名')
    return
  } else if (pageCtl.props.password === '') {
    $message.error('请输入密码')
    return
  }
  pageCtl.loading = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/test_shared_disk',
    data: {
      path: pageCtl.props.path,
      domain: pageCtl.props.domain,
      username: pageCtl.props.username,
      password: pageCtl.props.password
    }
  }).then((body) => {
    body = body.toString()
    if (body.startsWith('Succeeded')) {
      $message.success(body)
    } else {
      $message.error(body)
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading = false
  })
}

const _attachements = computed(() => {
  const result = [] as Array<any>
  for (let i = 0; i < pageCtl.input.singles.length; i++) {
    const value = pageCtl.input.singles[i].value
    const type = pageCtl.input.singles[i].type
    if (value && type && type === 'file-output') {
      result.push({
        value: pageCtl.input.singles[i].label,
        label: value
      })
    }
  }
  return result
})

const _tips = computed(() => {
  return [
    '- 本节点会将生成的文件复制到远程服务器',
    '- 目前仅支持windows共享磁盘, SFTP会在有需求的时候增加',
    '- 因安全策略的需要, 共享磁盘必须使用账号密码来使用'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'CopyFileComponent'
}
</script>

<style lang="scss">
#CopyFileComponent {

}
</style>
