<template>
  <div class="available-varient">
    <el-collapse v-model="pageCtl.activeNames">
      <el-collapse-item title="可用参数" name="tab1">
        <el-divider content-position="left" v-if="_singleValues.length > 0">Single Value ({{ _singleValues.length }})</el-divider>
        <el-button size="small" v-for="(item, index) in _singleValues"
                   :key="'single_' + index"
                   :title="item.title"
                   :type="props.inactiveSingles.indexOf(item.label) !== -1 ? 'default' : 'primary'"
                   @click="insertToEditor(item.label)">
          {{ item.label }}
        </el-button>

        <el-divider content-position="left" v-if="_listValues.length > 0">Table Value ({{ _listValues.length }})</el-divider>
        <el-button size="small" v-for="(item, index) in _listValues"
                   :key="'list_' + index"
                   :title="item.title"
                   :type="props.inactiveLists.indexOf(item.label) !== -1 ? 'default' : 'primary'"
                   @click="insertToEditor(item.label)">
          {{ item.label }}
        </el-button>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script lang="ts" setup>
import { SingleVarient, ListVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'

const $message: any = inject('$message')
const $copyText: any = inject('$copyText')

interface Options {
  singles: Array<SingleVarient>,
  lists: Array<ListVarient>
}

// @ts-ignore
const props = withDefaults(defineProps<{
  options?: Options,
  inactiveSingles?: Array<string>,
  inactiveLists?: Array<string>,
  varientClick?: Function,
  copyAsPlaceholder?: boolean
}>(), {
  options: () => {
    return {
      singles: [],
      lists: []
    }
  },
  inactiveSingles: () => {
    return []
  },
  inactiveLists: () => {
    return []
  },
  varientClick: undefined,
  copyAsPlaceholder: true
})

const pageCtl = reactive({
  activeNames: ['tab1']
})

const insertToEditor = (name) => {
  if (props.varientClick) {
    props.varientClick(name)
  } else {
    if (props.copyAsPlaceholder) {
      $copyText('{' + name + '}')
    } else {
      $copyText(name)
    }
    $message.success('Varient copied!')
  }
}

const _singleValues = computed(() => {
  const values = [] as Array<SingleVarient>
  for (let i = 0; i < props.options.singles.length; i++) {
    values.push({
      type: props.options.singles[i].type,
      label: props.options.singles[i].label,
      value: props.options.singles[i].value,
      title: props.options.singles[i].title || props.options.singles[i].label
    })
  }
  values.push(
    { type: 'system-variant', value: '_DAY', label: '_DAY', title: 'Built-in: 当前日期, YYYY/MM/DD格式' },
    { type: 'system-variant', value: '_TIME', label: '_TIME', title: 'Built-in: 当前时间, HH24:MI:SS格式' }
  )
  return values
})

const _listValues = computed(() => {
  const values = [] as Array<ListVarient>
  for (let i = 0; i < props.options.lists.length; i++) {
    values.push({
      label: props.options.lists[i].label,
      value: props.options.lists[i].value,
      title: props.options.lists[i].title || props.options.lists[i].label
    })
  }
  return values
})
</script>

<script lang="ts">
export default {
  name: 'Varient'
}
</script>
<style lang="scss">
.available-varient {

  .el-button--small {
    font-size: 10px;
    height: 22px !important;
    padding: 4px 8px !important;
    margin-bottom: 4px;
  }

  .el-button--default:focus, .el-button--default:hover {
    border-color: var(--scp-text-color-hightlight-lighter);
    color: var(--scp-text-color-highlight);
    background-color: #fff;
  }

  .el-button {
    margin-left: 0 !important;
    margin-right: 10px !important;
  }

  .el-divider--horizontal {
    margin: 15px 0 !important;
  }

  .el-collapse {
    border: 0 !important;
  }

  .el-collapse-item__content {
    padding-bottom: 0 !important;
  }

  .el-collapse-item__wrap {
    border: 0 !important;
  }

  .el-collapse-item__header {
    height: 24px !important;
    line-height: 24px !important;
    border: 0 !important;
    font-size: 12px;
    font-style: italic;
    color: var(--scp-text-color);
  }
}
</style>
