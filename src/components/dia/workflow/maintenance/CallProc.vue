<template>
  <div id="CallProcComponent">
    <scp-draggable-resizable w="650px" h="400px" v-model="pageCtl.visible" :title="'Call Proc. - ' + pageCtl.id" :tips="_tips" :save="saveProps">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="18">
            <el-select v-model="pageCtl.props.targetProc" :placeholder="pageCtl.loading.init?'Loading...': 'Target Procedure'" filterable clearable
                       style="width: 100%" @change="changeProc" :loading="pageCtl.loading.init">
              <el-option
                  v-for="item in pageCtl.procList"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5" class="input-tips">Target Procedure</el-col>
        </el-row>
        <hr/>
        <div style="min-height: 48px" v-loading="pageCtl.loading.query">
          <el-row style="margin-bottom: 10px;" v-for="(item, index) in pageCtl.props.procArgs" :key="index">
            <el-col :span="18">
              <span>{{ index + 1 }}. </span>
              <span>{{ item.ARGUMENT_NAME }}</span>
              <div style="width: calc(100% - 180px); text-align: right;float: right" v-show="item.IN_OUT ==='IN' || item.IN_OUT ==='IN/OUT'">
                <el-select v-model="item.BINDING" placeholder="Stream Field" style="width: 100%" clearable allow-create filterable>
                  <el-option-group
                      v-for="group in _paramOptions"
                      :key="group.label"
                      :label="group.label">
                    <el-option
                        v-for="item2 in group.options"
                        :key="item2.label"
                        :label="item2.label"
                        :value="item2.label">
                    </el-option>
                  </el-option-group>
                </el-select>
              </div>
            </el-col>
            <el-col :span="6" class="input-tips">{{ item.IN_OUT }} - {{ item.DATA_TYPE }}</el-col>
          </el-row>
        </div>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'

const $axios: any = inject('$axios')
const $endWith: any = inject('$endWith')
const $saveProps: any = inject('WorkflowMaintenance.saveProps')

interface ProcArgs {
  ARGUMENT_NAME: string,
  DATA_TYPE: string,
  IN_OUT: string,
  BINDING: string
}

const pageCtl = reactive({
  visible: false,
  id: '',
  procList: [] as Array<string>,
  loading: {
    init: false,
    query: false
  },
  props: {
    targetProc: '',
    procArgs: [] as Array<ProcArgs>,
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [] as Array<any>,
    lists: [] as Array<any>
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true

  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/diagnosis/workflow/maintenance/query_proc_list'
  }).then((body) => {
    pageCtl.procList = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}
const saveProps = () => {
  pageCtl.visible = false
  $saveProps(pageCtl.id, pageCtl.props)
}
const changeProc = () => {
  if (pageCtl.props.targetProc) {
    pageCtl.loading.query = true
    pageCtl.props.procArgs = []
    $axios({
      method: 'post',
      url: '/diagnosis/workflow/maintenance/query_proc_args',
      data: {
        proc: pageCtl.props.targetProc
      }
    }).then((body) => {
      pageCtl.props.procArgs = body
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.query = false
    })
  } else {
    pageCtl.props.procArgs = []
  }
}

const _tips = computed(() => {
  return [
    '- 在工作流中难免会使用到一些更新或者删除操作, 但删除更新权限交由普通用户来调用是一个很不安全的行为',
    '- 所以我们设计了一个Call Proc.的组件, 旨在可以通过调用存储过程来执行一些更新删除操作',
    '- 目前只支持String类型输入参数, 其他类型可能会出现意想不到的问题'
  ].join('<br>')
})

const _paramOptions = computed(() => {
  const grouping = pageCtl.input.singles.filter(e => e.type === 'grouping')
  const opts = []
  opts.push({
    label: '单值变量',
    options: pageCtl.input.singles.filter(e => e.type !== 'grouping')
  })
  if (grouping.length > 0) {
    opts.push({
      label: '分组变量',
      options: grouping
    })
  }
  return opts
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'CallProcComponent'
}
</script>
