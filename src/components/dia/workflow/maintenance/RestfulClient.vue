<template>
  <div id="RestfulClientComponent">
    <scp-draggable-resizable w="900px" h="600px" v-model="pageCtl.visible" :title="'Restful Client - ' + pageCtl.id"
                             :save="saveProps" :tips="_tips">
      <div style="padding: 10px; height: calc(100% - 20px); overflow: auto;">
        <el-row style="margin-bottom: 10px;">
          <el-col :span="20">
            <el-input v-model="pageCtl.props.url" placeholder="URL"/>
          </el-col>
          <el-col :span="4" class="input-tips">
            API URL
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <ul>
              <li>
                <el-row style="text-align: center; margin-bottom: 10px">
                  <el-col :span="8">
                    <el-input v-model="pageCtl.defaultKey" disabled></el-input>
                  </el-col>
                  <el-col :span="2" style="display: flex;
                    justify-content: center; align-items: center;">
                    <font-awesome-icon icon="arrow-right"/>
                  </el-col>
                  <el-col :span="13">
                    <el-input v-model="pageCtl.defaultValue" disabled></el-input>
                  </el-col>
                </el-row>
              </li>
              <li v-for="(headers,index) in pageCtl.props.headers" :key="index">
                <el-row style="text-align: center; margin-bottom: 10px">
                  <el-col :span="8">
                    <el-input v-model="headers.key" placeholder="Key"></el-input>
                  </el-col>
                  <el-col :span="2" style="display: flex;
                    justify-content: center; align-items: center;">
                    <font-awesome-icon icon="arrow-right"/>
                  </el-col>
                  <el-col :span="13">
                    <el-input v-model="headers.value" placeholder="Value"></el-input>
                  </el-col>
                  <el-col :span="1" v-if="index===0">
                    <div @click="newLine()" style="color:var(--scp-text-color-highlight);height: 100%; display: flex;
                    justify-content: center; align-items: center; cursor: pointer">
                      <font-awesome-icon icon="plus"/>
                    </div>
                  </el-col>
                  <el-col :span="1" v-if="index!==0">
                    <div @click="delLine(index)" style="color: var(--scp-text-color-error);height: 100%; display: flex;
                    justify-content: center; align-items: center; cursor: pointer">
                      <font-awesome-icon icon="minus"/>
                    </div>
                  </el-col>
                </el-row>
              </li>
            </ul>
          </el-col>
          <el-col :span="4" class="input-tips">
            Request Headers
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px;">
          <el-col :span="20">
            <scp-ace-editor v-model="pageCtl.props.body" lang="json"
                            style="border: 1px dotted var(--scp-border-color);width: 100%; height: 250px"/>
          </el-col>
          <el-col :span="4" class="input-tips">
            Request Body<br/>
            <el-tooltip effect="light" content="Format JSON" :show-after="500">
              <font-awesome-icon  style="cursor: pointer;font-size: 0.5rem;margin-top:10px;" icon="align-left" @click="formatJSON()"/>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row style="margin-bottom: 10px">
          <el-col :span="20">
            <varient :options="pageCtl.input"/>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import Varient from './Varient.vue'
import { getPreVarient, ListVarient, SingleVarient } from '@/assets/js/logicflow/global'
import { computed, inject, reactive } from 'vue'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $message: any = inject('$message')

interface Header {
  key: string,
  value: string
}

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl = reactive({
  visible: false,
  id: '',
  defaultKey: 'Content-Type',
  defaultValue: 'application/json;charset=UTF-8',
  props: {
    resKey: '',
    url: '',
    headers: [
      {
        key: '',
        value: ''
      }
    ] as Array<Header>,
    body: '',
    _output: {
      singles: [] as Array<SingleVarient>,
      lists: [] as Array<ListVarient>
    }
  },
  input: {
    singles: [] as Array<SingleVarient>,
    lists: [] as Array<ListVarient>
  }
})

const newLine = () => {
  pageCtl.props.headers.push(
    {
      key: '',
      value: ''
    }
  )
}
const delLine = (index) => {
  pageCtl.props.headers.splice(index, 1)
}

const formatJSON = () => {
  try {
    const obj = JSON.parse(pageCtl.props.body)
    pageCtl.props.body = JSON.stringify(obj, null, 4)
  } catch (e) {
    $message.error('Invalid JSON format!')
  }
}

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true
}

const saveProps = () => {
  const resKey = '_RESTFUL_CLIENT_RESPONSE_' + pageCtl.id.substring(pageCtl.id.length - 4, pageCtl.id.length).toUpperCase()
  pageCtl.props.resKey = resKey
  pageCtl.props._output.singles = [{
    value: resKey,
    label: resKey,
    title: 'RESTful Client Response: ' + pageCtl.props.resKey
  }]
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}

const _tips = computed(() => {
  return [
    '- 本节点会以POST方式, 调用一个Restful API',
    '- 单值变量执行直接替换',
    '- 列表变量, 取第一个值替换, 如果列表为空, 替换为空',
    '- 后续的节点将会获得一个名为<b>_RESTFUL_CLIENT_RESPONSE_ + 4位随机数</b>的参数, 返回的是RESTful API的结果'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'RestfulClientComponent'
}
</script>

<style lang="scss">
#RestfulClientComponent {
}
</style>
