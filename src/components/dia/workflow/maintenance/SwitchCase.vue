<template>
  <div id="SwitchCaseComponent">
    <scp-draggable-resizable w="800px" h="450px" v-model="pageCtl.visible" :save="saveProps" :title="'Switch Case - ' + pageCtl.id" :tips="_tips">
      <div style="padding: 10px; height: calc(100% - 20px);overflow: auto;">
        <el-row style="margin-bottom: 10px">
          <el-col :span="16">
            <el-select v-model="pageCtl.props.field" placeholder="Field.toString()" style="width: 100%" clearable filterable>
              <el-option-group
                  v-for="group in pageCtl.fieldOpts"
                  :key="group.label"
                  :label="group.label">
                <el-option
                    v-for="item2 in group.options"
                    :key="item2.value"
                    :label="item2.value"
                    :value="item2.value">
                </el-option>
              </el-option-group>
            </el-select>
          </el-col>
          <el-col :span="8" class="input-tips"> Switch Field</el-col>
        </el-row>
        <hr style="margin-top:5px;margin-bottom: 10px;">
        <el-row style="margin-bottom: 10px" v-for="(element, elIndex) in pageCtl.props.conditions" :key="elIndex">
          <el-col :span="9">
            <el-select v-model="element.value" placeholder="Value" style="width: 100%" clearable filterable allow-create>
              <el-option
                  v-for="item in ['[default]']"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1" style="text-align: center;line-height: 1.17rem">
            <font-awesome-icon icon="long-arrow-alt-right"/>
          </el-col>
          <el-col :span="12">
            <el-select v-model="element.action" placeholder="Action" style="width: var(--scp-input-width)" clearable filterable>
              <el-option
                  v-for="item in pageCtl.actionOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2">
            <el-tag type="info" style="cursor: pointer;font-size: 10px" @click="newLine()" v-show="elIndex===0">
              <font-awesome-icon icon="plus"/>
            </el-tag>
            <el-tag type="info" style="cursor: pointer;font-size: 10px" @click="delLine(elIndex)" v-show="elIndex>0">
              <font-awesome-icon icon="minus"/>
            </el-tag>
          </el-col>
        </el-row>
      </div>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import { getPreVarient } from '@/assets/js/logicflow/global'
import { inject, reactive, computed } from 'vue'

const $saveProps: any = inject('WorkflowMaintenance.saveProps')
const pageCtl: any = reactive({
  visible: false,
  id: '',
  fieldOpts: [],
  actionOpts: [] as Array<any>,
  props: {
    field: '',
    conditions: [],
    _output: {
      singles: [],
      lists: []
    }
  },
  input: {
    singles: [],
    lists: []
  }
})

const showWin = (id, props, graph, params) => {
  // 从前序步骤获取所有可用的参数, 保存在_input对象中
  pageCtl.input = getPreVarient(id, graph, params)
  pageCtl.id = id
  pageCtl.props = props
  pageCtl.visible = true

  const nodesMap = {}
  for (let i = 0; i < graph.nodes.length; i++) {
    nodesMap[graph.nodes[i].id] = graph.nodes[i].text.value
  }

  const nextActions = [] as Array<any>
  for (let i = 0; i < graph.edges.length; i++) {
    if (graph.edges[i].sourceNodeId === id) {
      nextActions.push({
        label: nodesMap[graph.edges[i].targetNodeId] + ' (' + graph.edges[i].targetNodeId + ')',
        value: graph.edges[i].targetNodeId
      })
    }
  }
  pageCtl.actionOpts = nextActions

  pageCtl.fieldOpts = [{
    label: '列表变量',
    options: pageCtl.input.lists
  }, {
    label: '单值变量',
    options: pageCtl.input.singles
  }]
}
const saveProps = () => {
  $saveProps(pageCtl.id, pageCtl.props)
  pageCtl.visible = false
}
const newLine = () => {
  pageCtl.props.conditions.push(
    {
      value: '',
      action: ''
    }
  )
}
const delLine = (index) => {
  pageCtl.props.conditions.splice(index, 1)
}

const _tips = computed(() => {
  return [
    '- 本节点可以根据数据动态决定执行步骤',
    '- 如果有Range判断需求, 建议在SQL中提前判断, 因为Switch Case只可以做值匹配',
    '- 所有的值在对比之前会先转化为字符串, 然后再对比字符串',
    '- 如果有多个条件满足, 优先选择第一个符合条件的节点',
    '- 对列表变量进行操作时, 将默认取第一行记录进行判断'
  ].join('<br>')
})

defineExpose({
  showWin
})
</script>

<script lang="ts">
export default {
  name: 'SwitchCaseComponent'
}
</script>

<style lang="scss">
#SwitchCaseComponent {
}
</style>
