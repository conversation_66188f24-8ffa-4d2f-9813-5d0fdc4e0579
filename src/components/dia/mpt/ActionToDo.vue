<template>
  <div class="left-sidebar" id="ActionToDo">
    <div class="widget">
      <div class="widget-body">
        <el-row class="search-box">
          <el-col :span="5">
            <scp-cascader
                v-model="pageCtl.conditions.filterList"
                :loading="pageCtl.loading.filter"
                :options="pageCtl.filterOpts"/>
          </el-col>
          <el-col :span="5">
            <el-date-picker
                style="width: calc(100% - 35px)"
                size="small"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="2">
            <scp-search ref="searchRef" :click-native="search" :data-exclude="['dateRange', 'report3DateRange']"
                        :data="pageCtl.conditions"/>
            &nbsp;
            <el-tooltip :show-after="1000" effect="light" placement="bottom" content="Download All Subjects">
              <el-button @click="downloadAllSubjects" :loading="pageCtl.loading.download">
                <font-awesome-icon icon="fa-download"/>
              </el-button>
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="part1">
            <div class="subscript-container"
                 v-loading="pageCtl.loading.report1 || pageCtl.loading.filter">
              <scp-subscript id="ATDC"/>
              <div class="subscript-title">Action To Do</div>
              <div class="tree-table-box">
                <el-table style="border:0;height: 100%" :data="pageCtl.report1Data" height="var(--scp-input-width)" row-key="id"
                          border lazy :load="load2" @cell-contextmenu="onCellRightClick"
                          :tree-props="{children: 'children', hasChildren: 'hasChildren'}" class="tree-table">
                  <el-table-column prop="CATEGORY1" :label="pageCtl.conditions.category1Value" min-width="100px"/>
                  <el-table-column prop="CATEGORY2" :label="pageCtl.conditions.category2Value">
                    <template #header>
                      <el-select v-model="pageCtl.conditions.category2Value" size="small"
                                 style="border:0 !important;width:100%" filterable
                                 class="hide-input-border">
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </template>
                    <template v-slot="scope">
                      <el-popover
                          v-if="isSubjectCol(scope.column)"
                          :show-after="200"
                          placement="bottom"
                          popper-class="atd-popover"
                          trigger="hover">
                        <el-descriptions :column="1" size="small" border class="subject-details">
                          <el-descriptions-item label="Category">{{ scope.row.parent }}</el-descriptions-item>
                          <el-descriptions-item label="Subject">{{ scope.row.CATEGORY2 }}</el-descriptions-item>
                          <el-descriptions-item label="Description">
                            <div v-html="scope.row.DESCRIPTION"></div>
                          </el-descriptions-item>
                          <el-descriptions-item label="Proposal Action">
                            <div v-html="scope.row.PROPOSAL_ACTION"></div>
                          </el-descriptions-item>
                          <el-descriptions-item label="Kpi Impacted">{{ scope.row.KPI_IMPACTED }}</el-descriptions-item>
                          <el-descriptions-item label="Classification">{{ scope.row.CLASSIFICATION }}</el-descriptions-item>
                          <el-descriptions-item label="Create By">{{ scope.row.SUBJECT_OWNER_NAME }} ({{ scope.row.SUBJECT_OWNER_SESA }})</el-descriptions-item>
                          <el-descriptions-item label="Error">
                            <span v-if="scope.row.ERROR_FROM_OTHERS_LINES">
                              {{ $thousandBitSeparator(scope.row.ERROR_FROM_MPT_LINES, 0) }} / {{ $thousandBitSeparator(scope.row.ERROR_FROM_OTHERS_LINES, 0) }}
                            </span>
                            <span v-if="!scope.row.ERROR_FROM_OTHERS_LINES">
                              {{ $thousandBitSeparator(scope.row.ERROR_FROM_MPT_LINES, 0) }}
                            </span>
                          </el-descriptions-item>
                          <el-descriptions-item label="Feedback">{{ $thousandBitSeparator(scope.row.FEEDBACK_LINES, 0) }}</el-descriptions-item>
                          <el-descriptions-item label="Forward">{{ $thousandBitSeparator(scope.row.FORWARD_LINES, 0) }}</el-descriptions-item>
                          <el-descriptions-item label="Escalation">{{ $thousandBitSeparator(scope.row.ESCALATION_LINES, 0) }}</el-descriptions-item>
                          <el-descriptions-item label="Exclusion">{{ $thousandBitSeparator(scope.row.EXCLUSION_LINES, 0) }}</el-descriptions-item>
                          <el-descriptions-item label="Workflow Id">{{ scope.row.WORKFLOW_ID }}</el-descriptions-item>
                        </el-descriptions>
                        <template #reference>
                          <div class="category2-div">{{ scope.row.CATEGORY2 }}</div>
                        </template>
                      </el-popover>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ERROR_FROM_MPT_LINES" label="Error" class-name="atd-first-priority">
                    <template v-slot="scope">
                      <el-tooltip :show-after="1000" effect="light" placement="bottom">
                        <template #content>
                          - Error from rules: {{ $thousandBitSeparator(scope.row.ERROR_FROM_MPT_LINES, 0) }} <br>
                          - Error from others: {{ $thousandBitSeparator(scope.row.ERROR_FROM_OTHERS_LINES, 0) }} <br>
                          - Total: {{ $thousandBitSeparator(scope.row.ERROR_FROM_MPT_LINES + scope.row.ERROR_FROM_OTHERS_LINES, 0) }}
                        </template>
                        <div v-contextmenu:report1Contextmenu @contextmenu.prevent="rightClick"
                             :data-value="(scope.row.CATEGORY1 || scope.row.parent) + ',' + (scope.row.CATEGORY2 || '')">
                          {{!!scope.row.ERROR_FROM_OTHERS_LINES ? $thousandBitSeparator(scope.row.ERROR_FROM_MPT_LINES, 0) + ' / ' + $thousandBitSeparator(scope.row.ERROR_FROM_OTHERS_LINES, 0)
                                : $thousandBitSeparator(scope.row.ERROR_FROM_MPT_LINES, 0)}}
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ESCALATION_LINES" label="Escalation" class-name="atd-first-priority">
                    <template v-slot="scope">
                      <div v-contextmenu:report1Contextmenu @contextmenu.prevent="rightClick"
                           :data-value="(scope.row.CATEGORY1 || scope.row.parent) + ',' + (scope.row.CATEGORY2 || '')">
                        {{ $thousandBitSeparator(scope.row.ESCALATION_LINES, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="FEEDBACK_LINES" label="Feedback" class-name="atd-second-priority">
                    <template v-slot="scope">
                      <div v-contextmenu:report1Contextmenu @contextmenu.prevent="rightClick"
                           :data-value="(scope.row.CATEGORY1 || scope.row.parent) + ',' + (scope.row.CATEGORY2 || '')">
                        {{ $thousandBitSeparator(scope.row.FEEDBACK_LINES, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="FORWARD_LINES" label="Forward" class-name="atd-second-priority">
                    <template v-slot="scope">
                      <div v-contextmenu:report1Contextmenu @contextmenu.prevent="rightClick"
                           :data-value="(scope.row.CATEGORY1 || scope.row.parent) + ',' + (scope.row.CATEGORY2 || '')">
                        {{ $thousandBitSeparator(scope.row.FORWARD_LINES, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="EXCLUSION_LINES" label="Exclusion" class-name="atd-second-priority">
                    <template v-slot="scope">
                      <div v-contextmenu:report1Contextmenu @contextmenu.prevent="rightClick"
                           :data-value="(scope.row.CATEGORY1 || scope.row.parent) + ',' + (scope.row.CATEGORY2 || '')">
                        {{ $thousandBitSeparator(scope.row.EXCLUSION_LINES, 0) }}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" class="part4">
            <div class="subscript-container subscript-container-left"
                 v-loading="pageCtl.loading.report4 || pageCtl.loading.filter">
              <scp-subscript id="ATDP"/>
              <el-row>
                <el-col :span="12" class="report-sub-title-right" :style="{ transform: 'translateX(-50px) translateY(3px)', right: '20px' }">
                  <scp-cascader
                      style="width: 100%"
                      v-model="pageCtl.conditions.report4FilterList"
                      :loading="pageCtl.loading.report4Filter"
                      :options="pageCtl.report4FilterOpts"/>
                </el-col>
              </el-row>
              <chart ref="report4ChartRef" :height="350" :option="_report4Opt"/>
            </div>
          </el-col>
          <el-col :span="14" v-loading="pageCtl.loading.report2">
            <div class="subscript-container subscript-container-right" style="height: 350px">
              <scp-subscript id="ATDT" ref="report2SubRef"/>
              <div class="front">
                <chart v-contextmenu:report2ContextmenuRef :height="350" :option="_report2Opt"/>
              </div>
              <div class="back"><!-- 高度已调整 -->
                <div class="box">
                  <div class="box-title">Treemap Level Settings</div>
                  <el-row>
                    <el-col :span="6">Level1</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level1" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level2</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level2" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :value="item"
                            :label="item"/>
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level3</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level3" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :label="item"
                            :value="item"
                            :key="item"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level4</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level4" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :value="item"
                            :label="item"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="6">Level5</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.level5" placeholder="Select..." filterable>
                        <el-option
                            v-for="item in _pivotColumns"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                      </el-select>
                    </el-col>
                    <div class="box-title">&nbsp;</div>
                    <el-col :span="6">Display level</el-col>
                    <el-col :span="18">
                      <el-select v-model="pageCtl.conditions.leafDepth" placeholder="Select...">
                        <el-option
                            v-for="item in [1, 2]"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                      </el-select>
                    </el-col>
                  </el-row>
                </div>
                <div class="box">
                  <div class="box-title">Tooltips</div>
                  <el-checkbox-group v-model="pageCtl.conditions.report2Tooltips"
                                     style="height: calc(100% - 70px); overflow: auto">
                    <el-checkbox
                        v-for="item in pageCtl.report2TooltipsOpts"
                        :key="item"
                        :label="item"
                        :value="item"/>
                  </el-checkbox-group>
                </div>
                <div class="clearfix"/>
                <div style="text-align: right; margin-right: 5px">
                  <el-button
                      @click="report2SubRef.toggleView()">
                    Back
                  </el-button>
                  <el-button
                      type="primary"
                      @click="report2SubRef.toggleView(); searchReport2()">
                    Search
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="part3" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="ATDG" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report3ViewType" size="small" filterable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.report3DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}">
                  </el-date-picker>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ShowType" size="small">
                    <el-option label="View by value" value="VIEW_BY_VALUE"/>
                    <el-option label="View by percentage" value="VIEW_BY_PERCENT"/>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <el-button size="small" @click="searchReport3">
                    <font-awesome-icon icon="search"/>
                  </el-button>
                </el-col>
              </el-row>
              <chart ref="report3ChartRef" :height="350" :option="_report3Opt"/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <v-contextmenu ref="report1Contextmenu">
      <v-contextmenu-item @click="viewReport1Details" v-show="pageCtl.conditions.viewCategory1 !== ''">
        View {{ pageCtl.conditions.report1ViewType }} Details
      </v-contextmenu-item>
    </v-contextmenu>

    <!-- report2 contextmenu-->
    <v-contextmenu ref="report2ContextmenuRef">
      <v-contextmenu-item @click="viewParent" v-show="pageCtl.selectedParentLevel !== ''">
        View {{ pageCtl.selectedParentLevel }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="viewCurrent">
        View {{ pageCtl.selectedCurrentLevel }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { ViewDetailConfig } from '@/assets/js/function'

const $downloadFile: any = inject('$downloadFile')
const $viewDetails: any = inject('$viewDetails')
const $dateFormatter: any = inject('$dateFormatter')
const $randomString: any = inject('$randomString')
const $toFixed: any = inject('$toFixed')
const $shortenNumber: any = inject('$shortenNumber')
const $px2Rem: any = inject('$px2Rem')
const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')
const $setDefaultMaterialOwner: any = inject('$setDefaultMaterialOwner')
const $axios: any = inject('$axios')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')

const report3ChartRef = ref()
const report3SubRef = ref()
const searchRef = ref()
const report2SubRef = ref()
const report4ChartRef = ref()

const pageCtl = reactive({
  loading: {
    filter: false,
    report4Filter: false,
    report1: false,
    report2: false,
    report3: false,
    report4: false,
    download: false
  } as any,
  filterOpts: [] as any,
  report4FilterOpts: [],
  selectedParentLevel: '',
  selectedCurrentLevel: '',
  report1Data: [] as any,
  report2Data: [] as any,
  report3Data: [] as any,
  report4Data: [] as any,
  report3ViewTypeOpts: ['CATEGORY', 'CLASSIFICATION', 'KPI_IMPACTED', 'ORGANIZATION', 'PRIORITY', 'SESA_CODE', 'SUBJECT', 'SUB_CATEGORY', 'USER_NAME'],
  report2TooltipsOpts: [],
  conditions: {
    level1: 'CLASSIFICATION',
    level2: 'KPI_IMPACTED',
    level3: 'OWNER_NAME',
    level4: 'SUBJECT',
    level5: 'SUBJECT',
    leafDepth: 1,
    report1ViewType: '',
    report2Tooltips: [],
    selectedTreePath: '',
    category1Value: 'Category',
    category2Value: 'Subject',
    expandCategory1: '',
    viewCategory1: '',
    viewCategory2: '',
    report3ViewType: 'CATEGORY',
    report3TaskType: 'ERRORS',
    report3SeriesType: 'line',
    dateRange: [] as any,
    report3DateRange: [] as any,
    report3ShowType: 'VIEW_BY_VALUE',
    report3SelectedValue: '',
    report3SelectedSeriesName: '',
    report4DetailsType: '' as any,
    filterList: [] as any,
    report4FilterList: [] as any
  }
})

onMounted(() => {
  initFilter()
  report3ChartRef.value.chart().on('dblclick', (obj) => {
    pageCtl.conditions.report3SelectedValue = obj.name
    pageCtl.conditions.report3SelectedSeriesName = obj.seriesName

    $viewDetails({
      url: '/diagnosis/my_planning_today/action_to_do/query_report3_details',
      durl: '/diagnosis/my_planning_today/action_to_do/download_report3_details',
      params: pageCtl.conditions,
      title: 'Action To Do Details [' + [obj.name].join(', ') + '  ' + [obj.seriesName].join(', ') + ']'
    } as ViewDetailConfig)
  })
  report3ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report3SeriesType = obj.currentType
    }
  })
  report4ChartRef.value.chart().on('dblclick', (params) => {
    pageCtl.conditions.report4DetailsType = params.name

    $viewDetails({
      url: '/diagnosis/my_planning_today/action_to_do/query_report4_details',
      durl: '/diagnosis/my_planning_today/action_to_do/download_report4_details',
      params: pageCtl.conditions,
      title: 'View Details [' + (pageCtl.conditions.report4DetailsType ? pageCtl.conditions.report4DetailsType : 'Total') + ']'
    } as ViewDetailConfig)
  })
})

const search = () => {
  pageCtl.conditions.selectedTreePath = ''
  searchReport1()
  searchReport2()
  searchReport3()
  searchReport4()
}

const viewParent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedParentLevel
  searchReport3()
}

const viewCurrent = () => {
  pageCtl.conditions.selectedTreePath = pageCtl.selectedCurrentLevel
  searchReport3()
}

const initFilter = () => {
  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 7 * 9)
  pageCtl.conditions.dateRange = [
    $dateFormatter(end, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/action_to_do/query_filters'
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.report4FilterOpts = body.report4cascader
    $setDefaultMaterialOwner(pageCtl.filterOpts, pageCtl.conditions.filterList, 'OWNER_SESA')
    searchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const onCellRightClick = (row, column) => {
  pageCtl.conditions.report1ViewType = column.label
}

const rightClick = (e) => {
  const cs = e.target.dataset.value.split(',')
  if (cs[0] !== 'Total') {
    pageCtl.conditions.viewCategory1 = cs[0]
    pageCtl.conditions.viewCategory2 = cs[1]
  } else {
    pageCtl.conditions.viewCategory1 = ''
    pageCtl.conditions.viewCategory2 = ''
  }
}

const isSubjectCol = (col) => {
  const label = col?.label ?? ''
  return label.toUpperCase() === 'SUBJECT'
}

const searchReport1 = () => {
  pageCtl.loading.report1 = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/action_to_do/query_report1',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report1Data = parseReport1Data(body, 1)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report1 = false
  })
}

const searchReport2 = () => {
  pageCtl.loading.report2 = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/action_to_do/query_report2',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report2Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report2 = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/action_to_do/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const searchReport4 = () => {
  pageCtl.loading.report4 = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/action_to_do/query_report4',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report4Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report4 = false
  })
}

const parseReport1Data = (raw, level) => {
  const result = [] as Array<any>
  const total = {
    id: 'total_' + $randomString(8),
    CATEGORY1: level === 1 ? 'Total' : '',
    ERROR_FROM_MPT_LINES: 0,
    ERROR_FROM_OTHERS_LINES: 0,
    FEEDBACK_LINES: 0,
    FORWARD_LINES: 0,
    ESCALATION_LINES: 0,
    EXCLUSION_LINES: 0
  }

  for (let i = 0; i < raw.length; i++) {
    const c = raw[i]
    c.id = 'id_' + $randomString(8)
    c.hasChildren = level === 1
    if (level === 2) {
      c.parent = pageCtl.conditions.expandCategory1
    }

    total.ERROR_FROM_MPT_LINES += (c.ERROR_FROM_MPT_LINES || 0)
    total.ERROR_FROM_OTHERS_LINES += (c.ERROR_FROM_OTHERS_LINES || 0)
    total.FEEDBACK_LINES += (c.FEEDBACK_LINES || 0)
    total.FORWARD_LINES += (c.FORWARD_LINES || 0)
    total.ESCALATION_LINES += (c.ESCALATION_LINES || 0)
    total.EXCLUSION_LINES += (c.EXCLUSION_LINES || 0)

    result.push(c)
  }

  if (level === 1) {
    result.push(total)
  }
  return result
}

const load2 = (tree, treeNode, resolve) => {
  pageCtl.conditions.expandCategory1 = tree.CATEGORY1

  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/action_to_do/query_report1_sub',
    data: pageCtl.conditions
  }).then((body) => {
    resolve(parseReport1Data(body, 2))
  }).catch((error) => {
    console.log(error)
  })
}

const viewReport1Details = () => {
  $viewDetails({
    url: '/diagnosis/my_planning_today/action_to_do/query_report1_details',
    durl: () => {
      return '/diagnosis/my_planning_today/action_to_do/download_report1_details?t=' + encodeURIComponent(pageCtl.conditions.viewCategory1)
    },
    params: pageCtl.conditions,
    title: '[Action To Do ' + pageCtl.conditions.report1ViewType + ' Details] ' + pageCtl.conditions.viewCategory1 + (pageCtl.conditions.viewCategory2 ? ' - ' + pageCtl.conditions.viewCategory2 : '')
  } as ViewDetailConfig)
}

const downloadAllSubjects = () => {
  pageCtl.loading.download = true
  $downloadFile('/diagnosis/my_planning_today/action_to_do/download_all_subjects', pageCtl.conditions, () => {
    pageCtl.loading.download = false
  })
}

watch(() => [pageCtl.conditions.category1Value, pageCtl.conditions.category2Value], () => {
  searchReport1()
})

watch(() => [pageCtl.conditions.report4FilterList], () => {
  searchReport4()
})

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})

const _report2Opt = computed(() => {
  const rootName = 'Action To Do'
  return {
    title: {
      text: 'Action To Do Treemap',
      textStyle: {
        fontSize: 14
      }
    },
    toolbox: $toolbox({
      opts: []
    }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any

        tip.push('<div style="width:11.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(params.value))
        tip.push('</span>')
        tip.push('</div>')

        const treePathInfo = params.treePathInfo || []
        let levels = [] as any
        if (treePathInfo.length > 1) {
          const currentNode = treePathInfo[treePathInfo.length - 1]
          levels.push(currentNode.name)

          for (let i = treePathInfo.length - 2; i > -1; i--) {
            const parentNode = treePathInfo[i]
            levels.push(parentNode.name)

            tip.push('<div>')
            tip.push('Share [' + (parentNode.name === rootName ? 'Total' : parentNode.name) + ']')
            tip.push('<span style="float: right">')
            tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
            tip.push('</span>')
            tip.push('</div>')
          }
        }
        levels = levels.slice(0, levels.length - 1)
        levels = levels.reverse()
        pageCtl.selectedCurrentLevel = levels.join(' > ')
        if (levels.length > 0) {
          levels = levels.slice(0, levels.length - 1)
          pageCtl.selectedParentLevel = levels.join(' > ')
        }

        const tooltips = params.data.tips
        let first = true
        for (const k in tooltips) {
          if (!tooltips.hasOwnProperty(k)) {
            continue
          }
          if (first) {
            tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
            first = false
          } else {
            tip.push('<div>')
          }

          tip.push(k.toUpperCase())
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(tooltips[k]))
          tip.push('</span>')
          tip.push('</div>')
        }
        tip.push('</div>')
        return tip.join('')
      }
    },
    series: [{
      name: rootName,
      type: 'treemap',
      silent: false,
      roam: false,
      leafDepth: pageCtl.conditions.leafDepth,
      ...$grid({ type: 'treemap' }),
      data: pageCtl.report2Data,
      levels: [
        {
          itemStyle: {
            borderWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: pageCtl.conditions.leafDepth === 1 ? 0 : 1,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        },
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 0
          }
        }
      ]
    }]
  }
})

const _report3Opt = computed(() => {
  const series = [] as Array<any>
  const legend = [] as Array<any>

  let yAxisData: any = pageCtl.report3Data

  if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report3Data.xAxis

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report3Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report3Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report3SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }

  return {
    title: {
      text: 'Action To Do Trend Graph ' +
          (pageCtl.conditions.selectedTreePath ? 'by [' + pageCtl.conditions.selectedTreePath + '] ' : ''),
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as Array<any>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params.sort((e2, e1) => e1.value - e2.value)
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:11rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:11rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report3SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const _report4Opt = computed(() => {
  const colorSettings = {
    'No Owner': '#d25924',
    'Abnormal Owner': '#c6b601',
    'Normal Owner': '#539017'
  }
  const colors = [] as any
  const data = pageCtl.report4Data
  for (let i = 0; i < data.length; i++) {
    colors.push(colorSettings[data[i].name])
    data[i].label = { color: colorSettings[data[i].name] }
  }
  return {
    color: colors,
    title: {
      text: 'Action To Do Material Owner Analysis'
    },
    toolbox: $toolbox({
      opts: [],
      view: {
        url: '/diagnosis/my_planning_today/action_to_do/query_report4_details',
        durl: '/diagnosis/my_planning_today/action_to_do/download_report4_details',
        params: () => {
          pageCtl.conditions.report4DetailsType = ''
          return pageCtl.conditions
        }
      }
    }),
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const marker = params.marker
        const tip = [] as any
        tip.push('<div style="width:9.5rem;">')
        tip.push('<div>')
        tip.push(marker)
        tip.push(params.name)
        tip.push('<span style="float: right">')
        tip.push($thousandBitSeparator(params.value)) // 因为Plant在考核MPT时需要精确到个位数, 故不用单位省略, 仅做千分位分割
        tip.push('(')
        tip.push(params.percent)
        tip.push('%)')
        tip.push('</span>')
        tip.push('</div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ type: 'pie' }),
    series: [
      {
        type: 'pie',
        radius: '60%',
        center: ['30%', '55%'],
        data
      }
    ]
  }
})
</script>

<style lang="scss">

#ActionToDo {
  .el-descriptions--small .el-descriptions__body .el-descriptions__table .el-descriptions__cell {
    font-size: 0.5rem !important;
  }

  .subject-details {
    .el-descriptions-item__cell {
      vertical-align: top !important;
    }
  }

  .part1 {
    border-color: transparent;

    .el-table {
      --el-table-border-color: transparent !important;
    }

    .tree-table-box {
      height: 15rem;
      width: calc(100% - 5px);
      overflow: auto;
    }

    .hide-input-border {
      input {
        border: 0 !important;
      }

      .el-input__inner {
        text-align: center;
      }
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: var(--scp-bg-color-success) !important;
      color: #fff !important;
    }

    .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
      border-right: none;
    }

    .el-table td, .el-table th.is-leaf {
      border-bottom: none;
    }

    .el-table, .el-table__expanded-cell {
      background-color: transparent;
    }

    .hide-input-border input {
      border: 0 !important;
    }

    .el-select__wrapper {
      background-color: initial;
      box-shadow: 0 0 0 0;
    }

    .category1-div {
      width: calc(100% - 23px);
      height: 100%;
      float: right;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .category2-div {
      width: 100%;
      height: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      cursor: pointer;
    }

    .tree-table {
      table {
        tr:last-child {
          font-weight: bold;

          td:first-child {
            padding-left: 22px !important
          }
        }

        tr {
          background-color: transparent;

          th {
            padding: 1px 4px !important;
            border-bottom: 0 !important;
            border-right: solid 5px var(--scp-bg-color) !important;
            background-color: var(--scp-bg-color-fill);

            &:nth-last-child(1) {
              border-right: none !important;
            }

            .cell {
              font-size: 0.5rem !important;
              font-weight: normal;
              text-align: center;
              line-height: 21px !important;
              white-space: nowrap;
            }
          }

          td {
            padding: 0 !important;
            background-color: transparent;

            .cell {
              padding-left: 0 !important;
              font-size: 0.5rem !important;
              line-height: 21px !important;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .part4 {
    .el-cascader__tags .el-tag:not(.is-hit) {
      min-width: 25%;
    }

    .el-cascader__search-input {
      min-width: 20%;
    }
  }

  .part3 {
    .selectedLeft {
      display: flex;
      justify-content: left;
      align-items: center;
      margin: 0 0 10px 10px;
      height: 100%;
    }
  }

  .el-table__placeholder {
    width: auto;
  }

  .overflow {
    overflow: auto
  }

  .el-tabs__header {
    margin: 0 0 8px;
  }

  .el-tabs__nav {
    height: 30px;
  }

  .el-tabs__item {
    line-height: 25px;
  }

  .el-carousel__container {
    position: static;
    height: auto
  }

  .el-table__header, .el-table__body {
    .atd-first-priority {
      text-align: right !important;
    }
    .atd-second-priority {
      color: var(--scp-text-color-secondary) !important;
      text-align: right !important;
    }
  }
}

.atd-popover {
  * {
    font-size: 0.45rem !important;
  }

  padding: 6px !important;

  .is-bordered-label {
    width: 120px !important;
  }

  .is-bordered-content {
    width: 400px !important;
  }

  .el-descriptions__cell {
    line-height: 16px !important;
  }
}

</style>
