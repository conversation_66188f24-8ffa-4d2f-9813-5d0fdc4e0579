<template>
  <div class="left-sidebar" id="mptWorkspace">
    <div class="widget">
      <div class="widget-body">
        <el-row>
          <el-col :span="8" style="padding-right: 5px">
            <div class="widget">
              <div class="widget-header">
                <div class="title">
                  <font-awesome-icon icon="podcast"/>
                  Latest Notice
                </div>
              </div>
              <div class="widget-body" style="height: 200px;overflow: auto">
                <ul v-infinite-scroll="searchLatestNews" class="list-ul" :infinite-scroll-disabled="pageCtl.latestNewsDisable">
                  <li v-for="(item, index) in pageCtl.latestNews" :key="index" class="list-item" @click="viewTicket(item.PARENT_ID)">
                    <el-row>
                      <el-col :span="1">{{ index + 1 }}.</el-col>
                      <el-col :span="15" style="font-size: 0.5rem;">
                        <span v-if="item.IS_NEW==='Y'"><font-awesome-icon icon="sun" style="color: var(--scp-text-color-highlight)"/>&nbsp;</span>
                        <el-tooltip class="item" effect="light" placement="right" :show-after="500" :content="item.CONTENT">
                          {{ item.CONTENT }}
                        </el-tooltip>
                      </el-col>
                      <el-col :span="3" style="font-size: 85%;">{{ item.USER_NAME }}</el-col>
                      <el-col :span="5" style="font-size: 85%;text-align: right">{{ item.CREATE_DATE }}</el-col>
                    </el-row>
                  </li>
                </ul>
              </div>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="widget">
              <div class="widget-header">
                <div class="title">
                  <font-awesome-icon icon="angle-double-right"/>
                  Quick Access
                </div>
              </div>
              <div class="widget-body" style="height: 200px;overflow: auto">
                <el-card class="box-card new-ticket" shadow="hover" @click="newTicket">
                  <font-awesome-icon icon="edit"/> &nbsp;
                  New Ticket
                </el-card>

                <el-card class="box-card" shadow="hover" v-for="item in pageCtl.quickAccess.approval" :key="item.ID" @click="viewTicket(item.ID)">
                  <div class="title">
                    <font-awesome-icon icon="check"/> &nbsp;
                    An exclusion need your approval.
                  </div>
                  <div class="sub-title">{{ item.USER_NAME }} - {{ item.TITLE }}</div>
                </el-card>

                <el-card class="box-card" shadow="hover" v-for="item in pageCtl.quickAccess.forward" :key="item.ID" @click="viewTicket(item.ID)">
                  <div class="title">
                    <font-awesome-icon icon="share"/> &nbsp;
                    {{ item.USER_NAME }} forwarded a ticket to you
                  </div>
                  <div class="sub-title">{{ item.TITLE }}</div>
                </el-card>

                <el-card class="box-card" shadow="hover" v-for="item in pageCtl.quickAccess.escalation" :key="item.ID" @click="viewTicket(item.ID)">
                  <div class="title">
                    <font-awesome-icon icon="star"/> &nbsp;
                    An escalation need your confirmation.
                  </div>
                  <div class="sub-title">{{ item.USER_NAME }} - {{ item.TITLE }}</div>
                </el-card>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" :style="{height:Math.max(pageCtl.height - 250, 450) + 'px'}" style="margin-top: 10px">
            <mpt-workspace-view-tickets ref="viewTicketRef" :after-approve="search" :after-close="search"/>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
  <mpt-workspace-new-tickets ref="newTicketRef" :after-save="search"/>

</template>

<script lang="ts" setup>
import { inject, onBeforeMount, onMounted, reactive, ref } from 'vue'
import MptWorkspaceNewTickets from '@/components/dia/mpt/MPTWorkspace/NewTickets.vue'
import MptWorkspaceViewTickets from '@/components/dia/mpt/MPTWorkspace/ViewTickets.vue'
import { useRoute } from 'vue-router'

const $axios: any = inject('$axios')
const newTicketRef = ref()
const viewTicketRef = ref()

interface QuickAccessItem {
  ID: String,
  USER_NAME: String,
  TITLE: String
}

interface NewsItem {
  PARENT_ID: String,
  IS_NEW: String,
  CONTENT: String,
  USER_NAME: String,
  CREATE_DATE: String
}

const pageCtl = reactive({
  visible: {
    newTicket: false
  },
  height: 0,
  latestNewsDisable: false,
  latestNews: [] as Array<NewsItem>,
  quickAccess: {
    approval: [] as Array<QuickAccessItem>,
    forward: [] as Array<QuickAccessItem>,
    escalation: [] as Array<QuickAccessItem>
  }
})

const search = () => {
  viewTicketRef.value.searchReport1()
  searchQuickAccess()

  pageCtl.latestNews = []
  searchLatestNews()
}

const viewTicket = (id) => {
  viewTicketRef.value.viewClick({ ID: id })
}

const searchQuickAccess = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/query_quick_access'
  }).then((body) => {
    pageCtl.quickAccess = body
  }).catch((error) => {
    console.log(error)
  })
}

const searchLatestNews = () => {
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/query_latest_news',
    data: {
      offset: pageCtl.latestNews.length
    }
  }).then((body) => {
    if (body.length === 0) {
      pageCtl.latestNewsDisable = true
    } else {
      pageCtl.latestNews.push(...body)
    }
  }).catch((error) => {
    console.log(error)
  })
}

const newTicket = () => {
  newTicketRef.value.showNewTicketWin()
}

onMounted(() => {
  searchQuickAccess()
})

onBeforeMount(() => {
  pageCtl.height = document.documentElement.clientHeight - 100
})

</script>

<style lang="scss">
#mptWorkspace {
  .new-ticket {
    line-height: 3rem;
    background-color: var(--scp-text-color-highlight) !important;
    color: #fff;

    .el-card__body {
      padding: 0 0.8333rem !important;
    }
  }

  .el-card__body {
    --el-card-padding: 0.8333rem 0.8333rem 0 0.8333rem;
  }

  .box-card {
    display: inline-block;
    margin: 0 10px 0 0;
    font-size: 0.5rem;
    cursor: pointer;
    height: 3rem;
    padding: unset !important;

    .sub-title {
      flex: 1 1 auto;
      flex-basis: 0;
      font-size: 0.45rem !important;
      color: var(--scp-text-color-secondary) !important;
    }
  }

  .list-ul {
    .list-item {
      border-bottom: 1px solid var(--scp-border-color-lighter);
      cursor: pointer;
      height: 1.5rem;
      line-height: 1.5rem;

      .el-col {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .list-item:hover {
      background-color: var(--scp-bg-color-fill);
    }
  }
}
</style>
