<template>
  <scp-draggable-resizable v-model="newTicketCtl.visible" h="650" w="1024" title="New Ticket"
                           :save="saveNewTicket" save-text="Submit Ticket" :save-loading="newTicketCtl.loading.save">
    <el-form :model="newTicketCtl.form" label-width="120px" style="margin-top: 10px" class="new-ticket-form" ref="formRef">
      <el-form-item label="Title" required prop="title">
        <el-input v-model="newTicketCtl.form.title" placeholder="Provide a summary of this ticket so it can be easily found later"/>
      </el-form-item>
      <el-form-item label="Category" required prop="category">
        <el-select v-model="newTicketCtl.form.category"
                   :placeholder="newTicketCtl.loading.init? 'loading...' : 'Select a category'"
                   style="width: 100%;" @change="querySubjectOpts">
          <el-option v-for="item in newTicketCtl.categoryOpts"
                     :key="item"
                     :label="item"
                     :value="item"/>
        </el-select>
      </el-form-item>
      <el-form-item label="Subject" required prop="subject">
        <el-select v-model="newTicketCtl.form.subject"
                   :placeholder="newTicketCtl.loading.subject? 'loading...' : 'Select one or more subjects you want this ticket take effect to'"
                   multiple collapse-tags filterable clearable style="width: 100%;">
          <el-option v-for="item in newTicketCtl.subjectOpts"
                     :key="item"
                     :label="item"
                     :value="item"/>
        </el-select>
      </el-form-item>
      <el-form-item label="Action Type" required prop="action">
        <el-select v-model="newTicketCtl.form.action"
                   placeholder='Select the action you want to take'
                   style="width: 100%;" @change="resetActivePeriod">
          <el-option v-for="item in newTicketCtl.actionOpts"
                     :key="item"
                     :label="item"
                     :value="item"/>
        </el-select>
      </el-form-item>
      <el-form-item label="Active Period" required prop="dateRange">
        <el-row style="width: 100%">
          <el-col :span="12">
            <el-date-picker
                v-model="newTicketCtl.form.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :disabled-date="activePeriodControl">
            </el-date-picker>
          </el-col>
          <el-col :span="12">
            <el-tooltip class="item" effect="light" placement="right">
              <template #default>
                <font-awesome-icon icon="question"/>
              </template>
              <template #content>
                <div>
                  <p><b>时间约束</b></p>
                  <p>- <b>Feedback</b>: 最多只可以向后申请半年(182天)</p>
                  <p>- <b>Exclusion</b>: 不设限, 半年(&lt;=182天)以内审批人为Plant Approver, 超过半年(>182天)审批人变更为Central Approver</p>
                  <p>- <b>Forward, Escalation</b>: 不设限</p>
                  <p><b>字段含义: </b>当前申请的Ticket, 希望在哪个时间内生效.</p>
                  <p>- <b>Exclusion</b>: 在生效期间内, 填写在列表中的Material/SO/MO/PO将不再发送相关通知</p>
                  <p>- <b>Feedback</b>: 以正在处理的方式展示在报表中</p>
                </div>
              </template>
            </el-tooltip>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="newTicketCtl.cascader.label" :required="newTicketCtl.form.action !== ACTION_TYPE.feedback" prop="action">
        <scp-cascader v-model="newTicketCtl.form.collaborators" :options="_collaboratorsOpts" :placeholder-text="newTicketCtl.cascader.label"
                      :multiple=newTicketCtl.cascader.multiple style="width: 100%" :show-copy="false"/>
      </el-form-item>
      <el-form-item label="Brief Introduction" required prop="comments">
        <scp-ck-editor v-model="newTicketCtl.form.comments" placeholder="Briefly introduction for this ticket"
                       style="height: 220px;width: 750px"/>
      </el-form-item>
      <el-form-item label="Filter by" required>
        <el-select v-model="newTicketCtl.form.filterBy"
                   placeholder='Select a group to filter data'
                   @change="clearFormList"
                   style="width: 100%;">
          <el-option v-for="item in _filterByOpts"
                     :key="item"
                     :label="item"
                     :value="item"/>
        </el-select>
      </el-form-item>
      <el-form-item label="Details" required>
        <scp-table2
            :data="_newTicketListData"
            :context-menu-items="newTicketCtl.contextItem"
            :overwrite-context-menu="true"
            :after-change="(changes, hotInstance)=>afterNewTicketInputTableChanged(changes, hotInstance)"
            :after-remove-row="(index, amount, physicalRows, hotInstance)=>afterNewTicketInputTableChanged(1, hotInstance)"
            :max-height="400"
            :columns="_columns"/>
      </el-form-item>
      <div style="height: 200px"></div>
    </el-form>
  </scp-draggable-resizable>
</template>

<script lang="ts" setup>
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import { computed, inject, onBeforeMount, reactive, watch, ref, readonly } from 'vue'
import type { FormInstance, Action } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const $axios: any = inject('$axios')
const $dateFormatter: any = inject('$dateFormatter')
const $message: any = inject('$message')
const $toFixed: any = inject('$toFixed')

const formRef = ref<FormInstance>()

const ACTION_TYPE = readonly({
  feedback: '1. Feedback',
  exclusion: '2. Exclusion',
  forward: '3. Forward',
  escalation: '4. Escalation'
})

const newTicketCtl = reactive({
  visible: false,
  loading: {
    init: false,
    subject: false,
    save: false
  },
  cascader: {
    label: 'Collaborators',
    multiple: true
  },
  form: {
    filterBy: '',
    title: '',
    category: '',
    subject: [],
    lists: [],
    dateRange: [] as any,
    action: ACTION_TYPE.feedback,
    collaborators: [] as any,
    comments: ''
  },
  categoryOpts: [],
  subjectOpts: [],
  actionOpts: [ACTION_TYPE.feedback, ACTION_TYPE.exclusion, ACTION_TYPE.forward],
  collaboratorsOpts: [],
  centralApprovalOpts: [],
  plantApprovalOpts: [],
  n1Manager: [],
  contextItem: {
    row_above: {},
    row_below: {},
    split5: { name: '---------' },
    remove_row: {}
  },
  timeThreshold: 15724800000 // 最多只能往前选182天
})

// @ts-ignore
const props = withDefaults(defineProps<{
  afterSave?: Function
}>(), {
  afterSave: undefined
})

const clearFormList = () => {
  newTicketCtl.form.lists = []
}

const afterNewTicketInputTableChanged = (change, hotInstance) => {
  if (change) {
    newTicketCtl.form.lists = hotInstance.getSourceData()
  }
}

const _newTicketListData = computed(() => {
  if (newTicketCtl.form.lists.length === 0) {
    return [{}]
  } else {
    return [...newTicketCtl.form.lists.filter(e => {
      const keys = Object.keys(e)
      let r = false
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        r = r || !!e[key]
      }
      return r
    }), {}]
  }
})

const showNewTicketWin = () => {
  newTicketCtl.visible = true
}

const saveNewTicket = () => {
  if (newTicketCtl.form.title === '') {
    $message.error('Please input the title')
    return
  }
  if (newTicketCtl.form.subject.length === 0) {
    $message.error('Please select one or more subjects')
    return
  }
  if (newTicketCtl.form.category === '') {
    $message.error('Please select a category')
    return
  }
  if (newTicketCtl.form.comments === '') {
    $message.error('Please give some brief introduction')
    return
  }
  if (_newTicketListData.value.length <= 1) {
    $message.error('Please input the details.')
    return
  }
  const start = newTicketCtl.form.dateRange[0].split('/')
  const end = newTicketCtl.form.dateRange[1].split('/')
  const startDate = new Date(start[0], start[1], start[2])
  const endDate = new Date(end[0], end[1], end[2])

  const today = $dateFormatter(new Date(), 'yyyy/MM/dd')
  const n3month = $dateFormatter(new Date(new Date().getFullYear(), new Date().getMonth() + 3, new Date().getDate()), 'yyyy/MM/dd')

  const days = (endDate - startDate) / 86400000 + 1

  if (days < 7) {
    ElMessageBox.confirm(
      '<b>测到异常的Action Period</b><br>' +
        'Action Period代表当前 Ticket，您希望在哪个时间段内生效<br><br>' +
        '比如, 您希望在接下来三个月<br>' +
        '- 列表中的Material/SO/MO/PO将不再发送相关通知(Exclusion)</p>' +
        '- 或以正在处理的方式展示在报表中(Feedback)</p>' +
        '您可以填 <b>' + today + ' - ' + n3month + '</b><br><br>' +
        '目前，您的Action Period只有<b style="color: var(--scp-text-color-error)">' + $toFixed(days, 0) + '</b>天，这很可能是一个错误输入<br>' +
        '请选择您的操作: ',
      '警告',
      {
        distinguishCancelAndClose: true,
        confirmButtonText: '我需要再确认一下',
        cancelButtonText: '我确认填写信息无误, 确认提交',
        confirmButtonClass: 'confirm-btn',
        type: 'error',
        customClass: 'message-box-maxwith500',
        dangerouslyUseHTMLString: true
      }
    ).catch((action: Action) => {
      if (action === 'cancel') {
        saveNewTicketAction()
      }
    })
  } else {
    saveNewTicketAction()
  }
}

const resetActivePeriod = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  newTicketCtl.form.dateRange = [$dateFormatter(today, 'yyyy/MM/dd'), $dateFormatter(new Date(year, month, day + 30), 'yyyy/MM/dd')]
}

const activePeriodControl = (e) => {
  if (newTicketCtl.form.action === ACTION_TYPE.feedback) {
    const time = e.getTime() - new Date().getTime()
    return time > newTicketCtl.timeThreshold
  } else {
    return false
  }
}

const saveNewTicketAction = () => {
  newTicketCtl.loading.save = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/save_new_ticket',
    data: newTicketCtl.form
  }).then(() => {
    if (props.afterSave) {
      props.afterSave()
    }
    resetForm()
    newTicketCtl.visible = false
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    newTicketCtl.loading.save = false
  })
}

const resetForm = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  newTicketCtl.form = {
    filterBy: '',
    title: '',
    category: '',
    subject: [],
    lists: [],
    dateRange: [$dateFormatter(today, 'yyyy/MM/dd'), $dateFormatter(new Date(year, month, day + 30), 'yyyy/MM/dd')],
    action: ACTION_TYPE.feedback,
    collaborators: [] as any,
    comments: ''
  }
  formRef.value?.resetFields()
}

const initNewTicks = () => {
  newTicketCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/init_new_tickets'
  }).then((body) => {
    newTicketCtl.categoryOpts = body.categoryOpts
    newTicketCtl.plantApprovalOpts = body.plantApprovalOpts
    newTicketCtl.centralApprovalOpts = body.centralApprovalOpts
    newTicketCtl.collaboratorsOpts = body.collaboratorsOpts
    newTicketCtl.n1Manager = body.manager
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    newTicketCtl.loading.init = false
  })
}

const querySubjectOpts = () => {
  newTicketCtl.loading.subject = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/query_subject_opts',
    data: {
      category: newTicketCtl.form.category
    }
  }).then((body) => {
    newTicketCtl.subjectOpts = body
    newTicketCtl.form.subject = []
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    newTicketCtl.loading.subject = false
  })
}

const _filterByOpts = computed(() => {
  let result = []
  if (newTicketCtl.form.category === '1. Master Data (Mat)') {
    result = ['MATERIAL + PLANT_CODE']
  } else if (newTicketCtl.form.category === '2. Master Data (Vend)') {
    result = ['MATERIAL + PLANT_CODE + VENDOR', 'MATERIAL + PLANT_CODE']
  } else if (newTicketCtl.form.category === '3. Sales Order') {
    result = ['SALES_ORDER_NUMBER + SALES_ORDER_ITEM', 'SALES_ORDER_NUMBER', 'MATERIAL + PLANT_CODE']
  } else if (newTicketCtl.form.category === '4. Purchasing Order') {
    result = ['PURCH_ORDER_NUMBER + PURCH_ORDER_ITEM', 'PURCH_ORDER_NUMBER', 'MATERIAL + PLANT_CODE']
  } else if (newTicketCtl.form.category === '5. Manufacturing Order') {
    result = ['MO_NUMBER', 'MATERIAL + PLANT_CODE']
  } else if (newTicketCtl.form.category === '7. Inventory') {
    result = ['MATERIAL + PLANT_CODE', 'MATERIAL + PLANT_CODE + STORAGE_LOCATION + SPECIAL_ST + SCOPE$']
  }
  if (result.length > 0) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    newTicketCtl.form.filterBy = result[0]
  }
  return result
})

const _activePeriodGay = computed(() => {
  const startDate = new Date(newTicketCtl.form.dateRange[0])
  const endDate = new Date(newTicketCtl.form.dateRange[1])
  return endDate.getTime() - startDate.getTime()
})

const _collaboratorsOpts = computed(() => {
  if (newTicketCtl.form.action === ACTION_TYPE.feedback || newTicketCtl.form.action === ACTION_TYPE.forward) {
    return newTicketCtl.collaboratorsOpts
  } if (newTicketCtl.form.action === ACTION_TYPE.exclusion && _activePeriodGay.value > newTicketCtl.timeThreshold) {
    return newTicketCtl.centralApprovalOpts
  } else {
    return newTicketCtl.plantApprovalOpts
  }
})

watch(() => newTicketCtl.form.dateRange, (val) => {
  const startDate = new Date(val[0])
  const endDate = new Date(val[1])
  if (endDate.getTime() - startDate.getTime() > newTicketCtl.timeThreshold) {
    for (let i = 0; i < newTicketCtl.centralApprovalOpts.length; i++) {
      const v = newTicketCtl.centralApprovalOpts[i]
      if (v.children.length > 0) {
        newTicketCtl.form.collaborators = [[v.value, v.children[0].value]]
      }
    }
  }
})

const _columns = computed(() => {
  const s = newTicketCtl.form.filterBy.split('+')
  const column = []
  for (let i = 0; i < s.length; i++) {
    column.push({ data: s[i].trim() })
  }
  return column
})

watch(() => newTicketCtl.form.action, (val) => {
  switch (val) {
    case ACTION_TYPE.feedback: {
      newTicketCtl.cascader.label = 'Collaborators'
      newTicketCtl.cascader.multiple = true
      newTicketCtl.form.collaborators = []
      break
    }
    case ACTION_TYPE.exclusion: {
      newTicketCtl.cascader.label = 'Approver'
      newTicketCtl.cascader.multiple = false
      newTicketCtl.form.collaborators = newTicketCtl.n1Manager.length > 0 ? [[newTicketCtl.n1Manager[0], newTicketCtl.n1Manager[1]]] : []
      break
    }
    case ACTION_TYPE.forward: {
      newTicketCtl.cascader.label = 'Forward to'
      newTicketCtl.cascader.multiple = false
      newTicketCtl.form.collaborators = []
      break
    }
    case ACTION_TYPE.escalation: {
      newTicketCtl.cascader.label = 'Escalate to'
      newTicketCtl.cascader.multiple = false
      newTicketCtl.form.collaborators = newTicketCtl.n1Manager.length > 0 ? [[newTicketCtl.n1Manager[0], newTicketCtl.n1Manager[1]]] : []
      break
    }
    default:
      break
  }
})

onBeforeMount(() => {
  resetActivePeriod()
  initNewTicks()
})

defineExpose({
  showNewTicketWin
})
</script>

<script lang="ts">
export default {
  name: 'MptWorkspaceNewTickets'
}
</script>

<style lang="scss">
.message-box-maxwith500 {
  max-width: 500px;
}

.confirm-btn {
  color: #fff !important;
  background-color: var(--scp-text-color-highlight) !important;
  border-color: var(--scp-text-color-highlight) !important;
}

.new-ticket-form {
  .el-form-item__content {
    padding-right: 120px !important;
  }
}
</style>
