<template>
  <div id="ViewTickets">
    <el-tabs v-model="pageCtl.conditions.activeName" @tab-click="handleTabClick">
      <el-tab-pane label="My Request" name="MY_REQUEST" v-loading="pageCtl.loading.tableData">
        <el-row class="search-box">
          <el-col :span="5">
            <el-date-picker
                style="width: 90%"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <el-button @click="searchReport1">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <view-tickets-table
            :data="pageCtl.tableData"
            :viewClick="viewClick"/>
      </el-tab-pane>
      <el-tab-pane label="My Approval" name="MY_APPROVAL" v-loading="pageCtl.loading.tableData">
        <el-row class="search-box">
          <el-col :span="5">
            <el-date-picker
                style="width: 90%"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <el-button @click="searchReport1">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <view-tickets-table
            :data="pageCtl.tableData"
            :viewClick="viewClick"/>
      </el-tab-pane>
      <el-tab-pane label="Referred to Me" name="REFERRED_TO_ME" v-loading="pageCtl.loading.tableData">
        <el-row class="search-box">
          <el-col :span="5">
            <el-date-picker
                style="width: 90%"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <el-button @click="searchReport1">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <view-tickets-table
            :data="pageCtl.tableData"
            :viewClick="viewClick"/>
      </el-tab-pane>
      <el-tab-pane label="Finished" name="FINISHED" v-loading="pageCtl.loading.tableData">
        <el-row class="search-box">
          <el-col :span="5">
            <el-date-picker
                style="width: 90%"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <el-button @click="searchReport1">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <view-tickets-table
            :data="pageCtl.tableData"
            :viewClick="viewClick"/>
      </el-tab-pane>
      <el-tab-pane label="All Requests" name="ALL_REQUESTS" v-loading="pageCtl.loading.tableData">
        <el-row class="search-box">
          <el-col :span="5">
            <el-date-picker
                style="width: 90%"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="1">
            <el-button @click="searchReport1">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <view-tickets-table
            :data="pageCtl.tableData"
            :viewClick="viewClick"/>
      </el-tab-pane>
      <el-tab-pane label="Admin" name="ADMIN" v-loading="pageCtl.loading.tableData" v-if="pageCtl.isAdmin">
        <el-row class="search-box">
          <el-col :span="4">
            <el-date-picker
                style="width: 90%"
                v-model="pageCtl.conditions.dateRange"
                type="daterange"
                unlink-panels
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                range-separator="to"
                :clearable="false"
                start-placeholder="Start date"
                end-placeholder="End date"
                :picker-options="{}">
            </el-date-picker>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.owners" multiple collapse-tags clearable filterable
                       :placeholder="pageCtl.loading.init? 'loading...' : 'Request By / Action Owner'">
              <el-option v-for="item in pageCtl.ownerOpts"
                         :key="item.key"
                         :label="item.label"
                         :value="item.key"/>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="pageCtl.conditions.category" multiple collapse-tags clearable filterable
                       :placeholder="pageCtl.loading.init? 'loading...' : 'Categories'">
              <el-option v-for="item in pageCtl.categoryOpts"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input v-model="pageCtl.conditions.title" placeholder="Title / Subject" style="width: calc(100% - 15px)" clearable/>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.actionType" multiple collapse-tags clearable filterable
                       placeholder="Action Type">
              <el-option v-for="item in [ACTION_TYPE.feedback, ACTION_TYPE.exclusion, ACTION_TYPE.forward, ACTION_TYPE.escalation]"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-select v-model="pageCtl.conditions.status" multiple collapse-tags clearable filterable
                       placeholder="Status">
              <el-option v-for="item in ['Waiting for Approval', 'Waiting for Confirmation', 'Taking Effect', 'Closed', 'Expired', 'Not Started', 'Rejected']"
                         :key="item"
                         :label="item"
                         :value="item"/>
            </el-select>
          </el-col>
          <el-col :span="1">
            <el-button @click="searchReport1">
              <font-awesome-icon icon="search"/>
            </el-button>
          </el-col>
        </el-row>
        <view-tickets-table
            :data="pageCtl.tableData"
            :viewClick="viewClick"/>
      </el-tab-pane>
    </el-tabs>

    <scp-draggable-resizable v-model="pageCtl.visible" h="670" w="1024"
                             :title="$store.state.maintainer==='Y' ? 'View Ticket - ' + pageCtl.detailData.info.ID : 'View Ticket'">
      <template v-slot="{height}">
        <div :style="{minHeight: (height - 90) + 'px'}">
          <el-row>
            <el-col :span="24" style="padding: 5px 5px 0 5px;">
              <el-descriptions :column="4" size="small" border>
                <el-descriptions-item label="Title" :span="4">{{ pageCtl.detailData.info.TITLE }}</el-descriptions-item>
                <el-descriptions-item label="Category" :span="2">{{ pageCtl.detailData.info.CATEGORY }}</el-descriptions-item>
                <el-descriptions-item label="Action Type" :span="2">{{ pageCtl.detailData.info.ACTION }}</el-descriptions-item>
                <el-descriptions-item label="Subject(s)" :span="2">
                  <div v-html="pageCtl.detailData.info.SUBJECTS"/>
                </el-descriptions-item>
                <el-descriptions-item label="Collaborators" :span="2" v-if="pageCtl.detailData.info.ACTION === '1. Feedback'">
                  <scp-cascader v-model="pageCtl.detailData.info.COLLABORATORS" :options="pageCtl.collaboratorsOpts" :show-copy="false"
                                :placeholder-text="pageCtl.loading.collaborators? 'Loading...': 'Collaborators'" style="width: 300px;margin-right: 15px;"/>
                  <el-tooltip effect="light" content="Save Collaborators" :show-after="500">
                    <font-awesome-icon :spin="pageCtl.loading.updateCollaborators" @click="updateCollaborators"
                                       :icon="pageCtl.loading.updateCollaborators ? 'spinner': 'check'"
                                       style="color: var(--scp-text-color-highlight);cursor: hand" :loading="true"/>
                  </el-tooltip>
                </el-descriptions-item>
                <el-descriptions-item label="Active Period" :span="2">
                  <div v-if="pageCtl.isAdmin">
                    <el-date-picker
                        v-model="pageCtl.dateRange"
                        type="daterange"
                        unlink-panels
                        format="YYYY/MM/DD"
                        value-format="YYYY/MM/DD"
                        range-separator="to"
                        :clearable="false"
                        start-placeholder="Start date"
                        end-placeholder="End date"
                        style="margin-right: 15px;"
                        :picker-options="{}">
                    </el-date-picker>
                    <el-tooltip effect="light" content="Update Action Period" :show-after="500">
                      <font-awesome-icon :spin="pageCtl.loading.updateActionPeriod" @click="updateActionPeriod"
                                         :icon="pageCtl.loading.updateActionPeriod ? 'spinner': 'check'"
                                         style="color: var(--scp-text-color-highlight);cursor: hand" :loading="true"/>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{pageCtl.dateRange.join(' ~ ')}}
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="Status" :span="2">{{ pageCtl.detailData.info.STATUS }}</el-descriptions-item>
                <el-descriptions-item label="Action Owner" :span="2"
                                      v-if="pageCtl.detailData.info.ACTION === '3. Forward' ||  pageCtl.detailData.info.ACTION === '4. Escalation'">
                  <div v-if="pageCtl.isAdmin">
                    <el-select v-model="pageCtl.actionOwner" placeholder='Action Owner' style="width: 370px;margin-right: 15px;" filterable>
                      <el-option v-for="item in _ownerOpts"
                                 :key="item.key"
                                 :label="item.label"
                                 :value="item.key"/>
                    </el-select>
                    <el-tooltip effect="light" content="Update Action Owner" :show-after="500">
                      <font-awesome-icon :spin="pageCtl.loading.updateActionOwner" @click="updateActionOwner"
                                         :icon="pageCtl.loading.updateActionOwner ? 'spinner': 'check'"
                                         style="color: var(--scp-text-color-highlight);cursor: hand" :loading="true"/>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{ pageCtl.detailData.info.ACTION_OWNER }}
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="Assigned By" :span="2"
                                      v-if="pageCtl.detailData.info.ACTION === '3. Forward' ||  pageCtl.detailData.info.ACTION === '4. Escalation'">
                  {{ pageCtl.detailData.info.ASSIGNED_BY }}
                </el-descriptions-item>
                <el-descriptions-item label="Create By" :span="2">{{ pageCtl.detailData.info.CREATE_BY }}</el-descriptions-item>
                <el-descriptions-item label="Create Time" :span="pageCtl.detailData.info.ACTION === '1. Feedback'?2:4">
                  {{ pageCtl.detailData.info.CREATE_TIME }}
                </el-descriptions-item>
                <el-descriptions-item label="Approver" :span="4" v-if="pageCtl.detailData.info.ACTION === '2. Exclusion'">{{
                    pageCtl.detailData.info.APPROVER
                  }}
                </el-descriptions-item>
                <el-descriptions-item label="Brief Introduction" :span="4">
                  <div v-html="pageCtl.detailData.info.CONTENT" style="overflow: auto" class="view-tickets-content"></div>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col style="padding: 0 5px 5px 5px">
              <el-tabs v-model="pageCtl.activeName">
                <el-tab-pane label="Detail" name="DETAILS">
                  <scp-table2
                      :data="pageCtl.detailData.details"
                      :max-height="300"
                      :columns="detailsColumn"/>
                </el-tab-pane>
                <el-tab-pane label="Comment" name="REPLIES">
                  <el-row>
                    <el-col :span="7" style="padding: 0 5px 5px 5px">
                      <el-timeline>
                        <el-timeline-item placement="top" :timestamp="activity.OPR_TIME"
                                          v-for="(activity, index) in pageCtl.detailData.logs"
                                          :key="index">
                          <el-card class="timeline-card">
                            <h4>{{ activity.OPERATION }}</h4>
                            <p>{{ activity.OPERATOR }}</p>
                          </el-card>
                        </el-timeline-item>
                      </el-timeline>
                    </el-col>
                    <el-col :span="16" class="ticket-reply" :offset="1">
                      <div v-if="pageCtl.detailData.replies.length === 0" style="text-align: center; color: var(--scp-text-color-lighter)">
                        Looking forward to your comments...
                      </div>
                      <div v-for="(activity, index) in pageCtl.detailData.replies" :key="index">
                        <el-row v-if="activity.IS_ME">
                          <el-col :span="22" style="text-align: right;margin-bottom: 5px">
                            <el-card class="ticket-reply-card is-me">
                              <h4>
                                <el-tooltip effect="light" placement="top" :content="activity.REPLIER">
                                  {{ (activity.REPLIER + '').split('[')[0] }}
                                </el-tooltip>
                                <span class="ticket-reply-card-header-time">&nbsp;{{ activity.REPLY_TIME }}</span>
                                &nbsp;&nbsp;<font-awesome-icon icon="times" class="delete-btn" @click="deleteComment(activity.ROW_ID)"/>
                              </h4>
                              <p v-html="activity.CONTENT || '&nbsp;'"></p>
                            </el-card>
                          </el-col>
                          <el-col :span="2" style="text-align: center">
                            <el-avatar :size="22" src="/img/avatar.png"/>
                          </el-col>
                        </el-row>
                        <el-row v-else>
                          <el-col :span="1" style="text-align: left">
                            <el-avatar :size="22" src="/img/avatar.png"/>
                          </el-col>
                          <el-col :span="23" style="margin-bottom: 5px">
                            <el-card class="ticket-reply-card">
                              <h4>
                                <el-tooltip effect="light" placement="top" :content="activity.REPLIER">
                                  {{ (activity.REPLIER + '').split('[')[0] }}
                                </el-tooltip>
                                <span class="ticket-reply-card-header-time">&nbsp;{{ activity.REPLY_TIME }}</span>
                              </h4>
                              <p v-html="activity.CONTENT || '&nbsp;'"></p>
                            </el-card>
                          </el-col>
                        </el-row>
                      </div>

                      <el-row>
                        <el-col :span="24" style="margin-top: 30px">
                          <scp-ck-editor v-model="pageCtl.comment" placeholder="Write a comment"
                                         style="height: 200px;width: 100%;"/>
                        </el-col>
                        <el-col :span="24" style="padding-top: 5px">
                          <el-button type="primary" @click="postComment" :loading="pageCtl.loading.post">Post</el-button>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-tab-pane>
              </el-tabs>
            </el-col>
          </el-row>
        </div>
      </template>
      <template #footer>
        <el-popconfirm title="确定关闭当前Ticket? 关闭后, 当前Ticke内容将不再生效"
                       iconColor="var(--scp-text-color-error)"
                       @confirm="closeTicket"
                       confirmButtonType="danger"
                       confirmButtonText='确定'
                       cancelButtonText='取消'
                       style="margin-left:15px">
          <template #reference>
            <el-button size="small" type="info" :loading="pageCtl.loading.closeTicket" style="float: left" v-if="pageCtl.detailData.info.STATUS !== 'Closed'">
              Close this Ticket
            </el-button>
            <span v-else>&nbsp;</span>
          </template>
        </el-popconfirm>
        <el-button size="small" @click="closeWin">Close</el-button>

        <!-- accept -->
        <el-popconfirm title="Reject this forward?"
                       iconColor="var(--scp-text-color-error)"
                       @confirm="approveTicket('refuse')"
                       confirmButtonType="danger"
                       confirmButtonText='Confirm'
                       cancelButtonText='Cancel'
                       style="margin-left:15px">
          <template #reference>
            <el-button size="small" type="danger" v-if="pageCtl.detailData.handler.IS_ACCEPTER" :loading="pageCtl.loading.approve">
              <font-awesome-icon icon="times"/>&nbsp;I don't totally agree
            </el-button>
            <span v-else/>
          </template>
        </el-popconfirm>
        <el-popconfirm title="Accept this forward? You'll receive MPT's notification related to this ticket."
                       iconColor="var(--scp-text-color-highlight)"
                       @confirm="approveTicket('accept')"
                       confirmButtonType="primary"
                       confirmButtonText='Confirm'
                       cancelButtonText='Cancel'
                       style="margin-left:15px">
          <template #reference>
            <el-button size="small" type="primary" v-if="pageCtl.detailData.handler.IS_ACCEPTER" :loading="pageCtl.loading.approve">
              <font-awesome-icon icon="check"/>&nbsp;Accept
            </el-button>
            <span v-else/>
          </template>
        </el-popconfirm>

        <!-- approve -->
        <el-button size="small" type="primary" @click="approveTicket('approve')" v-if="pageCtl.detailData.handler.IS_APPROVER"
                   :loading="pageCtl.loading.approve">
          <font-awesome-icon icon="check"/>&nbsp; Approve
        </el-button>
        <el-popconfirm title="Reject this ticket?"
                       iconColor="var(--scp-text-color-error)"
                       @confirm="approveTicket('reject')"
                       confirmButtonType="danger"
                       confirmButtonText='Confirm'
                       cancelButtonText='Cancel'
                       style="margin-left:15px">
          <template #reference>
            <el-button size="small" type="danger" v-if="pageCtl.detailData.handler.IS_APPROVER" :loading="pageCtl.loading.approve">
              <font-awesome-icon icon="times"/>&nbsp;Reject
            </el-button>
            <span v-else/>
          </template>
        </el-popconfirm>
        <div class="clearfix"/>
      </template>
    </scp-draggable-resizable>
  </div>
</template>

<script lang="ts" setup>
import viewTicketsTable from '@/components/dia/mpt/MPTWorkspace/ViewTicketsTable.vue'
import { computed, inject, onMounted, reactive, readonly, ref } from 'vue'
import ScpCkEditor from '@/components/starter/components/CKEditor.vue'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

const $store = useStore()
const $route = useRoute()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $dateFormatter: any = inject('$dateFormatter')

const pageCtl = reactive({
  isAdmin: false,
  activeName: 'REPLIES',
  dateRange: [],
  actionOwner: '',
  loading: {
    init: false,
    tableData: false,
    collaborators: false,
    post: false,
    approve: false,
    closeTicket: false,
    updateCollaborators: false,
    updateActionPeriod: false,
    updateActionOwner: false
  },
  disable: true,
  visible: false,
  conditions: {
    title: '',
    activeName: 'MY_REQUEST',
    dateRange: [],
    category: [],
    owners: [],
    status: [],
    actionType: []
  },
  tableData: [],
  detailData: {
    info: {} as any,
    handler: {} as any,
    logs: [] as Array<any>,
    details: [] as Array<any>,
    replies: [] as Array<any>
  } as any,
  comment: '',
  collaboratorsOpts: [],
  categoryOpts: [],
  subjectOpts: [],
  ownerOpts: []
})

const ACTION_TYPE = readonly({
  feedback: '1. Feedback',
  exclusion: '2. Exclusion',
  forward: '3. Forward',
  escalation: '4. Escalation'
})

// @ts-ignore
const props = withDefaults(defineProps<{
  afterApprove?: Function,
  afterClose?: Function
}>(), {
  afterApprove: undefined,
  afterClose: undefined
})

const detailsColumn = ref([])

const closeWin = () => {
  pageCtl.visible = false
}

const approveTicket = (status) => {
  pageCtl.loading.tableData = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/approve_ticket_by_id',
    data: {
      id: pageCtl.detailData.info.ID,
      status
    }
  }).then(() => {
    if (props.afterApprove) {
      props.afterApprove()
    }
    closeWin()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.tableData = false
  })
}

const closeTicket = () => {
  pageCtl.loading.tableData = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/close_ticket_by_id',
    data: {
      id: pageCtl.detailData.info.ID
    }
  }).then(() => {
    if (props.afterClose) {
      props.afterClose()
    }
    closeWin()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.tableData = false
  })
}

const handleTabClick = (tabsPaneContext) => {
  pageCtl.conditions.activeName = tabsPaneContext.paneName
  searchReport1()
}

const viewClick = (data) => {
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/query_ticket_by_id',
    data: {
      id: data.ID
    }
  }).then((body) => {
    if (body !== null) {
      pageCtl.dateRange = [body.info.START_DATE, body.info.END_DATE]
      pageCtl.detailData = body
      pageCtl.actionOwner = body.info.ACTION_OWNER_SESA

      if (body.info.CATEGORY === '1. Master Data (Mat)') {
        detailsColumn.value = [{ data: 'MATERIAL' }, { data: 'PLANT_CODE' }]
      } else if (body.info.CATEGORY === '2. Master Data (Vend)') {
        detailsColumn.value = [{ data: 'MATERIAL' }, { data: 'PLANT_CODE' }, { data: 'VENDOR_CODE' }]
      } else if (body.info.CATEGORY === '3. Sales Order') {
        detailsColumn.value = [{ data: 'SALES_ORDER_NUMBER' }, { data: 'SALES_ORDER_ITEM' }, { data: 'MATERIAL' }, { data: 'PLANT_CODE' }]
      } else if (body.info.CATEGORY === '4. Purchasing Order') {
        detailsColumn.value = [{ data: 'PURCH_ORDER_NUMBER' }, { data: 'PURCH_ORDER_ITEM' }, { data: 'MATERIAL' }, { data: 'PLANT_CODE' }]
      } else if (body.info.CATEGORY === '5. Manufacturing Order') {
        detailsColumn.value = [{ data: 'MO_NUMBER' }, { data: 'MATERIAL' }, { data: 'PLANT_CODE' }]
      } else if (body.info.CATEGORY === '7. Inventory') {
        detailsColumn.value = [{ data: 'MATERIAL' }, { data: 'PLANT_CODE' }, { data: 'STORAGE_LOCATION' }, { data: 'SPECIAL_ST' }, { data: 'SCOPE$' }]
      } else {
        detailsColumn.value = []
      }
      pageCtl.visible = true
    } else {
      $message.error('Could not find tickey by id <' + data.ID + '>')
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.tableData = false
  })
}

const searchReport1 = () => {
  pageCtl.loading.tableData = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/query_tickets_by_status',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.tableData = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.tableData = false
  })
}

const postComment = () => {
  pageCtl.loading.post = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/save_ticket_comment',
    data: {
      id: pageCtl.detailData.info.ID,
      comment: pageCtl.comment
    }
  }).then(() => {
    pageCtl.comment = ''
    viewClick(pageCtl.detailData.info)
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.post = false
  })
}

const deleteComment = (rowid) => {
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/delete_ticket_comment',
    data: {
      id: pageCtl.detailData.info.ID,
      rowid
    }
  }).then(() => {
    $message.success('Comment deleted')
    viewClick(pageCtl.detailData.info)
  }).catch((error) => {
    console.log(error)
  })
}

const initViewTickets = () => {
  pageCtl.loading.init = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/mpt_workspace/init_view_tickets'
  }).then((body) => {
    pageCtl.ownerOpts = body.ownerOpts
    pageCtl.categoryOpts = body.categoryOpts
    pageCtl.collaboratorsOpts = body.collaboratorsOpts
    pageCtl.isAdmin = body.isAdmin

    if ($route.params.id) {
      viewClick({
        ID: $route.params.id
      })
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.init = false
  })
}

const updateCollaborators = () => {
  if (pageCtl.loading.updateCollaborators === false) {
    pageCtl.loading.updateCollaborators = true
    $axios({
      method: 'post',
      url: '/diagnosis/my_planning_today/mpt_workspace/update_collaborators',
      data: {
        id: pageCtl.detailData.info.ID,
        collaborators: pageCtl.detailData.info.COLLABORATORS
      }
    }).then(() => {
      $message.success('Collaborators Updated.')
      viewClick(pageCtl.detailData.info)
      searchReport1()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.updateCollaborators = false
    })
  }
}

const updateActionPeriod = () => {
  if (pageCtl.loading.updateActionPeriod === false) {
    pageCtl.loading.updateActionPeriod = true
    $axios({
      method: 'post',
      url: '/diagnosis/my_planning_today/mpt_workspace/update_action_period',
      data: {
        id: pageCtl.detailData.info.ID,
        dateRange: pageCtl.dateRange
      }
    }).then(() => {
      $message.success('Action Period Updated.')
      viewClick(pageCtl.detailData.info)
      searchReport1()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.updateActionPeriod = false
    })
  }
}

const updateActionOwner = () => {
  if (pageCtl.loading.updateActionOwner === false) {
    pageCtl.loading.updateActionOwner = true
    $axios({
      method: 'post',
      url: '/diagnosis/my_planning_today/mpt_workspace/update_action_owner',
      data: {
        id: pageCtl.detailData.info.ID,
        owner: pageCtl.actionOwner,
        fromOwnerName: pageCtl.detailData.info.ACTION_OWNER,
        toOwnerName: _newOwnerName.value
      }
    }).then(() => {
      $message.success('Action Owner Updated.')
      viewClick(pageCtl.detailData.info)
      searchReport1()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.updateActionOwner = false
    })
  }
}

const _ownerOpts = computed(() => {
  const result = []
  for (let i = 0; i < pageCtl.ownerOpts.length; i++) {
    if (pageCtl.ownerOpts[i].key !== pageCtl.detailData.info.ASSIGNED_BY_SESA) {
      result.push(pageCtl.ownerOpts[i])
    }
  }
  return result
})

const _newOwnerName = computed(() => {
  for (let i = 0; i < pageCtl.ownerOpts.length; i++) {
    if (pageCtl.ownerOpts[i].key === pageCtl.actionOwner) {
      return pageCtl.ownerOpts[i].label
    }
  }
  return ''
})

onMounted(() => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const day = today.getDate()
  pageCtl.conditions.dateRange = [$dateFormatter(new Date(year, month - 1, day), 'yyyy/MM/dd'), $dateFormatter(today, 'yyyy/MM/dd')]
  initViewTickets()
  searchReport1()
})

defineExpose({
  searchReport1, viewClick
})
</script>

<script lang="ts">
export default {
  name: 'MptWorkspaceViewTickets'
}
</script>

<style lang="scss">
#ViewTickets {
  .el-table .cell {
    white-space: nowrap !important;
  }

  .timeline-card {
    .el-card__body {
      padding: 0.5rem !important;
    }

    h4 {
      margin: 0 !important;
    }

    p {
      font-size: 82%;
    }
  }

  .el-timeline-item__wrapper {
    padding-left: 20px !important;
  }

  .el-divider--horizontal {
    margin: 0.5rem 0 0.8rem 0 !important;
  }

  .el-descriptions__cell {
    padding: 2px 5px !important;
    font-size: 0.47rem !important;
    line-height: 2 !important;
  }

  .ticket-reply {
    border-left: 1px solid var(--scp-border-color-lighter);
    overflow: hidden;
    padding: 5px 5px 0 10px;
    position: relative;
    width: 100%;
    transition: box-shadow 0.1s ease 0s;

    .ticket-reply-card {
      display: inline-block;
      background-color: var(--scp-bg-color);
      padding: 0 12px 5px 12px;
      position: relative;
      max-width: 100%;
      border-radius: 2px 8px 8px;

      .el-card__body {
        padding: 7px 0.5rem 5px 10px;
      }

      h4 {
        margin: 0 !important;
        font-size: 0.5rem;

        .ticket-reply-card-header-time {
          font-size: 82% !important;
          font-weight: normal !important;
        }
      }
    }

    .ticket-reply-card.is-me {
      border-radius: 8px 2px 8px 8px;
      text-align: left !important;
      border-color: var(--scp-text-color-highlight);
      background-color: var(--scp-bg-color);

      .delete-btn {
        color: var(--scp-border-color);
        cursor: pointer;
      }

      .delete-btn:hover {
        color: var(--scp-text-color-error);
      }
    }
  }

  .view-tickets-content {
    table.escalation-table {
      border-collapse: collapse;
      border: 1px solid var(--scp-border-color);

      th {
        font-size: 0.5rem;
        font-family: Calibri, DengXian, serif;
        background-color: var(--scp-bg-color-fill);
        padding: 0 5px;
      }

      th:not(:last-child) {
        border-right: 1px solid var(--scp-border-color);
      }

      td {
        font-size: 0.5rem;
        font-family: Calibri, DengXian, serif;
        border: 1px solid var(--scp-border-color);
        text-align: right;
        padding: 0 5px;
      }
    }
  }
}

.ticket-detail {
  .el-form-item__content {
    padding-right: 120px !important;
  }
}
</style>
