<template>
  <el-table :data="props.data" style="width: 100%" border :max-height="_height">
    <el-table-column type="index" width="50"/>
    <el-table-column prop="TITLE" label="Title" width="200"/>
    <el-table-column prop="CATEGORY" label="Category" width="200"/>
    <el-table-column prop="SUBJECTS" label="Subject" width="200">
      <template #default="scope">
        <el-popover trigger="hover" placement="top" width="auto" :show-after="500">
          <template #default>
            <div v-html="scope.row.SUBJECTS_TITLE"/>
          </template>
          <template #reference>
            <div v-html="scope.row.SUBJECTS"/>
          </template>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column prop="ACTION" label="Action Type" width="150"/>
    <el-table-column prop="STATUS" label="Status" width="150">
      <template #default="scope">
        <el-tag effect="dark" round :type="getType(scope.row.STATUS)">{{ scope.row.STATUS }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="ACTIVE_PERIOD" label="Active Period" width="200"/>
    <el-table-column prop="REQUEST_BY" label="Request By" width="200"/>
    <el-table-column prop="ACTION_OWNER" label="Action Owner" width="200"/>
    <el-table-column prop="COLLABORATORS" label="Collaborators" width="200">
      <template #default="scope">
        <el-popover trigger="hover" placement="top" width="auto" :show-after="500">
          <template #default>
            <div v-html="scope.row.COLLABORATORS_TITLE"/>
          </template>
          <template #reference>
            <div v-html="scope.row.COLLABORATORS"/>
          </template>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column prop="APPROVER" label="Approver" width="200"/>
    <el-table-column prop="CREATE_TIME" label="Create Time" width="150"/>
    <el-table-column fixed="right" label="Operations" width="150">
      <template #default="scope">
        <el-button link type="primary" size="small" @click="handleViewClick(scope.$index)">View</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  data?: Array<any>,
  viewClick?: Function
}>(), {
  data: () => {
    return []
  },
  viewClick: undefined
})

const handleViewClick = (index) => {
  if (props.viewClick) {
    props.viewClick(props.data[index])
  }
}

const getType = (status) => {
  switch (status) {
    case 'Waiting for Approval':
    case 'Waiting for Confirmation':
    case 'Taking Effect':
      return ''
    case 'Rejected':
      return 'danger'
    case 'Not Started':
    case 'Expired':
      return 'warning'
  }
  return 'info'
}

const _height = computed(() => {
  return Math.max(props.data?.length * 38, 400)
})
</script>

<script lang="ts">
export default {
  name: 'ViewTicketsTable'
}
</script>
