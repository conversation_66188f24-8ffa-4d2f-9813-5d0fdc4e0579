<template>
  <div class="left-sidebar" id="MyPlanningTodayFeedback">
    <div class="widget">
      <div class="widget-body">
<!--        <el-row style="margin-bottom: var(&#45;&#45;scp-widget-margin)">-->
<!--          <el-col :span="6">-->
<!--            <scp-cascader-->
<!--                style="width: calc(100% - 0.5rem);"-->
<!--                v-model="pageCtl.conditions.filterList"-->
<!--                :loading="pageCtl.loading.filter"-->
<!--                :options="pageCtl.filterOpts"/>-->
<!--          </el-col>-->
<!--          <el-col :span="2">-->
<!--            <scp-search :click-native="search" :data="pageCtl.conditions"/>-->
<!--          </el-col>-->
<!--        </el-row>-->
        <el-row>
          <el-col :span="24">
            <div class="subscript-container">
              <scp-subscript id="FEDL"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <scp-cascader
                      style="width: calc(100% - 0.5rem);"
                      v-model="pageCtl.conditions.filterList"
                      :loading="pageCtl.loading.filter"
                      :options="pageCtl.filterOpts"/>
                </el-col>
                <el-col :span="5">
                  <el-date-picker
                      style="width: 90%"
                      v-model="pageCtl.conditions.report1DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}">
                  </el-date-picker>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="pageCtl.conditions.report1SelectedColumn" placeholder="Columns" multiple
                             collapse-tags filterable clearable>
                    <el-option
                        v-for="item in _pivotColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <scp-search ref="searchRef" :click-native="searchReport1" :data="pageCtl.conditions"
                              :data-exclude="['report1DateRange', 'report3SelectedDate','report3DateRange']"/>
                </el-col>
              </el-row>
              <scp-table ref="report1TableRef"
                         :lazy="true"
                         url="/diagnosis/my_planning_today/feedback/query_report1"
                         download-url="/diagnosis/my_planning_today/feedback/download_report1"
                         :params="pageCtl.conditions"
                         :fixed-columns-left="_report1SelectedColumn.length"
                         :columns="pageCtl.report1Columns">
              </scp-table>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-loading="pageCtl.loading.report3">
            <div class="subscript-container">
              <scp-subscript id="MPDL" ref="report3SubRef"/>
              <el-row class="search-box">
                <el-col :span="5">
                  <scp-cascader
                      style="width: calc(100% - 0.5rem);"
                      v-model="pageCtl.conditions.pendingFilterList"
                      :loading="pageCtl.loading.filter"
                      :options="pageCtl.pendingFilterOpts"/>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="pageCtl.conditions.report3SelectedColumn" size="small" filterable>
                    <el-option
                        v-for="item in _pivotPendingColumns"
                        :key="item"
                        :label="item"
                        :value="item">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-date-picker
                      size="small"
                      v-model="pageCtl.conditions.report3DateRange"
                      type="daterange"
                      unlink-panels
                      format="YYYY/MM/DD"
                      value-format="YYYY/MM/DD"
                      range-separator="to"
                      :clearable="false"
                      start-placeholder="Start date"
                      end-placeholder="End date"
                      :picker-options="{}">
                  </el-date-picker>
                </el-col>
                <el-col :span="3">
                  <el-select v-model="pageCtl.conditions.report3ShowType" size="small">
                    <el-option label="View by Value" value="VIEW_BY_VALUE"/>
                    <el-option label="View by Percentage" value="VIEW_BY_PERCENT"/>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <scp-search ref="report3SearchRef" :click-native="searchReport3" :data="pageCtl.conditions"
                              :data-exclude="['report1DateRange', 'report3SelectedDate','report3DateRange']"
                  />
                </el-col>
              </el-row>
              <chart ref="report3ChartRef" :height="350" :option="_report3Opt"/>
            </div>
          </el-col>
        </el-row>
<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <div class="subscript-container">-->
<!--              <scp-subscript id="FEDS"/>-->
<!--              <el-row class="search-box">-->
<!--                <el-col :span="5">-->
<!--                  <el-date-picker-->
<!--                      style="width: 90%"-->
<!--                      v-model="pageCtl.conditions.report2DateRange"-->
<!--                      type="daterange"-->
<!--                      unlink-panels-->
<!--                      format="YYYY/MM/DD"-->
<!--                      value-format="YYYY/MM/DD"-->
<!--                      range-separator="to"-->
<!--                      :clearable="false"-->
<!--                      start-placeholder="Start date"-->
<!--                      end-placeholder="End date"-->
<!--                      :picker-options="{}">-->
<!--                  </el-date-picker>-->
<!--                </el-col>-->
<!--                <el-col :span="4">-->
<!--                  <el-select v-model="pageCtl.conditions.report2SelectedColumn" placeholder="Columns" multiple-->
<!--                             collapse-tags filterable clearable>-->
<!--                    <el-option-->
<!--                        v-for="item in _report2ColumnOpts"-->
<!--                        :key="item"-->
<!--                        :label="item"-->
<!--                        :value="item">-->
<!--                    </el-option>-->
<!--                  </el-select>-->
<!--                </el-col>-->
<!--                <el-col :span="1">-->
<!--                  <el-button @click="searchReport2">-->
<!--                    <font-awesome-icon icon="search"/>-->
<!--                  </el-button>-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--              <scp-table2 style="margin-bottom: var(&#45;&#45;scp-widget-margin);"-->
<!--                          ref="report2TableRef"-->
<!--                          :data="pageCtl.report2Data"-->
<!--                          :fixed-columns-left="_report2SelectedColumn.length"-->
<!--                          :columns="pageCtl.report2Columns['headers']"-->
<!--                          :nested-headers="pageCtl.report2Columns['nestedHeaders']">-->
<!--              </scp-table2>-->
<!--            </div>-->
<!--          </el-col>-->
<!--        </el-row>-->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onMounted, reactive, ref } from 'vue'
import { ViewDetailConfig } from '@/assets/js/function'

const $dateFormatter: any = inject('$dateFormatter')
const $axios: any = inject('$axios')
const $viewDetails: any = inject('$viewDetails')
const $toFixed: any = inject('$toFixed')
// const $thousandBitSeparator: any = inject('$thousandBitSeparator')
const $getPivotColumnByFilter: any = inject('$getPivotColumnByFilter')

const $toolbox: any = inject('$echarts.toolbox')
const $grid: any = inject('$echarts.grid')
const $legend: any = inject('$echarts.legend')
const $shortenNumber: any = inject('$shortenNumber')
const report1TableRef = ref()
// const report2TableRef = ref()
const report3ChartRef = ref()
const report3SubRef = ref()
const searchRef = ref()
const report3SearchRef = ref()

const pageCtl = reactive({
  filterOpts: [],
  pendingFilterOpts: [],
  conditions: {
    filterList: [],
    pendingFilterList: [['CLASSIFICATION', 'To Be Eliminated'], ['ACTION', '4. Escalation']],
    subjects: [],
    report1DateRange: [],
    // report2DateRange: [],
    report1SelectedColumn: ['OWNER', 'OWNER_NAME', 'ORGANIZATION', 'CATEGORY', 'SUB_CATEGORY', 'SUBJECT'],
    // report2SelectedColumn: ['OWNER', 'OWNER_NAME'],
    report3SelectedColumn: 'TOTAL_PENDING_DAYS_CD_RNAGE',
    report3ShowType: 'VIEW_BY_VALUE',
    report3SeriesType: 'line',
    report3SelectedDate: '',
    report3SelectedSeriesName: '',
    report3DateRange: [] as any
  } as any,
  loading: {
    filter: false,
    report3: false
  },
  report1Columns: [],
  // report2Columns: {
  //   headers: [],
  //   nestedHeaders: null as any
  // },
  // report2Data: [],
  // report2Subject: [],
  report3Data: [] as any
})

onMounted(() => {
  initFilter()
  report3ChartRef.value.chart().on('dblclick', (obj) => {
    if (obj.componentType === 'series') {
      pageCtl.conditions.report3SelectedDate = obj.name
      pageCtl.conditions.report3SelectedSeriesName = obj.seriesName
    } else {
      pageCtl.conditions.report3SelectedDate = obj.value
      pageCtl.conditions.report3SelectedSeriesName = ''
    }
    $viewDetails({
      url: '/diagnosis/my_planning_today/feedback/query_report3_details',
      durl: '/diagnosis/my_planning_today/feedback/download_report3_details',
      params: pageCtl.conditions,
      title: 'MPT Pending Details [' + [obj.name].join(', ') + '  ' + [obj.seriesName].join(', ') + ']'
    } as ViewDetailConfig)
  })
  report3ChartRef.value.chart().on('magictypechanged', (obj) => {
    if (obj.currentType === 'line' || obj.currentType === 'bar') {
      pageCtl.conditions.report3SeriesType = obj.currentType
    }
  })
})

const _report1SelectedColumn = computed(() => {
  if (pageCtl.conditions.report1SelectedColumn.length === 0) {
    return ['OWNER', 'OWNER_NAME', 'ORGANIZATION', 'CATEGORY', 'SUB_CATEGORY', 'SUBJECT']
  } else {
    return pageCtl.conditions.report1SelectedColumn
  }
})
// const _report2SelectedColumn = computed(() => {
//   const column = [] as any
//   for (let i = 0; i < pageCtl.conditions.report2SelectedColumn.length; i++) {
//     if (pageCtl.conditions.report2SelectedColumn[i] !== 'SUBJECT') {
//       column.push(pageCtl.conditions.report2SelectedColumn[i])
//     }
//   }
//
//   if (column.length === 0) {
//     return ['OWNER', 'OWNER_NAME', 'ORGANIZATION', 'CATEGORY', 'SUB_CATEGORY']
//   } else {
//     return column
//   }
// })

const _pivotColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.filterOpts)
})
const _pivotPendingColumns = computed(() => {
  return $getPivotColumnByFilter(pageCtl.pendingFilterOpts)
})

// const _report2ColumnOpts = computed(() => {
//   const r = _pivotColumns.value.filter(e => e !== 'SUBJECT')
//   r.push('TOTAL')
//   return r
// })

const initFilter = () => {
  const today = $dateFormatter(new Date(), 'yyyy/MM/dd')

  const now = new Date()
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const start = new Date(end.getTime() - 86400000 * 7 * 9)

  pageCtl.conditions.report1DateRange = [today, today]
  // pageCtl.conditions.report2DateRange = [today, today]
  pageCtl.conditions.report3DateRange = [
    $dateFormatter(start, 'yyyy/MM/dd'),
    $dateFormatter(end, 'yyyy/MM/dd')
  ]
  pageCtl.loading.filter = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/feedback/query_filters',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.filterOpts = body.cascader
    pageCtl.pendingFilterOpts = body.pendingCascader
    searchRef.value.loadAndClick()
    report3SearchRef.value.loadAndClick()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}

const searchReport3 = () => {
  pageCtl.loading.report3 = true
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/feedback/query_report3',
    data: pageCtl.conditions
  }).then((body) => {
    pageCtl.report3Data = body
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.report3 = false
  })
}

const _report3Opt = computed(() => {
  const series = [] as Array<any>
  const legend = [] as Array<any>

  let yAxisData: any = pageCtl.report3Data

  if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
    yAxisData = {}
    const stackTotal = {}
    const xAxis = pageCtl.report3Data.xAxis

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        for (let i = 0; i < xAxis.length; i++) {
          const x = xAxis[i]
          stackTotal[x] = (stackTotal[x] || 0) + pageCtl.report3Data[key][i]
        }
      }
    }

    for (const key in pageCtl.report3Data) {
      if (pageCtl.report3Data.hasOwnProperty(key) && key !== 'xAxis') {
        yAxisData[key] = []
        for (let i = 0; i < xAxis.length; i++) {
          const total = stackTotal[xAxis[i]]
          const v = pageCtl.report3Data[key][i]
          if (v && total) {
            yAxisData[key].push($toFixed(v * 100 / total, 3))
          } else {
            yAxisData[key].push(null)
          }
        }
      }
    }
  }

  for (const key in yAxisData) {
    if (yAxisData.hasOwnProperty(key) && key !== 'xAxis') {
      legend.push(key)
      series.push({
        name: key,
        type: pageCtl.conditions.report3SeriesType,
        smooth: false,
        stack: 'sum',
        areaStyle: {},
        data: yAxisData[key] || []
      })
    }
  }

  return {
    title: {
      text: 'MPT Pending Trend',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis',
      triggerOn: 'mousemove',
      formatter: (params) => {
        const tip = [] as Array<any>
        tip.push('<div>')
        tip.push(params[0].name)
        tip.push('<br>')

        let total = 0.0
        const p = params.sort((e2, e1) => e1.value - e2.value)
        for (const i in p) {
          if (!p.hasOwnProperty(i)) {
            continue
          }

          const value = p[i].value || 0
          total += value
          tip.push('<div style="width:11rem;">')
          const marker = p[i].marker

          tip.push(marker)
          tip.push(p[i].seriesName)
          tip.push('<span style="float: right">')
          tip.push($shortenNumber(value))
          if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
            tip.push('%')
          }
          tip.push('</span></div>')
        }
        tip.push('<div style="width:11rem;">')
        tip.push('Total')
        tip.push('<span style="float: right">')
        tip.push($shortenNumber(total))
        if (pageCtl.conditions.report3ShowType === 'VIEW_BY_PERCENT') {
          tip.push('%')
        }
        tip.push('</span></div>')
        tip.push('</div>')
        return tip.join('')
      }
    },
    legend: $legend({ data: legend }),
    grid: $grid(),
    xAxis: {
      type: 'category',
      boundaryGap: pageCtl.conditions.report3SeriesType === 'line' ? false : [0, '10%'],
      data: pageCtl.report3Data.xAxis || [],
      triggerEvent: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, '10%'],
      axisLabel: {
        formatter: (value) => {
          return $shortenNumber(value, 0)
        }
      }
    },
    toolbox: $toolbox({ opts: ['line', 'stack', 'no-details'] }),
    series
  }
})

const searchReport1 = () => {
  report1TableRef.value.setLoading(true)
  $axios({
    method: 'post',
    url: '/diagnosis/my_planning_today/feedback/query_privileges',
    data: pageCtl.conditions
  }).then((body) => {
    const isAdmin = body.isAdmin
    const column = [] as any
    for (let i = 0; i < _report1SelectedColumn.value.length; i++) {
      column.push({ data: _report1SelectedColumn.value[i] })
    }
    column.push({ data: 'OPEN_LINES', title: 'Sum of Open Errors', type: 'numeric' })
    if (isAdmin) {
      column.push({ data: 'ACCESS_LINES', title: 'Access', type: 'numeric' })
      column.push({ data: 'VIEW_LINES', title: 'View', type: 'numeric' })
      column.push({ data: 'DOWNLOAD_LINES', title: 'Download', type: 'numeric' })
      column.push({ data: 'ACCESS_CNT', title: 'Access Cnt', type: 'numeric' })
      column.push({ data: 'VIEW_CNT', title: 'View Cnt', type: 'numeric' })
      column.push({ data: 'DOWNLOAD_CNT', title: 'Download Cnt', type: 'numeric' })
    }
    column.push({ data: 'YESTERDAY_SOLVED', title: 'D-1 Solved', type: 'numeric' })
    column.push({ data: 'YESTERDAY_ADDED', title: 'D-1 Newly Added', type: 'numeric' })
    pageCtl.report1Columns = column
    nextTick(() => {
      report1TableRef.value.search()
    })
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.filter = false
  })
}
// const searchReport2 = () => {
//   report2TableRef.value.setLoading(true)
//   $axios({
//     method: 'post',
//     url: '/diagnosis/my_planning_today/feedback/query_report2_subjects',
//     data: pageCtl.conditions
//   }).then((body) => {
//     const subjects = [] as any // 存放所有的subject
//     const category = {} // 为了生成一级表头, 添加的临时变量
//     for (let i = 0; i < body.length; i++) {
//       subjects.push(body[i].SUBJECT)
//       category[body[i].CATEGORY] = (category[body[i].CATEGORY] || 0) + 1
//     }
//     pageCtl.report2Subject = subjects
//     pageCtl.conditions.subjects = subjects
//
//     const headers = [] as any
//     for (let i = 0; i < _report2SelectedColumn.value.length; i++) {
//       headers.push({ data: _report2SelectedColumn.value[i] })
//     }
//     for (let i = 0; i < pageCtl.report2Subject.length; i++) {
//       headers.push({ data: pageCtl.report2Subject[i], render: renderReport2Cell })
//     }
//     const nestedHeaders = [[{ label: '', colspan: _report2SelectedColumn.value.length }]]
//     for (const key in category) {
//       nestedHeaders[0].push({ label: key, colspan: category[key] })
//     }
//
//     pageCtl.report2Columns = {
//       headers,
//       nestedHeaders
//     }
//
//     $axios({
//       method: 'post',
//       url: '/diagnosis/my_planning_today/feedback/query_report2',
//       data: pageCtl.conditions
//     }).then((body) => {
//       pageCtl.report2Data = body
//     }).catch((error) => {
//       console.log(error)
//     }).finally(() => {
//       report2TableRef.value.setLoading(false)
//     })
//   }).catch((error) => {
//     console.log(error)
//   })
// }
// const renderReport2Cell = (hotInstance, td, row, column, prop, value) => {
//   if (value) {
//     const v = JSON.parse(value)
//     td.style.textAlign = 'right'
//     td.title = 'D-1 Solved:  ' + $thousandBitSeparator(v.SOLVED) +
//         '\r\nD-1 Newly Added:  ' + $thousandBitSeparator(v.ADDED) +
//         '\r\nSum of Open Errors:  ' + $thousandBitSeparator(v.OPEN)
//     // 如果新增的大于解决的, 说明问题在严重, 用红色上升箭头
//     // 反之, 说明问题在减轻, 用绿色下降箭头
//     const delta = v.ADDED - v.SOLVED
//     if (delta === 0) {
//       td.innerHTML = '<span style="color: gray">' + delta + ' <i>&minus;</i></span>'
//     } else if (delta > 0) {
//       const open = v.OPEN - v.ADDED + v.SOLVED
//       let per = ''
//       if (open !== 0) {
//         per = '' + (delta / open * 100).toFixed(0) + '%'
//       }
//       td.innerHTML = '<div style="float:left;color: rgba(255,0,0,0.7);font-size: 70%">' + per + '</div><span style="color: var(--scp-text-color-error);font-weight: bold">' + delta + '&nbsp;' +
//           '<svg class="svg-inline--fa fa-arrow-up" aria-hidden="true" focusable="false" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M214.6 41.4c-12.5-12.5-32.8-12.5-45.3 0l-160 160c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L160 141.2V448c0 17.7 14.3 32 32 32s32-14.3 32-32V141.2L329.4 246.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-160-160z"></path></svg>' +
//           '</span>'
//     } else if (delta < 0) {
//       const open = v.OPEN - v.ADDED + v.SOLVED
//       let per = ''
//       if (open !== 0) {
//         per = ((delta * -1) / open * 100).toFixed(0) + '%'
//       }
//       td.innerHTML = '<div style="float:left;color: rgba(0,128,0,0.7);font-size: 70%">' + per + '</div><span style="color: green;font-weight: bold">' + delta +
//           '<svg class="svg-inline--fa fa-arrow-down" aria-hidden="true" focusable="false" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M169.4 470.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 370.8 224 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 306.7L54.6 265.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"></path></svg>' +
//           '</span>'
//     } else {
//       td.innerHTML = delta
//     }
//   } else {
//     td.innerHTML = ''
//   }
// }

</script>
