<template>
  <!-- 传统账号密码登录, 一般用于测试环境 -->
  <div class="login-container" id="login">
    <el-form status-icon
             ref="loginForm"
             label-position="left"
             label-width="0px"
             class="login-page">
      <h3 class="title">SIGN IN</h3>
      <el-form-item prop="username">
        <el-input type="text"
                  class="form-control"
                  v-model="data.username"
                  autocomplete="new-password"
                  placeholder="USERNAME"
        ></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input type="password"
                  class="form-control"
                  v-model="data.password"
                  autocomplete="new-password"
                  placeholder="PASSWORD"
                  show-password
                  @keypress.enter="handleSubmit"
        ></el-input>
      </el-form-item>
      <el-checkbox
          v-model="data.checked"
          class="rememberme"
      >Remember Me
      </el-checkbox>
      <el-form-item style="width:100%;margin-top:26px;">
        <el-button style="width:80px;height:50px;margin-left: 220px" @click="handleSubmit" :loading="data.logining">Sign In</el-button>
      </el-form-item>
    </el-form>

    <scp-popup v-model="$route.fullPath"/>
  </div>
</template>

<script lang="ts" setup>
import { reactive, inject, onBeforeMount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import ScpPopup from '@/components/starter/components/Popup.vue'

const $route = useRoute()
const $router = useRouter()
const $axios: any = inject('$axios')
const $notify: any = inject('$notify')

const data = reactive({
  logining: false,
  username: '',
  password: '',
  checked: false
})

const handleSubmit = () => {
  data.logining = true
  $axios({
    method: 'post',
    url: '/login',
    data: {
      username: data.username,
      password: data.password
    }
  }).then((body) => {
    if (body.status === 200) {
      localStorage.setItem('remember', data.checked ? 'Y' : 'N')
      localStorage.setItem('username', data.username.toUpperCase())
      localStorage.setItem('name', body.name)
      localStorage.setItem('token', body.token)

      const lastPage = localStorage.getItem('page')
      if ($route.query.redirect) {
        $router.push($route.query.redirect.toString())
      } else if (lastPage) {
        $router.push(lastPage)
      } else {
        $router.push('/')
      }
    } else {
      $notify({
        title: 'Login Failed',
        message: 'Username or password is incorrect!',
        type: 'error'
      })
    }
    data.logining = false
  }).catch((error) => {
    data.logining = false
    console.log(error)
  })
}

onBeforeMount(() => {
  if (localStorage.getItem('remember') === 'Y') {
    data.checked = true
    data.username = '' + localStorage.getItem('username')
  }
})
</script>

<style lang="scss">
#login {
  overflow: hidden;
  width: 100%;
  height: 100%;
  padding-top: 3%;

  .title {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 20px;
    margin: 0 0 32px 0;
    color: var(--scp-text-color-primary);
    line-height: unset;
    font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  }

  .login-page {
    margin: 0 auto 50px auto;
    width: 300px;
    height: 350px;
    padding: 30px;
    background: var(--scp-bg-color);
    border: 1px solid var(--scp-border-color-lighter);
    box-shadow: 0 0 2px 2px #f8f8f8;
  }

  .form-control {
    height: 50px !important;
    line-height: 50px !important;
    padding-left: 0;
    padding-right: 0;
    background-color: var(--scp-bg-color) !important;
    transition: all 0.3s ease;
    margin-bottom: 10px;

    .el-input__wrapper {
      box-shadow: none;
      border-radius: 0;
      border-bottom: 1px solid var(--scp-border-color);
    }

    .el-input__wrapper:hover {
      border-bottom: 1px solid var(--scp-text-color-highlight);
    }

    .el-input__inner {
      background-color: transparent !important;
    }

    input {
      height: 100%;
      padding: 0;
      width: 100%;
      line-height: 100%;
      font-size: 16px !important;
    }
  }

  label.el-checkbox.rememberme {
    text-align: left;
    margin-top: 5px;
  }
}
</style>
