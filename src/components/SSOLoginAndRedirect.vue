<template>
  <!-- 使用SSO登录系统, 与PINGSSO登录不同的是, 用户无需点击PINGSSO按钮, 系统会自动登录, 并且跳转到需要跳转的页面-->
  <div id="ssoLogin2">
    <p>
      <font-awesome-icon icon="fa-spinner" spin/>
      Ping SSO Login...
    </p>
    <p>
      <el-link type="primary" @click="gotoLoginPage" style="color: var(--scp-text-color-hightlight-lighter)"><i>Link to Login Page</i></el-link>
    </p>
  </div>
</template>

<script lang="ts" setup>
import { inject, onBeforeMount, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const $route = useRoute()
const $router = useRouter()
const $axios: any = inject('$axios')
const $notify: any = inject('$notify')
const $redirectHttp2Https: any = inject('$redirectHttp2Https')

const gotoLoginPage = () => {
  $router.push('/login')
}

/**
 * 1. 用户点击SSO页面时, 会判断当前是否为HTTPS, 如果是HTTPS则直接走PINGSSO流程
 * 2. 如果当前页面是HTTP, 则给HTTP链接添加一个r=pingsso的后缀, 并且跳转到HTTPS页面
 * 3. 接第2步, 进入HTTPS页面后, 首先校验localstorage, 判断是否有生效的token, 如果有生效的token, 则直接使用token登录
 * 4. 如果无token, 或者token失效, 判断url是否有r=pingsso的后缀
 * 5. 如果有r=pingsso后缀, 则说明用户是从pingsso跳转过来的, 如果当前是https, 则直接跳转至pingsso
 * 6. 如果是http, 则将http替换成https, 然后走1步校验逻辑
 * 7. 如果无后缀, 则说明用户是正常访问的HTTPS页面, 走正常路程
 */
const autoLogin = () => {
  let nextPath = '/'
  const lastPage = localStorage.getItem('page')
  if ($route.query.redirect) {
    nextPath = $route.query.redirect + ''
    localStorage.setItem('page', nextPath)
  } else if (lastPage) {
    nextPath = lastPage
  }
  if (localStorage.getItem('token')) {
    // 校验token是否过期, 不判断是否有权限
    $axios({
      method: 'post',
      url: '/check_route_auth',
      headers: {
        route: nextPath
      }
    }).then((body) => {
      // 如果返回408, 说明用户token失效, 需要pingsso
      if (body.status === 408) {
        $router.push('/pingsso')
      } else {
        // 在这一级不判断route权限, 交由router.beforeEach拦截器来判断
        $router.push(nextPath)
      }
    }).catch((error) => {
      console.log(error)
    })
  } else {
    $router.push('/pingsso')
  }
}

onBeforeMount(() => {
  $redirectHttp2Https()
  autoLogin()
})
</script>
<style lang="scss">
#ssoLogin2 {
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
  padding-top: 15%;
  color: var(--scp-text-color-lighter);
}
</style>
