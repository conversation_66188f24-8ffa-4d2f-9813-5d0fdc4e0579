<template>
  <div class="left-sidebar" id="agentRegistration">
    <div style="padding-top: 5px;" class="agent-container">
      <div class="agent-container-left">
        <scp-tree-menu
          ref="treeRef"
          style="height: calc(100% - 15px);overflow: auto;"
          url="/rag/agent_registration/query_agent_list"
          :new-click="()=>pageCtl.visible.create = true"
          :node-click="nodeClick"
        ></scp-tree-menu>
      </div>
      <div class="agent-container-right" :style="{minHeight: _initHeight + 'px'}" v-loading="pageCtl.loading.content">
        <div v-if="pageCtl.loading.content">
          <!-- 加载的时候不显示任何内容-->
        </div>
        <div v-else-if="pageCtl.viewAgent.AGENT_ID===''">
          <scp-md-preview v-model="pageCtl.introduction" editorId="api_introduction" />
        </div>
        <div v-else :style="{height: (_initHeight + 70) + 'px'}" style="overflow: auto;">
          <div style="margin-bottom: 10px">
            <div class="markdown-body">
              <h2>{{ pageCtl.viewAgent.SUBJECT }}
                <span style="float: right;text-align:right; font-size: 0.45rem;color: var(--scp-text-color-secondary);font-weight: normal;">
                作者: {{ pageCtl.viewAgent.CREATE_BY }} | 创建时间: {{ pageCtl.viewAgent.CREATE_DATE }} | 最后编辑:
                {{ pageCtl.viewAgent.UPDATE_BY || pageCtl.viewAgent.CREATE_BY }} | 编辑时间:
                {{ pageCtl.viewAgent.UPDATE_DATE || pageCtl.viewAgent.CREATE_DATE }} | Agent ID: {{ pageCtl.viewAgent.AGENT_ID }}
              </span>
              </h2>
            </div>
          </div>
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="2" class="title">Agent Type</el-col>
                <el-col :span="12">
                  <el-radio-group v-model="pageCtl.viewAgent.AGENT_TYPE">
                    <el-radio-button label="Built-in Agent" value="BUILT_IN" />
                    <el-radio-button label="Web API" value="WEB_API" />
                  </el-radio-group>
                </el-col>
              </el-row>
            </div>
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="2" class="title">Subject</el-col>
                <el-col :span="12">
                  <el-input
                    size="small"
                    placeholder="Subject"
                    v-model="pageCtl.viewAgent.SUBJECT"
                    clearable>
                  </el-input>
                </el-col>
                <el-col :span="1" class="title">Group</el-col>
                <el-col :span="8">
                  <el-autocomplete
                    class="inline-input"
                    v-model="pageCtl.viewAgent.GROUPS"
                    :maxlength="200"
                    size="small"
                    style="width: var(--scp-input-width) !important;"
                    :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                    placeholder="Group"
                    show-word-limit
                  ></el-autocomplete>
                </el-col>
              </el-row>
            </div>
            <div style="margin-bottom: 10px" v-show="pageCtl.viewAgent.AGENT_TYPE === 'WEB_API'">
              <el-row>
                <el-col :span="2" class="title">API URL</el-col>
                <el-col :span="21">
                  <el-input
                    size="small"
                    placeholder="API URL"
                    v-model="pageCtl.viewAgent.REQUEST_URL"
                    style="width: var(--scp-input-width) !important;"
                    clearable>
                  </el-input>
                </el-col>
              </el-row>
            </div>
            <div style="margin-bottom: 10px" v-show="pageCtl.viewAgent.AGENT_TYPE === 'WEB_API'">
              <el-row>
                <el-col :span="2" class="title">Request Body</el-col>
                <el-col :span="10">
                  <scp-ace-editor v-model="pageCtl.viewAgent.REQUEST_BODY" lang="json"
                                  style="width: 100%;border: 1px dotted var(--scp-border-color);height: 130px;" />
                </el-col>
                <el-col :span="2" class="title">Request Header</el-col>
                <el-col :span="9">
                  <scp-ace-editor v-model="pageCtl.viewAgent.REQUEST_HEADER" lang="json"
                                  style="width: calc(100% - 16px);border: 1px dotted var(--scp-border-color);height: 130px" />
                </el-col>
              </el-row>
            </div>
            <div>
              <el-row>
                <el-col :span="2" class="title">Agent Introduction</el-col>
                <el-col :span="21" style="padding-right: 15px">
                  <scp-md-editor v-model="pageCtl.viewAgent.CONTENT"  :style="{'height': (_initHeight - (pageCtl.viewAgent.AGENT_TYPE === 'WEB_API' ? 320 : 138)) + 'px'}" />
                </el-col>
              </el-row>
            </div>
            <div>
              <el-row>
                <el-col :span="16">&nbsp;</el-col>
                <el-col :span="7" style="padding-right: 15px;text-align: right;">
                  <el-popconfirm title="确定删除这个数据接口?"
                                 iconColor="var(--scp-text-color-error)"
                                 @confirm="deleteAgent"
                                 confirmButtonType="danger"
                                 confirmButtonText='确定'
                                 cancelButtonText='取消'>
                    <template #reference>
                      <el-button type="danger">Delete</el-button>
                    </template>
                  </el-popconfirm>
                  <el-button type="primary" @click="modifyAgent">Save</el-button>
                </el-col>
                <el-col :span="1">&nbsp;</el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>

      <!-- new agent -->
      <scp-draggable-resizable w="80vw" h="70vh" title="Register Agent"
                               v-model="pageCtl.visible.create" :save="saveAgent" :save-loading="pageCtl.loading.modify">
        <template v-slot="{height}">
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="2" class="title">Agent Type</el-col>
                <el-col :span="12">
                  <el-radio-group v-model="pageCtl.newAgent.AGENT_TYPE">
                    <el-radio-button label="Built-in Agent" value="BUILT_IN" />
                    <el-radio-button label="Web API" value="WEB_API" />
                  </el-radio-group>
                </el-col>
              </el-row>
            </div>
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="2" class="title">Subject</el-col>
                <el-col :span="12">
                  <el-input
                    size="small"
                    placeholder="Subject"
                    v-model="pageCtl.newAgent.SUBJECT"
                    clearable>
                  </el-input>
                </el-col>
                <el-col :span="1" class="title">Group</el-col>
                <el-col :span="8">
                  <el-autocomplete
                    class="inline-input"
                    v-model="pageCtl.newAgent.GROUPS"
                    :maxlength="200"
                    size="small"
                    style="width: var(--scp-input-width) !important;"
                    :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                    placeholder="Group"
                    show-word-limit
                  ></el-autocomplete>
                </el-col>
              </el-row>
            </div>
            <div style="margin-bottom: 10px" v-show="pageCtl.newAgent.AGENT_TYPE === 'WEB_API'">
              <el-row>
                <el-col :span="2" class="title">API URL</el-col>
                <el-col :span="21">
                  <el-input
                    size="small"
                    placeholder="API URL"
                    v-model="pageCtl.newAgent.REQUEST_URL"
                    style="width: var(--scp-input-width) !important;"
                    clearable>
                  </el-input>
                </el-col>
              </el-row>
            </div>
            <div style="margin-bottom: 10px" v-show="pageCtl.newAgent.AGENT_TYPE === 'WEB_API'">
              <el-row>
                <el-col :span="2" class="title">Request Body</el-col>
                <el-col :span="10">
                  <scp-ace-editor v-model="pageCtl.newAgent.REQUEST_BODY" lang="json"
                                  style="width: 100%;border: 1px dotted var(--scp-border-color);height: 75px;" />
                </el-col>
                <el-col :span="2" class="title">Request Header</el-col>
                <el-col :span="9">
                  <scp-ace-editor v-model="pageCtl.newAgent.REQUEST_HEADER" lang="json"
                                  style="width: calc(100% - 16px);border: 1px dotted var(--scp-border-color);height: 75px" />
                </el-col>
              </el-row>
            </div>
            <div>
              <el-row>
                <el-col :span="2" class="title">Agent Introduction</el-col>
                <el-col :span="21" style="padding-right: 15px">
                  <scp-md-editor v-model="pageCtl.newAgent.CONTENT" :style="{'height': (height - (pageCtl.newAgent.AGENT_TYPE === 'WEB_API' ? 320 : 193)) + 'px'}" />
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </scp-draggable-resizable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'
import { computed, inject, onMounted, reactive, ref } from 'vue'
import { useStore } from 'vuex'
import ScpAceEditor from '@/components/starter/components/AceEditor.vue'

const treeRef = ref()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $store: any = useStore()

const pageCtl = reactive({
  existsGroup: [],
  introduction: '',
  visible: {
    create: false
  },
  loading: {
    content: false,
    modify: false
  },
  newAgent: {
    AGENT_TYPE: 'BUILT_IN',
    SUBJECT: '',
    GROUPS: '',
    CONTENT: '',
    REQUEST_URL: '',
    REQUEST_HEADER: '{\n  "Content-Type": "application/json"\n}',
    REQUEST_BODY: ''
  },
  viewAgent: {
    AGENT_TYPE: '',
    AGENT_ID: '',
    GROUPS: '',
    SUBJECT: '',
    CONTENT: '',
    REQUEST_URL: '',
    REQUEST_HEADER: '',
    REQUEST_BODY: '',
    CREATE_BY: '',
    CREATE_DATE: '',
    UPDATE_BY: '',
    UPDATE_DATE: ''
  }
})

// 初始化页面
const initPage = () => {
  $axios({
    method: 'post',
    url: '/rag/agent_registration/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
    pageCtl.introduction = body.introduction
  }).catch((error) => {
    console.log(error)
  })
}

const checkInput = (params) => {
  if (!params.SUBJECT.trim()) {
    $message.error('Subject cannot be empty')
    return false
  } else if (!params.GROUPS.trim()) {
    $message.error('Group cannot be empty')
    return false
  } else if (!params.CONTENT.trim()) {
    $message.error('Agent introduction cannot be empty')
    return false
  } else if (params.AGENT_TYPE === 'WEB_API' && !params.REQUEST_URL.trim()) {
    $message.error('Api URL cannot be empty')
    return false
  } else if (params.AGENT_TYPE === 'WEB_API' && !params.REQUEST_HEADER.trim()) {
    $message.error('Request header cannot be empty')
    return false
  } else if (params.AGENT_TYPE === 'WEB_API' && !params.REQUEST_BODY.trim()) {
    $message.error('Request body cannot be empty')
    return false
  }
  if (params.AGENT_TYPE === 'WEB_API' && isNotJson(params.REQUEST_HEADER.trim())) {
    $message.error('Request header is not a valid json')
    return false
  } else if (params.AGENT_TYPE === 'WEB_API' && isNotJson(params.REQUEST_BODY.trim())) {
    $message.error('Request body is not a valid json')
    return false
  }

  return true
}

const isNotJson = (str) => {
  if (typeof str !== 'string') return false
  try {
    const parsed = JSON.parse(str)
    return typeof parsed === 'object' && parsed !== null
  } catch (e) {
    return false
  }
}

const saveAgent = () => {
  if (!checkInput(pageCtl.newAgent)) {
    return
  }
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/rag/agent_registration/save_agent',
    data: pageCtl.newAgent
  }).then(() => {
    $message.success('Agent Saved.')
    pageCtl.visible.create = false
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

const modifyAgent = () => {
  if (!checkInput(pageCtl.viewAgent)) {
    return
  }
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/rag/agent_registration/modify_agent',
    data: pageCtl.viewAgent
  }).then(() => {
    $message.success('Agent Saved.')
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

// 点击左侧树菜单触发
const nodeClick = (data) => {
  showAgent(data.key)
}

// 将选择的user显示在右侧
const showAgent = (agentId) => {
  pageCtl.loading.content = true
  $axios({
    method: 'post',
    url: '/rag/agent_registration/query_agent_with_id',
    data: {
      agentId
    }
  }).then((body) => {
    pageCtl.viewAgent.AGENT_TYPE = body.AGENT_TYPE
    pageCtl.viewAgent.AGENT_ID = body.AGENT_ID
    pageCtl.viewAgent.GROUPS = body.GROUPS
    pageCtl.viewAgent.SUBJECT = body.SUBJECT
    pageCtl.viewAgent.CONTENT = body.CONTENT
    pageCtl.viewAgent.REQUEST_URL = body.REQUEST_URL
    pageCtl.viewAgent.REQUEST_HEADER = body.REQUEST_HEADER
    pageCtl.viewAgent.REQUEST_BODY = body.REQUEST_BODY
    pageCtl.viewAgent.CREATE_BY = body.CREATE_BY
    pageCtl.viewAgent.CREATE_DATE = body.CREATE_DATE
    pageCtl.viewAgent.UPDATE_BY = body.UPDATE_BY
    pageCtl.viewAgent.UPDATE_DATE = body.UPDATE_DATE
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.content = false
  })
}

const initViewPrompt = () => {
  const keys = Object.keys(pageCtl.viewAgent)
  for (let i = 0; i < keys.length; i++) {
    pageCtl.viewAgent[keys[i]] = ''
  }
}

const deleteAgent = () => {
  $axios({
    method: 'post',
    url: '/rag/agent_registration/delete_agent',
    data: {
      agentId: pageCtl.viewAgent.AGENT_ID
    }
  }).then(() => {
    $message.success('Agent Deleted.')
    initViewPrompt()
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  initPage()
})

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 150
})
</script>

<style lang="scss">
#agentRegistration {
  height: 100%;

  .markdown-body {
    font-size: 0.5rem !important;
    border-bottom: 1px solid var(--scp-border-color);

    h2 {
      margin: 5px 0;
    }
  }

  .md-editor-content .md-editor-input-wrapper {
    overflow: auto;
  }

  .agent-container {
    height: calc(100% - 10px);

    .agent-container-left {
      height: 100%;
      float: left;
      width: 250px;
    }

    .agent-container-right {
      float: left;
      padding-left: 25px;
      width: calc(100% - 280px);
      border-left: 1px solid var(--scp-border-color);
    }

    .title {
      text-align: right;
      padding-right: 8px;
      font-weight: bold;
      line-height: 2;
    }

    .content {
      padding-right: 5px;
    }

    .el-row {
      margin-bottom: 12px;
    }
  }
}

</style>
