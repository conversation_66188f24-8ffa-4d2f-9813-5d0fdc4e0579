<template>
  <div class="left-sidebar" id="userPersona">
    <div style="padding-top: 5px;" class="persona-container">
      <div class="persona-container-left" v-show="pageCtl.isAdmin">
        <scp-tree-menu
          ref="treeRef"
          style="height: calc(100% - 15px);overflow: auto;"
          url="/rag/user_persona/query_persona_list"
          :node-click="nodeClick"
        ></scp-tree-menu>
      </div>
      <div class="persona-container-right" :style="{minHeight: _initHeight + 'px', width: pageCtl.isAdmin? 'calc(100% - 280px)': 'calc(100% - 35px)',
           paddingLeft: pageCtl.isAdmin? '25px': '8px'}"
           v-loading="pageCtl.loading.content">
        <div v-if="pageCtl.loading.content || pageCtl.selectedPersona.USER_ID===''">
          <!-- 加载的时候不显示任何内容. 如果没有选择任何人, 也不显示任何内容 -->
        </div>
        <!-- 如果选择了人, 但是内容为空, 提示需要输入 -->
        <div v-else-if="!pageCtl.viewPersona.CONTENT">
          <!-- 如果是管理员, 则显示被选中人的信息, 不是管理员, 直接用您替代 -->
          <h1 style="text-align: center;font-size: 1.6rem; line-height: 1.5; color: var(--scp-text-color-lighter);margin-top: 170px;margin-bottom: 80px">
            <span v-if="pageCtl.isAdmin">{{pageCtl.selectedPersona.USER_NAME}} [{{pageCtl.selectedPersona.USER_ID}}]<br>尚未维护个人用户画像, 请点击下面的按钮开始</span>
            <span v-else>您尚未维护个人用户画像, 请点击下面的按钮开始</span>
          </h1>
          <div style="text-align: center">
            <el-button type="primary" @click="showModifyWin">新建个人画像</el-button>
          </div>
        </div>
        <!-- 如果选择了人, 内容也不为空, 展示内容 -->
        <div v-else  :style="{height: (_initHeight + 70) + 'px'}" style="overflow: auto;">
          <div style="margin-bottom: 10px">
            <div class="markdown-body">
              <h2>{{ pageCtl.viewPersona.USER_NAME }} [{{ pageCtl.viewPersona.USER_ID }}]
                <span style="float: right;text-align:right; font-size: 0.45rem;color: var(--scp-text-color-secondary);font-weight: normal;">
                <font-awesome-icon icon="edit" style="margin-right: 10px;color: var(--scp-text-color-success);cursor: pointer"
                                   @click="showModifyWin" title="编辑" />
                | 作者: {{ pageCtl.viewPersona.CREATE_BY }} | 创建时间: {{ pageCtl.viewPersona.CREATE_DATE }} | 最后编辑:
                {{ pageCtl.viewPersona.UPDATE_BY || pageCtl.viewPersona.CREATE_BY }} | 编辑时间:
                {{ pageCtl.viewPersona.UPDATE_DATE || pageCtl.viewPersona.CREATE_DATE }}
              </span>
              </h2>
            </div>
          </div>
          <scp-md-preview v-model="pageCtl.viewPersona.CONTENT" :editorId="'editor_' + pageCtl.viewPersona.USER_ID"/>
        </div>
      </div>

      <!-- modify prompt -->
      <scp-draggable-resizable w="80vw" h="70vh" title="User Persona"
                               v-model="pageCtl.visible.modify" :save="modifyPersona" :save-loading="pageCtl.loading.modify"
                               delete-confirm-text="确定导入用户画像模版? 此操作会覆盖未保存的内容" delete-text="使用用户画像模版"
                               delete-btn-type="default" :delete="importPersonaTemplate">
        <template v-slot="{height}">
          <el-row>
            <el-col :span="24" style="padding: 5px">
              <scp-md-editor v-model="pageCtl.selectedPersona.CONTENT" :style="{'height': (height - 120) + 'px'}" />
            </el-col>
          </el-row>
        </template>
      </scp-draggable-resizable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'
import { computed, inject, onMounted, reactive, ref } from 'vue'
import { useStore } from 'vuex'

const treeRef = ref()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $store: any = useStore()

const pageCtl = reactive({
  isAdmin: false,
  existsGroup: [],
  visible: {
    modify: false
  },
  loading: {
    content: false,
    modify: false
  },
  selectedPersona: { // 用来控制显示和修改的用户画像信息
    USER_ID: '', // 当前激活的用户的ID
    USER_NAME: '', // 当前激活的用户的NAME
    CONTENT: '' // 如果有修改, 这里存放修改后的信息
  },
  viewPersona: {
    USER_ID: '',
    USER_NAME: '',
    CONTENT: '',
    CREATE_BY: '',
    CREATE_DATE: '',
    UPDATE_BY: '',
    UPDATE_DATE: ''
  }
})

// 初始化页面
const initPage = () => {
  $axios({
    method: 'post',
    url: '/rag/user_persona/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
    pageCtl.isAdmin = body.isAdmin
    if (!pageCtl.isAdmin) {
      // 非管理员进入页面, 锁住显示的内容, 永远是其本人
      pageCtl.selectedPersona.USER_ID = localStorage.getItem('username') + ''
      showSelectedPersona()
    } else {
      pageCtl.viewPersona.CONTENT = ''
    }
  }).catch((error) => {
    console.log(error)
  })
}

// 显示修改窗口
const showModifyWin = () => {
  $axios({
    method: 'post',
    url: '/rag/user_persona/query_persona_with_id',
    data: {
      userid: pageCtl.selectedPersona.USER_ID
    }
  }).then((body) => {
    pageCtl.selectedPersona.CONTENT = body.CONTENT || ''
    pageCtl.visible.modify = true
  }).catch((error) => {
    console.log(error)
  })
}

// 修改用户画像
const modifyPersona = () => {
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/rag/user_persona/modify_persona',
    data: pageCtl.selectedPersona
  }).then(() => {
    $message.success('User Persona Saved.')
    pageCtl.visible.modify = false
    showSelectedPersona()
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

// 点击左侧树菜单触发
const nodeClick = (data) => {
  // 只有点击Node才可以修改这个变量
  pageCtl.selectedPersona.USER_ID = data.key
  pageCtl.selectedPersona.USER_NAME = data.label
  showSelectedPersona()
}

// 将选择的user显示在右侧
const showSelectedPersona = () => {
  pageCtl.loading.content = true
  $axios({
    method: 'post',
    url: '/rag/user_persona/query_persona_with_id',
    data: {
      userid: pageCtl.selectedPersona.USER_ID
    }
  }).then((body) => {
    if (body && Object.keys(body).length > 0 && body.CONTENT) {
      pageCtl.viewPersona.USER_ID = body.USER_ID
      pageCtl.viewPersona.USER_NAME = body.USER_NAME
      pageCtl.viewPersona.CONTENT = body.CONTENT
      pageCtl.viewPersona.CREATE_BY = body.CREATE_BY
      pageCtl.viewPersona.CREATE_DATE = body.CREATE_DATE
      pageCtl.viewPersona.UPDATE_BY = body.UPDATE_BY
      pageCtl.viewPersona.UPDATE_DATE = body.UPDATE_DATE
    } else {
      pageCtl.viewPersona.CONTENT = ''
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.content = false
  })
}

// 导入模版
const importPersonaTemplate = () => {
  $axios({
    method: 'post',
    url: '/rag/user_persona/query_persona_template'
  }).then((body) => {
    if (body) {
      pageCtl.selectedPersona.CONTENT = body
    } else {
      $message.error('未找到Comments模版, 请联系管理员!')
    }
  }).catch((error) => {
    console.log(error)
  })
}

onMounted(() => {
  initPage()
})

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 150
})
</script>

<style lang="scss">
#userPersona {
  height: 100%;

  .markdown-body {
    font-size: 0.5rem !important;
    border-bottom: 1px solid var(--scp-border-color);

    h2 {
      margin: 5px 0;
    }
  }

  .anchor-active {
    display: inline-block;
  }

  .menu-btn {
    position: fixed;
    right: 1rem;
    top: 4.2rem;
    z-index: 10;
  }

  .menu-body {
    box-shadow: 0 0 1px var(--scp-border-color);
    z-index: 10;
    padding: 5px 0;
    position: fixed;
    right: 10px;
    width: 10rem;
    top: 4.2rem;
    background-color: var(--scp-bg-color);
    overflow: auto;
  }

  .md-editor-catalog-active > span {
    color: inherit !important;
  }

  .md-editor-content .md-editor-input-wrapper {
    overflow: auto;
  }

  .persona-container {
    height: calc(100% - 10px);

    .persona-container-left {
      height: 100%;
      float: left;
      width: 250px;
    }

    .persona-container-right {
      float: left;
      padding-left: 25px;
      border-left: 1px solid var(--scp-border-color);
    }
  }
}

</style>
