<template>
  <div class="left-sidebar" id="promptTemplate">
    <div style="padding-top: 5px" class="prompt-container">
      <div class="prompt-container-left">
        <scp-tree-menu
            ref="treeRef"
            url="/rag/prompt_template/query_prompt_list"
            :new-click="()=>pageCtl.visible.newPromptDiv = true"
            :node-click="nodeClick"
        ></scp-tree-menu>
      </div>
      <div class="prompt-container-right" :style="{minHeight: _initHeight + 'px'}"
           v-loading="pageCtl.loading.content">
        <div v-show="pageCtl.viewPrompt.CREATE_BY !== ''" style="margin-bottom: 10px">
          <div class="markdown-body">
            <h2>{{ pageCtl.viewPrompt.SUBJECT }}
              <span style="float: right;text-align:right; font-size: 0.45rem;color: var(--scp-text-color-secondary);font-weight: normal;">
                <span class="prompt-container-right-print-hidden">
                <font-awesome-icon icon="edit" style="margin-right: 10px;color: var(--scp-text-color-success);cursor: pointer" @click="showModifyDivWin"
                                   title="编辑"/>
                <el-popconfirm title="确定删除文档?"
                               iconColor="var(--scp-text-color-error)"
                               @confirm="deletePromptAction"
                               confirmButtonType="danger"
                               confirmButtonText='确定'
                               cancelButtonText='取消'>
                  <template #reference>
                    <font-awesome-icon icon="times" style="margin-right: 10px;color: var(--scp-text-color-error);cursor: pointer" title="删除"/>
                  </template>
                </el-popconfirm>
                | </span> 作者: {{ pageCtl.viewPrompt.CREATE_BY }} | 创建时间: {{ pageCtl.viewPrompt.CREATE_DATE }} | 最后编辑:
                {{ pageCtl.viewPrompt.UPDATE_BY || pageCtl.viewPrompt.CREATE_BY }} | 编辑时间:
                {{ pageCtl.viewPrompt.UPDATE_DATE || pageCtl.viewPrompt.CREATE_DATE }} | 文档ID:
                {{ pageCtl.viewPrompt.PROMPT_ID }}
              </span>
            </h2>
          </div>
        </div>
        <scp-md-preview v-model="pageCtl.viewPrompt.CONTENT" :editorId="'editor_' + pageCtl.viewPrompt.PROMPT_ID" :markedHeading="markedHeading"/>
      </div>

      <!-- new prompt -->
      <scp-draggable-resizable w="80vw" h="70vh" v-model="pageCtl.visible.newPromptDiv" title="New Prompt" :save="saveNewPromptAction"
                               :save-loading="pageCtl.loading.create">
        <template v-slot="{height}">
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="12">
                  <el-input
                      size="default"
                      placeholder="Subject"
                      v-model="pageCtl.newPrompt.subject"
                      style="width: var(--scp-input-width) !important;"
                      clearable>
                  </el-input>
                </el-col>
                <el-col :span="5">
                  <el-autocomplete
                      class="inline-input"
                      v-model="pageCtl.newPrompt.groups"
                      :maxlength="200"
                      size="default"
                      style="width: var(--scp-input-width) !important;"
                      :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                      placeholder="Group"
                      show-word-limit
                  ></el-autocomplete>
                </el-col>
              </el-row>
            </div>
            <hr style="margin: 10px 0">
            <div>
              <el-row>
                <el-col :span="24" style="padding-right: 15px">
                  <scp-md-editor v-model="pageCtl.newPrompt.content" :style="{'height': (height - 150) + 'px'}"/>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </scp-draggable-resizable>

      <!-- modify prompt -->
      <scp-draggable-resizable w="80vw" h="70vh" title="Modify Prompt" v-model="pageCtl.visible.modifyPromptDiv" :save="modifyPromptAction"
                               :save-loading="pageCtl.loading.modify">
        <template v-slot="{height}">
          <div style="padding: 5px">
            <div style="margin-bottom: 10px">
              <el-row>
                <el-col :span="12">
                  <el-input
                      size="default"
                      placeholder="Subject"
                      v-model="pageCtl.modifyPrompt.SUBJECT"
                      style="width: var(--scp-input-width) !important;"
                      clearable>
                  </el-input>
                </el-col>
                <el-col :span="5">
                  <el-autocomplete
                      class="inline-input"
                      v-model="pageCtl.modifyPrompt.GROUPS"
                      :maxlength="200"
                      size="default"
                      style="width: var(--scp-input-width) !important;"
                      :fetch-suggestions="(query, cb) => $autoCompleteSuggestion(pageCtl.existsGroup, query, cb)"
                      placeholder="Group"
                      show-word-limit
                  ></el-autocomplete>
                </el-col>
              </el-row>
            </div>
            <hr style="margin: 10px 0">
            <div>
              <el-row>
                <el-col :span="24" style="padding-right: 15px">
                  <scp-md-editor v-model="pageCtl.modifyPrompt.CONTENT" :style="{'height': (height - 150) + 'px'}"/>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </scp-draggable-resizable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'
import ScpMdEditor from '@/components/starter/components/MdEditor.vue'
import { computed, inject, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'

const treeRef = ref()
const $axios: any = inject('$axios')
const $message: any = inject('$message')
const $autoCompleteSuggestion: any = inject('$autoCompleteSuggestion')
const $store: any = useStore()

const pageCtl = reactive({
  existsGroup: [],
  visible: {
    newPromptDiv: false,
    modifyPromptDiv: false
  },
  loading: {
    content: false,
    create: false,
    modify: false
  },
  newPrompt: {
    subject: '',
    groups: '',
    content: ''
  },
  modifyPrompt: {
    PROMPT_ID: '',
    CONTENT: '',
    SUBJECT: '',
    GROUPS: ''
  },
  viewPrompt: {
    PROMPT_ID: '',
    CONTENT: '',
    SUBJECT: '',
    GROUPS: '',
    CREATE_BY: '',
    CREATE_DATE: '',
    UPDATE_BY: '',
    UPDATE_DATE: ''
  }
})

const initPage = () => {
  $axios({
    method: 'post',
    url: '/rag/prompt_template/init_page'
  }).then((body) => {
    pageCtl.existsGroup = body.existsGroup
  }).catch((error) => {
    console.log(error)
  })
}

const markedHeading = (text, level, index) => {
  return `${text}-${level}-${index}`
}

const saveNewPromptAction = () => {
  if (!pageCtl.newPrompt.groups) {
    $message.error('Invalid groups')
    return
  }
  if (pageCtl.newPrompt.subject) {
    pageCtl.loading.create = true
    $axios({
      method: 'post',
      url: '/rag/prompt_template/save_new_prompt',
      data: pageCtl.newPrompt
    }).then(() => {
      treeRef.value.search()
      pageCtl.visible.newPromptDiv = false
      $message.success('Prompt Saved.')
      initNewPrompt()
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      pageCtl.loading.create = false
    })
  } else {
    $message.error('Invalid subject')
  }
}

const showModifyDivWin = () => {
  $axios({
    method: 'post',
    url: '/rag/prompt_template/query_prompt',
    data: {
      promptid: pageCtl.viewPrompt.PROMPT_ID
    }
  }).then((body) => {
    pageCtl.modifyPrompt.PROMPT_ID = body.PROMPT_ID
    pageCtl.modifyPrompt.CONTENT = body.CONTENT
    pageCtl.modifyPrompt.SUBJECT = body.SUBJECT
    pageCtl.modifyPrompt.GROUPS = body.GROUPS
    pageCtl.visible.modifyPromptDiv = true
  }).catch((error) => {
    console.log(error)
  })
}

const modifyPromptAction = () => {
  if (!pageCtl.modifyPrompt.GROUPS) {
    $message.error('Invalid groups')
    return
  }
  if (!pageCtl.modifyPrompt.SUBJECT) {
    $message.error('Invalid subject')
    return
  }
  pageCtl.loading.modify = true
  $axios({
    method: 'post',
    url: '/rag/prompt_template/modify_prompt',
    data: pageCtl.modifyPrompt
  }).then(() => {
    $message.success('Prompt Saved.')
    pageCtl.visible.modifyPromptDiv = false
    getContentByPromptID(pageCtl.viewPrompt.PROMPT_ID)
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.modify = false
  })
}

const deletePromptAction = () => {
  $axios({
    method: 'post',
    url: '/rag/prompt_template/delete_prompt',
    data: {
      promptid: pageCtl.viewPrompt.PROMPT_ID
    }
  }).then(() => {
    $message.success('Prompt Deleted.')
    initViewPrompt()
    treeRef.value.search()
  }).catch((error) => {
    console.log(error)
  })
}

const nodeClick = (data) => {
  getContentByPromptID(data.key)
}

const getContentByPromptID = (promptid) => {
  pageCtl.loading.content = true
  $axios({
    method: 'post',
    url: '/rag/prompt_template/query_prompt_with_id',
    data: {
      promptid
    }
  }).then((body) => {
    const keys = Object.keys(pageCtl.viewPrompt)
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      pageCtl.viewPrompt[key] = body[key] || ''
    }
  }).catch((error) => {
    console.log(error)
  }).finally(() => {
    pageCtl.loading.content = false
  })
}

const initViewPrompt = () => {
  const keys = Object.keys(pageCtl.viewPrompt)
  for (let i = 0; i < keys.length; i++) {
    pageCtl.viewPrompt[keys[i]] = ''
  }
}

const initNewPrompt = () => {
  const keys = Object.keys(pageCtl.newPrompt)
  for (let i = 0; i < keys.length; i++) {
    pageCtl.newPrompt[keys[i]] = ''
  }
  localStorage.removeItem('prompt.new.content')
}

onMounted(() => {
  initPage()

  const newPromptContent = localStorage.getItem('prompt.new.content')
  if (newPromptContent) {
    pageCtl.newPrompt.content = newPromptContent
  }
})

const _initHeight = computed(() => {
  return document.documentElement.clientHeight - 150
})

watch(() => pageCtl.newPrompt.content, (newVal) => {
  localStorage.setItem('prompt.new.content', newVal)
})
</script>

<style lang="scss">
#promptTemplate {
  .markdown-body {
    font-size: 0.5rem !important;
    border-bottom: 1px solid var(--scp-border-color);

    h2 {
      margin: 5px 0;
    }
  }

  .anchor-active {
    display: inline-block;
  }

  .menu-btn {
    position: fixed;
    right: 1rem;
    top: 4.2rem;
    z-index: 10;
  }

  .menu-body {
    box-shadow: 0 0 1px var(--scp-border-color);
    z-index: 10;
    padding: 5px 0;
    position: fixed;
    right: 10px;
    width: 10rem;
    top: 4.2rem;
    background-color: var(--scp-bg-color);
    overflow: auto;
  }

  .md-editor-catalog-active > span {
    color: inherit !important;
  }

  .md-editor-content .md-editor-input-wrapper {
    overflow: auto;
  }

  .prompt-container {

    .prompt-container-left {
      float: left;
      width: 250px;
    }

    .prompt-container-right {
      float: left;
      width: calc(100% - 280px);
      padding-left: 25px;
      border-left: 1px solid var(--scp-border-color);
    }
  }

  @media print {
    .prompt-container {
      .prompt-container-left {
        display: none;
      }

      .prompt-container-right {
        border-left: none !important;
        width: 100% !important;
        padding-left: 0 !important;

        .prompt-container-right-print-hidden {
          display: none !important;
        }

        .menu-btn {
          display: none !important;
        }
      }
    }
  }
}

</style>
