<template>
  <div class="left-sidebar" style="width: 100%;display: flex" id="MOSS" :style="{height: pageCtl.layout.height + 'px'}">
    <!--  主功能区域 -->
    <div style="display: flex;flex-direction: column; align-items: center;width: 100%">
      <div class="chat-header">
        <div style="margin-bottom: 16px">&nbsp;</div>
      </div>
      <div class="chat-box" :style="{height: (pageCtl.layout.height - 170) + 'px'}">
        <el-row v-for="item in pageCtl.chatLogs" :key="item.key" style="margin-bottom: 16px" :style="{width: pageCtl.layout.width + 'px'}">
          <el-col :span="1" style="text-align: left">
            <div v-if="item.role==='assistant'">
              <el-avatar :size="36" src="/img/noob.png" />
            </div>
          </el-col>
          <el-col :span="22" :class="item.role==='assistant'?'align-left':'align-right'">
            <!-- 显示数据表格 -->
            <div v-if="item.type==='sql_to_table' && item.role==='assistant'" class="chat-box-table">
              <strong>完整数据如下：</strong>
              <scp-table
                :download-specify-column="false"
                :params="{sql: item.data}"
                url="/intelligent_agent/moss/query_data_by_sql"
                download-url="/intelligent_agent/moss/download_data_by_sql"
                :editable="false" />
            </div>
            <div v-else-if="item.type==='sql_to_chart' && item.role==='assistant'" class="chat-box-table">
              <moss-chart :sql="item.data.sql" :settings="item.data.settings" />
            </div>
            <!-- 显示助手对话 -->
            <scp-md-preview v-else-if="item.role==='assistant' && (!!item.display)" v-model="item.display"
                            style="border-radius: 8px;" :noKatex="true"
                            :theme="item.role==='assistant'?undefined:'dark'" :auto-fold-threshold="50" />
            <!-- 显示助手对话(空) -->
            <div v-else-if="item.role==='assistant' && (!item.display)"
                 style="height: 28px;text-align: left;background-color: #fff;border-radius: 8px;padding: 5px 12px;width: 64px;font-size: 0.65rem;font-weight: bold">
              {{ pageCtl.loading.text }}
            </div>
            <!-- 显示用户对话 -->
            <scp-md-preview v-model="item.display" style="border-radius: 8px;" v-else />
          </el-col>
          <el-col :span="1" style="text-align: right">
            <div v-if="item.role==='user'">
              <el-tooltip v-if="$store.state.maintainer==='Y'" :show-after="1000" effect="light" placement="bottom" content="You are one of DSS maintainers">
                <el-avatar :size="36" :src="_avatar">
                  <img src="/img/avatar-admin.png" alt="admin" />
                </el-avatar>
              </el-tooltip>
              <el-avatar v-if="$store.state.maintainer!=='Y'" :size="36" :src="_avatar">
                <img src="/img/avatar.png" alt="avatar" />
              </el-avatar>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="query-box" :style="{width: pageCtl.layout.width + 'px'}"
           style="border-radius: 8px;border: 1px solid #E5E5E5;padding: 5px;background-color: white;margin-bottom: 8px">
        <el-row>
          <el-col :span="24" style="border-bottom: 1px dotted #E5E5E5;padding-bottom: 3px;padding-top: 8px;" v-if="pageCtl.queryImages.length > 0">
            <el-badge value="x" class="item" v-for="(item, index) in pageCtl.queryImages" :key="item" style=" margin-right:18px;">
              <template #content>
                <font-awesome-icon icon="times" style="cursor: pointer" title="delete this image" @click="deleteImage(index)" />
              </template>
              <el-image
                style="width: 100px; height: 75px;border: 1px solid var(--scp-border-color-lighter)"
                :hide-on-click-modal="true"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[item]"
                show-progress
                :initial-index="4"
                fit="cover" />
            </el-badge>
          </el-col>
          <el-col :span="24" style="height: 125px">
            <el-input
              @paste="pasteTextarea"
              :disabled="pageCtl.loading.search"
              @keydown.enter.stop="quickSearch"
              @keydown.shift.enter.stop="insertEnter"
              v-model="pageCtl.query"
              style="width: 100%;"
              :rows="6"
              resize="none"
              type="textarea" />
          </el-col>
          <el-col :span="20" style="text-align: left;color: var(--scp-text-color-lighter);font-style: italic;padding-left: 5px;font-size: 0.45rem"
                  class="query-box-tips">
            ENTER快速发送消息，SHIFT + ENTER 换行 &nbsp;
          </el-col>
          <el-col :span="4" style="text-align: right;color: var(--scp-text-color-lighter);font-style: italic;padding-left: 5px;font-size: 0.45rem"
                  class="query-box-tips">
            <el-button @click="createNewConversation" style="margin-left: 5px;" title="开启新对话" type="primary" plain round
                       v-show="pageCtl.activeAgentId !== '' && pageCtl.loading.search === false">
              <div style="font-size: 10px">
                <font-awesome-icon icon="comment-alt" />&nbsp;开启新对话
              </div>
            </el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, nextTick, onBeforeUnmount, onMounted, reactive, watch } from 'vue'
import { useStore } from 'vuex'
import ScpMdPreview from '@/components/starter/components/MdPreview.vue'

import mqtt from 'mqtt'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import MossChart from '@/components/agt/agent/MossChart.vue'

const $store = useStore()
const $axios: any = inject('$axios')
const $randomString: any = inject('$randomString')

interface ChatLog {
  key: string, // log id
  type: string, // 对话日志的类型, 可选的有text, sql_to_table, sql_to_chart
  role: string, // 角色, 可选的有user和assistant
  reasoning: Array<string>, // 存放的reasoning信息, 因为reasoning可能会分批次传递过来, 所以以array存放
  answer: Array<string>, // 存放的answer信息, 同理reasoning
  images: [], // 用户在提问时, 可能会附加图片
  display: string, // 显示在页面中的信息
  data: any // 其他的信息
}

let client: any = {} // mqtt client
const pageCtl = reactive({
  loadingTimer: {},
  query: '',
  queryImages: [],
  activeAgentId: '',
  topic: 'scp/dss/ui/moss/' + (localStorage.getItem('username') || '').toLowerCase() + '-' + $randomString(4),
  loading: {
    search: false,
    text: '.'
  },
  chatLogs: [] as Array<ChatLog>,
  layout: {
    height: 0,
    width: 0
  }
})

const parseReasoningAndAnswer = (item) => {
  const reasoning = item.reasoning
  const answer = item.answer
  const result = []

  if (item.images && item.images.length > 0) {
    for (let i = 0; i < item.images.length; i++) {
      result.push('![image' + i + '](' + item.images[i] + ')')
    }
  }

  if (reasoning.length > 0) {
    result.push('> ' + reasoning.join('\n> '))
  }
  if (answer.length > 0) {
    if (reasoning.length > 0) {
      result.push('\n\n')
    }
    result.push(answer.join(''))
  }
  return result.join('')
}

const connectMqtt = () => {
  client = mqtt.connect('wss://scp-dss.cn.schneider-electric.com:61615/mqtt', {
    clientId: 'scp-ui-moss-' + (localStorage.getItem('username') || 'nobody').toLowerCase() + '-' + $randomString(4)
  })
  client.on('connect', e => {
    client.subscribe(pageCtl.topic, { qos: 2 }, (err) => {
      if (!err) {
        console.log('subscribe topic: ' + pageCtl.topic)
      }
    })
  })

  // 接收后台传递的消息
  client.on('message', (topic, message) => {
    const messageObj = JSON.parse('' + message)
    const answerType = messageObj.answer_type // 获取消息类型
    // 选择智能体并没有代表结束, 所以不能将loading状态取消
    if (answerType !== 'select_agent') {
      pageCtl.loading.search = false
    }
    if (answerType === 'select_agent') { // 选择智能体
      pageCtl.activeAgentId = messageObj.reasoning
      search(messageObj.answer, false)
    } else if (answerType === 'termination') { // 收到终止信号
      const msg = {
        key: $randomString(8),
        role: 'assistant',
        reasoning: [],
        answer: ['【新对话已开启】'],
        images: [],
        display: ''
      } as ChatLog

      if (messageObj.reasoning) {
        msg.reasoning.push(messageObj.reasoning)
      }
      msg.display = parseReasoningAndAnswer(msg)
      pageCtl.chatLogs.push(msg)
      pageCtl.activeAgentId = ''
    } else if (answerType === 'text') { // 处理文本消息
      // 因为有加载画面, 所以需要修改上一条对话记录
      const item = pageCtl.chatLogs[pageCtl.chatLogs.length - 1]
      if (item) {
        if (messageObj.reasoning) {
          item.reasoning.push(messageObj.reasoning)
        }
        if (messageObj.answer) {
          item.answer.push(messageObj.answer)
        }
        item.display = parseReasoningAndAnswer(item)
      }
    } else if (answerType === 'sql') {
      // 后台返回的类型为sql, 则直接转交sql_to_table处理
      pageCtl.chatLogs.push({
        role: 'assistant',
        type: 'sql_to_table',
        data: messageObj.answer
      } as ChatLog)
    } else if (answerType === 'chart') {
      // 后台返回的类型为chart, 生成sql_to_chart需要的参数类型
      const settings = JSON.parse(messageObj.answer)
      const chartSettings = JSON.parse(settings.chart_settings)
      // 因为table之前展示过了, 这里就不再展示
      if (chartSettings.type !== 'table') {
        pageCtl.chatLogs.push({
          role: 'assistant',
          type: 'sql_to_chart',
          data: { sql: settings.sql, settings: chartSettings }
        } as ChatLog)
      }
    }
    scrollChatBox()
  })
}

const quickSearch = (event) => {
  event.preventDefault()
  if (event.shiftKey === false && pageCtl.query.trim()) {
    searchAgentId()
  }
}

const prepareChat = () => {
  pageCtl.loading.search = true
  const userInput = {
    key: $randomString(8),
    role: 'user',
    reasoning: [],
    answer: [pageCtl.query],
    images: pageCtl.queryImages,
    display: ''
  } as ChatLog
  userInput.display = parseReasoningAndAnswer(userInput)
  pageCtl.chatLogs.push(userInput)

  pageCtl.chatLogs.push({
    key: $randomString(8),
    role: 'assistant',
    reasoning: [],
    answer: [],
    images: [],
    display: ''
  } as ChatLog)

  scrollChatBox()
  pageCtl.query = ''
  pageCtl.queryImages = []
}

const searchAgentId = () => {
  if (pageCtl.activeAgentId) {
    search(pageCtl.query)
  } else {
    const query = pageCtl.query
    const images = pageCtl.queryImages
    prepareChat()
    $axios({
      method: 'post',
      url: '/intelligent_agent/moss/search_agent_id',
      data: {
        query,
        images,
        topic: pageCtl.topic
      }
    }).then((body) => {
      if (body.status !== 200) {
        const item = pageCtl.chatLogs[pageCtl.chatLogs.length - 1]
        item.answer = [body.result]
        item.display = parseReasoningAndAnswer(item)
        $axios({
          method: 'post',
          url: '/intelligent_agent/moss/create_new_conversation',
          data: {
            topic: pageCtl.topic
          }
        })
      }
    }).catch((error) => {
      console.log(error)
    })
  }
}

const search = (query, needPrepare = true) => {
  const images = pageCtl.queryImages
  if (needPrepare) {
    prepareChat()
  }
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/search',
    data: {
      query,
      images,
      topic: pageCtl.topic,
      agentId: pageCtl.activeAgentId
    }
  }).then((body) => {
    if (body.status !== 200) {
      const item = pageCtl.chatLogs[pageCtl.chatLogs.length - 1]
      item.answer = [body.result]
      item.display = parseReasoningAndAnswer(item)
      $axios({
        method: 'post',
        url: '/intelligent_agent/moss/create_new_conversation',
        data: {
          topic: pageCtl.topic
        }
      })
    }
  }).catch((error) => {
    console.log(error)
  })
}

const createNewConversation = () => {
  if (pageCtl.loading.search) {
    return
  }
  pageCtl.loading.search = true
  $axios({
    method: 'post',
    url: '/intelligent_agent/moss/create_new_conversation',
    data: {
      topic: pageCtl.topic
    }
  })
}

const scrollChatBox = () => {
  nextTick(() => {
    const chatBox = document.getElementsByClassName('chat-box')[0] as HTMLPreElement
    chatBox.scrollTo({ top: chatBox.scrollHeight, behavior: 'smooth' })
  })
}

const insertEnter = (event) => {
  event.preventDefault()
  if (event.shiftKey && pageCtl.query.trim()) {
    const textarea = event.target
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    // 在光标位置插入换行符
    textarea.value = textarea.value.substring(0, start) + '\n' + textarea.value.substring(end)
    // 移动光标到换行符后面
    textarea.selectionStart = textarea.selectionEnd = start + 1
    nextTick(() => {
      const queryTextArea = document.getElementsByClassName('query-box')[0].getElementsByClassName('el-textarea__inner')[0] as HTMLPreElement
      queryTextArea.scrollTo({ top: queryTextArea.scrollHeight, behavior: 'smooth' })
    })
  }
}

watch(() => pageCtl.loading.search, (newVal) => {
  clearInterval(pageCtl.loadingTimer)
  pageCtl.loading.text = '.'
  if (newVal) {
    pageCtl.loadingTimer = setInterval(() => {
      switch (pageCtl.loading.text.length) {
        case 1:
          pageCtl.loading.text = '..'
          break
        case 2:
          pageCtl.loading.text = '...'
          break
        case 3:
          pageCtl.loading.text = '....'
          break
        case 4:
          pageCtl.loading.text = '.'
          break
      }
    }, 1000)
  } else {
    pageCtl.loading.text = ''
  }
})

const _avatar = computed(() => {
  return 'https://scp-dss.cn.schneider-electric.com/avatar/' + localStorage.getItem('username') + '.jpg'
})

const pasteTextarea = (e) => {
  const items = e.clipboardData?.items
  if (!items) return

  // 检查是否有文件
  const files = []
  for (const item of items) {
    if (item.kind === 'file' && item.type.startsWith('image/')) {
      files.push(item.getAsFile())
    }
  }

  if (files.length > 0) {
    // 阻止默认粘贴行为
    e.preventDefault()

    const data = new FormData()
    data.append('file', files[0])
    data.append('saveType', 'temp')

    $axios({
      method: 'post',
      url: '/system/save_image_to_local',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((body) => {
      pageCtl.queryImages.push(body)
    }).catch((error) => {
      console.log(error)
    })
  }
}

const deleteImage = (index) => {
  pageCtl.queryImages.splice(index, 1)
}

onMounted(() => {
  pageCtl.layout.height = document.documentElement.clientHeight - 60
  pageCtl.layout.width = Math.min(document.documentElement.clientWidth * 0.88, 1366)
  window.onresize = () => {
    pageCtl.layout.height = document.documentElement.clientHeight - 60
    pageCtl.layout.width = Math.min(document.documentElement.clientWidth * 0.88, 1366)
  }
  connectMqtt()
})

const closeMqttClient = () => {
  try {
    client.end()
  } catch (e) {

  }
}

const destory = async () => {
  closeMqttClient()
  await $axios({
    method: 'post',
    url: '/intelligent_agent/moss/create_new_conversation',
    data: {
      topic: pageCtl.topic
    }
  })
}

onBeforeUnmount(() => {
  destory()
})

window.onbeforeunload = () => {
  destory()
}
</script>

<style lang="scss">
#MOSS {
  background-color: #F3F5FA;

  .query-box {
    .el-textarea__inner {
      box-shadow: none !important;
      background-color: transparent !important;
    }

    .query-box-tips {
      .el-select__wrapper {
        box-shadow: none !important;
      }

      .el-select .el-input__inner {
        color: var(--scp-text-color-lighter);
        font-style: italic;
        font-size: 0.45rem
      }

      .el-select__selected-item {
        color: var(--scp-text-color-lighter);
      }

      .el-select .el-input .el-select__caret {
        color: transparent !important;
      }
    }
  }

  .chat-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;
    margin-bottom: 8px;

    .chat-box-table {
      padding: 10px 20px !important;
      border-radius: 8px;
      background-color: var(--scp-bg-color);
    }

    .align-right {
      .github-theme table tr {
        background-color: transparent !important;
      }

      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        float: right;
        border-radius: 8px !important;
        background-color: #2D65F7 !important;

        * {
          color: #fff !important;
        }

        svg, .md-editor-code {
          * {
            color: #333 !important;
          }
        }
      }
    }

    .align-left {
      padding-right: 8px;
      padding-left: 8px;

      .md-editor-previewOnly {
        border-radius: 8px !important;
        float: left;
      }
    }

    .md-editor-previewOnly {
      display: inline-block !important;
      width: auto !important;

      pre {
        margin-bottom: 5px !important;
      }

      .md-editor-preview {
        padding: 10px 20px !important;
      }

      .execute-button {
        text-align: right;
        padding-right: 2px;

        .svg-inline--fa {
          cursor: pointer;
        }

        .data-value {
          display: none !important;
        }
      }
    }

    .md-editor-preview {
      blockquote {
        border-left: 1.5px solid var(--scp-border-color) !important;
        color: #818080 !important;
        margin-left: 0 !important;
        margin-bottom: 0 !important;
      }

      blockquote + p {
        margin-top: 16px;
      }

      p:last-child {
        margin-bottom: 2px !important;
      }

      img {
        max-width: 200px;
        display: block;
        margin-bottom: var(--scp-widget-margin);
      }
    }
  }
}
</style>
