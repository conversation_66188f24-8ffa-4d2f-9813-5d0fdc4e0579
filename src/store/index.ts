import * as Vuex from 'vuex'

const state = {
  mail: 0,
  changeLogs: 0,
  currentPage: [],
  theme: 'light',
  maintainer: 'N',
  viewDetails: {
    id: '', // 通过对ID的监听, 来控制刷新表格数据
    visible: false,
    url: '',
    durl: '',
    title: '',
    columns: [],
    params: {}
  },
  viewChart: {
    title: '',
    visible: false,
    opts: {}
  },
  enableManualLogin: false,
  ckEditorUploading: false,
  pageHeight: 0
}
const mutations = {
  setMail (state, num) {
    state.mail = num
  },
  setTheme (state, theme) {
    state.theme = theme
  },
  setChangeLogs (state, num) {
    state.changeLogs = num
  },
  setCurrentPage (state, page) {
    state.currentPage = page
  },
  setMaintainer (state, maintainer) {
    state.maintainer = maintainer
  },
  setViewDetails (state, viewDetails) {
    state.viewDetails.visible = viewDetails.visible || false
    state.viewDetails.url = viewDetails.url || ''
    state.viewDetails.durl = viewDetails.durl || ''
    state.viewDetails.params = viewDetails.params || {}
    state.viewDetails.title = viewDetails.title || 'View Details'
    state.viewDetails.columns = viewDetails.columns || []
  },
  setViewDetailsID (state, id) {
    state.viewDetails.id = id
  },
  setViewDetailsVisible (state, visible) {
    state.viewDetails.visible = visible
  },
  setEnableManualLogin (state, manualLogin) {
    state.enableManualLogin = manualLogin
  },
  setViewChartFullscreen (state, viewChart) {
    state.viewChart.visible = viewChart.visible || false
    state.viewChart.title = viewChart.title || 'View Chart'
    state.viewChart.opts = viewChart.opts || {}
  },
  setCkEditorUploading (state, uploading) {
    state.ckEditorUploading = uploading
  },
  setPageHeight (state, height) {
    state.pageHeight = height
  }
}
const store = Vuex.createStore({
  state,
  mutations
})

export default store
