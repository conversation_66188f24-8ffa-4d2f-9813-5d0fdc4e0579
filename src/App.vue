<template>
  <div style="height:100%">
    <router-view/>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>
<style lang="scss">
/* 颜色字体等主题控制 */
@import "assets/css/theme.scss";
/* 布局css */
@import "assets/css/customer.scss";

body {
  margin: 0;
  font-size: .5rem;
  height: 100%;
  line-height: 0.83333rem;
  --el-font-size-base: 0.5rem;
  --el-component-size-small: 1.08333rem;
  --el-input-height: 1.08333rem;
  --el-border-radius-base: 0;
  --el-input-inner-height: var(--el-input-height, 24px);
  --el-font-size-small: var(--el-font-size-base);
}

html.dark {
  --el-bg-color-page: #0a0a0a;
}

.el-checkbox__label {
  --el-checkbox-font-size: 0.5rem;
}

.el-notification {
  --el-notification-title-font-size: 0.5rem;
}

.el-input {
  --el-border-radius-base: 0;
}
</style>
