import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import starter from './routers/starter'
import scp from './routers/scp'
import dia from './routers/dia'
import adm from './routers/adm'
import agt from './routers/agt'

const baseRoutes: Array<RouteRecordRaw> = []
const routes = baseRoutes.concat(starter, scp, dia, agt, adm, [{
  path: '/:catchAll(.*)',
  redirect: '/404'
}])

export default createRouter({
  history: createWebHashHistory(),
  routes
})
