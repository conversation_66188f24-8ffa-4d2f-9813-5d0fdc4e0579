import { RouteRecordRaw } from 'vue-router'

const routers: Array<RouteRecordRaw> = [{
  path: '/',
  component: () => import('@/components/starter/common/Menu.vue'),
  children: [{
    path: '/system',
    component: () => import('@/components/starter/common/Body.vue'),
    children: [{
      path: '/system/info',
      component: () => import('@/components/adm/monitor/SystemInfo.vue')
    },
    {
      path: '/system/access_logs',
      component: () => import('@/components/adm/monitor/AccessLogs.vue')
    },
    {
      path: '/system/cache_management',
      component: () => import('@/components/adm/monitor/CacheManagement.vue')
    },
    {
      path: '/system/sessions',
      component: () => import('@/components/adm/monitor/Sessions.vue')
    },
    {
      path: '/system/general_settings',
      component: () => import('@/components/adm/settings/GeneralSettings.vue')
    },
    {
      path: '/system/calendar',
      component: () => import('@/components/adm/settings/Calendar.vue')
    },
    {
      path: '/system/privileges',
      component: () => import('@/components/adm/settings/Privileges.vue')
    },
    {
      path: '/system/documentation',
      component: () => import('@/components/adm/document/Documentation.vue')
    },
    {
      path: '/system/mail',
      component: () => import('@/components/adm/document/Mail.vue')
    },
    {
      path: '/system/change_log',
      component: () => import('@/components/adm/monitor/ChangeLog.vue')
    },
    {
      path: '/system/demo',
      component: () => import('@/components/adm/others/Demo.vue')
    },
    {
      path: '/system/data_sync',
      component: () => import('@/components/adm/monitor/DataSync.vue')
    },
    {
      path: '/system/debug',
      component: () => import('@/components/adm/monitor/Debug.vue')
    },
    {
      path: '/system/broadcast',
      component: () => import('@/components/adm/monitor/Broadcast.vue')
    }]
  }]
}]

export default routers
