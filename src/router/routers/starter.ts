const routers = [
  {
    path: '/',
    redirect: '/redirect'
  },
  {
    path: '/afe6c103f31da8654539b176a551493f',
    component: () => import('@/components/Login.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/login',
    component: () => import('@/components/SSOLogin.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/login_and_redirect',
    component: () => import('@/components/SSOLoginAndRedirect.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/pingsso',
    component: () => import('@/components/starter/common/PingSSO.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/pingsso_callback',
    component: () => import('@/components/starter/common/PingSSOCallback.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/pingsso_failed',
    component: () => import('@/components/starter/common/PingSSOFailed.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/redirect',
    component: () => import('@/components/starter/common/Redirect.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/rlogin',
    component: () => import('@/components/starter/common/RemoteLogin.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/maintains',
    component: () => import('@/components/starter/common/Maintains.vue'),
    meta: {
      filter: false
    }
  },
  {
    path: '/',
    component: () => import('@/components/starter/common/Menu.vue'),
    children: [
      {
        path: '/404',
        component: () => import('@/components/starter/common/404.vue')
      },
      {
        path: '/public_documentation',
        component: () => import('@/components/starter/common/PublicDocumentation.vue')
      },
      {
        path: '/embed',
        component: () => import('@/components/starter/common/Frame.vue')
      },
      {
        path: '/home',
        component: () => import('@/components/starter/common/Body.vue'),
        children: [
          {
            path: '/home/<USER>',
            component: () => import('@/components/starter/home/<USER>')
          }
        ]
      },
      {
        path: '/users',
        component: () => import('@/components/starter/common/Body.vue'),
        children: [{
          path: '/users/my_favourites',
          component: () => import('@/components/starter/common/MyFavourites.vue')
        },
        {
          path: '/users/mail_signature',
          component: () => import('@/components/starter/common/MailSignature.vue')
        }
        ]
      },
      {
        path: '/components',
        component: () => import('@/components/starter/common/Body.vue'),
        children: [{
          path: '/components/wechat_bot_presentation',
          component: () => import('@/components/starter/private/WeChatBotPresentation.vue')
        }]
      },
      {
        path: '/components',
        component: () => import('@/components/starter/private/Components.vue'),
        children: [
          {
            path: '/components/table_viewer',
            component: () => import('@/components/starter/private/TableViewer.vue')
          },
          {
            path: '/components/material_owner',
            component: () => import('@/components/starter/private/MaterialOwner.vue')
          },
          {
            path: '/components/otds_official_report',
            component: () => import('@/components/starter/private/OtdsOfficialReport.vue')
          },
          {
            path: '/components/mr3_clo_target',
            component: () => import('@/components/starter/private/CloTarget.vue')
          },
          {
            path: '/components/sales_forcast',
            component: () => import('@/components/starter/private/SalesForcast.vue')
          },
          {
            path: '/components/slice_raw_data_manual',
            component: () => import('@/components/starter/private/SliceRawDataManual.vue')
          },
          {
            path: '/components/siop_all_bu_data',
            component: () => import('@/components/starter/private/SiopAllBuData.vue')
          },
          {
            path: '/components/consignment_time_limit',
            component: () => import('@/components/starter/private/ConsignmentTimeLimit.vue')
          },
          {
            path: '/components/mr3_product_group',
            component: () => import('@/components/starter/private/ProductGroupABC.vue')
          },
          {
            path: '/components/material_risk',
            component: () => import('@/components/starter/private/MaterialRisk.vue')
          }
        ]
      }]
  }
]

export default routers
