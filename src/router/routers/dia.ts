import { RouteRecordRaw } from 'vue-router'

const routers: Array<RouteRecordRaw> = [{
  path: '/',
  component: () => import('@/components/starter/common/Menu.vue'),
  children: [
    {
      path: '/diagnosis',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/diagnosis/workflow/maintenance',
          component: () => import('@/components/dia/workflow/WorkflowMaintenance.vue')
        },
        {
          path: '/diagnosis/workflow/distribution',
          component: () => import('@/components/dia/workflow/WorkflowDistribution.vue')
        },
        {
          path: '/diagnosis/workflow/tracking',
          component: () => import('@/components/dia/workflow/WorkflowTracking.vue')
        }
      ]
    },
    {
      path: '/diagnosis',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/diagnosis/my_planning_today/feedback',
          component: () => import('@/components/dia/mpt/Feedback.vue')
        },
        {
          path: '/diagnosis/my_planning_today/action_to_do',
          component: () => import('@/components/dia/mpt/ActionToDo.vue')
        },
        {
          path: '/diagnosis/my_planning_today/mpt_workspace',
          component: () => import('@/components/dia/mpt/MPTWorkspace.vue')
        },
        {
          path: '/diagnosis/my_planning_today/mpt_workspace/:id',
          component: () => import('@/components/dia/mpt/MPTWorkspace.vue'),
          meta: {
            filter: false
          }
        }
      ]
    }
  ]
}]

export default routers
