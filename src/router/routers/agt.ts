import { RouteRecordRaw } from 'vue-router'

const routers: Array<RouteRecordRaw> = [{
  path: '/',
  component: () => import('@/components/starter/common/Menu.vue'),
  children: [
    {
      path: '/agent',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/agent/rag/prompt_template',
          component: () => import('@/components/agt/rag/PromptTemplate.vue')
        },
        {
          path: '/agent/rag/user_persona',
          component: () => import('@/components/agt/rag/UserPersona.vue')
        },
        {
          path: '/agent/rag/agent_registration',
          component: () => import('@/components/agt/rag/AgentRegistration.vue')
        }
      ]
    }
  ]
},
{
  path: '/',
  component: () => import('@/components/starter/common/Menu.vue'),
  children: [
    {
      path: '/agent',
      component: () => import('@/components/starter/common/Body.vue'),
      children: [
        {
          path: '/agent/intelligent_agent/moss',
          component: () => import('@/components/agt/agent/Moss.vue')
        },
        {
          path: '/agent/intelligent_agent/noob_gpt',
          component: () => import('@/components/agt/agent/NoobGpt.vue')
        }
      ]
    }
  ]
}]

export default routers
