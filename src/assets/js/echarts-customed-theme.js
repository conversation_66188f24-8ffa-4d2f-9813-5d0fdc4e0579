import * as echarts from 'echarts/core'
function px2Rem (px) {
  const width = document.body.offsetWidth
  if (width <= 1400) {
    return px / 1.2
  }
  return px
}
(function (root, factory) {
  // eslint-disable-next-line no-undef
  if (typeof define === 'function' && define.amd) {
    // AMD. Register as an anonymous module.
    // eslint-disable-next-line no-undef
    define(['exports', 'echarts'], factory)
  } else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {
    // CommonJS
    factory(exports, require('echarts'))
  } else {
    // Browser globals
    factory({}, echarts)
  }
}(this, function (exports, echarts) {
  const log = function (msg) {
    if (typeof console !== 'undefined') {
      console && console.error && console.error(msg)
    }
  }

  if (!echarts) {
    log('ECharts is not Loaded')
    return
  }

  const _ScpTextColorPrimary = '#303133'
  const _ScpTextColorSecondary = '#909399'
  const _ScpBorderColor = '#cccccc'
  const _ScpBgColor = '#ffffff'

  echarts.registerTheme('customed', {
    textStyle: {
      fontSize: px2Rem(12)
    },
    color: [
      '#5470c6',
      '#73c0de',
      '#3ba272',
      '#91cc75',
      '#fac858',
      '#fc8452',
      '#ee6666',
      '#ea7ccc'
    ],
    backgroundColor: 'rgba(0, 0, 0, 0)',
    title: {
      textStyle: {
        color: _ScpTextColorPrimary,
        fontWeight: 'bold',
        fontSize: px2Rem(13)
      },
      subtextStyle: {
        color: _ScpTextColorSecondary
      }
    },
    line: {
      itemStyle: {
        borderWidth: 1
      },
      lineStyle: {
        width: 2
      },
      symbolSize: 2,
      symbol: 'circle',
      smooth: false
    },
    radar: {
      itemStyle: {
        borderWidth: 1
      },
      lineStyle: {
        width: 2
      },
      symbolSize: 4,
      symbol: 'emptyCircle',
      smooth: false
    },
    bar: {
      itemStyle: {
        barBorderWidth: 0,
        barBorderColor: _ScpBorderColor
      }
    },
    pie: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    scatter: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    boxplot: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    parallel: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    sankey: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    funnel: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    gauge: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      }
    },
    candlestick: {
      itemStyle: {
        color: '#eb5454',
        color0: '#47b262',
        borderColor: '#eb5454',
        borderColor0: '#47b262',
        borderWidth: 1
      }
    },
    graph: {
      itemStyle: {
        borderWidth: 0,
        borderColor: _ScpBorderColor
      },
      lineStyle: {
        width: 1,
        color: '#aaa'
      },
      symbolSize: 4,
      symbol: 'emptyCircle',
      smooth: false,
      color: [
        '#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc'
      ],
      label: {
        color: '#eee'
      }
    },
    map: {
      itemStyle: {
        areaColor: '#eee',
        borderColor: '#444',
        borderWidth: 0.5
      },
      label: {
        color: '#303133'
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(255,215,0,0.8)',
          borderColor: '#444',
          borderWidth: 1
        },
        label: {
          color: 'rgb(100,0,0)'
        }
      }
    },
    geo: {
      itemStyle: {
        areaColor: '#eee',
        borderColor: '#444',
        borderWidth: 0.5
      },
      label: {
        color: '#000'
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(255,215,0,0.8)',
          borderColor: '#444',
          borderWidth: 1
        },
        label: {
          color: 'rgb(100,0,0)'
        }
      }
    },
    categoryAxis: {
      axisLine: {
        show: true,
        lineStyle: {
          color: '#303133'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#303133'
        }
      },
      axisLabel: {
        show: true,
        color: '#303133',
        fontSize: px2Rem(12)
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: [
            '#E0E6F1'
          ]
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: [
            'rgba(250,250,250,0.2)',
            'rgba(210,219,238,0.2)'
          ]
        }
      }
    },
    valueAxis: {
      axisLine: {
        show: true,
        lineStyle: {
          color: '#303133'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#303133'
        }
      },
      axisLabel: {
        show: true,
        color: '#303133',
        fontSize: px2Rem(12)
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: [
            '#E0E6F1'
          ]
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: [
            'rgba(250,250,250,0.2)',
            'rgba(210,219,238,0.2)'
          ]
        }
      }
    },
    logAxis: {
      axisLine: {
        show: false,
        lineStyle: {
          color: '#303133'
        }
      },
      axisTick: {
        show: false,
        lineStyle: {
          color: '#303133'
        }
      },
      axisLabel: {
        show: true,
        color: '#303133'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: [
            '#E0E6F1'
          ]
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: [
            'rgba(250,250,250,0.2)',
            'rgba(210,219,238,0.2)'
          ]
        }
      }
    },
    timeAxis: {
      axisLine: {
        show: true,
        lineStyle: {
          color: '#303133'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#303133'
        }
      },
      axisLabel: {
        show: true,
        color: '#303133'
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: [
            '#E0E6F1'
          ]
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: [
            'rgba(250,250,250,0.2)',
            'rgba(210,219,238,0.2)'
          ]
        }
      }
    },
    toolbox: {
      iconStyle: {
        borderColor: _ScpTextColorSecondary
      },
      emphasis: {
        iconStyle: {
          borderColor: '#666'
        }
      }
    },
    legend: {
      textStyle: {
        color: _ScpTextColorPrimary,
        fontSize: px2Rem(12)
      }
    },
    tooltip: {
      appendToBody: true,
      borderRadius: 0,
      backgroundColor: _ScpBgColor,
      textStyle: {
        fontWeight: 'normal',
        fontSize: px2Rem(12),
        color: _ScpTextColorPrimary
      },
      axisPointer: {
        lineStyle: {
          color: _ScpBorderColor,
          width: 1
        },
        crossStyle: {
          color: _ScpBorderColor,
          width: 1
        }
      }
    },
    timeline: {
      lineStyle: {
        color: '#DAE1F5',
        width: 2
      },
      itemStyle: {
        color: '#A4B1D7',
        borderWidth: 1
      },
      controlStyle: {
        color: '#A4B1D7',
        borderColor: '#A4B1D7',
        borderWidth: 1
      },
      checkpointStyle: {
        color: '#316bf3',
        borderColor: 'fff'
      },
      label: {
        color: '#A4B1D7'
      },
      emphasis: {
        itemStyle: {
          color: '#FFF'
        },
        controlStyle: {
          color: '#A4B1D7',
          borderColor: '#A4B1D7',
          borderWidth: 1
        },
        label: {
          color: '#A4B1D7'
        }
      }
    },
    visualMap: {
      color: [
        '#bf444c',
        '#d88273',
        '#f6efa6'
      ]
    },
    dataZoom: {
      handleSize: 'undefined%',
      textStyle: {}
    },
    markPoint: {
      label: {
        color: '#eee'
      },
      emphasis: {
        label: {
          color: '#eee'
        }
      }
    }
  })
}))
