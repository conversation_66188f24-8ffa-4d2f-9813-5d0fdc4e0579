import { h, RectNode, RectNodeModel } from '@logicflow/core'
import global from './global'

/**
 * 关系&节点公共样式模板
 */
class RectBaseModel extends RectNodeModel {
  initNodeData (data) {
    data.text.x = data.x + global.width / 2
    data.text.y = data.y + 1
    super.initNodeData(data)
    this.height = global.height
    this.radius = 5
  }

  setAttributes () {
    this.width = 100
  }

  getNodeStyle () {
    const style = super.getNodeStyle()
    style.fill = '#a6bbcf'
    if (this.isSelected) {
      style.strokeWidth = 2
      style.stroke = '#ff7f0e'
    } else {
      style.strokeWidth = 1
      style.stroke = '#999'
    }
    return style
  }

  getDefaultAnchor () {
    const { x, y, id, width, height } = this
    return [
      {
        x: x + width / 2,
        y,
        id: `${id}_right`,
        type: 'right'
      },
      {
        x: x - width / 2,
        y,
        id: `${id}_left`,
        type: 'left'
      }
    ]
  }

  getOutlineStyle () {
    const style = super.getOutlineStyle()
    style.stroke = 'transparent'
    style.hover.stroke = 'transparent'
    return style
  }

  getTextStyle () {
    const style = super.getTextStyle()
    style.fontSize = global.fontSize
    return style
  }
}

class RectBase extends RectNode {
  getAnchorShape (anchorData) {
    const { x, y, type } = anchorData
    return h('rect', {
      x: x - 5,
      y: y - 5,
      width: 10,
      height: 10,
      className: 'custom-anchor'
    })
  }

  getIcon (viewBox, d) {
    const {
      width,
      height
    } = this.props.model
    return h('svg', {
      width: global.width - 8,
      height: global.height - 6,
      fill: '#fff',
      x: -width / 2 + 4.5,
      y: -height / 2 + 2.5,
      viewBox
    }, h('path', {
      d
    })
    )
  }

  getShape () {
    const {
      text,
      x,
      y,
      width,
      height,
      radius
    } = this.props.model
    const style = this.props.model.getNodeStyle()
    return h(
      'g',
      {
        className: 'lf-node'
      },
      [
        h('rect', {
          ...style,
          x: x - width / 2,
          y: y - height / 2,
          width,
          height,
          rx: radius,
          ry: radius
        }),
        h('g', {
          style: 'pointer-events: none;',
          transform: `translate(${x}, ${y})`
        }, [
          h('rect', {
            x: -width / 2,
            y: -height / 2,
            width: global.width,
            height: global.height,
            fill: '#000',
            fillOpacity: 0.05,
            stroke: 'none'
          }),
          this.getIcon(),
          h('path', {
            d: `M ${global.width - width / 2} ${1 - height / 2} l 0 ${global.height - 2}`,
            stroke: '#000',
            strokeOpacity: 0.1,
            strokeWidth: 1
          })
        ])
      ]
    )
  }
}

export default {
  type: 'node',
  view: RectBase,
  model: RectBaseModel
}
