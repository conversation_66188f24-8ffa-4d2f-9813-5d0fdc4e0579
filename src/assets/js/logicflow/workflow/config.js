import { getStyle } from '@/assets/js/logicflow/global'
import { definition as faTableList } from '@fortawesome/free-solid-svg-icons/faTableList'
import { definition as faMailBulk } from '@fortawesome/free-solid-svg-icons/faMailBulk'
import { definition as faFileDownload } from '@fortawesome/free-solid-svg-icons/faFileDownload'
import { definition as faFileContract } from '@fortawesome/free-solid-svg-icons/faFileContract'
import { definition as faTable } from '@fortawesome/free-solid-svg-icons/faTable'
import { definition as faRandom } from '@fortawesome/free-solid-svg-icons/faRandom'
import { definition as faStopCircle } from '@fortawesome/free-solid-svg-icons/faStopCircle'
import { definition as faArrowsSplitUpAndLeft } from '@fortawesome/free-solid-svg-icons/faArrowsSplitUpAndLeft'
import { definition as faCube } from '@fortawesome/free-solid-svg-icons/faCube'
import { definition as faCubes } from '@fortawesome/free-solid-svg-icons/faCubes'
import { definition as faDrawPolygon } from '@fortawesome/free-solid-svg-icons/faDrawPolygon'
import { definition as faDraftingCompass } from '@fortawesome/free-solid-svg-icons/faDraftingCompass'
import { definition as faListOl } from '@fortawesome/free-solid-svg-icons/faListOl'
import { definition as faShekelSign } from '@fortawesome/free-solid-svg-icons/faShekelSign'
import { definition as faJava } from '@fortawesome/free-brands-svg-icons/faJava'
import { definition as faNetworkWired } from '@fortawesome/free-solid-svg-icons/faNetworkWired'
import { definition as faCopy } from '@fortawesome/free-solid-svg-icons/faCopy'

const theme = ['#005eaa', '#E86819', '#21a366', '#d96570']

/*
  type: 节点类型名
  text: 显示文字
  fill: 填充色
  color: 文字色
  iconName: icon在fontawesome中的名称
  pathd: icon的SVG图
  viewBox: SVG viewBox
  properties: 节点默认属性

  properties: { // properties节点下, 非_output节点, 都是后台计算所需要的
    sql: 'select * from dual',
    _output: {    // 该节点只会影响前台显示, 后台计算并不会用到_output和其中的内容
      singles: [],
      lists: []
    }
  }
 */

const config = {
  tableInput: {
    type: 'table-input',
    text: 'Table Input',
    ...getStyle(faTableList, theme[0], '#fff'),
    properties: {
      sql: 'select * from dual',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  makeValue: {
    type: 'make-value',
    text: 'Make Value',
    ...getStyle(faCube, theme[0], '#fff'),
    properties: {
      key: '',
      value: '',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  makeText: {
    type: 'make-text',
    text: 'Make Text',
    ...getStyle(faCubes, theme[0], '#fff'),
    properties: {
      key: '',
      value: '',
      render: 'plain_text',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  mailOutput: {
    type: 'mail-output',
    text: 'Send Mail',
    ...getStyle(faMailBulk, theme[1], '#fff'),
    properties: {
      subject: '',
      method: 'MESSAGE',
      to: [],
      cc: [],
      groupby: [],
      body: '',
      attachement: [],
      appointment_date: '',
      appointment_start: '',
      appointment_end: '',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  fileOutput: {
    type: 'file-output',
    text: 'Save File',
    ...getStyle(faFileDownload, theme[1], '#fff'),
    properties: {
      filename: 'filename.xlsx',
      filekey: '',
      outputFields: [],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  logOutput: {
    type: 'log-output',
    text: 'Write to Log',
    ...getStyle(faFileContract, theme[1], '#fff'),
    properties: {
      level: 'DEBUG',
      template: '',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  tableOutput: {
    type: 'table-output',
    text: 'Table Output',
    ...getStyle(faTable, theme[1], '#fff'),
    properties: {
      targetTable: '',
      tableColumns: [],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  copyFile: {
    type: 'copy-file',
    text: 'Copy File',
    ...getStyle(faCopy, theme[1], '#fff'),
    properties: {
      type: 'smb',
      path: '',
      username: '',
      password: '',
      domain: 'APA',
      rename: 'none',
      files: [],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  priority: {
    type: 'priority',
    text: 'Priority',
    ...getStyle(faListOl, theme[2], '#fff'),
    properties: {
      priority: 0,
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  grouping: {
    type: 'grouping',
    text: 'Grouping',
    ...getStyle(faRandom, theme[2], '#fff'),
    properties: {
      groupby: [],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  stopIf: {
    type: 'stop-if',
    text: 'Stop If',
    ...getStyle(faStopCircle, theme[2], '#fff'),
    properties: {
      conditions: [{
        field: '',
        props: '.toString()',
        operator: '',
        value: ''
      }],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  switchCase: {
    type: 'switch-case',
    text: 'Switch Case',
    ...getStyle(faArrowsSplitUpAndLeft, theme[2], '#fff'),
    properties: {
      field: '',
      conditions: [{
        value: '',
        action: ''
      }],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  linearRegression: {
    type: 'linear-regression',
    text: 'Linear',
    ...getStyle(faDrawPolygon, theme[3], '#fff'),
    properties: {
      xAxis: '',
      yAxis: '',
      thresholdFrom: -100,
      thresholdTo: 100,
      outputPolicy: 'IN',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  callProc: {
    type: 'call-proc',
    text: 'Call Proc.',
    ...getStyle(faDraftingCompass, theme[3], '#fff'),
    properties: {
      targetProc: '',
      procArgs: [],
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  callWorkflow: {
    type: 'call-workflow',
    text: 'Call W.Flow',
    ...getStyle(faNetworkWired, theme[3], '#fff'),
    properties: {
      targetWorkflow: '',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  restfulClient: {
    type: 'restful-client',
    text: 'RESTful Cli.',
    ...getStyle(faShekelSign, theme[3], '#fff'),
    properties: {
      resKey: '',
      url: '',
      headers: [{
        key: '',
        value: ''
      }],
      body: '',
      _output: {
        singles: [],
        lists: []
      }
    }
  },
  javaCli: {
    type: 'java-client',
    text: 'JAVA Cli.',
    ...getStyle(faJava, theme[3], '#fff'),
    properties: {
      java: 'import java.util.*;\n' +
          'import com.dia.workflow.engine.log.WorkflowLogDespatcher;\n' +
          '/* 1. JavaClient这个名称请不要变更, 否则将无法编译\n' +
          '*  2. 如果需要引用其他jar包, 可以直接在最上方添加import或者在方法体中使用全名称\n' +
          '*     比如org.apache.commons.lang3.StringUtils.trim()\n' +
          '*  3. import只可以引用scp-service中已有的jar包\n' +
          '*     可以使用在方法中执行System.out.println(System.getProperty("java.class.path"))来查看可用jar包\n' +
          '*  4. 因为编写空间有限, 如果必须使用多个类, 请使用内部类来完成\n' +
          '*  5. 除了主类(JavaClient)和_process/_outputVarient方法不可修改外, 可以自由添加任意方法和内部类\n' +
          '*/\n' +
          'public class JavaClient {\n' +
          '    /* 入口函数\n' +
          '    * 1. singleValues指的是前序步骤传递过来的单值参数, 可以使用singleValues.get(key)来获取\n' +
          '    * 2. tableValues指的是前序步骤传递过来的列表参数, 可以直接遍历使用\n' +
          '    * 3. 如果需要向后续步骤传递参数, 可以使用_outputSingleValues或_outputTableValues执行结果输出\n' +
          '    * 4. 不要修改_process方法的名称和参数列表\n' +
          '    * 5. 可以使用logger.debug(String), logger.info(String), logger.warning(String), logger.error(String)来输出日志到控制台\n' +
          '    */\n' +
          '    public void _process(Map<String, Object> singleValues, List<LinkedHashMap<String, Object>> tableValues, WorkflowLogDespatcher logger){\n' +
          '        // add your code here\n' +
          '    }\n' +
          '    \n' +
          '    // 为了防止错误调用, 再输出变量时, 请使用_outputVarient执行变量输出操作\n' +
          '    // 原则上是不允许删除singleValues中的变量名, 也不应该与前序变量名产生冲突, 否则会造成不可预料的错误\n' +
          '    private void _outputVarient(Map<String, Object> singleValues, String key, Object value){\n' +
          '        singleValues.put(key, value);\n' +
          '    }\n' +
          '    \n' +
          '    // 为了防止错误调用, 再输出变量时, 请使用_outputVarient执行变量输出操作\n' +
          '    private void _outputVarient(List<LinkedHashMap<String, Object>> tableValues, List<LinkedHashMap<String, Object>> newValues){\n' +
          '        tableValues.clear();\n' +
          '        tableValues.addAll(newValues);\n' +
          '    }\n' +
          '}',
      _output: {
        singles: [],
        lists: []
      }
    }
  }
}

export default {
  nodeMap: {
    Input: [
      config.tableInput,
      config.makeValue,
      config.makeText
    ],
    Flow: [
      config.priority,
      config.grouping,
      config.stopIf,
      config.switchCase
    ],
    Processing: [
      config.linearRegression,
      config.callProc,
      config.callWorkflow,
      config.restfulClient,
      config.javaCli
    ],
    Output: [
      config.mailOutput,
      config.fileOutput,
      config.copyFile,
      config.logOutput,
      config.tableOutput
    ]
  }
}
