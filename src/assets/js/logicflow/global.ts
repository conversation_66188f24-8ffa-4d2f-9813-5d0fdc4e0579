// 在不修改toProps属性的前提下, 复制值
// 仅复制display, filter和fillColor节点
export function copyProps (fromProps, toProps) {
  if (fromProps && fromProps.display) {
    toProps.display = deepClone(fromProps.display)
  } else {
    toProps.display = []
  }
  if (fromProps && fromProps.fillColor) {
    toProps.fillColor = deepClone(fromProps.fillColor)
  }
  const keys = Object.keys(toProps.filter)
  if (fromProps && fromProps.filter) {
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      toProps.filter[key] = fromProps.filter[key]
    }
  } else {
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      if (typeof toProps.filter[key] === 'string') {
        toProps.filter[key] = ''
      } else {
        toProps.filter[key] = []
      }
    }
  }
}

export interface SingleVarient {
  type: string,
  value: string,
  label: string,
  title: string
}

export interface ListVarient {
  value: string,
  label: string,
  title: string
}

function deepClone (obj) {
  const objClone = Array.isArray(obj) ? [] : {}
  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 判断obj子元素是否为对象，如果是，递归复制
        if (obj[key] && typeof obj[key] === 'object') {
          objClone[key] = deepClone(obj[key])
        } else {
          // 如果不是，简单复制
          objClone[key] = obj[key]
        }
      }
    }
  } else {
    return obj
  }
  return objClone
}

export function isJSONString (str) {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str)
      return !!(typeof obj === 'object' && obj)
    } catch (e) {
      return false
    }
  } else {
    return false
  }
}

/**
 * 找出图中当前节点前序的, 所有节点的输出字段
 * 节点输出字段保存在properties > _output
 * 将properties > _output的数据合并输出
 *
 * @param id 当前节点ID
 * @param graph 整个图的结构, 用来查询前置节点
 * @param params 全局参数
 * @returns {*|*[]}
 */
export function getPreVarient (id, graph, params) {
  const result = {
    singles: [] as Array<SingleVarient>,
    lists: [] as Array<ListVarient>
  }

  // 加载全局参数
  if (isJSONString(params)) {
    const paramsObj = JSON.parse(params)
    const keys = Object.keys(paramsObj)
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      if (typeof paramsObj[key] !== 'object') {
        result.singles.push({
          type: 'global-variant',
          value: '$' + key,
          label: '$' + key,
          title: 'Parameter: ' + paramsObj[key]
        })
      } else if (typeof paramsObj[key] === 'object') {
        if (Array.isArray(paramsObj[key])) {
          if (paramsObj[key].length > 0) {
            const obj0 = paramsObj[key][0]
            if (typeof obj0 === 'object' && Array.isArray(obj0) === false) {
              const objKeys = Object.keys(obj0)
              for (let i = 0; i < objKeys.length; i++) {
                const objKey = objKeys[i]
                result.lists.push({
                  value: '$' + objKey,
                  label: '$' + objKey,
                  title: 'Parameter: ' + paramsObj[key][objKey]
                })
              }
            }
          }
        }
      }
    }
  }

  // 配置任务属性
  result.singles.push({
    type: 'global-variant',
    value: '_CURRENT_WORKFLOW_ID',
    label: '_CURRENT_WORKFLOW_ID',
    title: graph['workflow-id']
  })

  const edges = graph.edges
  const matchedNodes = [] as Array<string>
  const tempNodes = [id] // 存放所有符合条件的targetNodeId
  let tempLength = -1 // 控制循环的变量
  // 希望你能理解, 因为我写的, 我再回头看都看不懂 ( ^_^ )
  // 遍历edges找到targetNodeId in tempNodes的所有sourceNodeId
  do {
    tempLength = tempNodes.length
    for (let i = 0; i < edges.length; i++) {
      const tid = edges[i].targetNodeId
      if (tempNodes.indexOf(tid) !== -1) {
        if (matchedNodes.indexOf(edges[i].sourceNodeId) === -1) {
          matchedNodes.push(edges[i].sourceNodeId)
        }
        if (tempNodes.indexOf(edges[i].sourceNodeId) === -1) {
          tempNodes.push(edges[i].sourceNodeId)
        }
      }
    }
    // 遍历一次如果targetNodeId没有增加, 说明整个图中没有更多符合条件的内容了
  } while (tempLength !== tempNodes.length)

  const nodes = graph.nodes
  const nodeMap = {}
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i]
    nodeMap[node.id] = node
  }
  matchedNodes.reverse()
  for (let i = 0; i < matchedNodes.length; i++) {
    const node = nodeMap[matchedNodes[i]]
    // singles 永远是附加在后面
    result.singles.push(...(node.properties._output || { singles: [] }).singles)
    // lists只要获取到最新的, 就会清空以前的
    if ((node.properties._output || { lists: [] }).lists.length > 0) {
      result.lists = (node.properties._output || { lists: [] }).lists
    }
  }
  // 保险起见与原数据脱敏
  return deepClone(result)
}

export function getStyle (_componenet, _fill, _color) {
  return {
    fill: _fill,
    color: _color,
    iconPrefix: _componenet.prefix,
    iconName: _componenet.iconName,
    pathd: _componenet.icon[4],
    viewBox: [0, 0, _componenet.icon[0], _componenet.icon[1]].join(' ')
  }
}

export default {
  height: 24, // Node icon 高度
  width: 26, // Node icon 宽度
  fontSize: 10 // 字体大小
}
