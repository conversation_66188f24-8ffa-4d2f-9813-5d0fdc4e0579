import _mqtt from 'mqtt'
import useClipboard from 'vue-clipboard3'

const { toClipboard } = useClipboard()

export default {
  $thousandBitSeparator: thousandBitSeparator,
  $toggleFullScreen: toggleFullScreen,
  $isFullScreen: isFullScreen,
  $overrideF11: overrideF11,
  $firstCharUpperCase: firstCharUpperCase,
  $downloadFile: downloadFile,
  $converColumnName: converColumnName,
  $dateFormatter: dateFormatter,
  $px2Rem: px2Rem,
  $rem2Px: rem2Px,
  $calcMonthGap: calcMonthGap,
  $renderColumnName: renderColumnName,
  $addMonth: addMonth,
  $shortenNumber: shortenNumber,
  $startWith: startWith,
  $endWith: endWith,
  $randomString: randomString,
  $join: join,
  $renderColumnAsNumber: renderColumnAsNumber,
  $renderColumnAsPositiveNumber: renderColumnAsPositiveNumber,
  $addDay: addDay,
  $getNext25Months: getNext25Months,
  $convertDateStr: convertDateStr,
  $isEmpty: isEmpty,
  $autoCompleteSuggestion: autoCompleteSuggestion,
  $selectValidator: selectValidator,
  $deepClone: deepClone,
  $distinctArray: distinctArray,
  $intersection: intersection,
  $setDefaultMaterialOwner: setDefaultMaterialOwner,
  $renderTdTitle: renderTdTitle,
  $toFixed: toFixed,
  $watermark: watermark,
  $redirectHttp2Https: redirectHttp2Https,
  $copyText: copyText,
  $splitTextareaValueIntoArray: splitTextareaValueIntoArray,
  $getPivotColumnByFilter: getPivotColumnByFilter,
  $viewDetails: viewDetails,
  $toPixel: toPixel,
  $isDate: isDate,
  $delHtmlTag: delHtmlTag
}

function getPivotColumnByFilter (filterOpts : Array<any>, ...additionOpts : any[]) {
  const result: any[] = []
  result.push(...filterOpts.map((e) => e.value))
  result.push(...additionOpts.map((e) => e))

  return Array.from(new Set(result)).sort()
}

// 将px, vh, vw等转义为像素
function toPixel (anyUnitOfLength) {
  if (!anyUnitOfLength) {
    return anyUnitOfLength
  } else if (typeof anyUnitOfLength === 'number') {
    return anyUnitOfLength
  } else if (anyUnitOfLength.endsWith('px')) {
    return parseInt(anyUnitOfLength.replace('px', ''))
  } else if (anyUnitOfLength.endsWith('vh')) {
    const vh = anyUnitOfLength.replace('vh', '')
    return document.documentElement.clientHeight * parseInt(vh) / 100
  } else if (anyUnitOfLength.endsWith('vw')) {
    const vw = anyUnitOfLength.replace('vw', '')
    return document.documentElement.clientWidth * parseInt(vw) / 100
  } else {
    return parseInt(anyUnitOfLength)
  }
}

export interface ViewDetailConfig {
  title?: string | Function,
  url: string | Function,
  durl?: string | Function,
  params?: object | Function,
  columns?: object | Function
}

function viewDetails (config: ViewDetailConfig) {
  if (config && config.url) {
    // @ts-ignore
    window.$vueApp.config.globalProperties.$store.commit('setViewDetails', {
      visible: true,
      url: config.url instanceof Function ? config.url.apply(null) : config.url,
      durl: config.durl instanceof Function ? config.durl.apply(null) : config.durl,
      title: config.title instanceof Function ? config.title.apply(null) : config.title,
      params: config.params instanceof Function ? config.params.apply(null) : config.params,
      columns: config.columns instanceof Function ? config.columns.apply(null) : config.columns
    })
    // @ts-ignore
    window.$vueApp.config.globalProperties.$store.commit('setViewDetailsID', randomString(16))
  } else {
    // @ts-ignore
    window.$vueApp.config.globalProperties.$message({
      type: 'success',
      dangerouslyUseHTMLString: true,
      message: 'View details is unavailable in the report.',
      iconClass: 'el-message__icon el-icon-loading color-white',
      customClass: 'el-message el-message--success',
      duration: 3000
    })
  }
}

function splitTextareaValueIntoArray (val) {
  val = val.replace(/，/g, ' ') // 将输入框内的逗号替换为空格
  val = val.replace(/,/g, ' ') // 将输入框内的逗号替换为空格
  val = val.replace(/\t/g, ' ') // 将输入框内的tab替换为空格
  val = val.replace(/\r/g, '\n') // 将\r换行替换为\n
  while (val.indexOf('  ') !== -1) { // 移除两个以上的空格
    val = val.replace(/ {2}/g, ' ')
  }
  while (val.indexOf('\n\n') !== -1) { // 移除两个以上的换行
    val = val.replace(/\n{2}/g, '\n')
  }
  return val.split('\n') // 输出数组
}

async function copyText (msg) {
  try {
    // 复制
    await toClipboard(msg)
    // 复制成功
  } catch (e) {
    // 复制失败
  }
}

function thousandBitSeparator (num, precision) {
  if (precision !== undefined && precision !== null && num && isNaN(num) === false) {
    num = parseFloat(parseFloat(num).toFixed(precision))
  }
  return num && (num.toString().indexOf('.') !== -1
    ? num.toString().replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
      return $1 + ','
    })
    : num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, function ($0, $1) {
      return $1 + ','
    }))
}

function overrideF11 (e) {
  if (e.key === 'F11') {
    toggleFullScreen()
    e.preventDefault()
  }
}

function distinctArray (array, key) {
  const result:any[] = []
  for (let i = 0; i < array.length; i++) {
    const _key = key ? array[i][key] : array[i] as any
    if (result.indexOf(_key) === -1) {
      result.push(_key)
    }
  }
  return result
}

function firstCharUpperCase (data) {
  data = data.toLowerCase()
  return data ? data.replace(data.charAt(0), data.charAt(0).toUpperCase()) : ''
}

function downloadFile (url, param, callback, opts) {
  let _client:any = {}
  const _messageid = '_' + randomString(8)
  const _mqtttopic = 'scp/dss/ui/download/' + (localStorage.getItem('username') || '').toLowerCase()
  // @ts-ignore
  const $message = window.$vueApp.config.globalProperties.$message
  // @ts-ignore
  const $axios = window.$vueApp.config.globalProperties.$axios

  function _connectMqtt () {
    _client = _mqtt.connect('wss://scp-dss.cn.schneider-electric.com:61615/mqtt', {
      clientId: 'scp-ui-download-' + (localStorage.getItem('username') || '').toLowerCase() + '-' + randomString(4)
    })
    _client.on('connect', e => {
      _client.subscribe(_mqtttopic, { qos: 2 }, (err) => {
        if (!err) {
          console.log('subscribe topic: ' + _mqtttopic)
        }
      })
    })

    _client.on('end', e => {
      console.log('download mqtt client closed.')
    })

    _client.on('message', (topic, message) => {
      const messageDom : any = document.getElementById(_messageid)
      if (messageDom) {
        messageDom.innerHTML = message + ''
      }
    })
  }

  const message = $message({
    type: 'success',
    dangerouslyUseHTMLString: true,
    message: '<span id="' + _messageid + '">Sending request, please wait...</span> ',
    iconClass: 'el-message__icon el-icon-loading color-white',
    customClass: 'el-message el-message--success',
    duration: 0
  })

  let result = true
  _connectMqtt()
  $axios({
    method: 'post',
    url,
    data: param || {},
    responseType: 'blob',
    headers: {
      'download-topic': _mqtttopic
    }
  }).then(function (res) {
    opts = opts || {}
    const type = opts.type || 'application/vnd.ms-excel'
    const blob = new Blob([res.data], {
      type
    })
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = (e: any) => {
      const a:HTMLAnchorElement = document.createElement('a')
      a.style.display = 'none'
      a.href = e.target.result || ''
      a.download = res.headers.filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      message.close()
    }
  }).catch((error) => {
    console.log(error)
    result = false
    message.close()
    $message.error('Error occurred, download interrupted!')
  }).finally(() => {
    _client.end()
    if (callback) {
      if (callback) {
        callback(result)
      }
    }
  })
}

function isFullScreen () {
  // @ts-ignore
  return document.fullscreen || document.mozIsFullScreen || document.webkitIsFullScreen
}

function toggleFullScreen () {
  if (isFullScreen()) {
    exitFullScreen()
  } else {
    openFullscreen()
  }
}

function exitFullScreen () {
  const el = document
  // @ts-ignore
  const cfs = el.cancelFullScreen || el.webkitCancelFullScreen || el.mozCancelFullScreen || el.exitFullScreen
  let wscript

  if (typeof cfs !== 'undefined' && cfs) {
    cfs.call(el)
    return
  }

  // @ts-ignore
  if (typeof window.ActiveXObject !== 'undefined') {
    // eslint-disable-next-line no-undef
    wscript = new ActiveXObject('WScript.Shell')
    if (wscript != null) {
      wscript.SendKeys('{F11}')
    }
  }
}

function openFullscreen () {
  const el = document.documentElement
  // @ts-ignore
  const rfs = el.requestFullScreen || el.webkitRequestFullScreen || el.mozRequestFullScreen || el.msRequestFullScreen
  let wscript

  if (typeof rfs !== 'undefined' && rfs) {
    rfs.call(el)
    return
  }

  // @ts-ignore
  if (typeof window.ActiveXObject !== 'undefined') {
    // eslint-disable-next-line no-undef
    wscript = new ActiveXObject('WScript.Shell')
    if (wscript) {
      wscript.SendKeys('{F11}')
    }
  }
}

function converColumnName (name) {
  const n = name.split('_')
  for (let i = 0; i < n.length; i++) {
    n[i] = n[i].slice(0, 1).toUpperCase() + n[i].slice(1).toLowerCase()
  }
  return n.join(' ')
}

function dateFormatter (date, fmt) {
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
  }
  return fmt
}

function px2Rem (px) {
  const width = document.body.offsetWidth
  if (width <= 1400) {
    return px / 1.2
  }
  return px
}

function toFixed (data, precision) {
  if (isNaN(data)) {
    return data
  } else {
    const d = data.toFixed(precision)
    return parseFloat(d)
  }
}

function rem2Px (rem) {
  const width = document.body.offsetWidth
  if (width <= 1400) {
    return rem * 20
  }
  return rem * 24
}

const UNIQUE_NAME = ['CD', 'WD', 'RCA', 'BU', 'UU', 'GIT', 'SOH', 'SO', 'PO', 'MO', 'UD', 'AMU', 'AMF',
  'MM', 'MVP', 'MRP', 'MRPCN', 'GR', 'LA', 'MB', 'ID', 'IP', 'SS', 'PL', 'PF', 'SE', 'IG', 'OG', 'MKT',
  'FCST', 'CRD', 'PR', 'AB', 'HFM', 'KG', 'SAP', 'in', 'of', 'at', 'on', 'to', 'HS', 'LT', 'SS2', 'SS3',
  'DB', 'BTN', 'MOQ', 'SP', 'ST', 'ERP', 'JNI', '1W', '2W', '3W', '4W', '5W', '6W', 'ADF', 'ADU', 'ABC',
  'DC', 'FS', 'GRA', 'OS', 'ABLA', 'ETA', 'OTDS', 'OTDC', 'OTDT', 'OTDT', 'RMB', 'WIP', 'GI', 'AC2', 'FMR',
  'OI', 'INV', 'HKD', 'CB', 'CRDD', 'MPG', 'STPO', 'STSO', 'CUST', 'VEND', 'PLNT', 'STVP', 'BOM', 'BCD',
  'CC', 'MTD', 'QI', 'KPI', 'SESA', 'CNDC', 'MPQ', 'PIR', 'MRP1', 'PDT', 'vs', 'CPU', 'PGA', 'SQL', 'SID',
  'SPID', 'OneMM', 'DSS', 'CLO', 'ETO', 'GIN', 'DIN', 'NIN', 'XBoard']

function renderColumnName (name) {
  if (name) {
    name = name.replace(/ /g, '_')
    const cs = name.split('_')
    for (const i in cs) {
      if (cs.hasOwnProperty(i) === false) {
        continue
      }

      cs[i] = cs[i].slice(0, 1).toUpperCase() + cs[i].slice(1).toLowerCase()
      for (let j = 0; j < UNIQUE_NAME.length; j++) {
        const un = UNIQUE_NAME[j]
        if (un.toLowerCase() === cs[i].toLowerCase()) {
          cs[i] = un
        }
      }
    }
    return cs.join(' ')
  }
  return name
}

/**
 * 计算月份差
 * @param startMonthStr 202001
 * @param endMonthStr 202012
 * @returns {string|number}
 */
function calcMonthGap (startMonthStr, endMonthStr) {
  const startMonth = parseInt(startMonthStr)
  const endMonth = parseInt(endMonthStr)
  if (startMonth > endMonth) {
    return 0
  }

  if (endMonth - startMonth < 12) {
    return endMonth - startMonth
  }

  const gapMonth = endMonth - startMonth
  const gapYear = Math.ceil(gapMonth / 120.0)

  return (gapMonth - 88 * gapYear).toFixed(0) // gapYear * 12 - (100 * gapYear - gapMonth) 的简写
}

/**
 * 在orgMonth基础上加上N个月
 * @param orgMonth 原始月份 202001
 * @param n 需要增加的月, 可以为负数
 */
function addMonth (orgMonth, n) {
  let year = parseInt(orgMonth.substring(0, 4))
  let month = parseInt(orgMonth.substring(4, 6))
  month += n

  if (month > 0 && month < 13) {
    return year + (month < 10 ? '0' + month : month + '')
  } else {
    const y = parseInt(month / 12 + '')
    year += y
    month -= y * 12
    if (month < 1) {
      year--
      month += 12
    }
    return year + (month < 10 ? '0' + month : month + '')
  }
}

function shortenNumber (number, precision) {
  if (isNaN(number)) {
    return number
  }
  if (precision === undefined) {
    precision = 1
  }
  if (number === 0) {
    return number
  }
  const s = number < 0
  number = Math.abs(number)
  let result = 0
  let unit = ''
  if (number < 1000) {
    result = number
    if ((number + '').indexOf('.') === -1) { // 如果没有单位, 数字是整数, 那么不进行小数点约束
      precision = 0
    }
  } else if (number < 1000000) {
    result = number / 1000.0
    unit = 'K'
  } else if (number >= 1000000 && number < 10000000000) {
    result = number / 1000000.0
    unit = 'M'
  } else if (number >= 10000000000) {
    result = number / 1000000000.0
    unit = 'B'
  }

  if (s) {
    return thousandBitSeparator((result * -1), precision) + unit
  } else {
    return thousandBitSeparator(result, precision) + unit
  }
}

function startWith (string, start) {
  if (start === null || start === '' || string === null || string.length === 0 || start.length > string.length) {
    return false
  }
  return string.substr(0, start.length) === start
}

function endWith (string, end) {
  if (end === null || end === '' || string === null) {
    return false
  }
  string += ''
  if (string.length === 0 || end.length > string.length) {
    return false
  }
  return string.substr(string.length - end.length) === end
}

function randomString (len) {
  len = len || 4
  const $chars = 'abcdefhijkmnprstwxyz2345678'
  /** **默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  const maxPos = $chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}

function join () {
  const r:any[] = []
  for (let i = 0; i < arguments.length; i++) {
    if (arguments[i]) {
      r.push(arguments[i])
    }
  }
  return r.join(', ')
}

function renderColumnAsNumber (hotInstance, td, row, column, prop, value, precision, validate) {
  precision = precision || 0
  validate = validate || false
  td.style.textAlign = 'right'
  if (value) {
    if (validate) {
      if (isNaN(value) === false) {
        td.innerHTML = thousandBitSeparator(value, precision)
      } else {
        td.className = 'htInvalid'
        td.innerHTML = value
      }
    } else {
      td.innerHTML = thousandBitSeparator(value, precision)
    }
  } else {
    td.innerHTML = value
  }
}

function renderColumnAsPositiveNumber (hotInstance, td, row, column, prop, value, precision, validate) {
  precision = precision || 0
  validate = validate || false
  td.style.textAlign = 'right'
  if (value) {
    if (validate) {
      if (isNaN(value) === false && value >= 0) {
        td.innerHTML = thousandBitSeparator(value, precision)
      } else {
        td.className = 'htInvalid'
        td.innerHTML = value
      }
    } else {
      td.innerHTML = thousandBitSeparator(value, precision)
    }
  } else {
    td.innerHTML = value
  }
}

function addDay (date, n, fmt) { // 计算从今天起第几天以后的日期，正数为今天以后的日期，负数为今天之前的日期，0为当天
  fmt = fmt || 'yyyy/MM/dd'
  date.setDate(date.getDate() + n)
  return dateFormatter(date, fmt)
}

function getNext25Months (version) {
  if ((!version) || version.length !== 6) {
    return []
  }
  const result:string[] = []
  for (let i = 0; i < 25; i++) {
    result.push(convertDateStr(version))
    version = addMonth(version, 1)
  }

  return result
}

/**
 * 将日期从01的格式转为JAN的格式
 * 或
 * 将日期从202001的格式转为JAN-20的格式
 */
function convertDateStr (dateStr):string {
  if (!dateStr) {
    return dateStr
  }
  let year = ''
  let month = ''
  if (dateStr.length === 2) {
    month = dateStr
  } else if (dateStr.length === 6) {
    year = dateStr.substring(2, 4)
    month = dateStr.substring(4, 6)
  } else {
    return dateStr
  }

  const m = parseInt(month)
  let result
  switch (m) {
    case 1:
      result = 'JAN'
      break
    case 2:
      result = 'FEB'
      break
    case 3:
      result = 'MAR'
      break
    case 4:
      result = 'APR'
      break
    case 5:
      result = 'MAY'
      break
    case 6:
      result = 'JUN'
      break
    case 7:
      result = 'JUL'
      break
    case 8:
      result = 'AUG'
      break
    case 9:
      result = 'SEP'
      break
    case 10:
      result = 'OCT'
      break
    case 11:
      result = 'NOV'
      break
    case 12:
      result = 'DEC'
      break
  }
  return year !== '' ? (result + '-' + year) : result
}

function isEmpty (val) {
  if (val === 0) {
    return false
  }
  return !val
}

function autoCompleteSuggestion (source, query, cb) {
  const suggestion = source.map(e => {
    return { value: e }
  })
  const results = query ? suggestion.filter(createAutoComplete(query)) : suggestion
  cb(results)
}

function createAutoComplete (query) {
  return (suggestion) => {
    return ((suggestion.value || '').toLowerCase().indexOf(query.toLowerCase()) === 0)
  }
}

function selectValidator (selectValue, selectOptions, callback) {
  let valid = true
  if (selectOptions) {
    selectValue = selectValue || ''
    valid = selectOptions.indexOf(selectValue) !== -1
    callback(valid)
  } else {
    callback(valid)
  }
}

function deepClone (obj) {
  const objClone = Array.isArray(obj) ? [] : {}
  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 判断obj子元素是否为对象，如果是，递归复制
        if (obj[key] && typeof obj[key] === 'object') {
          objClone[key] = deepClone(obj[key])
        } else {
          // 如果不是，简单复制
          objClone[key] = obj[key]
        }
      }
    }
  } else {
    return obj
  }
  return objClone
}

function intersection (a, b) {
  const s = new Set(b)
  return [...new Set(a)].filter(x => s.has(x))
}

function setDefaultMaterialOwner (opts, filters, columnName = 'MATERIAL_OWNER_SESA') {
  opts = opts || []
  const owner = opts.filter(e => e.value === columnName)
  const user = (localStorage.getItem('username') || '').toUpperCase()
  if (owner && owner.length > 0) {
    const children = owner[0].children
    for (let i = 0; i < children.length; i++) {
      if (user === children[i].value) {
        filters.push([columnName, user])
      }
    }
  }
}

function renderTdTitle (hotInstance, td, row, column, prop, value, opts, titleMap) {
  const r = hotInstance.getSourceDataAtRow(row)
  if (titleMap[value]) {
    td.title = titleMap[value]
  } else {
    td.title = ''
  }
  if (typeof r[prop] !== 'undefined' && opts.indexOf(value) === -1) {
    td.className = 'htInvalid'
  }
  td.innerHTML = value
}

function redirectHttp2Https () {
  const url = window.location.href.toLowerCase()

  // 强制跳转到https, 如果希望禁用https, 只需要注释掉这个if
  if (url.indexOf('http://************') !== -1) {
    window.location.href = url.replace('http://************', 'https://scp-dss.cn.schneider-electric.com')
    return true
  }

  // 如果是测试环境, 强制跳转http
  if (url.indexOf('localhost') !== -1 || url.indexOf('************') !== -1 || url.indexOf('************') !== -1) {
    if (url.indexOf('https://') === 0) {
      window.location.href = url.replace('https://', 'http://')
      return true
    }
  } else {
    // 非测试环境, 强制跳转http
    if (url.indexOf('http://') === 0) {
      window.location.href = url.replace('http://', 'https://')
      return true
    }
  }

  return false
}

function watermark (settings) {
  // 默认设置
  const defaultSettings = {
    watermarl_element: 'body',
    watermark_txt: '',
    watermark_img: '',
    watermark_x: 20, // 水印起始位置x轴坐标
    watermark_y: 20, // 水印起始位置Y轴坐标
    watermark_rows: 9, // 水印行数
    watermark_cols: 10, // 水印列数
    watermark_x_space: 100, // 水印x轴间隔
    watermark_y_space: 50, // 水印y轴间隔
    watermark_color: '#000', // 水印字体颜色
    watermark_alpha: '0.07', // 水印透明度
    watermark_fontsize: '1rem', // 水印字体大小
    watermark_font: 'Arial', // 水印字体
    watermark_width: 210, // 水印宽度
    watermark_height: 80, // 水印长度
    watermark_angle: 35 // 水印倾斜度数
  }
  // 采用配置项替换默认值，作用类似jquery.extend
  if (arguments.length === 1 && typeof arguments[0] === 'object') {
    const src = arguments[0] || {}
    for (const key in src) {
      if (src[key] && defaultSettings[key] && src[key] === defaultSettings[key]) {

      } else if (src[key]) {
        defaultSettings[key] = src[key]
      }
    }
  }

  const oTemp = document.createDocumentFragment()

  const maskElement = document.documentElement

  // 获取页面最大宽度
  // eslint-disable-next-line camelcase
  const page_width = Math.max(maskElement.scrollWidth, maskElement.clientWidth)

  // 获取页面最大高度
  // eslint-disable-next-line camelcase
  const page_height = Math.max(maskElement.scrollHeight, maskElement.clientHeight)

  // 水印数量自适应元素区域尺寸
  // eslint-disable-next-line camelcase
  defaultSettings.watermark_cols = Math.floor(page_width / (defaultSettings.watermark_x_space + defaultSettings.watermark_width))
  // eslint-disable-next-line camelcase
  defaultSettings.watermark_rows = Math.floor(page_height / (defaultSettings.watermark_y_space + defaultSettings.watermark_height))
  let x
  let y
  for (let i = 0; i < defaultSettings.watermark_rows; i++) {
    y = defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i
    for (let j = 0; j < defaultSettings.watermark_cols; j++) {
      x = defaultSettings.watermark_x + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j
      // eslint-disable-next-line camelcase
      const maskDiv = document.createElement('div')
      // eslint-disable-next-line camelcase
      const maskImg = document.createElement('img')
      maskDiv.id = 'mask_div' + i + j
      maskDiv.className = 'mask_div'
      maskImg.className = 'mask_img'
      // mask_div.appendChild(document.createTextNode(defaultSettings.watermark_txt));
      maskDiv.innerHTML = (defaultSettings.watermark_txt)
      maskImg.src = defaultSettings.watermark_img
      maskImg.style.width = '150px'
      maskImg.style.height = '50px'

      // 空白图片会有占位,判断src为空时移除img标签  不为空时添加img
      if (defaultSettings.watermark_img === '') {
        maskImg.remove()
      } else {
        maskDiv.append(maskImg)
      }
      // 设置水印div倾斜显示
      maskDiv.style.webkitTransform = 'rotate(-' + defaultSettings.watermark_angle + 'deg)'
      maskDiv.style.transform = 'rotate(-' + defaultSettings.watermark_angle + 'deg)'
      maskDiv.style.visibility = ''
      maskDiv.style.position = 'absolute'
      maskDiv.style.left = x + 'px'
      maskDiv.style.top = y + 'px'
      maskDiv.style.overflow = 'hidden'
      maskDiv.style.zIndex = '9999'
      maskDiv.style.pointerEvents = 'none' // pointer-events:none  让水印不遮挡页面的点击事件
      maskDiv.style.opacity = defaultSettings.watermark_alpha
      maskDiv.style.fontSize = defaultSettings.watermark_fontsize
      maskDiv.style.fontFamily = defaultSettings.watermark_font
      maskDiv.style.color = defaultSettings.watermark_color
      maskDiv.style.textAlign = 'center'
      maskDiv.style.width = defaultSettings.watermark_width + 'px'
      maskDiv.style.height = defaultSettings.watermark_height + 'px'
      maskDiv.style.display = 'block'
      oTemp.appendChild(maskDiv)
    }
  }
  maskElement.appendChild(oTemp)
}

function isDate (dateStr) {
  // 日期格式不匹配
  const dateReg = /^(\d{4})\/(\d{2})\/(\d{2})$/
  if (!dateReg.test(dateStr)) {
    return false
  }

  const template = dateReg.exec(dateStr) || [0, 0, 0, 0]

  // 使用捕获组获取日期
  const year = parseInt(template[1] + '')
  const month = parseInt(template[2] + '') - 1
  const day = parseInt(template[3] + '')

  // 使用 Date() 对象，新建对象时会将日期转化为合法日期
  const dateObj = new Date(year, month, day)
  return year === dateObj.getFullYear() && month === dateObj.getMonth() && day === dateObj.getDate()
}

function delHtmlTag (myStr): string {
  if ((myStr === null) || (myStr === '')) {
    return ''
  } else {
    myStr = myStr.toString()
    return myStr.replace(/(<([^>]+)>)/ig, '') + ''
  }
}
