class CKEditorAdapter {
  constructor (loader) {
    this.loader = loader
  }

  async action (file) {
    const data = new FormData()
    data.append('file', file)
    const $axios = window.$vueApp.config.globalProperties.$axios
    const $store = window.$vueApp.config.globalProperties.$store

    let result = {}
    $store.commit('setCkEditorUploading', true)
    await $axios({
      method: 'post',
      url: '/system/save_image_to_local',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((body) => {
      result = body
    }).catch((error) => {
      console.log(error)
    }).finally(() => {
      $store.commit('setCkEditorUploading', false)
    })

    return {
      default: result
    }
  }

  upload () {
    return this.loader.file.then(file => {
      return this.action(file)
    })
  }
}

export default CKEditorAdapter
