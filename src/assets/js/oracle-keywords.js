const keywords = {
  keyword: [
    'SELECT',
    'FROM',
    'WHERE',
    'INNER JOIN',
    'LEFT JOIN',
    'RIGHT JOIN',
    'ON',
    'AND',
    'OR',
    'GROUP BY',
    'HAVING',
    'ORDER BY',
    'FETCH NEXT 10 ROWS ONLY',
    'ASC',
    'DESC',
    'BETWEEN',
    'BY',
    'CONNECT',
    'IN',
    'NOT',
    'IS NULL',
    'IS NOT NULL',
    'WITH',
    'SCP'
  ],
  function: [
    'MAX',
    'MIN',
    'AVG',
    'COUNT',
    'COUNT( ) OVER()',
    'COUNT( ) OVER(ORDER BY)',
    'COUNT( ) OVER(PARTITION BY ORDER BY)',
    'SUM',
    'SUM( ) OVER()',
    'SUM( ) OVER(ORDER BY)',
    'SUM( ) OVER(PARTITION BY ORDER BY)',
    'GREATEST',
    'LEAST',
    'LENGTH',
    'LOWER',
    'UPPER',
    'LTRIM',
    'RTRIM',
    'TRIM',
    'REPLACE',
    'SUBSTR',
    'ABS',
    'CEIL',
    'FLOOR',
    'ROUND',
    'ROUND( , 1)',
    'TRUNC',
    "TRUNC( , 'DD')",
    'TO_DATE',
    "TO_DATE( , 'YYYY/MM/DD')",
    "TO_DATE( , 'YYYY/MM/DD HH24:MI:SS')",
    'TO_NUMBER',
    'NVL',
    'SYSDATE',
    'LAST_DAY',
    'DECODE'
  ]
}
export default {
  ...keywords
}
