import { definition as faFileAlt } from '@fortawesome/free-solid-svg-icons/faFileAlt'
import Function from '@/assets/js/function'
// 可以将toolbox函数导出来, 供其他模块使用
export default {
  '$echarts.toolbox': toolbox,
  '$echarts.grid': grid,
  '$echarts.legend': legend,
  '$echarts.tooltip': tooltip,
  '$echarts.label.scaleLable': scaleLabel
}

function scaleLabel (params, totalLabel, maxDisplayLabel) {
  const dataIndex = params.dataIndex
  if (totalLabel > maxDisplayLabel) {
    const step = Math.ceil(totalLabel / maxDisplayLabel)
    if (dataIndex === 0 || dataIndex === totalLabel - 1 || dataIndex % (step + 1) === 0) {
      return params.data + ''
    } else {
      return ''
    }
  } else {
    return params.data + ''
  }
}

// config: {opts: ['line', 'stack'], view: {url: '', durl: '', params: {}}, custom: {}}
function toolbox (config) {
  config = config || {}
  const opts = config.opts || [] as Array<string>
  opts.sort()

  const view = config.view as any
  // @ts-ignore
  const $store = window.$vueApp.config.globalProperties.$store

  // 定义了一个默认的toolbox对象, 其中包含了几个基本的功能：
  // dataZoom（数据缩放）、
  // magicType（图表类型切换）和saveAsImage（保存图表为图片）
  const toolbox = {
    show: true,
    // left: -1, // 距离左侧
    // top: 0, // 距离顶部
    feature: {
      dataZoom: {
        show: false,
        title: {
          zoom: 'Zoom',
          back: 'Back'
        }
      },
      magicType: {
        type: [] as Array<string>
      },
      saveAsImage: {},
      myFullscreen: {
        show: true,
        title: '全屏查看',
        icon: 'M874.62 169.29c0-0.63-0.03-1.26-0.1-1.88-0.03-0.31-0.09-0.61-0.13-0.91-0.05-0.34-0.09-0.69-0.16-1.03-0.07-0.34-0.16-0.67-0.25-1.01-0.07-0.3-0.14-0.59-0.23-0.89-0.1-0.33-0.22-0.64-0.34-0.96-0.11-0.3-0.2-0.59-0.32-0.89-0.12-0.3-0.27-0.58-0.41-0.87-0.14-0.3-0.28-0.61-0.43-0.9-0.15-0.28-0.32-0.54-0.48-0.81-0.17-0.29-0.34-0.59-0.53-0.87-0.2-0.29-0.42-0.57-0.63-0.85-0.18-0.24-0.35-0.49-0.54-0.72-0.4-0.49-0.83-0.96-1.28-1.41l-0.05-0.05c-0.01-0.01-0.03-0.02-0.04-0.04-0.45-0.45-0.93-0.88-1.42-1.29-0.24-0.19-0.48-0.36-0.73-0.54-0.28-0.21-0.55-0.43-0.84-0.62-0.29-0.19-0.59-0.36-0.88-0.54-0.27-0.16-0.52-0.32-0.8-0.47-0.3-0.16-0.61-0.3-0.91-0.44-0.29-0.14-0.57-0.28-0.86-0.4-0.3-0.12-0.6-0.22-0.9-0.33-0.32-0.11-0.63-0.23-0.95-0.33-0.3-0.09-0.61-0.16-0.91-0.23-0.33-0.08-0.65-0.18-0.99-0.24-0.35-0.07-0.7-0.11-1.06-0.16-0.29-0.04-0.58-0.1-0.88-0.13-0.66-0.06-1.31-0.1-1.97-0.1H683.31c-11.05 0-20 8.95-20 20s8.95 20 20 20h123.03L583.51 412.2c-7.81 7.81-7.81 20.47 0 28.28 3.91 3.91 9.02 5.86 14.14 5.86s10.24-1.95 14.14-5.86l222.82-222.82v123.03c0 11.05 8.95 20 20 20s20-8.95 20-20V169.38c0.01-0.03 0.01-0.06 0.01-0.09zM412.2 583.51L189.38 806.34V683.31c0-11.05-8.95-20-20-20s-20 8.95-20 20v171.31c0 0.66 0.03 1.32 0.1 1.97 0.03 0.3 0.09 0.59 0.13 0.88 0.05 0.35 0.09 0.7 0.16 1.05 0.07 0.34 0.16 0.66 0.24 0.99 0.08 0.3 0.14 0.61 0.23 0.91 0.1 0.32 0.22 0.63 0.33 0.95 0.11 0.3 0.21 0.6 0.33 0.9 0.12 0.29 0.27 0.58 0.4 0.86 0.14 0.31 0.28 0.61 0.44 0.91 0.15 0.27 0.31 0.53 0.47 0.8 0.18 0.29 0.34 0.59 0.53 0.88 0.2 0.29 0.41 0.57 0.62 0.85 0.18 0.24 0.35 0.49 0.54 0.72 0.41 0.49 0.84 0.97 1.29 1.42 0.01 0.01 0.02 0.03 0.04 0.04l0.05 0.05c0.45 0.45 0.92 0.87 1.41 1.28 0.24 0.2 0.49 0.37 0.73 0.55 0.28 0.21 0.55 0.42 0.84 0.62 0.29 0.19 0.59 0.36 0.89 0.54 0.26 0.16 0.52 0.32 0.79 0.47 0.3 0.16 0.61 0.3 0.92 0.44 0.28 0.13 0.56 0.27 0.85 0.39 0.3 0.12 0.61 0.22 0.91 0.33 0.31 0.11 0.62 0.23 0.94 0.33 0.3 0.09 0.61 0.16 0.92 0.24 0.33 0.08 0.65 0.17 0.98 0.24 0.35 0.07 0.71 0.11 1.06 0.16 0.29 0.04 0.58 0.1 0.87 0.13 0.66 0.06 1.31 0.1 1.97 0.1h171.31c11.05 0 20-8.95 20-20s-8.95-20-20-20H217.66L440.49 611.8c7.81-7.81 7.81-20.47 0-28.29s-20.48-7.81-28.29 0zM854.62 663.31c-11.05 0-20 8.95-20 20v123.03L611.8 583.51c-7.81-7.81-20.47-7.81-28.29 0-7.81 7.81-7.81 20.47 0 28.29l222.82 222.82H683.31c-11.05 0-20 8.95-20 20s8.95 20 20 20h171.31c0.66 0 1.32-0.03 1.97-0.1 0.3-0.03 0.58-0.08 0.87-0.13 0.35-0.05 0.71-0.09 1.06-0.16 0.33-0.07 0.66-0.16 0.98-0.24 0.31-0.08 0.61-0.14 0.92-0.24 0.32-0.1 0.63-0.22 0.94-0.33 0.3-0.11 0.61-0.21 0.91-0.33 0.29-0.12 0.57-0.26 0.85-0.39 0.31-0.15 0.62-0.28 0.93-0.45 0.27-0.14 0.52-0.3 0.78-0.46 0.3-0.18 0.61-0.35 0.9-0.55 0.28-0.19 0.54-0.39 0.81-0.59 0.25-0.19 0.52-0.37 0.76-0.57 0.46-0.38 0.9-0.78 1.32-1.2 0.05-0.04 0.09-0.08 0.14-0.13 0.04-0.04 0.08-0.09 0.12-0.14 0.42-0.42 0.82-0.86 1.2-1.33 0.2-0.24 0.37-0.5 0.56-0.75 0.2-0.27 0.42-0.54 0.6-0.82 0.19-0.29 0.36-0.59 0.54-0.89 0.16-0.26 0.32-0.52 0.47-0.79 0.16-0.3 0.3-0.61 0.44-0.91 0.14-0.29 0.28-0.57 0.4-0.86 0.12-0.29 0.22-0.59 0.33-0.89 0.11-0.32 0.24-0.63 0.33-0.96 0.09-0.3 0.15-0.6 0.23-0.89 0.08-0.33 0.18-0.66 0.25-1.01 0.07-0.34 0.11-0.68 0.16-1.03 0.04-0.3 0.1-0.6 0.13-0.91 0.06-0.63 0.09-1.25 0.1-1.88V683.31c0-11.04-8.95-20-20-20zM217.66 189.38h123.03c11.05 0 20-8.95 20-20s-8.95-20-20-20H169.38c-0.66 0-1.32 0.03-1.97 0.1-0.3 0.03-0.59 0.08-0.88 0.13-0.35 0.05-0.7 0.09-1.05 0.16-0.34 0.07-0.66 0.16-0.99 0.24-0.3 0.08-0.6 0.14-0.9 0.23-0.33 0.1-0.64 0.22-0.96 0.33-0.3 0.11-0.6 0.2-0.89 0.33-0.3 0.12-0.58 0.27-0.87 0.41-0.3 0.14-0.6 0.28-0.9 0.43-0.28 0.15-0.54 0.32-0.81 0.48-0.29 0.17-0.58 0.34-0.86 0.53-0.3 0.2-0.58 0.43-0.87 0.64-0.23 0.17-0.47 0.34-0.7 0.52-1.02 0.84-1.96 1.77-2.79 2.79-0.19 0.23-0.35 0.46-0.52 0.69-0.22 0.29-0.44 0.57-0.64 0.88-0.19 0.28-0.35 0.57-0.52 0.86-0.16 0.27-0.33 0.54-0.48 0.82-0.16 0.3-0.29 0.6-0.43 0.9-0.14 0.29-0.28 0.58-0.41 0.87-0.12 0.29-0.22 0.59-0.33 0.89-0.11 0.32-0.24 0.63-0.33 0.96-0.09 0.3-0.16 0.6-0.23 0.9-0.08 0.33-0.18 0.66-0.24 1-0.07 0.35-0.11 0.7-0.16 1.05-0.04 0.3-0.1 0.59-0.13 0.88-0.06 0.66-0.1 1.31-0.1 1.97v171.31c0 11.05 8.95 20 20 20s20-8.95 20-20V217.66L412.2 440.49c3.91 3.91 9.02 5.86 14.14 5.86s10.24-1.95 14.14-5.86c7.81-7.81 7.81-20.47 0-28.28L217.66 189.38z',
        iconStyle: {
          color: 'var(--scp-text-color-secondary)',
          borderColor: 'var(--scp-text-color-secondary)',
          borderWidth: 0.5,
          borderType: 'solid'
        },
        onclick: function (e) {
          const opts = e.getOption()
          // 禁用所有自定义插件, 因为自定义插件会有显示和层叠问题
          const ks = Object.keys(opts.toolbox[0].feature)
          for (let i = 0; i < ks.length; i++) {
            if (ks[i].indexOf('my') === 0) {
              opts.toolbox[0].feature[ks[i]].show = false
            }
          }

          let title = ''
          if ((opts.title || []).length > 0) {
            opts.title[0].show = false
            title = opts.title[0].text
          }
          $store.commit('setViewChartFullscreen', {
            title,
            opts,
            visible: true
          })
        }
      },
      myViewDetails: {
        show: true,
        title: 'View Details',
        icon: faFileAlt.icon[4],
        iconStyle: {
          color: 'var(--scp-text-color-secondary)',
          borderColor: 'var(--scp-text-color-secondary)',
          borderWidth: 0.5,
          borderType: 'solid'
        },
        onclick: () => {
          Function.$viewDetails(view)
        }
      }
    }
  }

  const custom = config.custom
  if (custom) {
    for (const key in custom) {
      toolbox.feature['my' + key] = custom[key]
    }
  }

  // 参数列表（opts）中是否包含'stack'. 如果包含, 则在magicType.type数组中添加两种类型：'stack'堆叠和'tiled'平铺
  if (opts.indexOf('stack') !== -1) {
    toolbox.feature.magicType.type.push('stack', 'tiled')
  }
  if (opts.indexOf('no-details') !== -1) {
    toolbox.feature.myViewDetails.show = false
  }
  // 参数列表中是否包含'line'. 如果包含, 则设置dataZoom.show为true, 并在magicType.type数组中添加两种类型：'line'折线图和'bar'柱状图
  if (opts.indexOf('line') !== -1) {
    toolbox.feature.dataZoom.show = true
    toolbox.feature.magicType.type.push('line', 'bar')
  }

  return toolbox
}

function nvl (val, defaultValue) {
  if (val === 0) {
    return 0
  } else {
    return val || defaultValue
  }
}

function legend (config) {
  config = config || {}
  if (config.type === 'pie') {
    return {
      top: 'center',
      left: 'right',
      width: '35%',
      data: config.data
    }
  } else {
    return {
      data: config.data,
      type: 'scroll',
      y: 'bottom',
      selected: config.selected
    }
  }
}

function grid (config) {
  config = config || {}
  const type = config.type
  const top = config.top
  const left = config.left
  const right = config.right
  const bottom = config.bottom
  if (top === 0 || top || left === 0 || left || right === 0 || right || bottom === 0 || bottom) {
    return {
      top: Function.$px2Rem(nvl(top, 40)),
      right: Function.$px2Rem(nvl(right, 5)),
      bottom: Function.$px2Rem(nvl(bottom, 30)),
      left: Function.$px2Rem(nvl(left, 5)),
      containLabel: true
    }
  } else {
    switch (type) {
      case 'treemap':
        return {
          left: Function.$px2Rem(5),
          right: Function.$px2Rem(5),
          top: Function.$px2Rem(30),
          bottom: Function.$px2Rem(30)
        }
      default:
        return {
          top: Function.$px2Rem(40),
          right: Function.$px2Rem(5),
          bottom: Function.$px2Rem(30),
          left: Function.$px2Rem(5),
          containLabel: true
        }
    }
  }
}

// 计算running total
const calculateSumRatio = (data, targetName: any) => {
  const totalSum = data.reduce((acc, obj) => acc + (obj.value || 0), 0)
  const index = data.findIndex(obj => obj.name === targetName)
  if (index < 0) return 0
  const sumUpToTarget = data.slice(0, index + 1).reduce((acc, obj) => acc + (obj.value || 0), 0)
  return sumUpToTarget / totalSum
}

function tooltip (config, data) {
  config = config || {}
  let selectedCurrentLevel = ''
  let selectedParentLevel = ''
  const formatter = (params) => {
    const marker = params.marker
    const tip = [] as any

    tip.push('<div style="width: auto; min-width: 15.5rem">')
    tip.push('<div>')
    tip.push(marker)
    tip.push(params.name)
    tip.push('<span style="float: right">')
    tip.push(Function.$shortenNumber(params.value, undefined))
    tip.push('</span>')
    tip.push('</div>')

    let currentRatio = ''
    const lt = params.treePathInfo.length
    if (lt - 1 > 0 && lt > 2) {
      const index = data.findIndex(obj => obj.name === params.treePathInfo[lt - 2].name)
      if (index >= 0) {
        currentRatio = (calculateSumRatio(data[index].children, params.name) * 100).toFixed(1)
      }
    } else {
      currentRatio = (calculateSumRatio(data, params.name) * 100).toFixed(1)
    }

    tip.push('<div>')
    tip.push('Running Total')
    tip.push('<span style="float: right">')
    tip.push(currentRatio + '%')
    tip.push('</span>')
    tip.push('</div>')

    const treePathInfo = params.treePathInfo || []
    let levels = [] as any
    if (treePathInfo.length > 1) {
      const currentNode = treePathInfo[treePathInfo.length - 1]
      levels.push(currentNode.name)

      for (let i = treePathInfo.length - 2; i > -1; i--) {
        const parentNode = treePathInfo[i]
        levels.push(parentNode.name)

        tip.push('<div>')
        tip.push('Share [' + (parentNode.name === 'SO' ? 'Total' : parentNode.name) + ']')
        tip.push('<span style="float: right">')
        tip.push((currentNode.value / parentNode.value * 100).toFixed(1) + '%')
        tip.push('</span>')
        tip.push('</div>')
      }
    }
    levels = levels.slice(0, levels.length - 1)
    levels = levels.reverse()
    selectedCurrentLevel = levels.join(' > ')
    if (levels.length > 0) {
      levels = levels.slice(0, levels.length - 1)
      selectedParentLevel = levels.join(' > ')
    }
    if (typeof config.callback === 'function') {
      config.callback({
        selectedParentLevel,
        selectedCurrentLevel
      })
    }
    const tooltips = params.data.tips
    let first = true
    for (const k in tooltips) {
      if (!tooltips.hasOwnProperty(k)) {
        continue
      }
      if (first) {
        tip.push('<div style="border-top: 1px dotted #ccc;margin-top:5px">')
        first = false
      } else {
        tip.push('<div>')
      }

      tip.push(k.toUpperCase())
      tip.push('<span style="float: right">')
      tip.push(Function.$shortenNumber(tooltips[k], undefined))
      tip.push('</span>')
      tip.push('</div>')
    }
    tip.push('</div>')
    return tip.join('')
  }
  return {
    trigger: 'item',
    triggerOn: 'mousemove',
    formatter
  }
}
