.el-icon-success {
  font-size: 0.583rem
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

/*外层轨道。可以用display:none让其不显示，也可以添加背景图片，颜色改变显示效果*/

::-webkit-scrollbar-track {
  width: 7px;
  background-color: rgba(238, 238, 238, 0.5);
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}

/*滚动条的设置*/
::-webkit-scrollbar-thumb {
  background-color: rgba(222, 222, 222, 0.75);
  background-clip: padding-box;
  min-height: 28px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}

/*滚动条移上去的背景*/
::-webkit-scrollbar-thumb:hover {
  background-color: var(--scp-text-color-highlight);
}

.body-wrapper {
  background: var(--scp-bg-color);
  border-bottom: 1px dashed var(--el-border-color-extra-light);
  height: calc(100% - 55px);
  overflow-y: auto;
  min-height: 480px;

  .left-sidebar {
    width: 100%;

    .widget {
      background: var(--scp-bg-color);
      clear: both;
      margin-top: 0;
      margin-bottom: 0;

      .widget-header {
        border-bottom: 1px solid var(--scp-border-color);
        height: 1rem;
        line-height: 1rem;
        padding: 4px 10px 4px 12px;

        .title {
          float: left;
          font-weight: bold;
          font-size: 0.54rem;

          .attribution, .mini-title {
            font-size: 11px;
            padding-left: 4px;
            font-weight: normal;
          }
        }

        span.tools {
          padding: 0;
          float: right;
          margin: 0;
          cursor: pointer;

          > a {
            display: inline-block;
            margin-right: 5px;
            margin-top: 3px;

            &:hover {
              text-decoration: none;
              opacity: .6;
            }
          }
        }
      }

      .widget-body {
        padding: var(--scp-widget-margin);
      }

      .el-input__inner {
        background-color: transparent !important;
      }

      .el-range-input {
        background-color: transparent !important;
      }
    }
  }
}

.el-pagination {
  .btn-next {
    background-color: transparent !important;
  }

  .btn-prev {
    background-color: transparent !important;
  }

  .el-input__inner {
    background-color: transparent !important;
  }

  .el-select .el-input .el-input__inner {
    border-radius: 2px !important;
  }

  .el-pagination__total {
    height: 1.1rem;
    line-height: 1.1rem;
    font-size: 0.5rem !important;
  }

  button, span:not([class*=suffix]) {
    height: 1.1rem !important;
    line-height: 1.1rem !important;
  }

  .el-pager li {
    height: 1.1rem;
    line-height: 1.1rem;
    background-color: transparent;
  }
}

.el-pagination__sizes {
  .el-input--mini .el-input__inner {
    height: 1.1rem !important;
    line-height: 1.1rem !important;
  }

  .el-input .el-input__inner {
    font-size: 0.5rem !important;
  }
}

.handsontable th, .handsontable td {
  white-space: nowrap !important;
  text-overflow: ellipsis;
  border-color: var(--scp-border-color) !important;
}

.handsontable {
  .autocompleteEditor {
    .wtHider {
      position: fixed !important;
    }
  }
}

.el-dialog {
  border-radius: 0 !important;
}

.handle {
  width: 6px !important;
  height: 6px !important;
  background: #eee;
  border: 1px solid var(--scp-text-color-primary)
}

.handle-tl {
  top: -6px !important;
  left: -6px !important;
  cursor: nw-resize
}

.handle-tm {
  top: -6px !important;
  left: 50%;
  margin-left: -5px;
  cursor: n-resize
}

.handle-tr {
  top: -6px !important;
  right: -6px !important;
  cursor: ne-resize
}

.handle-ml {
  left: -6px !important;
  cursor: w-resize
}

.handle-mr {
  right: -6px !important;
  cursor: e-resize
}

.handle-bl {
  bottom: -6px !important;
  left: -6px !important;
  cursor: sw-resize
}

.handle-bm {
  bottom: -6px !important;
  left: 50%;
  margin-left: -5px;
  cursor: s-resize
}

.handle-br {
  bottom: -6px !important;
  right: -6px !important;
  cursor: se-resize
}

/* 修复多选框高度过高的问题 */
.el-select--medium .el-tag--small {
  height: 0.833rem !important;
  line-height: 0.75rem !important
}

.el-select__tags .el-select__input {
  height: 1rem;
}

.el-upload-list__item {
  font-size: 0.5rem !important;
}

.el-upload-list__item-name {
  padding-left: 0 !important;
}

.subscript-container.subscript-container-right {
  margin-left: calc(var(--scp-widget-margin) / 2);
}

.subscript-container.subscript-container-left {
  margin-right: calc(var(--scp-widget-margin) / 2);
}

.subscript-container {
  position: relative;
  padding: 5px;
  border: 1px solid var(--scp-border-color);
  background-color: var(--scp-bg-color);
  margin-bottom: var(--scp-widget-margin);

  .el-cascader__search-input {
    background-color: transparent !important;
  }

  .subscript-title {
    font-size: 0.54rem;
    font-weight: 700;
    padding: 5px 0 10px 5px;
    color: var(--scp-text-color-primary);
  }

  .subscript {
    color: var(--scp-bg-color);
    height: 12px;
    width: 100px;
    position: absolute;
    right: -54px;
    text-align: center;
    cursor: pointer;
    z-index: 800;
    background-color: #ff4c4c;
    -moz-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);

    .el-loading-spinner .circular {
      height: 30px !important;
    }
  }

  .subscript-langtime {
    color: var(--scp-bg-color);
    height: 12px;
    width: 100px;
    position: absolute;
    right: -54px;
    text-align: center;
    cursor: pointer;
    z-index: 800;
    background-color: var(--scp-text-color-highlight);
    -moz-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);

    .el-loading-spinner .circular {
      height: 30px !important;
    }
  }

  .front, .back {
    width: calc(100% - 5px);
    height: calc(100% - 10px);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: ease-in-out 500ms;
  }

  .back {
    transform: rotateY(-180deg);
    position: absolute;
    top: 0;
    overflow: auto;
    display: none;

    .box {
      width: calc(50% - 16px);
      padding: 0 5px;
      float: left;

      .el-row {
        .el-col {
          margin-bottom: 5px;
        }
      }
    }

    .box-title {
      width: 80%;
      font-weight: bold;
      padding: 5px 0;
      margin-bottom: 10px;
      border-bottom: solid 1px var(--scp-border-color);
    }

    .box-footer {
      position: fixed;
      bottom: 5px;
      right: 10px;
    }
  }
}

.htContextMenu:not(.htGhostTable) {
  z-index: 2000 !important;
}

//右键菜单深色模式
.htContextMenu table tbody tr td {
  background: var(--scp-bg-color) !important;
}

.htContextMenu table tbody tr td.current, .htContextMenu table tbody tr td.zeroclipboard-is-hover {
  background: var(--el-color-info-light-5) !important;
}

// 表格按钮深色模式
.handsontable .changeType {
  background: var(--scp-bg-color-page) !important;
}

.el-form-item--small .el-form-item__error {
  padding-top: 0 !important;
}

@keyframes shineRed {
  from {
    -webkit-box-shadow: 0 0 5px #bbb;
  }
  50% {
    -webkit-box-shadow: 0 0 10px red;
  }
  to {
    -webkit-box-shadow: 0 0 5px #bbb;
  }
}

@-moz-keyframes shineRed {
  from {
    -webkit-box-shadow: 0 0 5px #bbb;
  }
  50% {
    -webkit-box-shadow: 0 0 10px red;
  }
  to {
    -webkit-box-shadow: 0 0 5px #bbb;
  }
}

@-webkit-keyframes shineRed {
  from {
    -webkit-box-shadow: 0 0 5px #bbb;
  }
  50% {
    -webkit-box-shadow: 0 0 10px red;
  }
  to {
    -webkit-box-shadow: 0 0 5px #bbb;
  }
}

.shine-red {
  animation: shineRed 3s 5;
  -webkit-animation: shineRed 3s 5;
}

.hero-title {
  padding-bottom: 6px;
  padding-left: 15px;
  font-size: 0.583rem;
  font-weight: bold;
}

.privileges-transfer {
  display: inline-block;
  background-color: var(--scp-border-color);
  border-radius: 4px;
  padding: 10px;
  margin-top: 4px;
  margin-right: 20px;
  margin-bottom: 20px;
}

.demand-unit .privileges-transfer:nth-child(4) {
  margin-top: 4px;
}

/*el-table select 样式*/
.tree-table-box .el-table_1_column_1 .cell, .tree-table-box .el-table_1_column_2 .cell {
  padding-right: 0 !important;
}

.tree-table-box .el-input__inner, .tree-table-box .el-input__inner {
  padding-left: 0 !important;
}

/*穿梭框 button样式*/
.my-privileges .el-transfer__buttons {
  padding: 0 8px;
}

.my-privileges .el-transfer__buttons button {
  display: block;
  padding: 2px 6px;
}

.my-privileges .el-transfer__button:first-child {
  margin-bottom: 10px;
}

.my-privileges .el-transfer__button:last-child {
  margin-left: 0;
}

.top-nav ul li {
  min-width: 130px;
}

.top-nav ul li .router-link-active {
  background: var(--scp-text-color-highlight);
  color: var(--scp-bg-color);

  &:hover {
    background: var(--scp-text-color-highlight);
    color: var(--scp-bg-color);
  }

  &:after {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--scp-bg-color) transparent transparent transparent;
  }
}

.sub-nav ul li .router-link-active {
  color: var(--scp-text-color-highlight);
  font-weight: 500;
  opacity: 1;
}

.color-white {
  color: var(--scp-bg-color);
}

@media (min-width: 1500px) {
  html {
    font-size: 24px;
  }
}

@media (max-width: 1500px) {
  html {
    font-size: 20px;
  }
}

.v-contextmenu {
  font-size: 0.5rem !important;
  padding: 0 !important;
  border-radius: 2px !important;
  border: 1px solid #CCCCCC;
  z-index: 900 !important;
  background-color: var(--scp-bg-color) !important;

  .v-contextmenu-item {
    height: 26px !important;
    line-height: 26px !important;
    min-width: 200px;
    padding: 0 10px 0 15px !important;
    color: var(--scp-text-color-primary) !important;
  }
}

.v-contextmenu-item--hover {
  background-color: var(--el-color-info-light-5) !important;
  color: var(--scp-text-color-primary) !important;
}

body * {
  outline: none !important;
}

.search-box {
  .el-col {
    .el-select, .el-cascader {
      width: var(--scp-input-width);
      margin-bottom: var(--scp-widget-margin);
    }

    .filter-cascader-box {
      width: var(--scp-input-width);
    }

    .el-date-editor {
      width: calc(100% - 30px);
      margin-bottom: var(--scp-widget-margin);
    }
  }
}

.htCore {
  .warning-tr {
    background-color: #c12e34 !important;
    color: var(--scp-bg-color) !important;

    td {
      background-color: #c12e34 !important;
      color: var(--scp-bg-color) !important;
    }
  }

  .notice-tr {
    background-color: #fffccc !important;

    td {
      background-color: #fffccc !important;
    }
  }

  .info-tr {
    background-color: #2b821d !important;
    color: var(--scp-bg-color) !important;

    td {
      background-color: #2b821d !important;
      color: var(--scp-bg-color) !important;
    }
  }

  .striped-odd3-tr {
    background-color: var(--scp-bg-color) !important;

    td {
      background-color: var(--scp-bg-color) !important;
    }
  }

  .striped-even3-tr {
    background-color: var(--el-color-info-light-8) !important;

    td {
      background-color: var(--el-color-info-light-8) !important;
    }
  }
}

.htDropdownMenu:not(.htGhostTable) {
  z-index: 2060 !important; /* needs to be higher than 1050 - z-index for Twitter Bootstrap modal (#1569) */
}

.htFiltersConditionsMenu:not(.htGhostTable) {
  z-index: 2070 !important;
}

.search-group-left {
  .el-input__wrapper,.el-select__wrapper {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    box-shadow: 0.5px 0 0 1px var(--el-input-border-color, var(--scp-border-color)) inset !important;
  }
}

.search-group-right {
  .el-input__wrapper,.el-select__wrapper {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    box-shadow: -0.5px 0 0 1px var(--el-input-border-color, var(--scp-border-color)) inset !important;
  }

  textarea {
    line-height: var(--el-input-inner-height) !important;
    min-height: auto !important;
    height: var(--el-input-inner-height) !important;
    padding: 0 7px !important;
    border-radius: 0 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    resize: none;
    box-shadow: -0.5px 0 0 1px var(--el-input-border-color, var(--scp-border-color)) inset !important;
  }
}

.inline-textarea {
  textarea {
    line-height: 1.2;
    min-height: auto !important;
    height: 1.1rem !important;
    padding: 0.2rem 10px !important;
    border-radius: 0 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    resize: none;
  }
}

pre.display {
  border: 0 !important;
  font-size: 0.5rem !important;
  border-radius: 0 !important;
  background: transparent !important;
  height: 100%;
  overflow: auto;
}

.input-padding-5 {
  .el-input__inner {
    padding: 0 5px !important;
  }
}

.el-input {
  .el-input__count {
    color: #bbbbbb !important;
  }
}

.el-autocomplete-suggestion__wrap {
  padding: 5px 0 !important;
}

.el-autocomplete-suggestion li {
  font-size: 0.5rem !important;
  line-height: 28px !important;
}

.el-input-number__decrease {
  background-color: transparent !important;
  border: 0 !important;
}

.el-input-number__increase {
  background-color: transparent !important;
  border: 0 !important;
}

.el-cascader__tags .el-tag {
  max-width: calc(100% - 130px) !important;
}

.el-message--warning {
  background-color: #fb8c00 !important;
}

/*
.el-select--medium {
  .el-input__inner {
    height: 1.1rem !important;
  }
}
*/

.el-tree {
  background: transparent !important;

  .el-tree-node__content {
    height: 1rem !important;
  }

  .el-tree-node__label {
    font-size: 0.5rem !important;
  }
}

.textarea-input {
  textarea {
    line-height: 1.2;
    min-height: auto !important;
    height: 1.1rem !important;
    padding: 0.2rem 10px !important;
    border-radius: 0 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    resize: none;
  }
}


table.gridtable {
  font-size: 10px;
  color: var(--scp-text-color-primary);
  border-width: 1px;
  border-color: var(--scp-border-color);
  border-collapse: collapse;
  user-select: text;
}

table.gridtable th {
  border-width: 1px;
  padding: 2px 3px;
  border-style: solid;
  border-color: var(--scp-border-color);
  background-color: var(--scp-bg-color-page);
}

table.gridtable .fixed {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  background-color: var(--scp-bg-color-fill) !important;
}

table.gridtable .splitline {
  border: 1px solid #ccc;
}

table.gridtable tbody tr:nth-child(odd) td {
  background-color: var(--scp-bg-color-fill);
}

table.gridtable tbody tr:nth-child(even) td {
  background-color: var(--scp-bg-color);
}

table.gridtable td {
  border-width: 1px;
  padding: 2px 5px;
  border-style: solid;
  border-color: #ccc;
  text-align: right;
}

.el-dropdown__icon {
  font-size: 0.4rem !important;
}

.el-button--inactive {
  color: var(--scp-bg-color);
  background-color: #b8b8b8;
  border-color: #b8b8b8
}

.el-button--inactive:focus, .el-button--inactive:hover {
  background: #c7c7c7;
  border-color: #c7c7c7;
  color: var(--scp-bg-color)
}

.el-button--inactive.is-active, .el-button--inactive:active {
  background: #b2b2b2;
  border-color: #b2b2b2;
  color: var(--scp-bg-color)
}

.el-button--inactive:active {
  outline: 0
}

.el-button--inactive.is-disabled, .el-button--inactive.is-disabled:active, .el-button--inactive.is-disabled:focus, .el-button--inactive.is-disabled:hover {
  color: var(--scp-bg-color);
  background-color: #dadada;
  border-color: #dadada
}

.el-button--inactive.is-plain {
  color: #bdbcbc;
  background: #fff8e6;
  border-color: #a6a6a5
}

.el-button--inactive.is-plain:focus, .el-button--inactive.is-plain:hover {
  background: #9d9d9c;
  border-color: #9d9d9c;
  color: var(--scp-bg-color)
}

.el-button--inactive.is-plain:active {
  background: #bebebc;
  border-color: #bebebc;
  color: var(--scp-bg-color);
  outline: 0
}

.el-button--inactive.is-plain.is-disabled, .el-button--inactive.is-plain.is-disabled:active, .el-button--inactive.is-plain.is-disabled:focus, .el-button--inactive.is-plain.is-disabled:hover {
  color: #cbcbcb;
  background-color: #fff8e6;
  border-color: #d9d9d9
}

.hand {
  cursor: pointer !important;
}

.handsontable td.htInvalid {
  color: var(--scp-bg-color) !important;
}

.handsontable .collapsibleIndicator {
  color: var(--scp-text-color-primary) !important;
  box-shadow: 0 0 0 6px var(--el-color-info-light-7) !important;
  background: var(--scp-bg-color-fill) !important;
}

.el-dialog__header {
  padding: 10px !important;
}

.drag-handle {
  cursor: move;
}

.body-wrapper .left-sidebar .widget .widget-header {
  background-color: var(--scp-bg-color) !important;
  background-image: initial !important;
  height: 0.85rem;
  line-height: 0.85rem;

  .title {
    font-size: 0.5rem !important;
  }
}

/* logic flow 样式*/
.lf-graph {
  .custom-anchor {
    stroke: #999;
    stroke-width: 1;
    fill: #d9d9d9;
    cursor: crosshair;
    rx: 3;
    ry: 3;
  }

  .custom-anchor:hover {
    fill: #ff7f0e;
    stroke: #ff7f0e;
  }

  .lf-relationship {
    cursor: move;
  }

  .lf-node {
    cursor: move;
  }
}

.input-tips {
  padding-left: 10px;
  font-style: italic;
  color: #777;
  height: 24px;
  line-height: 24px;
}

body {
  margin: 0;
  padding: 0;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

p {
  margin: 0;
  padding: 0;
}

hr {
  margin: 20px 0;
  border: 0;
  border-top: 1px solid var(--el-border-color-light);
}

.el-message .el-message__icon {
  color: #fff !important;
  font-size: 0.5rem !important;
}

.el-message__content {
  color: #fff !important;
  font-size: 0.5rem !important;
}

.el-message--success {
  background-color: var(--scp-text-color-highlight) !important;
  border-color: var(--el-color-primary-light-3) !important;
}

.el-message--warning {
  background-color: var(--el-color-warning) !important;
  border-color: var(--el-color-warning-light-3) !important
}

.el-message--error {
  background-color: var(--el-color-error) !important;
  border-color: var(--el-color-error-light-3) !important
}

.clearfix {
  *zoom: 1;

  &:before {
    display: table;
    content: "";
    line-height: 0;
  }

  &:after {
    display: table;
    content: "";
    line-height: 0;
    clear: both;
  }
}

a {
  text-decoration: none;
}

.md-editor {
  --md-scrollbar-bg-color: rgba(238, 238, 238, 0.5) !important;
  --md-scrollbar-thumb-color: rgba(222, 222, 222, 0.75) !important;
  --md-scrollbar-thumb-hover-color: var(--scp-text-color-highlight) !important;
  --md-scrollbar-thumb-avtive-color: var(--scp-text-color-highlight) !important;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.handsontable {
  font-size: 0.5rem !important;
  font-family: inherit !important;
  color: var(--scp-text-color-primary) !important;
}

.htDropdownMenu table tbody tr td {
  background: black;
}

// hot-table表格筛选框深色
.handsontable .ht_master table td.htCustomMenuRenderer {
  background-color: var(--scp-bg-color) !important;
}

.htDropdownMenu table tbody tr td {
  background: var(--scp-bg-color) !important;
}

.handsontable .htUISelectCaption {
  background: var(--scp-bg-color) !important;
}

.htFiltersConditionsMenu table tbody tr td {
  background: var(--scp-bg-color) !important;
}

.htFiltersConditionsMenu table tbody tr td.current, .htFiltersConditionsMenu table tbody tr td.zeroclipboard-is-hover {
  background: var(--el-color-info-light-5) !important;
}

.htDropdownMenu table tbody tr td.current, .htDropdownMenu table tbody tr td.zeroclipboard-is-hover {
  background: var(--el-color-info-light-5) !important;
}

.htDropdownMenu table tbody tr td.current, .htDropdownMenu table tbody tr td.zeroclipboard-is-hover {
  background: var(--el-color-info-light-5) !important;
}

.ht_editor_visible {
  color: var(--scp-text-color-highlight) !important;
  font-size: 0.5rem !important;
}

.el-dialog__title {
  font-size: 0.54rem !important;
  font-weight: bold;
}

.el-dialog__body {
  padding: 15px 20px !important;
}

.handsontable th {
  background-color: var(--scp-bg-color-fill) !important;
  color: var(--scp-text-color-primary) !important;
}

.handsontableInput {
  background-color: var(--scp-bg-color) !important;
  color: var(--scp-text-color-primary) !important;
}

.pika-single {
  background-color: var(--scp-bg-color) !important;
  color: var(--scp-text-color-primary) !important;
}

.pika-button {
  background-color: var(--scp-bg-color) !important;
  color: var(--scp-text-color-primary) !important;
}

.pika-button:hover {
  background-color: var(--el-color-primary-light-3) !important;
  color: var(--scp-text-color-primary) !important;
}

.pika-label {
  background-color: var(--scp-bg-color) !important;
  color: var(--scp-text-color-primary) !important;
}

.table-striped tbody > tr:nth-child(even) > {
  td {
    background-color: var(--scp-bg-color-fill-lighter)
  }
}

.table-striped tbody > tr:nth-child(odd) > {
  td {
    background-color: var(--scp-bg-color)
  }
}

.is-selected .pika-button, .has-event .pika-button {
  box-shadow: none;
  border: 1px solid var(--scp-text-color-highlight);
}

.el-tabs--card {
  --el-tabs-header-height: auto !important;
}

.el-input--small {
  font-size: 0.5rem;
  height: 1.08333rem !important;

  .el-input__wrapper {
    padding-left: 0.5rem !important;
  }

  .el-input__inner {
    font-size: 0.5rem;
  }
}

.el-select--small {
  line-height: 1.08333rem !important;
  min-height: 1.08333rem !important;
  .el-select__selected-item:first-child {
    max-width: calc(100% - 40px) !important;
  }
}

.el-select--small .el-select__wrapper {
  line-height: 1.08333rem !important;
  min-height: 1.08333rem !important;
  padding: 0 8px !important;
}

.el-select-dropdown__item {
  height: 1.1rem !important;
  line-height: 1.1rem !important;
}

pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: .5rem;
  line-height: .75rem;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px
}

code, pre {
  padding: 0 3px 2px;
  font-family: Monaco, Menlo, Consolas, Courier New, monospace;
  font-size: .5rem;
  color: #333;
  border-radius: 3px
}

.md-editor-content {
  .default-theme {
    pre {
      margin: 0 0 20px 0 !important;
      padding: 0 !important;
      box-shadow: none !important;
    }
  }
}

table {
  max-width: 100%;
  background-color: transparent;
  border-collapse: collapse;
  border-spacing: 0;
}

.table {
  width: 100%;
  margin-bottom: 10px;

  th, td {
    padding: 8px;
    line-height: 20px;
    text-align: left;
    vertical-align: top;
    border-top: 1px solid var(--scp-border-color);
  }

  th {
    font-weight: bold;
    padding: 10px 8px
  }

  thead th {
    vertical-align: bottom;
  }

  caption + thead tr:first-child {
    th, td {
      border-top: 0;
    }
  }

  colgroup + thead tr:first-child {
    th, td {
      border-top: 0;
    }
  }

  thead:first-child tr:first-child {
    th, td {
      border-top: 0;
    }
  }

  tbody + tbody {
    border-top: 2px solid var(--scp-border-color);
  }

  .table {
    background-color: var(--scp-bg-color);
  }
}

.table-condensed {
  th, td {
    padding: 4px 5px;
  }

  th {
    padding: 4px 5px;
  }
}

.table-bordered {
  border: 1px solid var(--scp-border-color);
  border-left: 0;

  th, td {
    border-left: 1px solid var(--scp-border-color);
  }

  caption + {
    thead tr:first-child th {
      border-top: 0;
    }

    tbody tr:first-child {
      th, td {
        border-top: 0;
      }
    }
  }

  colgroup + {
    thead tr:first-child th {
      border-top: 0;
    }

    tbody tr:first-child {
      th, td {
        border-top: 0;
      }
    }
  }

  thead:first-child tr:first-child th {
    border-top: 0;
  }

  tbody:first-child tr:first-child {
    th, td {
      border-top: 0;
    }
  }
}

.table-striped tbody > tr:nth-child(odd) > {
  th {
    background-color: var(--scp-bg-color-fill-lighter);
  }
}

.table-striped tbody > tr:nth-child(even) > {
  td {
    background-color: var(--scp-bg-color-fill-lighter);
  }
}

.table-hover tbody tr:hover {
  td, th {
    background-color: var(--scp-bg-color-fill-lighter);
  }
}

table {
  td[class*="span"], th[class*="span"] {
    display: table-cell;
    float: none;
    margin-left: 0;
  }
}

.row-fluid table {
  td[class*="span"], th[class*="span"] {
    display: table-cell;
    float: none;
    margin-left: 0;
  }
}

.table {
  td.span1, th.span1 {
    float: none;
    width: 44px;
    margin-left: 0;
  }

  td.span2, th.span2 {
    float: none;
    width: 124px;
    margin-left: 0;
  }

  td.span3, th.span3 {
    float: none;
    width: 204px;
    margin-left: 0;
  }

  td.span4, th.span4 {
    float: none;
    width: 284px;
    margin-left: 0;
  }

  td.span5, th.span5 {
    float: none;
    width: 364px;
    margin-left: 0;
  }

  td.span6, th.span6 {
    float: none;
    width: 444px;
    margin-left: 0;
  }

  td.span7, th.span7 {
    float: none;
    width: 524px;
    margin-left: 0;
  }

  td.span8, th.span8 {
    float: none;
    width: 604px;
    margin-left: 0;
  }

  td.span9, th.span9 {
    float: none;
    width: 684px;
    margin-left: 0;
  }

  td.span10, th.span10 {
    float: none;
    width: 764px;
    margin-left: 0;
  }

  td.span11, th.span11 {
    float: none;
    width: 844px;
    margin-left: 0;
  }

  td.span12, th.span12 {
    float: none;
    width: 924px;
    margin-left: 0;
  }

  tbody tr {
    &.success td {
      background-color: #dff0d8;
    }

    &.error td {
      background-color: #f2dede;
    }

    &.warning td {
      background-color: #fcf8e3;
    }

    &.info td {
      background-color: #d9edf7;
    }
  }
}

.table-hover tbody tr {
  &.success:hover td {
    background-color: #d0e9c6;
  }

  &.error:hover td {
    background-color: #ebcccc;
  }

  &.warning:hover td {
    background-color: #faf2cc;
  }

  &.info:hover td {
    background-color: #c4e3f3;
  }
}

.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
  font-size: 0.5rem !important;
  color: #303330 !important;
  font-weight: 600 !important;
}

.el-transfer-panel .el-transfer-panel__header {
  height: 30px !important;
  line-height: 30px !important;
  background: var(--scp-bg-color-fill) !important;
  margin: 0 !important;
  padding-left: 15px !important;
  border-bottom: 1px solid var(--scp-border-color-lighter) !important;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box !important;
  color: var(--scp-text-color-primary) !important;
}

.el-transfer-panel__filter {
  text-align: center !important;
  margin: 10px !important;
  box-sizing: border-box !important;
  display: block !important;
  width: auto !important;

  .el-input__wrapper {
    width: calc(100% - 30px);

    .el-input__inner {
      height: 22px !important;
      width: 100% !important;
      font-size: 0.5rem !important;
      display: inline-block !important;
      -webkit-box-sizing: border-box !important;
      box-sizing: border-box !important;
      border-radius: 2px !important;
      padding-right: 10px !important;
    }
  }
}

.el-button--small {
  --el-button-size: 1.08333rem !important;
  font-size: 0.5rem !important;
}

pre {
  background-color: var(--scp-bg-color-fill-lighter);
  color: var(--scp-text-color-primary)
}

.el-popover.el-popper {
  width: auto !important;
  min-width: auto !important;
}

/* CK Editor 菜单栏*/
:root {
  --ck-color-button-default-hover-background: var(--el-color-info-light-9) !important;
  --ck-color-base-text: var(--scp-text-color-primary) !important;
  --ck-color-base-foreground: var(--scp-bg-color-fill) !important;
  --ck-color-base-background: var(--scp-bg-color) !important;
  --ck-color-button-on-hover-background: var(--el-color-info-light-9) !important;
  --ck-color-button-on-active-background: var(--el-color-info-light-9) !important;
  --ck-color-button-default-active-background: var(--el-color-info-light-9) !important;
  --ck-color-button-on-background: var(--el-color-info-light-9) !important;
}

.ck.ck-toolbar {
  background: inherit !important;
  border: 1px solid var(--scp-border-color) !important;
  border-bottom: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.ck-toolbar-container {
  z-index: 2500 !important;
}

/* CK Editor 添加默认边框*/
.ck-content {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  border: 1px solid var(--scp-border-color) !important;
}

/* CK Editor 移除阴影, 添加边框颜色*/
.ck-focused {
  border: 1px solid var(--scp-text-color-highlight) !important;
}

.ck.ck-button:active, .ck.ck-button:focus, a.ck.ck-button:active, a.ck.ck-button:focus {
  border: none !important;
  box-shadow: none !important;
}

.el-collapse {
  --el-collapse-header-height: 1.5rem !important;
  --el-collapse-header-font-size: 0.5rem !important;
  --el-collapse-content-font-size: 0.5rem !important;
}

.el-tag {
  --el-tag-border-radius: 0 !important;
}

.el-divider__text {
  font-size: 0.5rem !important;
}

.lf-graph {
  background-color: var(--scp-bg-color) !important;
}

.el-table thead {
  --el-table-header-text-color: var(--scp-text-color-primary);

  /* 很重要, 用户点击箭头的时候会触发独立的排序事件, 这会导致自定义排序的失效 */
  .sort-caret.ascending {
    pointer-events: none;
  }
  .sort-caret.descending {
    pointer-events: none;
  }
}

.el-select--small {
  .el-select__tags-text {
    font-size: 0.45rem !important;
  }
}

.el-loading-mask {
  z-index: 1000 !important;
}

.el-range-editor--small .el-range-input {
  height: 0.91667rem;
  line-height: 0.91667rem;
  font-size: 0.5rem !important;
}

.el-table--small {
  font-size: 0.5rem !important;
}

/* 去掉el-table的边框 */
.el-table--border .el-table__inner-wrapper::after, .el-table--border::after, .el-table--border::before,
.el-table__inner-wrapper::before, .el-table__border-left-patch, .el-table__border-left-patch::after {
  background: transparent !important;
  width: 0 !important;
  height: 0 !important;
}

.el-popper, .el-scrollbar__bar, .el-checkbox__inner,
.handsontable .collapsibleIndicator, .handsontable .changeType, .handsontable .htUISelectCaption,
.handsontable .htUIMultipleSelectSearch input, .handsontable .htUIInput input {
  border-radius: 0 !important;
}

.handsontable .htUIInput.htUIButtonOK input {
  background-color: var(--scp-text-color-highlight) !important;
  border: 1px solid var(--scp-text-color-highlight) !important;
}

.handsontable .htUIInput.htUIButtonCancel input {
  background-color: var(--scp-bg-color) !important;
  color: var(--scp-text-color-primary) !important;
}

.handsontable .collapsibleIndicator {
  box-shadow: none !important;
}

.report-sub-title-right {
  position: absolute;
  right: 10px;
  z-index: 101;
}
