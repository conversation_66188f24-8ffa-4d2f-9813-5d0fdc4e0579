/* 系统标准颜色定义 */
html.light {
  /* 全局字体 */
  --scp-font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;

  /* 边框颜色 */
  --scp-border-color: #dcdcdc; /* 主要边框颜色 */
  --scp-border-color-lighter: #ebeef5; /* 淡边框颜色 */
  --scp-border-color-error: #f56c6c; /* 错误边框颜色 */
  --scp-border-color-success: #67c23a; /* 错误边框颜色 */
  --scp-border-color-warning: #e6a23c; /* 错误边框颜色 */
  --scp-border-color-highlight: #409eff; /* 高亮边框颜色 */

  /* 字体颜色 */
  --scp-text-color-primary: #303133; /* 主要字体颜色 */
  --scp-text-color-secondary: #909399; /* 次要字体颜色 */
  --scp-text-color-lighter: #cccccc; /* 提示字体颜色 */
  --scp-text-color-error: var(--scp-border-color-error); /* 错误字体颜色 */
  --scp-text-color-highlight: var(--scp-border-color-highlight); /* 高亮字体颜色 */
  --scp-text-color-hightlight-lighter: #94cee5; /* 浅色高亮字体颜色 */
  --scp-text-color-success: var(--scp-border-color-success); /* 成功字体颜色 */
  --scp-text-color-warning: var(--scp-border-color-warning); /* 警告字体颜色 */

  /* 背景颜色 */
  --scp-bg-color: #fff; /* 默认背景色 */
  --scp-bg-color-highlight: var(--scp-text-color-highlight); /* 高亮背景色 */
  --scp-bg-color-success: var(--scp-text-color-success); /* 成功背景色 */
  --scp-bg-color-error: var(--scp-border-color-error); /* 错误背景色 */
  --scp-bg-color-warning: var(--scp-text-color-warning); /* 警告背景颜色 */
  --scp-bg-color-fill: #f0f0f0; /* 填充背景色 */
  --scp-bg-color-fill-lighter: #fafafa; /* 填充背景色浅 */
  --scp-bg-color-selected: #f5f7fa; /* 选中背景色 */
  --scp-bg-color-hover: #ecf5ff; /* 鼠标悬停背景色 */


  /* input & select width */
  --scp-input-width: calc(100% - 0.5rem);

  /* 报表之间的间距 */
  --scp-widget-margin: 0.3333rem;

  /* 卡片圆角 */
  .el-card {
    --el-card-border-radius: 0;
  }
}

/* 将系统颜色应用到Element UI */
body {
  /* 字体映射 */
  --el-font-family: var(--scp-font-family);
  font-family: var(--el-font-family);

  /* 字体颜色映射 */
  color: var(--scp-text-color-primary);
  --el-text-color-regular: var(--scp-text-color-primary);
  --el-text-color-primary: var(--scp-text-color-primary);
  --el-color-info: var(--scp-text-color-secondary);
  --el-color-primary: var(--scp-text-color-highlight);
  --el-text-color-secondary: var(--scp-text-color-secondary);

  /* 背景色映射 */
  --el-bg-color: var(--scp-bg-color);
  --el-fill-color-lighter: var(--scp-bg-color-fill-lighter);
  --el-fill-color: var(--scp-bg-color-fill);
  --el-bg-color-page: var(--scp-bg-color-fill);

  /* 边框颜色映射 */
  --el-border-color-lighter: var(--scp-border-color-lighter);
  --el-border-color: var(--scp-border-color);
  --el-select-input-focus-border-color: var(--scp-border-color-highlight);
}

/* 自定义主题元素 */
