{"name": "scp-ui-vue3", "version": "0.1.0", "private": true, "scripts": {"fix-memory-limit": "cross-env LIMIT=8048 increase-memory-limit", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@ckeditor/ckeditor5-build-decoupled-document": "^36.0.1", "@ckeditor/ckeditor5-vue": "^4.0.1", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@handsontable/vue3": "^12.3.1", "@logicflow/core": "^1.2.4", "@logicflow/extension": "^1.2.4", "ace-builds": "^1.23.4", "axios": "^1.8.2", "core-js": "^3.8.3", "dhtmlx-gantt": "^8.0.0", "echarts": "^5.6.0", "echarts-stat": "^1.2.0", "element-plus": "^2.9.11", "handsontable": "^12.3.1", "heatmap.js": "^2.0.5", "html2canvas": "^1.4.1", "md-editor-v3": "^5.3.2", "mqtt": "^4.3.7", "nprogress": "^0.2.0", "url": "^0.11.0", "v-contextmenu": "^3.0.0", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.7.3", "vue-router": "^4.1.6", "vue3-ace-editor": "^2.2.2", "vue3-draggable-resizable": "^1.6.5", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.25.7", "@babel/plugin-proposal-private-methods": "^7.18.6", "@types/heatmap.js": "^2.0.37", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "@vue/cli-plugin-babel": "^5.0.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^8.0.1", "crypto-browserify": "^3.12.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.1.0", "eslint-plugin-vue": "^9.28.0", "process": "^0.11.10", "sass": "^1.58.3", "sass-loader": "^13.2.0", "speed-measure-webpack-plugin": "^1.5.0", "stream-browserify": "^3.0.0", "typescript": "^4.9.5", "vm-browserify": "^1.1.2", "webpack": "^5.95.0"}, "eslintConfig": {"root": true, "env": {"node": true, "vue/setup-compiler-macros": true}, "extends": ["plugin:vue/vue3-essential", "@vue/standard"], "parserOptions": {"parser": "@typescript-eslint/parser"}, "plugins": ["vue", "@typescript-eslint"], "rules": {"vue/no-mutating-props": 0, "no-unused-vars": 0, "no-prototype-builtins": 0, "vue/multi-word-component-names": 0, "no-empty": 0, "no-case-declarations": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}