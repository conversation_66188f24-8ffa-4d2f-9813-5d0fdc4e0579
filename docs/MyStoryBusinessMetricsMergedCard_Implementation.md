# MyStoryBusinessMetricsMergedCard 实现总结

## 完成的工作

### 1. 创建新的合并卡片组件
- 创建了 `MyStoryBusinessMetricsMergedCard.vue` 组件
- 将四张业务指标卡片（Order Intake、Sales、FCST、Backlog）合并成一张卡片
- 保留了树表结构，支持父子层级关系
- 实现了多级标题样式，参照原来的样式设计

### 2. 组件特性
- **多级表头设计**：
  - 第一级：Entity | Order Intake | Sales | FCST | Backlog
  - 第二级：每个业务指标下包含 MTD、Y ON Y、WEIGHT 三个子列
- **数据合并逻辑**：
  - 将四个数据源（orderIntakeData、salesData、fcstData、backlogData）合并
  - 按 entity 字段进行数据合并
  - 保持原有的 rowType、children 等树表属性
- **样式保持一致**：
  - 保留原有卡片的颜色主题和字体样式
  - 支持箭头指示器和颜色编码
  - 字段和数字对齐显示

### 3. 修改父组件布局
- 修改了 `MyStoryBusinessMetrics.vue` 的布局结构
- 将原来的四列布局调整为两列：
  - 左侧（16列）：新的合并业务指标卡片
  - 右侧（8列）：其他性能指标卡片
- 导入并使用新的合并卡片组件

### 4. 数据结构
```typescript
interface MergedCardData {
  id: string,
  entity: string,
  rowType: string,
  orderIntake?: {
    mtd: string,
    mtdColor?: string,
    yoy: string,
    yoyColor?: string,
    weight: string,
    weightColor?: string,
    arrowValue?: number,
    arrowColor?: string
  },
  sales?: { /* 同上结构 */ },
  fcst?: { /* 同上结构 */ },
  backlog?: { /* 同上结构 */ },
  children?: MergedCardData[]
}
```

### 5. 组件属性
```typescript
const props = {
  monthName: string,           // 月份名称，用于动态标题
  orderIntakeData: CardData[], // Order Intake 数据
  salesData: CardData[],       // Sales 数据
  fcstData: CardData[],        // FCST 数据
  backlogData: CardData[]      // Backlog 数据
}
```

## 技术实现要点

### 1. 数据合并算法
- 使用 Map 数据结构按 entity 进行数据分组
- 支持父子层级数据的递归合并
- 处理缺失数据的情况（显示 '--'）

### 2. 多级表头实现
- 使用 Element UI 的 el-row 和 el-col 组件构建多级表头
- 通过 CSS 样式实现边框和对齐效果
- 动态显示月份名称

### 3. 树表功能
- 使用 Element UI 的 el-table 组件的 tree-props 属性
- 支持展开/收起子项功能
- 保持原有的字体大小和样式区分

### 4. 样式继承
- 继承原有卡片组件的样式设计
- 支持 PI/KPI 类型的颜色主题
- 保持箭头指示器和百分比显示

## 使用方式

在父组件中使用：
```vue
<my-story-business-metrics-merged-card
    v-loading="pageCtl.loading.report1"
    :month-name="pageCtl.conditions.monthName"
    :order-intake-data="pageCtl.data.orderIntake"
    :sales-data="pageCtl.data.sales"
    :fcst-data="pageCtl.data.fcst"
    :backlog-data="pageCtl.data.backlog"/>
```

## 最新调整（数据宽度和列名对齐优化）

### 1. 表格列宽优化
- **Entity列**：设置固定宽度100px，并设置为固定列（fixed="left"）
- **数据列**：使用 `min-width` 替代固定 `width`，让列能够自适应扩展
  - MTD列：min-width="100px"
  - Y ON Y列：min-width="100px"
  - Weight列：min-width="80px"
- **表格布局**：添加 `table-layout: fixed` 确保列宽分配更均匀

### 2. 数据对齐优化
- **数字对齐**：所有数据列使用居中对齐（`text-align: center`）
- **内边距调整**：移除额外的padding，保持简洁的显示效果
- **表头对齐**：重新设计多级表头布局，使用flexbox确保对齐

### 3. 表头布局重构
- **第一级表头**：
  - Entity：固定宽度100px
  - 业务指标组：使用flex布局，每组最小宽度280px
- **第二级表头**：
  - 使用flexbox布局确保子列均匀分布
  - 每个子列使用 `flex: 1` 实现等宽分布

### 4. CSS样式优化
```scss
.header-row {
  display: flex;
  width: 100%;

  .entity-header {
    width: 100px;
    flex-shrink: 0;
  }

  .group-header {
    flex: 1;
    min-width: 280px;
  }

  .sub-headers-group {
    flex: 1;
    display: flex;
    min-width: 280px;

    .sub-header-cell {
      flex: 1;
      text-align: center;
    }
  }
}
```

## 编译状态
✅ 组件编译成功，无语法错误
✅ ESLint 检查通过
✅ 开发服务器运行正常
✅ 数据宽度和对齐问题已修复

## 预期效果
- 数据列现在会充分利用表格宽度
- 数字居中对齐，视觉效果更均衡
- 表头与数据列完美对齐
- Entity列固定，便于查看长列表时的实体名称

## 下一步建议
1. 在浏览器中访问 http://localhost:8082 查看优化效果
2. 测试不同屏幕尺寸下的显示效果
3. 验证树表展开/收起功能
4. 测试实际数据的显示效果
